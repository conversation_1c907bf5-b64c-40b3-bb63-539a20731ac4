// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserSession _$UserSessionFromJson(Map<String, dynamic> json) => UserSession(
  id: json['id'] as String,
  userId: json['userId'] as String,
  startTime: DateTime.parse(json['startTime'] as String),
  endTime: json['endTime'] == null
      ? null
      : DateTime.parse(json['endTime'] as String),
  duration: json['duration'] == null
      ? null
      : Duration(microseconds: (json['duration'] as num).toInt()),
  screenViews: (json['screenViews'] as num?)?.toInt() ?? 0,
  actionsCount: (json['actionsCount'] as num?)?.toInt() ?? 0,
  deviceInfo: json['deviceInfo'] as String,
  appVersion: json['appVersion'] as String,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$UserSessionToJson(UserSession instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'duration': instance.duration?.inMicroseconds,
      'screenViews': instance.screenViews,
      'actionsCount': instance.actionsCount,
      'deviceInfo': instance.deviceInfo,
      'appVersion': instance.appVersion,
      'createdAt': instance.createdAt.toIso8601String(),
    };

UserAction _$UserActionFromJson(Map<String, dynamic> json) => UserAction(
  id: json['id'] as String,
  sessionId: json['sessionId'] as String,
  userId: json['userId'] as String,
  actionType: json['actionType'] as String,
  actionName: json['actionName'] as String,
  screenName: json['screenName'] as String?,
  parameters: json['parameters'] as Map<String, dynamic>? ?? const {},
  timestamp: DateTime.parse(json['timestamp'] as String),
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$UserActionToJson(UserAction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sessionId': instance.sessionId,
      'userId': instance.userId,
      'actionType': instance.actionType,
      'actionName': instance.actionName,
      'screenName': instance.screenName,
      'parameters': instance.parameters,
      'timestamp': instance.timestamp.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
    };

AppPerformance _$AppPerformanceFromJson(Map<String, dynamic> json) =>
    AppPerformance(
      id: json['id'] as String,
      sessionId: json['sessionId'] as String,
      metricName: json['metricName'] as String,
      metricValue: (json['metricValue'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$AppPerformanceToJson(AppPerformance instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sessionId': instance.sessionId,
      'metricName': instance.metricName,
      'metricValue': instance.metricValue,
      'timestamp': instance.timestamp.toIso8601String(),
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
    };

UserBehaviorAnalytics _$UserBehaviorAnalyticsFromJson(
  Map<String, dynamic> json,
) => UserBehaviorAnalytics(
  userId: json['userId'] as String,
  totalSessions: (json['totalSessions'] as num).toInt(),
  totalActions: (json['totalActions'] as num).toInt(),
  averageSessionDuration: Duration(
    microseconds: (json['averageSessionDuration'] as num).toInt(),
  ),
  mostUsedFeatures: (json['mostUsedFeatures'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  screenTimeDistribution:
      (json['screenTimeDistribution'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, Duration(microseconds: (e as num).toInt())),
      ),
  engagementScore: (json['engagementScore'] as num).toDouble(),
  retentionRate: (json['retentionRate'] as num).toDouble(),
  preferredTimes: (json['preferredTimes'] as List<dynamic>)
      .map((e) => (e as num).toInt())
      .toList(),
);

Map<String, dynamic> _$UserBehaviorAnalyticsToJson(
  UserBehaviorAnalytics instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'totalSessions': instance.totalSessions,
  'totalActions': instance.totalActions,
  'averageSessionDuration': instance.averageSessionDuration.inMicroseconds,
  'mostUsedFeatures': instance.mostUsedFeatures,
  'screenTimeDistribution': instance.screenTimeDistribution.map(
    (k, e) => MapEntry(k, e.inMicroseconds),
  ),
  'engagementScore': instance.engagementScore,
  'retentionRate': instance.retentionRate,
  'preferredTimes': instance.preferredTimes,
};

BusinessIntelligence _$BusinessIntelligenceFromJson(
  Map<String, dynamic> json,
) => BusinessIntelligence(
  totalUsers: (json['totalUsers'] as num).toInt(),
  activeUsers: (json['activeUsers'] as num).toInt(),
  newUsers: (json['newUsers'] as num).toInt(),
  sessionCount: (json['sessionCount'] as num).toInt(),
  averageSessionDuration: Duration(
    microseconds: (json['averageSessionDuration'] as num).toInt(),
  ),
  bounceRate: (json['bounceRate'] as num).toDouble(),
  retentionRates: (json['retentionRates'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(k, (e as num).toDouble()),
  ),
  featureUsage: Map<String, int>.from(json['featureUsage'] as Map),
  userGrowth: (json['userGrowth'] as List<dynamic>)
      .map((e) => (e as num).toInt())
      .toList(),
  engagementMetrics: (json['engagementMetrics'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(k, (e as num).toDouble()),
  ),
);

Map<String, dynamic> _$BusinessIntelligenceToJson(
  BusinessIntelligence instance,
) => <String, dynamic>{
  'totalUsers': instance.totalUsers,
  'activeUsers': instance.activeUsers,
  'newUsers': instance.newUsers,
  'sessionCount': instance.sessionCount,
  'averageSessionDuration': instance.averageSessionDuration.inMicroseconds,
  'bounceRate': instance.bounceRate,
  'retentionRates': instance.retentionRates,
  'featureUsage': instance.featureUsage,
  'userGrowth': instance.userGrowth,
  'engagementMetrics': instance.engagementMetrics,
};

EngagementMetrics _$EngagementMetricsFromJson(Map<String, dynamic> json) =>
    EngagementMetrics(
      dailyActiveUsers: (json['dailyActiveUsers'] as num).toDouble(),
      weeklyActiveUsers: (json['weeklyActiveUsers'] as num).toDouble(),
      monthlyActiveUsers: (json['monthlyActiveUsers'] as num).toDouble(),
      sessionFrequency: (json['sessionFrequency'] as num).toDouble(),
      averageSessionLength: Duration(
        microseconds: (json['averageSessionLength'] as num).toInt(),
      ),
      retentionDay1: (json['retentionDay1'] as num).toDouble(),
      retentionDay7: (json['retentionDay7'] as num).toDouble(),
      retentionDay30: (json['retentionDay30'] as num).toDouble(),
      churnRate: (json['churnRate'] as num).toDouble(),
      featureAdoption: (json['featureAdoption'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$EngagementMetricsToJson(EngagementMetrics instance) =>
    <String, dynamic>{
      'dailyActiveUsers': instance.dailyActiveUsers,
      'weeklyActiveUsers': instance.weeklyActiveUsers,
      'monthlyActiveUsers': instance.monthlyActiveUsers,
      'sessionFrequency': instance.sessionFrequency,
      'averageSessionLength': instance.averageSessionLength.inMicroseconds,
      'retentionDay1': instance.retentionDay1,
      'retentionDay7': instance.retentionDay7,
      'retentionDay30': instance.retentionDay30,
      'churnRate': instance.churnRate,
      'featureAdoption': instance.featureAdoption,
    };

UserSegment _$UserSegmentFromJson(Map<String, dynamic> json) => UserSegment(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  criteria: json['criteria'] as Map<String, dynamic>,
  userIds: (json['userIds'] as List<dynamic>).map((e) => e as String).toList(),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$UserSegmentToJson(UserSegment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'criteria': instance.criteria,
      'userIds': instance.userIds,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

ConversionFunnel _$ConversionFunnelFromJson(Map<String, dynamic> json) =>
    ConversionFunnel(
      id: json['id'] as String,
      name: json['name'] as String,
      steps: (json['steps'] as List<dynamic>).map((e) => e as String).toList(),
      stepCounts: Map<String, int>.from(json['stepCounts'] as Map),
      conversionRates: (json['conversionRates'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      dateRange: DateTime.parse(json['dateRange'] as String),
    );

Map<String, dynamic> _$ConversionFunnelToJson(ConversionFunnel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'steps': instance.steps,
      'stepCounts': instance.stepCounts,
      'conversionRates': instance.conversionRates,
      'dateRange': instance.dateRange.toIso8601String(),
    };

UserJourney _$UserJourneyFromJson(Map<String, dynamic> json) => UserJourney(
  userId: json['userId'] as String,
  actions: (json['actions'] as List<dynamic>)
      .map((e) => UserAction.fromJson(e as Map<String, dynamic>))
      .toList(),
  totalDuration: Duration(microseconds: (json['totalDuration'] as num).toInt()),
  startScreen: json['startScreen'] as String,
  endScreen: json['endScreen'] as String,
  touchpoints: (json['touchpoints'] as num).toInt(),
  path: (json['path'] as List<dynamic>).map((e) => e as String).toList(),
);

Map<String, dynamic> _$UserJourneyToJson(UserJourney instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'actions': instance.actions,
      'totalDuration': instance.totalDuration.inMicroseconds,
      'startScreen': instance.startScreen,
      'endScreen': instance.endScreen,
      'touchpoints': instance.touchpoints,
      'path': instance.path,
    };

PerformanceMetrics _$PerformanceMetricsFromJson(Map<String, dynamic> json) =>
    PerformanceMetrics(
      appStartTime: (json['appStartTime'] as num).toDouble(),
      screenLoadTime: (json['screenLoadTime'] as num).toDouble(),
      apiResponseTime: (json['apiResponseTime'] as num).toDouble(),
      memoryUsage: (json['memoryUsage'] as num).toDouble(),
      cpuUsage: (json['cpuUsage'] as num).toDouble(),
      crashCount: (json['crashCount'] as num).toInt(),
      errorRate: (json['errorRate'] as num).toDouble(),
      customMetrics: (json['customMetrics'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$PerformanceMetricsToJson(PerformanceMetrics instance) =>
    <String, dynamic>{
      'appStartTime': instance.appStartTime,
      'screenLoadTime': instance.screenLoadTime,
      'apiResponseTime': instance.apiResponseTime,
      'memoryUsage': instance.memoryUsage,
      'cpuUsage': instance.cpuUsage,
      'crashCount': instance.crashCount,
      'errorRate': instance.errorRate,
      'customMetrics': instance.customMetrics,
    };

RevenueAnalytics _$RevenueAnalyticsFromJson(Map<String, dynamic> json) =>
    RevenueAnalytics(
      totalRevenue: (json['totalRevenue'] as num).toDouble(),
      monthlyRecurringRevenue: (json['monthlyRecurringRevenue'] as num)
          .toDouble(),
      averageRevenuePerUser: (json['averageRevenuePerUser'] as num).toDouble(),
      customerLifetimeValue: (json['customerLifetimeValue'] as num).toDouble(),
      churnRate: (json['churnRate'] as num).toDouble(),
      revenueByFeature: (json['revenueByFeature'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      monthlyRevenue: (json['monthlyRevenue'] as List<dynamic>)
          .map((e) => (e as num).toDouble())
          .toList(),
    );

Map<String, dynamic> _$RevenueAnalyticsToJson(RevenueAnalytics instance) =>
    <String, dynamic>{
      'totalRevenue': instance.totalRevenue,
      'monthlyRecurringRevenue': instance.monthlyRecurringRevenue,
      'averageRevenuePerUser': instance.averageRevenuePerUser,
      'customerLifetimeValue': instance.customerLifetimeValue,
      'churnRate': instance.churnRate,
      'revenueByFeature': instance.revenueByFeature,
      'monthlyRevenue': instance.monthlyRevenue,
    };

CohortAnalysis _$CohortAnalysisFromJson(Map<String, dynamic> json) =>
    CohortAnalysis(
      cohortId: json['cohortId'] as String,
      cohortDate: DateTime.parse(json['cohortDate'] as String),
      initialSize: (json['initialSize'] as num).toInt(),
      retentionByPeriod: (json['retentionByPeriod'] as Map<String, dynamic>)
          .map((k, e) => MapEntry(int.parse(k), (e as num).toInt())),
      retentionRateByPeriod:
          (json['retentionRateByPeriod'] as Map<String, dynamic>).map(
            (k, e) => MapEntry(int.parse(k), (e as num).toDouble()),
          ),
      averageRetention: (json['averageRetention'] as num).toDouble(),
    );

Map<String, dynamic> _$CohortAnalysisToJson(CohortAnalysis instance) =>
    <String, dynamic>{
      'cohortId': instance.cohortId,
      'cohortDate': instance.cohortDate.toIso8601String(),
      'initialSize': instance.initialSize,
      'retentionByPeriod': instance.retentionByPeriod.map(
        (k, e) => MapEntry(k.toString(), e),
      ),
      'retentionRateByPeriod': instance.retentionRateByPeriod.map(
        (k, e) => MapEntry(k.toString(), e),
      ),
      'averageRetention': instance.averageRetention,
    };
