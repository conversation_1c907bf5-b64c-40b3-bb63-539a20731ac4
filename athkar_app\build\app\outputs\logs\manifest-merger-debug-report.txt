-- Merging decision tree log ---
application
INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:16:5-67:19
INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-17:19
MERGED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-17:19
MERGED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa901e4755a3670193aa5d35ba930f95\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa901e4755a3670193aa5d35ba930f95\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:21:5-36:19
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:21:5-36:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1f7a922323fd2e7e330d6c2a2b6d1d4b\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1f7a922323fd2e7e330d6c2a2b6d1d4b\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de09d56ede5c875ebbc0232053614904\transformed\jetified-firebase-analytics-22.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de09d56ede5c875ebbc0232053614904\transformed\jetified-firebase-analytics-22.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8fbf22bcb175ef393e26705bdeff75e\transformed\jetified-play-services-measurement-sdk-22.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8fbf22bcb175ef393e26705bdeff75e\transformed\jetified-play-services-measurement-sdk-22.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\244ebfbd542d32fc159ad1c2e863258a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\244ebfbd542d32fc159ad1c2e863258a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\500a0c9cb71e092f235465c1951cf452\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\500a0c9cb71e092f235465c1951cf452\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7795f05fbf8283409257f8d4d50c7a58\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7795f05fbf8283409257f8d4d50c7a58\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\526f75cc9f188c5990293f2bea0a07f1\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\526f75cc9f188c5990293f2bea0a07f1\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\03c47e1b6cff250dae244f6621ea4a52\transformed\jetified-play-services-measurement-base-22.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\03c47e1b6cff250dae244f6621ea4a52\transformed\jetified-play-services-measurement-base-22.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
	android:extractNativeLibs
		INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:1:1-79:12
MERGED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:1:1-79:12
INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:app_settings] D:\projects\12july\athkar\athkar_app\build\app_settings\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:battery_plus] D:\projects\12july\athkar\athkar_app\build\battery_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:device_info_plus] D:\projects\12july\athkar\athkar_app\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:network_info_plus] D:\projects\12july\athkar\athkar_app\build\network_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:wakelock_plus] D:\projects\12july\athkar\athkar_app\build\wakelock_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] D:\projects\12july\athkar\athkar_app\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sensors_plus] D:\projects\12july\athkar\athkar_app\build\sensors_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-35:12
MERGED from [:shared_preferences_android] D:\projects\12july\athkar\athkar_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:webview_flutter_android] D:\projects\12july\athkar\athkar_app\build\webview_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:audioplayers_android] D:\projects\12july\athkar\athkar_app\build\audioplayers_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_local_notifications] D:\projects\12july\athkar\athkar_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:local_auth_android] D:\projects\12july\athkar\athkar_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:app_links] D:\projects\12july\athkar\athkar_app\build\app_links\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:connectivity_plus] D:\projects\12july\athkar\athkar_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_keyboard_visibility] D:\projects\12july\athkar\athkar_app\build\flutter_keyboard_visibility\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_native_splash] D:\projects\12july\athkar\athkar_app\build\flutter_native_splash\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_overlay_window] D:\projects\12july\athkar\athkar_app\build\flutter_overlay_window\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-11:12
MERGED from [:flutter_plugin_android_lifecycle] D:\projects\12july\athkar\athkar_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_secure_storage] D:\projects\12july\athkar\athkar_app\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:google_sign_in_android] D:\projects\12july\athkar\athkar_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:in_app_purchase_android] D:\projects\12july\athkar\athkar_app\build\in_app_purchase_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] D:\projects\12july\athkar\athkar_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] D:\projects\12july\athkar\athkar_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:rive_common] D:\projects\12july\athkar\athkar_app\build\rive_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sign_in_with_apple] D:\projects\12july\athkar\athkar_app\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] D:\projects\12july\athkar\athkar_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:vibration] D:\projects\12july\athkar\athkar_app\build\vibration\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:video_player_android] D:\projects\12july\athkar\athkar_app\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:workmanager] D:\projects\12july\athkar\athkar_app\build\workmanager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\1908db6613957e4395aaf44167e7ffc8\transformed\jetified-camera-video-1.5.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85ddfbdedd6c8e7f8f223d7a66a912f2\transformed\jetified-camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa901e4755a3670193aa5d35ba930f95\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f84db7003533a22de0405c5251ecb704\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:2:1-38:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1f7a922323fd2e7e330d6c2a2b6d1d4b\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\14c47a061c0f4340fe3cb3b4b456289f\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd3d9e1ce1f4362a391d5da65b7fedd\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de09d56ede5c875ebbc0232053614904\transformed\jetified-firebase-analytics-22.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8fbf22bcb175ef393e26705bdeff75e\transformed\jetified-play-services-measurement-sdk-22.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b83b8b00b8346c9e7414a1f1298f055d\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a295c1332cd792405fffabf7b4bbac54\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\972419750b36e9fbf2d0c26a45927d82\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb70f75a7acc5c45772a8855e98a189b\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\244ebfbd542d32fc159ad1c2e863258a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\500a0c9cb71e092f235465c1951cf452\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79275990ee9dddfd68bc7c9d7157e0cd\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\faeaeacf92c1b16df86d680ce0e0019c\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\32f4a2813ac7c771e056d42a720055af\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c244a6ce50b3288fe79d3f6ae212397f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\306016bcb4195b3238dbb4d76cafb64c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d813ca98b23cd30a6261e035c9b7933\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e72f610bb8a20735f78a04c908b9b793\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\425b3275685a974b685af27ff4ed6b1d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f71e40716bc29995f4cada24da499d83\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\31eccd218f5b1fd8272959453f411784\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\7535a935f9e65beb6c79d36312378a64\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\479b3bf32f00901a230d7d79262001b9\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\99bb3b712005fd2917858cd813cd885d\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7795f05fbf8283409257f8d4d50c7a58\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\526f75cc9f188c5990293f2bea0a07f1\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\03c47e1b6cff250dae244f6621ea4a52\transformed\jetified-play-services-measurement-base-22.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\93eeca70efd8419049cd49df8af72af1\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\83912700adcc0cb17e29b0477e0a9782\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3d4a68d34c8d0707a882a3ad9413a7d5\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c231ba1d521ae8b6b7437abf156b2096\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fa9e73b263a4d4afdc522127c7d12c6\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a235e50dcfe56df509b4e045938a6261\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3f2e728699f00eba298007738e88a2\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4e3c52189f16aa8ca269e158ba45254\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\123aa427351ecf51faaf5e936235c4a4\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\553b9a3de3dbb119c5d26ae33e3e2242\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1bb6847dc23bab7c067fa1b5dd94da7e\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66c3f8d759689e7c8bf8d566a47d4905\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d1284f70efaea5e2f7428ac2d9ae231\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\185f2479ab24942c0bba65b9ff947d79\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08d6944c906bcd30c9d42a63993176cf\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f87704cc6ac259b753f491455f413615\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7995343eeb8e0fb97f769a330ffeb5c4\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7058885b6dd2982185c832c670598b5a\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\b87823aa9ee377cf4f75094088b20314\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4759cb2b9879373320a8cc691959fe18\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\277474532995b9fd32fdd0e838dc6db6\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\36ca2e486ebda362c3c4d416726daa9c\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6afb3321da3bdd543c39e1a76ead511d\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c94026d29d9c0642fe4083bec5be2f24\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c6ec98e94082ed41f89d97124b61597\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\587570bc1d83a08ca6f63ae47663ef81\transformed\jetified-safe-parcel-1.7.0\AndroidManifest.xml:6:1-14:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:3:5-80
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:3:22-78
uses-permission#android.permission.VIBRATE
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:4:5-66
MERGED from [:flutter_local_notifications] D:\projects\12july\athkar\athkar_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:flutter_local_notifications] D:\projects\12july\athkar\athkar_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:vibration] D:\projects\12july\athkar\athkar_app\build\vibration\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:vibration] D:\projects\12july\athkar\athkar_app\build\vibration\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:4:22-63
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:5:5-68
MERGED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-68
MERGED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-68
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:flutter_overlay_window] D:\projects\12july\athkar\athkar_app\build\flutter_overlay_window\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:flutter_overlay_window] D:\projects\12july\athkar\athkar_app\build\flutter_overlay_window\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:5:22-65
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:6:5-76
MERGED from [:flutter_local_notifications] D:\projects\12july\athkar\athkar_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [:flutter_local_notifications] D:\projects\12july\athkar\athkar_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
	android:name
		ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:6:22-74
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:9:5-78
MERGED from [:flutter_overlay_window] D:\projects\12july\athkar\athkar_app\build\flutter_overlay_window\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-78
MERGED from [:flutter_overlay_window] D:\projects\12july\athkar\athkar_app\build\flutter_overlay_window\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-78
	android:name
		ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:9:22-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:10:5-77
MERGED from [:flutter_overlay_window] D:\projects\12july\athkar\athkar_app\build\flutter_overlay_window\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-77
MERGED from [:flutter_overlay_window] D:\projects\12july\athkar\athkar_app\build\flutter_overlay_window\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:10:22-74
uses-permission#android.permission.INTERNET
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:13:5-67
MERGED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:13:5-67
MERGED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:13:5-67
MERGED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] D:\projects\12july\athkar\athkar_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] D:\projects\12july\athkar\athkar_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:13:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:14:5-79
MERGED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:network_info_plus] D:\projects\12july\athkar\athkar_app\build\network_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:network_info_plus] D:\projects\12july\athkar\athkar_app\build\network_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] D:\projects\12july\athkar\athkar_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] D:\projects\12july\athkar\athkar_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3f2e728699f00eba298007738e88a2\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3f2e728699f00eba298007738e88a2\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66c3f8d759689e7c8bf8d566a47d4905\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66c3f8d759689e7c8bf8d566a47d4905\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
	android:name
		ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:14:22-76
queries
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:73:5-78:15
MERGED from [:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:12:5-19:15
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:12:5-19:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:74:9-77:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:75:13-72
	android:name
		ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:75:21-70
data
ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:76:13-50
	android:mimeType
		ADDED from D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:76:19-48
uses-sdk
INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:app_settings] D:\projects\12july\athkar\athkar_app\build\app_settings\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:app_settings] D:\projects\12july\athkar\athkar_app\build\app_settings\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:battery_plus] D:\projects\12july\athkar\athkar_app\build\battery_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:battery_plus] D:\projects\12july\athkar\athkar_app\build\battery_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] D:\projects\12july\athkar\athkar_app\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] D:\projects\12july\athkar\athkar_app\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:network_info_plus] D:\projects\12july\athkar\athkar_app\build\network_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:network_info_plus] D:\projects\12july\athkar\athkar_app\build\network_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] D:\projects\12july\athkar\athkar_app\build\wakelock_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] D:\projects\12july\athkar\athkar_app\build\wakelock_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] D:\projects\12july\athkar\athkar_app\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] D:\projects\12july\athkar\athkar_app\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sensors_plus] D:\projects\12july\athkar\athkar_app\build\sensors_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sensors_plus] D:\projects\12july\athkar\athkar_app\build\sensors_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\projects\12july\athkar\athkar_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\projects\12july\athkar\athkar_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] D:\projects\12july\athkar\athkar_app\build\webview_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] D:\projects\12july\athkar\athkar_app\build\webview_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:audioplayers_android] D:\projects\12july\athkar\athkar_app\build\audioplayers_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:audioplayers_android] D:\projects\12july\athkar\athkar_app\build\audioplayers_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] D:\projects\12july\athkar\athkar_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] D:\projects\12july\athkar\athkar_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:local_auth_android] D:\projects\12july\athkar\athkar_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:local_auth_android] D:\projects\12july\athkar\athkar_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:app_links] D:\projects\12july\athkar\athkar_app\build\app_links\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:app_links] D:\projects\12july\athkar\athkar_app\build\app_links\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] D:\projects\12july\athkar\athkar_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] D:\projects\12july\athkar\athkar_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_keyboard_visibility] D:\projects\12july\athkar\athkar_app\build\flutter_keyboard_visibility\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_keyboard_visibility] D:\projects\12july\athkar\athkar_app\build\flutter_keyboard_visibility\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_native_splash] D:\projects\12july\athkar\athkar_app\build\flutter_native_splash\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_native_splash] D:\projects\12july\athkar\athkar_app\build\flutter_native_splash\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_overlay_window] D:\projects\12july\athkar\athkar_app\build\flutter_overlay_window\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_overlay_window] D:\projects\12july\athkar\athkar_app\build\flutter_overlay_window\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] D:\projects\12july\athkar\athkar_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] D:\projects\12july\athkar\athkar_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] D:\projects\12july\athkar\athkar_app\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] D:\projects\12july\athkar\athkar_app\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:google_sign_in_android] D:\projects\12july\athkar\athkar_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] D:\projects\12july\athkar\athkar_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:in_app_purchase_android] D:\projects\12july\athkar\athkar_app\build\in_app_purchase_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:in_app_purchase_android] D:\projects\12july\athkar\athkar_app\build\in_app_purchase_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\projects\12july\athkar\athkar_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\projects\12july\athkar\athkar_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] D:\projects\12july\athkar\athkar_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] D:\projects\12july\athkar\athkar_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:rive_common] D:\projects\12july\athkar\athkar_app\build\rive_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:rive_common] D:\projects\12july\athkar\athkar_app\build\rive_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sign_in_with_apple] D:\projects\12july\athkar\athkar_app\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sign_in_with_apple] D:\projects\12july\athkar\athkar_app\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] D:\projects\12july\athkar\athkar_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] D:\projects\12july\athkar\athkar_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:vibration] D:\projects\12july\athkar\athkar_app\build\vibration\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:vibration] D:\projects\12july\athkar\athkar_app\build\vibration\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] D:\projects\12july\athkar\athkar_app\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] D:\projects\12july\athkar\athkar_app\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:workmanager] D:\projects\12july\athkar\athkar_app\build\workmanager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:workmanager] D:\projects\12july\athkar\athkar_app\build\workmanager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\1908db6613957e4395aaf44167e7ffc8\transformed\jetified-camera-video-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\1908db6613957e4395aaf44167e7ffc8\transformed\jetified-camera-video-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85ddfbdedd6c8e7f8f223d7a66a912f2\transformed\jetified-camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\85ddfbdedd6c8e7f8f223d7a66a912f2\transformed\jetified-camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa901e4755a3670193aa5d35ba930f95\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa901e4755a3670193aa5d35ba930f95\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f84db7003533a22de0405c5251ecb704\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f84db7003533a22de0405c5251ecb704\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1f7a922323fd2e7e330d6c2a2b6d1d4b\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1f7a922323fd2e7e330d6c2a2b6d1d4b\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\14c47a061c0f4340fe3cb3b4b456289f\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\14c47a061c0f4340fe3cb3b4b456289f\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd3d9e1ce1f4362a391d5da65b7fedd\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd3d9e1ce1f4362a391d5da65b7fedd\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de09d56ede5c875ebbc0232053614904\transformed\jetified-firebase-analytics-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de09d56ede5c875ebbc0232053614904\transformed\jetified-firebase-analytics-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8fbf22bcb175ef393e26705bdeff75e\transformed\jetified-play-services-measurement-sdk-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8fbf22bcb175ef393e26705bdeff75e\transformed\jetified-play-services-measurement-sdk-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b83b8b00b8346c9e7414a1f1298f055d\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b83b8b00b8346c9e7414a1f1298f055d\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a295c1332cd792405fffabf7b4bbac54\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a295c1332cd792405fffabf7b4bbac54\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\972419750b36e9fbf2d0c26a45927d82\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\972419750b36e9fbf2d0c26a45927d82\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb70f75a7acc5c45772a8855e98a189b\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb70f75a7acc5c45772a8855e98a189b\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\244ebfbd542d32fc159ad1c2e863258a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\244ebfbd542d32fc159ad1c2e863258a\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\500a0c9cb71e092f235465c1951cf452\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\500a0c9cb71e092f235465c1951cf452\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79275990ee9dddfd68bc7c9d7157e0cd\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79275990ee9dddfd68bc7c9d7157e0cd\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\faeaeacf92c1b16df86d680ce0e0019c\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\faeaeacf92c1b16df86d680ce0e0019c\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\32f4a2813ac7c771e056d42a720055af\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\32f4a2813ac7c771e056d42a720055af\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c244a6ce50b3288fe79d3f6ae212397f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c244a6ce50b3288fe79d3f6ae212397f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\306016bcb4195b3238dbb4d76cafb64c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\306016bcb4195b3238dbb4d76cafb64c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d813ca98b23cd30a6261e035c9b7933\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d813ca98b23cd30a6261e035c9b7933\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e72f610bb8a20735f78a04c908b9b793\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e72f610bb8a20735f78a04c908b9b793\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\425b3275685a974b685af27ff4ed6b1d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\425b3275685a974b685af27ff4ed6b1d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f71e40716bc29995f4cada24da499d83\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f71e40716bc29995f4cada24da499d83\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\31eccd218f5b1fd8272959453f411784\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\31eccd218f5b1fd8272959453f411784\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\7535a935f9e65beb6c79d36312378a64\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\7535a935f9e65beb6c79d36312378a64\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\479b3bf32f00901a230d7d79262001b9\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\479b3bf32f00901a230d7d79262001b9\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\99bb3b712005fd2917858cd813cd885d\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\99bb3b712005fd2917858cd813cd885d\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7795f05fbf8283409257f8d4d50c7a58\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7795f05fbf8283409257f8d4d50c7a58\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\526f75cc9f188c5990293f2bea0a07f1\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\526f75cc9f188c5990293f2bea0a07f1\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\03c47e1b6cff250dae244f6621ea4a52\transformed\jetified-play-services-measurement-base-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\03c47e1b6cff250dae244f6621ea4a52\transformed\jetified-play-services-measurement-base-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\93eeca70efd8419049cd49df8af72af1\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\93eeca70efd8419049cd49df8af72af1\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\83912700adcc0cb17e29b0477e0a9782\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\83912700adcc0cb17e29b0477e0a9782\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3d4a68d34c8d0707a882a3ad9413a7d5\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3d4a68d34c8d0707a882a3ad9413a7d5\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c231ba1d521ae8b6b7437abf156b2096\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c231ba1d521ae8b6b7437abf156b2096\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fa9e73b263a4d4afdc522127c7d12c6\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fa9e73b263a4d4afdc522127c7d12c6\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a235e50dcfe56df509b4e045938a6261\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a235e50dcfe56df509b4e045938a6261\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3f2e728699f00eba298007738e88a2\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3f2e728699f00eba298007738e88a2\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4e3c52189f16aa8ca269e158ba45254\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4e3c52189f16aa8ca269e158ba45254\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\123aa427351ecf51faaf5e936235c4a4\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\123aa427351ecf51faaf5e936235c4a4\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\553b9a3de3dbb119c5d26ae33e3e2242\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\553b9a3de3dbb119c5d26ae33e3e2242\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1bb6847dc23bab7c067fa1b5dd94da7e\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1bb6847dc23bab7c067fa1b5dd94da7e\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66c3f8d759689e7c8bf8d566a47d4905\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66c3f8d759689e7c8bf8d566a47d4905\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d1284f70efaea5e2f7428ac2d9ae231\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d1284f70efaea5e2f7428ac2d9ae231\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\185f2479ab24942c0bba65b9ff947d79\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\185f2479ab24942c0bba65b9ff947d79\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08d6944c906bcd30c9d42a63993176cf\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08d6944c906bcd30c9d42a63993176cf\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f87704cc6ac259b753f491455f413615\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f87704cc6ac259b753f491455f413615\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7995343eeb8e0fb97f769a330ffeb5c4\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7995343eeb8e0fb97f769a330ffeb5c4\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7058885b6dd2982185c832c670598b5a\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7058885b6dd2982185c832c670598b5a\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\b87823aa9ee377cf4f75094088b20314\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.12\transforms\b87823aa9ee377cf4f75094088b20314\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4759cb2b9879373320a8cc691959fe18\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4759cb2b9879373320a8cc691959fe18\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\277474532995b9fd32fdd0e838dc6db6\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\277474532995b9fd32fdd0e838dc6db6\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\36ca2e486ebda362c3c4d416726daa9c\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\36ca2e486ebda362c3c4d416726daa9c\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6afb3321da3bdd543c39e1a76ead511d\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6afb3321da3bdd543c39e1a76ead511d\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c94026d29d9c0642fe4083bec5be2f24\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c94026d29d9c0642fe4083bec5be2f24\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c6ec98e94082ed41f89d97124b61597\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c6ec98e94082ed41f89d97124b61597\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\587570bc1d83a08ca6f63ae47663ef81\transformed\jetified-safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\587570bc1d83a08ca6f63ae47663ef81\transformed\jetified-safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	tools:overrideLibrary
		ADDED from [:flutter_secure_storage] D:\projects\12july\athkar\athkar_app\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-50
	android:targetSdkVersion
		INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml
uses-feature#android.hardware.camera.any
ADDED from [:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-64
	android:name
		ADDED from [:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:19-61
uses-permission#android.permission.CAMERA
ADDED from [:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-65
	android:name
		ADDED from [:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from [:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
	android:name
		ADDED from [:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from [:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-13:38
	android:maxSdkVersion
		ADDED from [:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-35
	android:name
		ADDED from [:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-65
uses-permission#android.permission.READ_EXTERNAL_STORAGE
IMPLIED from D:\projects\12july\athkar\athkar_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12 reason: io.flutter.plugins.camerax requested WRITE_EXTERNAL_STORAGE
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:19
MERGED from [:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar
ADDED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:85
	android:value
		ADDED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-82
	android:name
		ADDED from [:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-128
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:network_info_plus] D:\projects\12july\athkar\athkar_app\build\network_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
	android:name
		ADDED from [:network_info_plus] D:\projects\12july\athkar\athkar_app\build\network_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
	android:resource
		ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
	android:name
		ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
	android:name
		ADDED from [:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
service#com.baseflow.geolocator.GeolocatorLocationService
ADDED from [:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
	android:enabled
		ADDED from [:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
	android:exported
		ADDED from [:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:foregroundServiceType
		ADDED from [:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
	android:name
		ADDED from [:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [:local_auth_android] D:\projects\12july\athkar\athkar_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [:local_auth_android] D:\projects\12july\athkar\athkar_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-69
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar
ADDED from [:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-130
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa901e4755a3670193aa5d35ba930f95\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa901e4755a3670193aa5d35ba930f95\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
uses-permission#com.android.vending.BILLING
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:5-67
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:22-64
intent#action:name:com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:13:9-15:18
action#com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:13-91
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:21-88
intent#action:name:com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:16:9-18:18
action#com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:13-116
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:21-113
meta-data#com.google.android.play.billingclient.version
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:22:9-24:37
	android:value
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:24:13-34
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:23:13-73
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:26:9-30:75
	android:exported
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:29:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:28:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:30:13-72
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:27:13-78
activity#com.android.billingclient.api.ProxyBillingActivityV2
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:31:9-35:75
	android:exported
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:34:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:33:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:35:13-72
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:32:13-80
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:27:22-76
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:19:17-115
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.islamicapps.athkar.athkar_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.islamicapps.athkar.athkar_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
