@echo off
"d:\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HC:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=21" ^
  "-DANDROID_PLATFORM=android-21" ^
  "-DANDROID_ABI=x86" ^
  "-DCMAKE_ANDROID_ARCH_ABI=x86" ^
  "-DANDROID_NDK=d:\\Sdk\\ndk\\27.0.12077973" ^
  "-DCMAKE_ANDROID_NDK=d:\\Sdk\\ndk\\27.0.12077973" ^
  "-DCMAKE_TOOLCHAIN_FILE=d:\\Sdk\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=d:\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\cxx\\RelWithDebInfo\\4g17w134\\obj\\x86" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\cxx\\RelWithDebInfo\\4g17w134\\obj\\x86" ^
  "-DCMAKE_BUILD_TYPE=RelWithDebInfo" ^
  "-BD:\\projects\\12july\\athkar\\athkar_app\\build\\.cxx\\RelWithDebInfo\\4g17w134\\x86" ^
  -GNinja ^
  -Wno-dev ^
  --no-warn-unused-cli
