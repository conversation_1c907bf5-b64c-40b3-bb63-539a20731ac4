<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\workmanager_android-0.8.0\android"
    name=":workmanager_android"
    type="LIBRARY"
    maven="dev.fluttercommunity.workmanager:workmanager_android:1.0-SNAPSHOT"
    agpVersion="8.7.3"
    buildFolder="D:\projects\12july\athkar\athkar_app\build\workmanager_android"
    bootClassPath="d:\Sdk\platforms\android-35\android.jar;d:\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      disable="InvalidPackage"
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
