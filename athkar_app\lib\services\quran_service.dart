import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quran_models.dart';

class QuranService {
  static const String _baseUrl = 'https://api.quran.com/api/v4';
  static List<Surah>? _surahs;
  static final Map<int, List<Ayah>> _ayahsCache = {};
  static final Map<String, List<Translation>> _translationsCache = {};
  static SharedPreferences? _prefs;
  
  // Initialize Quran data
  static Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await loadSurahs();
      await loadTranslations();
    } catch (e) {
      debugPrint('Error initializing Quran service: $e');
    }
  }

  // Load all surahs
  static Future<List<Surah>> loadSurahs() async {
    if (_surahs != null) return _surahs!;
    
    try {
      // Try to load from local assets first
      final String response = await rootBundle.loadString('assets/data/surahs.json');
      final List<dynamic> data = json.decode(response);
      _surahs = data.map((json) => Surah.fromJson(json)).toList();
      return _surahs!;
    } catch (e) {
      // Fallback to API
      return await _loadSurahsFromAPI();
    }
  }

  static Future<List<Surah>> _loadSurahsFromAPI() async {
    try {
      final response = await http.get(Uri.parse('$_baseUrl/chapters'));
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> chapters = data['chapters'];
        _surahs = chapters.map((json) => Surah.fromJson(json)).toList();
        return _surahs!;
      }
      throw Exception('Failed to load surahs');
    } catch (e) {
      debugPrint('Error loading surahs from API: $e');
      return [];
    }
  }

  /// Get all surahs (public method for external access)
  static Future<List<Surah>> getAllSurahs() async {
    return await loadSurahs();
  }

  // Load ayahs for a specific surah
  static Future<List<Ayah>> loadAyahs(int surahNumber) async {
    if (_ayahsCache.containsKey(surahNumber)) {
      return _ayahsCache[surahNumber]!;
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/verses/by_chapter/$surahNumber'),
      );
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> verses = data['verses'];
        final ayahs = verses.map((json) => Ayah.fromJson(json)).toList();
        _ayahsCache[surahNumber] = ayahs;
        return ayahs;
      }
      throw Exception('Failed to load ayahs');
    } catch (e) {
      debugPrint('Error loading ayahs: $e');
      return [];
    }
  }

  // Load translations
  static Future<void> loadTranslations() async {
    try {
      final response = await http.get(Uri.parse('$_baseUrl/resources/translations'));
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> translations = data['translations'];
        
        for (final translation in translations) {
          final lang = translation['language_name'] as String;
          if (!_translationsCache.containsKey(lang)) {
            _translationsCache[lang] = [];
          }
          _translationsCache[lang]!.add(Translation.fromJson(translation));
        }
      }
    } catch (e) {
      debugPrint('Error loading translations: $e');
    }
  }

  // Get verse of the day
  static Future<VerseOfTheDay> getVerseOfTheDay() async {
    try {
      // Generate a pseudo-random verse based on current date
      final now = DateTime.now();
      final dayOfYear = now.difference(DateTime(now.year, 1, 1)).inDays + 1;
      
      // Use day of year to select a verse
      final surahNumber = (dayOfYear % 114) + 1;
      final ayahs = await loadAyahs(surahNumber);
      
      if (ayahs.isNotEmpty) {
        final ayahIndex = dayOfYear % ayahs.length;
        final ayah = ayahs[ayahIndex];
        final surah = _surahs?.firstWhere((s) => s.id == surahNumber);
        
        return VerseOfTheDay(
          ayah: ayah,
          surah: surah,
          date: now,
        );
      }
      
      throw Exception('No verses available');
    } catch (e) {
      debugPrint('Error getting verse of the day: $e');
      return _getDefaultVerse();
    }
  }

  static VerseOfTheDay _getDefaultVerse() {
    return VerseOfTheDay(
      ayah: Ayah(
        id: 1,
        verseNumber: 1,
        textUthmani: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        textSimple: 'بسم الله الرحمن الرحيم',
        translation: 'In the name of Allah, the Most Gracious, the Most Merciful',
        transliteration: 'Bismillah hir-Rahman nir-Raheem',
      ),
      surah: Surah(
        id: 1,
        name: 'Al-Fatihah',
        arabicName: 'الفاتحة',
        englishName: 'The Opening',
        numberOfAyahs: 7,
        revelationPlace: 'Makkah',
      ),
      date: DateTime.now(),
    );
  }

  // Search verses
  static Future<List<SearchResult>> searchVerses(String query) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/search?q=${Uri.encodeComponent(query)}'),
      );
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> results = data['search']['results'];
        return results.map((json) => SearchResult.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error searching verses: $e');
      return [];
    }
  }

  // Get random verse
  static Future<Ayah?> getRandomVerse() async {
    try {
      if (_surahs == null) await loadSurahs();
      
      final random = DateTime.now().millisecondsSinceEpoch;
      final surahNumber = (random % 114) + 1;
      final ayahs = await loadAyahs(surahNumber);
      
      if (ayahs.isNotEmpty) {
        final ayahIndex = random % ayahs.length;
        return ayahs[ayahIndex];
      }
      return null;
    } catch (e) {
      debugPrint('Error getting random verse: $e');
      return null;
    }
  }

  // Get recitation audio URL
  static String getRecitationUrl(int surahNumber, int ayahNumber, {String reciter = 'ar.alafasy'}) {
    return 'https://verses.quran.com/$reciter/$surahNumber$ayahNumber.mp3';
  }

  // Get tafsir (commentary) for a verse
  static Future<List<Tafsir>> getTafsir(int surahNumber, int ayahNumber) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/verses/$surahNumber:$ayahNumber/tafsirs'),
      );
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> tafsirs = data['tafsirs'];
        return tafsirs.map((json) => Tafsir.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error loading tafsir: $e');
      return [];
    }
  }

  // Get bookmarks
  static List<BookmarkedVerse> getBookmarks() {
    // Load from local storage
    try {
      final bookmarksJson = _prefs?.getStringList('quran_bookmarks') ?? [];
      return bookmarksJson.map((json) {
        final data = jsonDecode(json) as Map<String, dynamic>;
        return BookmarkedVerse(
          surahNumber: data['surahNumber'] as int,
          ayahNumber: data['ayahNumber'] as int,
          note: data['note'] as String,
          createdAt: DateTime.parse(data['createdAt'] as String),
          tags: data['tags'] as String?,
        );
      }).toList();
    } catch (e) {
      debugPrint('Error loading bookmarks: $e');
      return [];
    }
  }

  // Add bookmark
  static Future<void> addBookmark(int surahNumber, int ayahNumber, String note) async {
    // Save to local storage
    try {
      final bookmark = BookmarkedVerse(
        surahNumber: surahNumber,
        ayahNumber: ayahNumber,
        note: note,
        createdAt: DateTime.now(),
        tags: null,
      );

      final bookmarks = getBookmarks();

      // Check if bookmark already exists
      final existingIndex = bookmarks.indexWhere(
        (b) => b.surahNumber == surahNumber && b.ayahNumber == ayahNumber,
      );

      if (existingIndex >= 0) {
        // Update existing bookmark
        bookmarks[existingIndex] = bookmark;
      } else {
        // Add new bookmark
        bookmarks.add(bookmark);
      }

      // Save to preferences
      final bookmarksJson = bookmarks.map((b) => jsonEncode({
        'surahNumber': b.surahNumber,
        'ayahNumber': b.ayahNumber,
        'note': b.note,
        'createdAt': b.createdAt.toIso8601String(),
        'tags': b.tags,
      })).toList();

      await _prefs?.setStringList('quran_bookmarks', bookmarksJson);
      debugPrint('Bookmark added: Surah $surahNumber, Ayah $ayahNumber');
    } catch (e) {
      debugPrint('Error adding bookmark: $e');
    }
  }

  // Remove bookmark
  static Future<void> removeBookmark(int surahNumber, int ayahNumber) async {
    // Remove from local storage
    try {
      final bookmarks = getBookmarks();

      // Remove the bookmark
      bookmarks.removeWhere(
        (b) => b.surahNumber == surahNumber && b.ayahNumber == ayahNumber,
      );

      // Save updated list to preferences
      final bookmarksJson = bookmarks.map((b) => jsonEncode({
        'surahNumber': b.surahNumber,
        'ayahNumber': b.ayahNumber,
        'note': b.note,
        'createdAt': b.createdAt.toIso8601String(),
        'tags': b.tags,
      })).toList();

      await _prefs?.setStringList('quran_bookmarks', bookmarksJson);
      debugPrint('Bookmark removed: Surah $surahNumber, Ayah $ayahNumber');
    } catch (e) {
      debugPrint('Error removing bookmark: $e');
    }
  }

  // Get reading progress
  static ReadingProgress getReadingProgress() {
    // Load from local storage
    try {
      final progressJson = _prefs?.getString('quran_reading_progress');
      if (progressJson != null) {
        final data = jsonDecode(progressJson) as Map<String, dynamic>;
        return ReadingProgress(
          completedSurahs: (data['completedSurahs'] as List<dynamic>).cast<int>(),
          currentSurah: data['currentSurah'] as int,
          currentAyah: data['currentAyah'] as int,
          totalAyahsRead: data['totalAyahsRead'] as int,
          lastReadDate: DateTime.parse(data['lastReadDate'] as String),
          totalReadingTime: Duration(seconds: data['totalReadingTimeSeconds'] as int? ?? 0),
        );
      }
    } catch (e) {
      debugPrint('Error loading reading progress: $e');
    }

    // Return default progress if none exists
    return ReadingProgress(
      completedSurahs: [],
      currentSurah: 1,
      currentAyah: 1,
      totalAyahsRead: 0,
      lastReadDate: DateTime.now(),
      totalReadingTime: Duration.zero,
    );
  }

  // Update reading progress
  static Future<void> updateReadingProgress(int surahNumber, int ayahNumber) async {
    // Save to local storage
    try {
      final currentProgress = getReadingProgress();

      // Update current position
      final updatedProgress = ReadingProgress(
        completedSurahs: currentProgress.completedSurahs,
        currentSurah: surahNumber,
        currentAyah: ayahNumber,
        totalAyahsRead: currentProgress.totalAyahsRead + 1,
        lastReadDate: DateTime.now(),
        totalReadingTime: currentProgress.totalReadingTime,
      );

      // Save to preferences
      final progressJson = jsonEncode({
        'completedSurahs': updatedProgress.completedSurahs,
        'currentSurah': updatedProgress.currentSurah,
        'currentAyah': updatedProgress.currentAyah,
        'totalAyahsRead': updatedProgress.totalAyahsRead,
        'lastReadDate': updatedProgress.lastReadDate.toIso8601String(),
        'totalReadingTimeSeconds': updatedProgress.totalReadingTime.inSeconds,
      });

      await _prefs?.setString('quran_reading_progress', progressJson);
      debugPrint('Reading progress updated: Surah $surahNumber, Ayah $ayahNumber');
    } catch (e) {
      debugPrint('Error updating reading progress: $e');
    }
  }

  // Get available translations
  static List<Translation> getAvailableTranslations([String language = 'English']) {
    return _translationsCache[language] ?? [];
  }

  // Get verse translation
  static Future<String?> getVerseTranslation(
    int surahNumber, 
    int ayahNumber, 
    int translationId,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/verses/$surahNumber:$ayahNumber/translations/$translationId'),
      );
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> translations = data['translations'];
        if (translations.isNotEmpty) {
          return translations.first['text'];
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error getting verse translation: $e');
      return null;
    }
  }

  // Clear cache
  static void clearCache() {
    _ayahsCache.clear();
    _translationsCache.clear();
  }
}
