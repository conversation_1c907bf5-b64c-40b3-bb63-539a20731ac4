import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:permission_handler/permission_handler.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  static bool _initialized = false;

  static Future<void> initialize() async {
    if (_initialized) return;

    // Request notification permissions
    await _requestPermissions();

    // Initialize notification settings
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _initialized = true;
  }

  static Future<void> _requestPermissions() async {
    if (defaultTargetPlatform == TargetPlatform.android) {
      await Permission.notification.request();
      
      // For Android 13+ (API level 33+)
      if (await Permission.notification.isDenied) {
        await Permission.notification.request();
      }
    }
  }

  static void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap
    debugPrint('Notification tapped: ${response.payload}');

    // Parse payload and navigate accordingly
    if (response.payload != null) {
      final payload = response.payload!;

      // For now, just log the payload
      // In a real implementation, you would use a navigation service
      // or global navigator key to navigate to the specific routine
      debugPrint('Navigating to routine: $payload');

      // Example: NavigationService.navigateToRoutine(payload);
    }
  }

  // Schedule a single notification
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
  }) async {
    if (!_initialized) await initialize();

    const androidDetails = AndroidNotificationDetails(
      'athkar_reminders',
      'Athkar Reminders',
      channelDescription: 'Notifications for athkar and dhikr reminders',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledTime, tz.local),
      notificationDetails,
      payload: payload,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time,
    );
  }

  // Schedule daily recurring notification
  static Future<void> scheduleDailyNotification({
    required int id,
    required String title,
    required String body,
    required TimeOfDay time,
    String? payload,
  }) async {
    if (!_initialized) await initialize();

    final now = DateTime.now();
    var scheduledTime = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );

    // If the scheduled time has passed today, schedule for tomorrow
    if (scheduledTime.isBefore(now)) {
      scheduledTime = scheduledTime.add(const Duration(days: 1));
    }

    await scheduleNotification(
      id: id,
      title: title,
      body: body,
      scheduledTime: scheduledTime,
      payload: payload,
    );
  }

  // Schedule weekly recurring notification
  static Future<void> scheduleWeeklyNotification({
    required int id,
    required String title,
    required String body,
    required TimeOfDay time,
    required List<int> daysOfWeek, // 1=Monday, 7=Sunday
    String? payload,
  }) async {
    if (!_initialized) await initialize();

    for (int dayOfWeek in daysOfWeek) {
      var scheduledTime = _getNextWeekday(dayOfWeek, time);

      await _notifications.zonedSchedule(
        id + dayOfWeek, // Unique ID for each day
        title,
        body,
        tz.TZDateTime.from(scheduledTime, tz.local),
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'athkar_reminders',
            'Athkar Reminders',
            channelDescription: 'Notifications for athkar and dhikr reminders',
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        payload: payload,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.dayOfWeekAndTime,
      );
    }
  }

  static DateTime _getNextWeekday(int weekday, TimeOfDay time) {
    final now = DateTime.now();
    var scheduledTime = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );

    // Calculate days until the target weekday
    int daysUntilTarget = (weekday - now.weekday) % 7;
    if (daysUntilTarget == 0 && scheduledTime.isBefore(now)) {
      daysUntilTarget = 7; // Schedule for next week if time has passed today
    }

    return scheduledTime.add(Duration(days: daysUntilTarget));
  }

  // Cancel a specific notification
  static Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  // Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  // Get pending notifications
  static Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  // Show immediate notification
  static Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_initialized) await initialize();

    const androidDetails = AndroidNotificationDetails(
      'athkar_immediate',
      'Immediate Notifications',
      channelDescription: 'Immediate notifications for athkar completion',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      id,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  // Helper method to create notification for athkar reminder
  static Future<void> scheduleAthkarReminder({
    required String routineId,
    required String routineTitle,
    required TimeOfDay time,
    List<int>? daysOfWeek,
  }) async {
    final id = routineId.hashCode;
    final title = 'Time for $routineTitle';
    final body = 'Don\'t forget your dhikr and remembrance of Allah';
    final payload = 'athkar_routine:$routineId';

    if (daysOfWeek != null && daysOfWeek.isNotEmpty) {
      await scheduleWeeklyNotification(
        id: id,
        title: title,
        body: body,
        time: time,
        daysOfWeek: daysOfWeek,
        payload: payload,
      );
    } else {
      await scheduleDailyNotification(
        id: id,
        title: title,
        body: body,
        time: time,
        payload: payload,
      );
    }
  }

  // Helper method to cancel athkar reminder
  static Future<void> cancelAthkarReminder(String routineId) async {
    final id = routineId.hashCode;
    await cancelNotification(id);
    
    // Cancel all day-specific notifications for weekly reminders
    for (int day = 1; day <= 7; day++) {
      await cancelNotification(id + day);
    }
  }
}
