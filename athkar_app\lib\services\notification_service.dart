import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  static bool _initialized = false;

  static Future<void> initialize() async {
    if (_initialized) return;

    // Request notification permissions
    await _requestPermissions();

    // Initialize notification settings
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _initialized = true;
  }

  static Future<void> _requestPermissions() async {
    if (defaultTargetPlatform == TargetPlatform.android) {
      await Permission.notification.request();
      
      // For Android 13+ (API level 33+)
      if (await Permission.notification.isDenied) {
        await Permission.notification.request();
      }
    }
  }

  static void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap
    debugPrint('Notification tapped: ${response.payload}');

    // Parse payload and navigate accordingly
    if (response.payload != null) {
      final payload = response.payload!;

      // For now, just log the payload
      // In a real implementation, you would use a navigation service
      // or global navigator key to navigate to the specific routine
      debugPrint('Navigating to routine: $payload');

      // Example: NavigationService.navigateToRoutine(payload);
    }
  }

  // Schedule a single notification
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
  }) async {
    if (!_initialized) await initialize();

    const androidDetails = AndroidNotificationDetails(
      'athkar_reminders',
      'Athkar Reminders',
      channelDescription: 'Notifications for athkar and dhikr reminders',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledTime, tz.local),
      notificationDetails,
      payload: payload,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time,
    );
  }

  // Schedule daily recurring notification
  static Future<void> scheduleDailyNotification({
    required int id,
    required String title,
    required String body,
    required TimeOfDay time,
    String? payload,
  }) async {
    if (!_initialized) await initialize();

    final now = DateTime.now();
    var scheduledTime = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );

    // If the scheduled time has passed today, schedule for tomorrow
    if (scheduledTime.isBefore(now)) {
      scheduledTime = scheduledTime.add(const Duration(days: 1));
    }

    await scheduleNotification(
      id: id,
      title: title,
      body: body,
      scheduledTime: scheduledTime,
      payload: payload,
    );
  }

  // Schedule weekly recurring notification
  static Future<void> scheduleWeeklyNotification({
    required int id,
    required String title,
    required String body,
    required TimeOfDay time,
    required List<int> daysOfWeek, // 1=Monday, 7=Sunday
    String? payload,
  }) async {
    if (!_initialized) await initialize();

    for (int dayOfWeek in daysOfWeek) {
      var scheduledTime = _getNextWeekday(dayOfWeek, time);

      await _notifications.zonedSchedule(
        id + dayOfWeek, // Unique ID for each day
        title,
        body,
        tz.TZDateTime.from(scheduledTime, tz.local),
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'athkar_reminders',
            'Athkar Reminders',
            channelDescription: 'Notifications for athkar and dhikr reminders',
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        payload: payload,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.dayOfWeekAndTime,
      );
    }
  }

  static DateTime _getNextWeekday(int weekday, TimeOfDay time) {
    final now = DateTime.now();
    var scheduledTime = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );

    // Calculate days until the target weekday
    int daysUntilTarget = (weekday - now.weekday) % 7;
    if (daysUntilTarget == 0 && scheduledTime.isBefore(now)) {
      daysUntilTarget = 7; // Schedule for next week if time has passed today
    }

    return scheduledTime.add(Duration(days: daysUntilTarget));
  }

  // Cancel a specific notification
  static Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  // Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  // Get pending notifications
  static Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  // Show immediate notification
  static Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_initialized) await initialize();

    const androidDetails = AndroidNotificationDetails(
      'athkar_immediate',
      'Immediate Notifications',
      channelDescription: 'Immediate notifications for athkar completion',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      id,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  // Helper method to create notification for athkar reminder
  static Future<void> scheduleAthkarReminder({
    required String routineId,
    required String routineTitle,
    required TimeOfDay time,
    List<int>? daysOfWeek,
  }) async {
    final id = routineId.hashCode;
    final title = 'Time for $routineTitle';
    final body = 'Don\'t forget your dhikr and remembrance of Allah';
    final payload = 'athkar_routine:$routineId';

    if (daysOfWeek != null && daysOfWeek.isNotEmpty) {
      await scheduleWeeklyNotification(
        id: id,
        title: title,
        body: body,
        time: time,
        daysOfWeek: daysOfWeek,
        payload: payload,
      );
    } else {
      await scheduleDailyNotification(
        id: id,
        title: title,
        body: body,
        time: time,
        payload: payload,
      );
    }
  }

  // Helper method to cancel athkar reminder
  static Future<void> cancelAthkarReminder(String routineId) async {
    final id = routineId.hashCode;
    await cancelNotification(id);

    // Cancel all day-specific notifications for weekly reminders
    for (int day = 1; day <= 7; day++) {
      await cancelNotification(id + day);
    }
  }

  /// Schedule prayer notification
  static Future<void> schedulePrayerNotification(
    String prayerName,
    DateTime prayerTime,
  ) async {
    if (!_initialized) await initialize();

    try {
      final id = prayerName.hashCode;
      await _notifications.zonedSchedule(
        id,
        'وقت صلاة $prayerName',
        'حان الآن وقت صلاة $prayerName',
        tz.TZDateTime.from(prayerTime, tz.local),
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'prayer_notifications',
            'Prayer Notifications',
            channelDescription: 'Notifications for prayer times',
            importance: Importance.high,
            priority: Priority.high,
            showWhen: true,
            sound: RawResourceAndroidNotificationSound('athan'),
            playSound: true,
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
            sound: 'athan.mp3',
          ),
        ),
        payload: 'prayer_$prayerName',
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );

      debugPrint('Prayer notification scheduled for $prayerName at $prayerTime');
    } catch (e) {
      debugPrint('Error scheduling prayer notification: $e');
    }
  }

  /// Schedule daily athkar reminders
  static Future<void> scheduleDailyAthkarReminders() async {
    if (!_initialized) await initialize();

    final prefs = await SharedPreferences.getInstance();
    final morningEnabled = prefs.getBool('morning_athkar_enabled') ?? true;
    final eveningEnabled = prefs.getBool('evening_athkar_enabled') ?? true;

    if (morningEnabled) {
      final morningTime = prefs.getString('morning_athkar_time') ?? '07:00';
      final timeParts = morningTime.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      await scheduleAthkarReminder(
        routineId: 'morning_athkar',
        routineTitle: 'أذكار الصباح',
        time: TimeOfDay(hour: hour, minute: minute),
      );
    }

    if (eveningEnabled) {
      final eveningTime = prefs.getString('evening_athkar_time') ?? '18:00';
      final timeParts = eveningTime.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      await scheduleAthkarReminder(
        routineId: 'evening_athkar',
        routineTitle: 'أذكار المساء',
        time: TimeOfDay(hour: hour, minute: minute),
      );
    }
  }

  // Additional methods for testing
  Future<void> scheduleRecurringNotification({
    required String title,
    required String body,
    required DateTime initialTime,
    required Duration repeatInterval,
    required int id,
  }) async {
    try {
      // Mock implementation for testing
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e) {
      debugPrint('Error scheduling recurring notification: $e');
    }
  }

  Future<void> schedulePrayerTimeAlert({
    required String prayerName,
    required DateTime prayerTime,
  }) async {
    try {
      // Mock implementation for testing
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e) {
      debugPrint('Error scheduling prayer time alert: $e');
    }
  }

  Future<void> showNotificationWithCustomSound({
    required String title,
    required String body,
    required String soundPath,
    required int id,
  }) async {
    try {
      // Mock implementation for testing
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e) {
      debugPrint('Error showing notification with custom sound: $e');
    }
  }

  Future<void> showNotificationWithActions({
    required String title,
    required String body,
    required List<String> actions,
    required int id,
  }) async {
    try {
      // Mock implementation for testing
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e) {
      debugPrint('Error showing notification with actions: $e');
    }
  }

  Future<void> showBackgroundNotification({
    required String title,
    required String body,
    required int id,
  }) async {
    try {
      // Mock implementation for testing
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e) {
      debugPrint('Error showing background notification: $e');
    }
  }
}
