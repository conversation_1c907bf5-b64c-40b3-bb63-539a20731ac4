import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';


class DataExportService {
  static final DatabaseHelper _dbHelper = DatabaseHelper();

  // Export all user data to JSON
  static Future<Map<String, dynamic>> exportUserData(String userId) async {
    try {
      final db = await _dbHelper.database;
      
      // Export athkar routines
      final routines = await db.query(
        'athkar_routines',
        where: 'user_id = ?',
        whereArgs: [userId],
      );

      // Export athkar steps for user routines
      final routineIds = routines.map((r) => r['id']).toList();
      List<Map<String, dynamic>> steps = [];
      
      if (routineIds.isNotEmpty) {
        final placeholders = routineIds.map((_) => '?').join(',');
        steps = await db.query(
          'athkar_steps',
          where: 'routine_id IN ($placeholders)',
          whereArgs: routineIds,
        );
      }

      // Export user progress
      final progress = await db.query(
        'user_progress',
        where: 'user_id = ?',
        whereArgs: [userId],
      );

      // Export custom tasbeeh items
      final tasbeehItems = await db.query(
        'tasbeeh_items',
        where: 'is_default = 0',
      );

      // Export tasbeeh sessions
      final tasbeehSessions = await db.query(
        'tasbeeh_sessions',
        where: 'user_id = ?',
        whereArgs: [userId],
      );

      // Export custom dua items
      final duaItems = await db.query(
        'dua_items',
        where: 'is_default = 0',
      );

      // Create export data structure
      final exportData = {
        'version': '1.0',
        'exportDate': DateTime.now().toIso8601String(),
        'userId': userId,
        'data': {
          'athkarRoutines': routines,
          'athkarSteps': steps,
          'userProgress': progress,
          'tasbeehItems': tasbeehItems,
          'tasbeehSessions': tasbeehSessions,
          'duaItems': duaItems,
        },
        'metadata': {
          'totalRoutines': routines.length,
          'totalSteps': steps.length,
          'totalProgress': progress.length,
          'totalTasbeehItems': tasbeehItems.length,
          'totalTasbeehSessions': tasbeehSessions.length,
          'totalDuaItems': duaItems.length,
        },
      };

      return exportData;
    } catch (e) {
      debugPrint('Error exporting user data: $e');
      rethrow;
    }
  }

  // Export data to file and share
  static Future<bool> exportToFile(String userId) async {
    try {
      // Get export data
      final exportData = await exportUserData(userId);
      
      // Convert to JSON string
      final jsonString = const JsonEncoder.withIndent('  ').convert(exportData);
      
      // Get temporary directory
      final directory = await getTemporaryDirectory();
      final fileName = 'athkar_backup_${DateTime.now().millisecondsSinceEpoch}.json';
      final file = File('${directory.path}/$fileName');
      
      // Write to file
      await file.writeAsString(jsonString);
      
      // Share the file
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Athkar App Data Backup',
        subject: 'My Athkar Data Export',
      );
      
      return true;
    } catch (e) {
      debugPrint('Error exporting to file: $e');
      return false;
    }
  }

  // Import data from JSON
  static Future<bool> importUserData(Map<String, dynamic> importData, String userId) async {
    try {
      // Validate import data structure
      if (!_validateImportData(importData)) {
        throw Exception('Invalid import data format');
      }

      final db = await _dbHelper.database;
      final data = importData['data'] as Map<String, dynamic>;

      // Start transaction
      await db.transaction((txn) async {
        // Import athkar routines
        final routines = data['athkarRoutines'] as List<dynamic>? ?? [];
        for (final routine in routines) {
          final routineMap = Map<String, dynamic>.from(routine);
          routineMap['user_id'] = userId; // Ensure correct user ID
          
          await txn.insert(
            'athkar_routines',
            routineMap,
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }

        // Import athkar steps
        final steps = data['athkarSteps'] as List<dynamic>? ?? [];
        for (final step in steps) {
          await txn.insert(
            'athkar_steps',
            Map<String, dynamic>.from(step),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }

        // Import user progress
        final progress = data['userProgress'] as List<dynamic>? ?? [];
        for (final progressItem in progress) {
          final progressMap = Map<String, dynamic>.from(progressItem);
          progressMap['user_id'] = userId; // Ensure correct user ID
          
          await txn.insert(
            'user_progress',
            progressMap,
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }

        // Import custom tasbeeh items
        final tasbeehItems = data['tasbeehItems'] as List<dynamic>? ?? [];
        for (final item in tasbeehItems) {
          await txn.insert(
            'tasbeeh_items',
            Map<String, dynamic>.from(item),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }

        // Import tasbeeh sessions
        final tasbeehSessions = data['tasbeehSessions'] as List<dynamic>? ?? [];
        for (final session in tasbeehSessions) {
          final sessionMap = Map<String, dynamic>.from(session);
          sessionMap['user_id'] = userId; // Ensure correct user ID
          
          await txn.insert(
            'tasbeeh_sessions',
            sessionMap,
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }

        // Import custom dua items
        final duaItems = data['duaItems'] as List<dynamic>? ?? [];
        for (final item in duaItems) {
          await txn.insert(
            'dua_items',
            Map<String, dynamic>.from(item),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
      });

      return true;
    } catch (e) {
      debugPrint('Error importing user data: $e');
      return false;
    }
  }

  // Import from file
  static Future<bool> importFromFile(String filePath, String userId) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Import file does not exist');
      }

      final jsonString = await file.readAsString();
      final importData = jsonDecode(jsonString) as Map<String, dynamic>;

      return await importUserData(importData, userId);
    } catch (e) {
      debugPrint('Error importing from file: $e');
      return false;
    }
  }

  // Validate import data structure
  static bool _validateImportData(Map<String, dynamic> data) {
    try {
      // Check required fields
      if (!data.containsKey('version') || 
          !data.containsKey('data') || 
          !data.containsKey('exportDate')) {
        return false;
      }

      // Check data structure
      final dataSection = data['data'] as Map<String, dynamic>?;
      if (dataSection == null) return false;

      // Validate that data contains expected arrays
      final expectedKeys = [
        'athkarRoutines',
        'athkarSteps',
        'userProgress',
        'tasbeehItems',
        'tasbeehSessions',
        'duaItems',
      ];

      for (final key in expectedKeys) {
        if (dataSection.containsKey(key) && dataSection[key] is! List) {
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('Error validating import data: $e');
      return false;
    }
  }

  // Get export statistics
  static Future<Map<String, int>> getExportStatistics(String userId) async {
    try {
      final exportData = await exportUserData(userId);
      final metadata = exportData['metadata'] as Map<String, dynamic>;
      
      return {
        'routines': metadata['totalRoutines'] as int,
        'steps': metadata['totalSteps'] as int,
        'progress': metadata['totalProgress'] as int,
        'tasbeehItems': metadata['totalTasbeehItems'] as int,
        'tasbeehSessions': metadata['totalTasbeehSessions'] as int,
        'duaItems': metadata['totalDuaItems'] as int,
      };
    } catch (e) {
      debugPrint('Error getting export statistics: $e');
      return {};
    }
  }

  // Clear all user data (for reset functionality)
  static Future<bool> clearUserData(String userId) async {
    try {
      final db = await _dbHelper.database;

      await db.transaction((txn) async {
        // Delete user routines and their steps
        final routines = await txn.query(
          'athkar_routines',
          columns: ['id'],
          where: 'user_id = ?',
          whereArgs: [userId],
        );

        final routineIds = routines.map((r) => r['id']).toList();
        
        if (routineIds.isNotEmpty) {
          final placeholders = routineIds.map((_) => '?').join(',');
          await txn.delete(
            'athkar_steps',
            where: 'routine_id IN ($placeholders)',
            whereArgs: routineIds,
          );
        }

        // Delete user data
        await txn.delete('athkar_routines', where: 'user_id = ?', whereArgs: [userId]);
        await txn.delete('user_progress', where: 'user_id = ?', whereArgs: [userId]);
        await txn.delete('tasbeeh_sessions', where: 'user_id = ?', whereArgs: [userId]);
        
        // Delete custom items (not default ones)
        await txn.delete('tasbeeh_items', where: 'is_default = 0');
        await txn.delete('dua_items', where: 'is_default = 0');
      });

      return true;
    } catch (e) {
      debugPrint('Error clearing user data: $e');
      return false;
    }
  }
}
