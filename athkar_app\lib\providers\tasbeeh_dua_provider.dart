import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/athkar_models.dart';
import '../repositories/tasbeeh_dua_repository.dart';

class TasbeehDuaProvider extends ChangeNotifier {
  final TasbeehDuaRepository _repository = TasbeehDuaRepository();
  
  List<TasbeehItem> _tasbeehItems = [];
  List<DuaItem> _duaItems = [];
  List<TasbeehSession> _tasbeehSessions = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<TasbeehItem> get tasbeehItems => _tasbeehItems;
  List<DuaItem> get duaItems => _duaItems;
  List<TasbeehSession> get tasbeehSessions => _tasbeehSessions;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Filtered getters
  List<TasbeehItem> getTasbeehByCategory(String category) {
    if (category == 'All') return _tasbeehItems;
    return _tasbeehItems.where((item) => item.category == category).toList();
  }

  List<DuaItem> getDuasByCategory(String category) {
    if (category == 'All') return _duaItems;
    return _duaItems.where((item) => item.category == category).toList();
  }

  List<TasbeehSession> getUserSessions(String userId) {
    return _tasbeehSessions.where((session) => session.userId == userId).toList();
  }

  TasbeehDuaProvider() {
    _initializeData();
    _loadFavorites();
  }

  Future<void> _initializeData() async {
    await loadTasbeehItems();
    await loadDuaItems();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Tasbeeh Items
  Future<void> loadTasbeehItems() async {
    try {
      _setLoading(true);
      _tasbeehItems = await _repository.getAllTasbeehItems();
    } catch (e) {
      _setError('Failed to load tasbeeh items: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<String?> addTasbeehItem(TasbeehItem item) async {
    try {
      _setLoading(true);
      final id = await _repository.insertTasbeehItem(item);
      await loadTasbeehItems();
      return id;
    } catch (e) {
      _setError('Failed to add tasbeeh item: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateTasbeehItem(TasbeehItem item) async {
    try {
      _setLoading(true);
      await _repository.updateTasbeehItem(item);
      await loadTasbeehItems();
    } catch (e) {
      _setError('Failed to update tasbeeh item: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteTasbeehItem(String id) async {
    try {
      _setLoading(true);
      await _repository.deleteTasbeehItem(id);
      await loadTasbeehItems();
    } catch (e) {
      _setError('Failed to delete tasbeeh item: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Dua Items
  Future<void> loadDuaItems() async {
    try {
      _setLoading(true);
      _duaItems = await _repository.getAllDuaItems();
    } catch (e) {
      _setError('Failed to load dua items: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<String?> addDuaItem(DuaItem item) async {
    try {
      _setLoading(true);
      final id = await _repository.insertDuaItem(item);
      await loadDuaItems();
      return id;
    } catch (e) {
      _setError('Failed to add dua item: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateDuaItem(DuaItem item) async {
    try {
      _setLoading(true);
      await _repository.updateDuaItem(item);
      await loadDuaItems();
    } catch (e) {
      _setError('Failed to update dua item: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteDuaItem(String id) async {
    try {
      _setLoading(true);
      await _repository.deleteDuaItem(id);
      await loadDuaItems();
    } catch (e) {
      _setError('Failed to delete dua item: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Tasbeeh Sessions
  Future<void> loadTasbeehSessions([String? userId]) async {
    try {
      _setLoading(true);
      if (userId != null) {
        _tasbeehSessions = await _repository.getUserTasbeehSessions(userId);
      } else {
        _tasbeehSessions = await _repository.getAllTasbeehSessions();
      }
    } catch (e) {
      _setError('Failed to load tasbeeh sessions: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<String?> startTasbeehSession(TasbeehSession session) async {
    try {
      _setLoading(true);
      final id = await _repository.insertTasbeehSession(session);
      await loadTasbeehSessions(session.userId);
      return id;
    } catch (e) {
      _setError('Failed to start tasbeeh session: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateTasbeehSession(TasbeehSession session) async {
    try {
      _setLoading(true);
      await _repository.updateTasbeehSession(session);
      await loadTasbeehSessions(session.userId);
    } catch (e) {
      _setError('Failed to update tasbeeh session: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> completeTasbeehSession(String sessionId, String userId) async {
    try {
      _setLoading(true);
      await _repository.completeTasbeehSession(sessionId);
      await loadTasbeehSessions(userId);
    } catch (e) {
      _setError('Failed to complete tasbeeh session: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Search functionality
  List<TasbeehItem> searchTasbeeh(String query) {
    if (query.isEmpty) return _tasbeehItems;
    
    final lowerQuery = query.toLowerCase();
    return _tasbeehItems.where((item) {
      return item.arabicText.contains(query) ||
             (item.transliteration?.toLowerCase().contains(lowerQuery) ?? false) ||
             (item.translation?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  List<DuaItem> searchDuas(String query) {
    if (query.isEmpty) return _duaItems;
    
    final lowerQuery = query.toLowerCase();
    return _duaItems.where((item) {
      return item.title.toLowerCase().contains(lowerQuery) ||
             item.arabicText.contains(query) ||
             (item.transliteration?.toLowerCase().contains(lowerQuery) ?? false) ||
             (item.translation?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  // Statistics
  Map<String, int> getTasbeehStats(String userId) {
    final userSessions = getUserSessions(userId);
    final completedSessions = userSessions.where((s) => s.isCompleted).length;
    final totalCount = userSessions.fold<int>(0, (sum, session) => sum + session.currentCount);
    
    return {
      'totalSessions': userSessions.length,
      'completedSessions': completedSessions,
      'totalCount': totalCount,
    };
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Favorites functionality
  final Set<String> _favoriteDuaIds = {};
  final Set<String> _favoriteTasbeehIds = {};

  Set<String> get favoriteDuaIds => _favoriteDuaIds;
  Set<String> get favoriteTasbeehIds => _favoriteTasbeehIds;

  List<DuaItem> get favoriteDuas =>
      _duaItems.where((dua) => _favoriteDuaIds.contains(dua.id)).toList();

  List<TasbeehItem> get favoriteTasbeeh =>
      _tasbeehItems.where((item) => _favoriteTasbeehIds.contains(item.id)).toList();

  void toggleDuaFavorite(String duaId) {
    if (_favoriteDuaIds.contains(duaId)) {
      _favoriteDuaIds.remove(duaId);
    } else {
      _favoriteDuaIds.add(duaId);
    }
    notifyListeners();
    _saveFavorites();
  }

  void toggleTasbeehFavorite(String tasbeehId) {
    if (_favoriteTasbeehIds.contains(tasbeehId)) {
      _favoriteTasbeehIds.remove(tasbeehId);
    } else {
      _favoriteTasbeehIds.add(tasbeehId);
    }
    notifyListeners();
    _saveFavorites();
  }

  bool isDuaFavorite(String duaId) => _favoriteDuaIds.contains(duaId);
  bool isTasbeehFavorite(String tasbeehId) => _favoriteTasbeehIds.contains(tasbeehId);

  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('favorite_tasbeeh_ids', _favoriteTasbeehIds.toList());
      await prefs.setStringList('favorite_dua_ids', _favoriteDuaIds.toList());
    } catch (e) {
      _setError('Failed to save favorites: $e');
    }
  }

  Future<void> _loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoriteTasbeehIds = prefs.getStringList('favorite_tasbeeh_ids') ?? [];
      final favoriteDuaIds = prefs.getStringList('favorite_dua_ids') ?? [];

      _favoriteTasbeehIds.clear();
      _favoriteTasbeehIds.addAll(favoriteTasbeehIds);

      _favoriteDuaIds.clear();
      _favoriteDuaIds.addAll(favoriteDuaIds);

      notifyListeners();
    } catch (e) {
      _setError('Failed to load favorites: $e');
    }
  }
}
