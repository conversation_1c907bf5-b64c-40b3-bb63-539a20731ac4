import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../services/prayer_times_service.dart';
import '../theme/app_theme.dart';

class PrayerTimesSettingsScreen extends StatefulWidget {
  final VoidCallback? onSettingsChanged;

  const PrayerTimesSettingsScreen({
    super.key,
    this.onSettingsChanged,
  });

  @override
  State<PrayerTimesSettingsScreen> createState() => _PrayerTimesSettingsScreenState();
}

class _PrayerTimesSettingsScreenState extends State<PrayerTimesSettingsScreen> {
  Map<String, int> _adjustments = {};
  bool _isLoading = true;

  final List<Map<String, dynamic>> _prayers = [
    {
      'name': 'Fajr',
      'arabicName': 'الفجر',
      'icon': MdiIcons.weatherSunset,
      'key': 'fajr',
    },
    {
      'name': 'Dhuhr',
      'arabicName': 'الظهر',
      'icon': MdiIcons.weatherSunny,
      'key': 'dhuhr',
    },
    {
      'name': 'Asr',
      'arabicName': 'العصر',
      'icon': MdiIcons.weatherPartlyCloudy,
      'key': 'asr',
    },
    {
      'name': 'Maghrib',
      'arabicName': 'المغرب',
      'icon': MdiIcons.weatherSunset,
      'key': 'maghrib',
    },
    {
      'name': 'Isha',
      'arabicName': 'العشاء',
      'icon': MdiIcons.weatherNight,
      'key': 'isha',
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadAdjustments();
  }

  Future<void> _loadAdjustments() async {
    try {
      final adjustments = await PrayerTimesService.getAllPrayerAdjustments();
      setState(() {
        _adjustments = adjustments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Prayer Times Settings'),
            Text(
              'إعدادات أوقات الصلاة',
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Amiri',
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.restore),
            onPressed: _resetAllAdjustments,
            tooltip: 'Reset all adjustments',
          ),
        ],
      ),
      body: _isLoading ? _buildLoadingState() : _buildContent(),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(color: AppTheme.primaryGreen),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(),
          const SizedBox(height: 20),
          _buildAdjustmentsCard(),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryGreen,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Prayer Time Adjustments',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'You can adjust individual prayer times by adding or subtracting minutes. This is useful for local variations or personal preferences.',
              style: TextStyle(
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يمكنك تعديل أوقات الصلاة الفردية بإضافة أو طرح دقائق. هذا مفيد للاختلافات المحلية أو التفضيلات الشخصية.',
              style: TextStyle(
                color: Colors.grey[600],
                height: 1.5,
                fontFamily: 'Amiri',
              ),
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdjustmentsCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Adjust Prayer Times',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ..._prayers.map((prayer) => _buildPrayerAdjustmentTile(prayer)),
          ],
        ),
      ),
    );
  }

  Widget _buildPrayerAdjustmentTile(Map<String, dynamic> prayer) {
    final adjustment = _adjustments[prayer['key']] ?? 0;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                prayer['icon'],
                color: AppTheme.primaryGreen,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      prayer['name'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      prayer['arabicName'],
                      style: const TextStyle(
                        fontSize: 14,
                        fontFamily: 'Amiri',
                        color: Colors.grey,
                      ),
                      textDirection: TextDirection.rtl,
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: adjustment == 0 
                      ? Colors.grey[300] 
                      : (adjustment > 0 ? Colors.green[100] : Colors.red[100]),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  adjustment == 0 
                      ? 'No adjustment' 
                      : '${adjustment > 0 ? '+' : ''}$adjustment min',
                  style: TextStyle(
                    color: adjustment == 0 
                        ? Colors.grey[600] 
                        : (adjustment > 0 ? Colors.green[700] : Colors.red[700]),
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => _adjustPrayer(prayer['key'], adjustment - 1),
                      icon: const Icon(Icons.remove_circle_outline),
                      color: Colors.red[600],
                    ),
                    Expanded(
                      child: Slider(
                        value: adjustment.toDouble(),
                        min: -30,
                        max: 30,
                        divisions: 60,
                        activeColor: AppTheme.primaryGreen,
                        onChanged: (value) => _adjustPrayer(prayer['key'], value.round()),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _adjustPrayer(prayer['key'], adjustment + 1),
                      icon: const Icon(Icons.add_circle_outline),
                      color: Colors.green[600],
                    ),
                  ],
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '-30 min',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
              Text(
                '+30 min',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _adjustPrayer(String prayerKey, int minutes) async {
    // Clamp the value between -30 and +30
    final clampedMinutes = minutes.clamp(-30, 30);
    
    setState(() {
      _adjustments[prayerKey] = clampedMinutes;
    });

    await PrayerTimesService.savePrayerAdjustment(prayerKey, clampedMinutes);
    
    // Notify parent screen to refresh
    widget.onSettingsChanged?.call();
  }

  Future<void> _resetAllAdjustments() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset All Adjustments'),
        content: const Text(
          'Are you sure you want to reset all prayer time adjustments to zero?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              
              // Reset all adjustments
              for (final prayer in _prayers) {
                await PrayerTimesService.savePrayerAdjustment(prayer['key'], 0);
              }
              
              // Reload adjustments
              await _loadAdjustments();
              
              // Notify parent screen
              widget.onSettingsChanged?.call();
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All prayer time adjustments have been reset'),
                  backgroundColor: AppTheme.primaryGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
