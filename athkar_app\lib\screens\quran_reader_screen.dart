import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../services/quran_service.dart';
import '../theme/app_theme.dart';
import '../models/quran_models.dart';

class QuranReaderScreen extends StatefulWidget {
  final Surah surah;

  const QuranReaderScreen({
    super.key,
    required this.surah,
  });

  @override
  State<QuranReaderScreen> createState() => _QuranReaderScreenState();
}

class _QuranReaderScreenState extends State<QuranReaderScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<QuranAyah> _ayahs = [];
  List<QuranAyah> _tafseerData = [];
  bool _isLoading = true;
  String? _error;
  QuranTextType _currentTextType = QuranTextType.uthmani;
  bool _showTafseer = false;
  int? _selectedAyahIndex;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _currentTextType = QuranService.getCurrentTextType();
    _loadSurahData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSurahData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final ayahs = await QuranService.getQuranText(widget.surah.id, _currentTextType);
      final tafseer = await QuranService.getSurahTafseer(widget.surah.id);

      setState(() {
        _ayahs = ayahs;
        _tafseerData = tafseer;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.surah.englishName,
              style: const TextStyle(fontSize: 18),
            ),
            Text(
              widget.surah.name,
              style: const TextStyle(
                fontSize: 16,
                fontFamily: 'Amiri',
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<QuranTextType>(
            icon: const Icon(Icons.text_fields),
            onSelected: _changeTextType,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: QuranTextType.uthmani,
                child: Text('Uthmani Script'),
              ),
              const PopupMenuItem(
                value: QuranTextType.simple,
                child: Text('Simple Text'),
              ),
              const PopupMenuItem(
                value: QuranTextType.original,
                child: Text('Original'),
              ),
            ],
          ),
          IconButton(
            icon: Icon(_showTafseer ? Icons.visibility_off : Icons.visibility),
            onPressed: () {
              setState(() {
                _showTafseer = !_showTafseer;
              });
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: AppTheme.accentGold,
          tabs: const [
            Tab(text: 'Read'),
            Tab(text: 'Info'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildReadingTab(),
          _buildInfoTab(),
        ],
      ),
    );
  }

  Widget _buildReadingTab() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: AppTheme.primaryGreen),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading Surah',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSurahData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Surah Header
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.primaryGreen.withValues(alpha: 0.1),
                AppTheme.primaryGreen.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            children: [
              Text(
                widget.surah.name,
                style: const TextStyle(
                  fontSize: 28,
                  fontFamily: 'Amiri',
                  fontWeight: FontWeight.bold,
                ),
                textDirection: TextDirection.rtl,
              ),
              const SizedBox(height: 8),
              Text(
                '${widget.surah.englishName} • ${widget.surah.numberOfAyahs} verses',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: AppTheme.primaryGreen.withValues(alpha: 0.3)),
                ),
                child: Text(
                  widget.surah.revelationType,
                  style: TextStyle(
                    color: AppTheme.primaryGreen,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // Ayahs List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _ayahs.length,
            itemBuilder: (context, index) {
              return _buildAyahCard(_ayahs[index], index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAyahCard(QuranAyah ayah, int index) {
    final isSelected = _selectedAyahIndex == index;
    final tafseerAyah = _tafseerData.firstWhere(
      (t) => t.ayahNumber == ayah.ayahNumber,
      orElse: () => QuranAyah(surahNumber: 0, ayahNumber: 0, text: ''),
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: isSelected ? 8 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected 
            ? BorderSide(color: AppTheme.primaryGreen, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _toggleAyahSelection(index),
        onLongPress: () => _showTafseerDialog(ayah, tafseerAyah),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Ayah number
              Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGreen,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${ayah.ayahNumber}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                  const Spacer(),
                  if (tafseerAyah.tafseer != null)
                    IconButton(
                      icon: Icon(
                        MdiIcons.commentTextOutline,
                        color: AppTheme.accentGold,
                      ),
                      onPressed: () => _showTafseerDialog(ayah, tafseerAyah),
                    ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Arabic text
              Text(
                ayah.text,
                style: const TextStyle(
                  fontSize: 22,
                  height: 2.0,
                  fontFamily: 'Amiri',
                ),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
              ),
              
              // Tafseer (if enabled and available)
              if (_showTafseer && tafseerAyah.tafseer != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.accentGold.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.accentGold.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            MdiIcons.commentText,
                            size: 16,
                            color: AppTheme.accentGold,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Tafseer',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: AppTheme.accentGold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        tafseerAyah.tafseer!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            'Surah Information',
            [
              _buildInfoRow('Name (Arabic)', widget.surah.name),
              _buildInfoRow('Name (English)', widget.surah.englishName),
              _buildInfoRow('Number', '${widget.surah.number}'),
              _buildInfoRow('Verses', '${widget.surah.numberOfAyahs}'),
              _buildInfoRow('Revelation', widget.surah.revelationType),
            ],
          ),
          const SizedBox(height: 20),
          _buildInfoCard(
            'Reading Options',
            [
              _buildTextTypeSelector(),
              _buildTafseerToggle(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, List<Widget> children) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Text Type',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<QuranTextType>(
          value: _currentTextType,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: const [
            DropdownMenuItem(
              value: QuranTextType.uthmani,
              child: Text('Uthmani Script'),
            ),
            DropdownMenuItem(
              value: QuranTextType.simple,
              child: Text('Simple Text'),
            ),
            DropdownMenuItem(
              value: QuranTextType.original,
              child: Text('Original'),
            ),
          ],
          onChanged: (value) {
            if (value != null) {
              _changeTextType(value);
            }
          },
        ),
      ],
    );
  }

  Widget _buildTafseerToggle() {
    return SwitchListTile(
      title: const Text('Show Tafseer'),
      subtitle: const Text('Display commentary below each verse'),
      value: _showTafseer,
      activeColor: AppTheme.primaryGreen,
      onChanged: (value) {
        setState(() {
          _showTafseer = value;
        });
      },
    );
  }

  void _changeTextType(QuranTextType textType) {
    setState(() {
      _currentTextType = textType;
    });
    QuranService.setTextType(textType);
    _loadSurahData();
  }

  void _toggleAyahSelection(int index) {
    setState(() {
      _selectedAyahIndex = _selectedAyahIndex == index ? null : index;
    });
  }

  void _showTafseerDialog(QuranAyah ayah, QuranAyah tafseerAyah) {
    if (tafseerAyah.tafseer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No Tafseer available for this verse'),
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(MdiIcons.commentText, color: AppTheme.accentGold),
            const SizedBox(width: 8),
            Text('Tafseer - Verse ${ayah.ayahNumber}'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Arabic text
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  ayah.text,
                  style: const TextStyle(
                    fontSize: 18,
                    fontFamily: 'Amiri',
                    height: 1.8,
                  ),
                  textAlign: TextAlign.right,
                  textDirection: TextDirection.rtl,
                ),
              ),
              const SizedBox(height: 16),
              // Tafseer
              Text(
                tafseerAyah.tafseer!,
                style: const TextStyle(
                  fontSize: 16,
                  height: 1.6,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
