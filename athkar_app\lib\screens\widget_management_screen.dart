import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/widget_manager.dart';
import '../services/language_service.dart';
import '../theme/app_theme.dart';
import '../widgets/widgets/widget_preview_card.dart';
import '../widgets/widgets/widget_creation_dialog.dart';

class WidgetManagementScreen extends StatefulWidget {
  const WidgetManagementScreen({super.key});

  @override
  State<WidgetManagementScreen> createState() => _WidgetManagementScreenState();
}

class _WidgetManagementScreenState extends State<WidgetManagementScreen>
    with TickerProviderStateMixin {
  final WidgetManager _widgetManager = WidgetManager();
  late TabController _tabController;
  
  List<WidgetConfig> _activeWidgets = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadActiveWidgets();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadActiveWidgets() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final widgets = await _widgetManager.getActiveWidgets();
      if (mounted) {
        setState(() {
          _activeWidgets = widgets;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          languageService.isArabic ? 'إدارة الودجت' : 'Widget Management',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryGreen,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadActiveWidgets,
            tooltip: languageService.isArabic ? 'تحديث' : 'Refresh',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              text: languageService.isArabic ? 'الودجت النشطة' : 'Active Widgets',
              icon: const Icon(Icons.widgets, size: 20),
            ),
            Tab(
              text: languageService.isArabic ? 'إنشاء جديد' : 'Create New',
              icon: const Icon(Icons.add_box, size: 20),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Active Widgets Tab
          _buildActiveWidgetsTab(languageService),
          
          // Create New Widget Tab
          _buildCreateWidgetTab(languageService),
        ],
      ),
    );
  }

  Widget _buildActiveWidgetsTab(LanguageService languageService) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
        ),
      );
    }

    if (_activeWidgets.isEmpty) {
      return _buildEmptyState(languageService);
    }

    return RefreshIndicator(
      onRefresh: _loadActiveWidgets,
      color: AppTheme.primaryGreen,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _activeWidgets.length,
        itemBuilder: (context, index) {
          final widget = _activeWidgets[index];
          return WidgetPreviewCard(
            config: widget,
            languageService: languageService,
            onEdit: () => _editWidget(widget),
            onDelete: () => _deleteWidget(widget),
            onRefresh: () => _refreshWidget(widget),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(LanguageService languageService) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppTheme.primaryGreen.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.widgets,
              size: 60,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            languageService.isArabic ? 'لا توجد ودجت نشطة' : 'No Active Widgets',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            languageService.isArabic
                ? 'قم بإنشاء ودجت جديدة لعرضها على الشاشة الرئيسية'
                : 'Create new widgets to display on your home screen',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () => _tabController.animateTo(1),
            icon: const Icon(Icons.add),
            label: Text(
              languageService.isArabic ? 'إنشاء ودجت' : 'Create Widget',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreateWidgetTab(LanguageService languageService) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            languageService.isArabic ? 'اختر نوع الودجت' : 'Choose Widget Type',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            languageService.isArabic
                ? 'اختر من بين مجموعة متنوعة من الودجت الإسلامية'
                : 'Choose from a variety of Islamic widgets',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          
          // Widget types grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.2,
            ),
            itemCount: WidgetType.values.length,
            itemBuilder: (context, index) {
              final widgetType = WidgetType.values[index];
              return _buildWidgetTypeCard(widgetType, languageService);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildWidgetTypeCard(
    WidgetType type,
    LanguageService languageService,
  ) {
    final info = _getWidgetTypeInfo(type, languageService);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _showWidgetCreationDialog(type),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  info['icon'] as IconData,
                  color: AppTheme.primaryGreen,
                  size: 24,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                info['title'] as String,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                info['description'] as String,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Map<String, dynamic> _getWidgetTypeInfo(
    WidgetType type,
    LanguageService languageService,
  ) {
    if (languageService.isArabic) {
      switch (type) {
        case WidgetType.routineCounter:
          return {
            'title': 'عداد الروتين',
            'description': 'تتبع تقدم الأذكار اليومية',
            'icon': Icons.track_changes,
          };
        case WidgetType.ayahOfDay:
          return {
            'title': 'آية اليوم',
            'description': 'آية قرآنية يومية',
            'icon': Icons.menu_book,
          };
        case WidgetType.athkarReminder:
          return {
            'title': 'تذكير الأذكار',
            'description': 'تذكيرات الأذكار القادمة',
            'icon': Icons.notifications_active,
          };
        case WidgetType.quranProgress:
          return {
            'title': 'تقدم القرآن',
            'description': 'تتبع قراءة القرآن',
            'icon': Icons.auto_stories,
          };
        case WidgetType.prayerTimes:
          return {
            'title': 'أوقات الصلاة',
            'description': 'مواقيت الصلاة اليومية',
            'icon': Icons.access_time,
          };
        case WidgetType.islamicCalendar:
          return {
            'title': 'التقويم الهجري',
            'description': 'التاريخ والأحداث الإسلامية',
            'icon': Icons.calendar_today,
          };
        case WidgetType.dhikrCounter:
          return {
            'title': 'عداد الذكر',
            'description': 'عداد للتسبيح والذكر',
            'icon': Icons.add_circle,
          };
        case WidgetType.statistics:
          return {
            'title': 'الإحصائيات',
            'description': 'إحصائيات الاستخدام',
            'icon': Icons.bar_chart,
          };
      }
    } else {
      switch (type) {
        case WidgetType.routineCounter:
          return {
            'title': 'Routine Counter',
            'description': 'Track daily Athkar progress',
            'icon': Icons.track_changes,
          };
        case WidgetType.ayahOfDay:
          return {
            'title': 'Ayah of Day',
            'description': 'Daily Quranic verse',
            'icon': Icons.menu_book,
          };
        case WidgetType.athkarReminder:
          return {
            'title': 'Athkar Reminder',
            'description': 'Upcoming Athkar reminders',
            'icon': Icons.notifications_active,
          };
        case WidgetType.quranProgress:
          return {
            'title': 'Quran Progress',
            'description': 'Track Quran reading',
            'icon': Icons.auto_stories,
          };
        case WidgetType.prayerTimes:
          return {
            'title': 'Prayer Times',
            'description': 'Daily prayer schedule',
            'icon': Icons.access_time,
          };
        case WidgetType.islamicCalendar:
          return {
            'title': 'Islamic Calendar',
            'description': 'Hijri dates and events',
            'icon': Icons.calendar_today,
          };
        case WidgetType.dhikrCounter:
          return {
            'title': 'Dhikr Counter',
            'description': 'Counter for Tasbih and Dhikr',
            'icon': Icons.add_circle,
          };
        case WidgetType.statistics:
          return {
            'title': 'Statistics',
            'description': 'Usage statistics',
            'icon': Icons.bar_chart,
          };
      }
    }
  }

  void _showWidgetCreationDialog(WidgetType type) {
    showDialog(
      context: context,
      builder: (context) => WidgetCreationDialog(
        widgetType: type,
        onWidgetCreated: (config) {
          _loadActiveWidgets();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'تم إنشاء الودجت بنجاح'
                    : 'Widget created successfully',
              ),
              backgroundColor: AppTheme.primaryGreen,
            ),
          );
        },
      ),
    );
  }

  void _editWidget(WidgetConfig config) {
    // Show edit dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          Provider.of<LanguageService>(context, listen: false).isArabic
              ? 'تعديل الودجت'
              : 'Edit Widget',
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: Provider.of<LanguageService>(context, listen: false).isArabic ? 'اسم الويدجت' : 'Widget Name',
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: Provider.of<LanguageService>(context, listen: false).isArabic ? 'الوصف' : 'Description',
                border: const OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              Provider.of<LanguageService>(context, listen: false).isArabic
                  ? 'إغلاق'
                  : 'Close',
            ),
          ),
        ],
      ),
    );
  }

  void _deleteWidget(WidgetConfig config) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          Provider.of<LanguageService>(context, listen: false).isArabic
              ? 'حذف الودجت'
              : 'Delete Widget',
        ),
        content: Text(
          Provider.of<LanguageService>(context, listen: false).isArabic
              ? 'هل تريد حذف هذا الودجت؟'
              : 'Do you want to delete this widget?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              Provider.of<LanguageService>(context, listen: false).isArabic
                  ? 'إلغاء'
                  : 'Cancel',
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await _widgetManager.deleteWidget(config.id);
              if (success) {
                _loadActiveWidgets();
                if (mounted) {
                  final languageService = Provider.of<LanguageService>(context, listen: false);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        languageService.isArabic
                            ? 'تم حذف الودجت'
                            : 'Widget deleted',
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(
              Provider.of<LanguageService>(context, listen: false).isArabic
                  ? 'حذف'
                  : 'Delete',
            ),
          ),
        ],
      ),
    );
  }

  void _refreshWidget(WidgetConfig config) async {
    await _widgetManager.refreshWidget(config.id);
    if (mounted) {
      final languageService = Provider.of<LanguageService>(context, listen: false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            languageService.isArabic
                ? 'تم تحديث الودجت'
                : 'Widget refreshed',
          ),
        backgroundColor: AppTheme.primaryGreen,
      ),
    );
    }
  }
}
