import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/language_service.dart';
import '../l10n/app_localizations.dart';
import '../theme/app_theme.dart';

class LanguageSettingsScreen extends StatefulWidget {
  const LanguageSettingsScreen({super.key});

  @override
  State<LanguageSettingsScreen> createState() => _LanguageSettingsScreenState();
}

class _LanguageSettingsScreenState extends State<LanguageSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    final localizations = AppLocalizations.of(context)!;

    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(localizations.languageSettings),
          backgroundColor: AppTheme.primaryGreen,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: Icon(languageService.backIcon),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Current Language Display
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      localizations.language,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${localizations.language}: ${languageService.currentLanguageName}',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${localizations.rtlSupport}: ${languageService.isRTL ? localizations.yes : localizations.no}',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Language Selection
            Card(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      localizations.language,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  
                  // Arabic Option
                  RadioListTile<String>(
                    title: Row(
                      children: [
                        const Icon(Icons.language, color: AppTheme.primaryGreen),
                        const SizedBox(width: 8),
                        Text(localizations.arabic),
                      ],
                    ),
                    subtitle: Text(
                      languageService.isArabic 
                          ? 'اللغة الافتراضية للتطبيق مع دعم الكتابة من اليمين لليسار'
                          : 'Default app language with RTL support',
                    ),
                    value: 'ar',
                    groupValue: languageService.currentLocale.languageCode,
                    activeColor: AppTheme.primaryGreen,
                    onChanged: (value) async {
                      if (value == 'ar') {
                        await languageService.setArabic();
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                languageService.isArabic 
                                    ? 'تم تغيير اللغة إلى العربية'
                                    : 'Language changed to Arabic',
                              ),
                              backgroundColor: AppTheme.primaryGreen,
                            ),
                          );
                        }
                      }
                    },
                  ),
                  
                  // English Option
                  RadioListTile<String>(
                    title: Row(
                      children: [
                        const Icon(Icons.language, color: AppTheme.primaryGreen),
                        const SizedBox(width: 8),
                        Text(localizations.english),
                      ],
                    ),
                    subtitle: Text(
                      languageService.isArabic 
                          ? 'اللغة الإنجليزية مع دعم الكتابة من اليسار لليمين'
                          : 'English language with LTR support',
                    ),
                    value: 'en',
                    groupValue: languageService.currentLocale.languageCode,
                    activeColor: AppTheme.primaryGreen,
                    onChanged: (value) async {
                      if (value == 'en') {
                        await languageService.setEnglish();
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                languageService.isArabic 
                                    ? 'تم تغيير اللغة إلى الإنجليزية'
                                    : 'Language changed to English',
                              ),
                              backgroundColor: AppTheme.primaryGreen,
                            ),
                          );
                        }
                      }
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Quick Toggle Button
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      localizations.language,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () async {
                          await languageService.toggleLanguage();
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  languageService.isArabic 
                                      ? 'تم تغيير اللغة بنجاح'
                                      : 'Language changed successfully',
                                ),
                                backgroundColor: AppTheme.primaryGreen,
                              ),
                            );
                          }
                        },
                        icon: const Icon(Icons.swap_horiz),
                        label: Text(
                          languageService.isArabic 
                              ? 'تبديل إلى ${languageService.isArabic ? "الإنجليزية" : "العربية"}'
                              : 'Switch to ${languageService.isArabic ? "English" : "Arabic"}',
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryGreen,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Language Information
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languageService.isArabic ? 'معلومات' : 'Information',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      languageService.isArabic 
                          ? '• العربية هي اللغة الافتراضية للتطبيق\n'
                            '• يدعم التطبيق الكتابة من اليمين لليسار (RTL)\n'
                            '• جميع النصوص الإسلامية متوفرة باللغة العربية\n'
                            '• يمكن تغيير اللغة في أي وقت من الإعدادات\n'
                            '• التطبيق يحفظ اختيار اللغة تلقائياً'
                          : '• Arabic is the default language of the app\n'
                            '• The app supports Right-to-Left (RTL) writing\n'
                            '• All Islamic texts are available in Arabic\n'
                            '• Language can be changed anytime from settings\n'
                            '• The app automatically saves language preference',
                      style: TextStyle(
                        color: Colors.grey[600],
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
