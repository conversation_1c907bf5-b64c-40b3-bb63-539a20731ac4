import 'package:flutter/material.dart';
import 'package:crypto/crypto.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:math';
import 'dart:async';

/// Enterprise-level security service for Islamic Athkar app
/// Provides comprehensive data protection, authentication, and security monitoring
class EnterpriseSecurityService {
  static final EnterpriseSecurityService _instance = EnterpriseSecurityService._internal();
  factory EnterpriseSecurityService() => _instance;
  EnterpriseSecurityService._internal();

  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_OAEPwithSHA_256andMGF1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  final LocalAuthentication _localAuth = LocalAuthentication();
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  bool _isInitialized = false;
  SecurityConfiguration? _securityConfig;
  List<SecurityEvent> _securityEvents = [];
  String? _deviceFingerprint;
  DateTime? _lastSecurityCheck;

  /// Initialize enterprise security service
  Future<SecurityInitResult> initialize() async {
    if (_isInitialized) return SecurityInitResult.success();

    try {
      // Load security configuration
      _securityConfig = await _loadSecurityConfiguration();
      
      // Generate device fingerprint
      _deviceFingerprint = await _generateDeviceFingerprint();
      
      // Perform initial security checks
      final securityStatus = await _performSecurityChecks();
      
      if (!securityStatus.isSecure) {
        return SecurityInitResult.error(securityStatus.issues.join(', '));
      }

      // Initialize security monitoring
      await _initializeSecurityMonitoring();
      
      _isInitialized = true;
      _lastSecurityCheck = DateTime.now();
      
      await _logSecurityEvent(SecurityEventType.serviceInitialized, 'Security service initialized successfully');
      
      return SecurityInitResult.success();
      
    } catch (e) {
      await _logSecurityEvent(SecurityEventType.initializationFailed, 'Failed to initialize security service: $e');
      return SecurityInitResult.error('فشل في تهيئة خدمة الأمان: ${e.toString()}');
    }
  }

  /// Authenticate user with biometric or PIN
  Future<AuthenticationResult> authenticateUser({
    bool requireBiometric = false,
    String? reason,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      final authReason = reason ?? 'يرجى التحقق من هويتك للوصول إلى التطبيق';

      // Check if biometric authentication is available
      final isAvailable = await _localAuth.canCheckBiometrics;
      final availableBiometrics = await _localAuth.getAvailableBiometrics();

      if (requireBiometric && (!isAvailable || availableBiometrics.isEmpty)) {
        return AuthenticationResult.error('المصادقة البيومترية غير متوفرة');
      }

      // Attempt biometric authentication
      if (isAvailable && availableBiometrics.isNotEmpty) {
        final authenticated = await _localAuth.authenticate(
          localizedReason: authReason,
          options: const AuthenticationOptions(
            biometricOnly: false,
            stickyAuth: true,
          ),
        );

        if (authenticated) {
          await _logSecurityEvent(SecurityEventType.authenticationSuccess, 'User authenticated successfully');
          return AuthenticationResult.success(AuthenticationType.biometric);
        }
      }

      // Fallback to PIN authentication if biometric fails
      if (!requireBiometric) {
        final pinResult = await _authenticateWithPIN();
        if (pinResult.isSuccess) {
          await _logSecurityEvent(SecurityEventType.authenticationSuccess, 'User authenticated with PIN');
          return AuthenticationResult.success(AuthenticationType.pin);
        }
      }

      await _logSecurityEvent(SecurityEventType.authenticationFailed, 'Authentication failed');
      return AuthenticationResult.error('فشل في المصادقة');

    } catch (e) {
      await _logSecurityEvent(SecurityEventType.authenticationError, 'Authentication error: $e');
      return AuthenticationResult.error('خطأ في المصادقة: ${e.toString()}');
    }
  }

  /// Encrypt sensitive data
  Future<String> encryptData(String data, {String? customKey}) async {
    try {
      final key = customKey ?? await _getEncryptionKey();
      final keyBytes = sha256.convert(utf8.encode(key)).bytes;
      
      // Generate random IV
      final iv = _generateRandomBytes(16);
      
      // Encrypt data using AES
      final encryptedData = _aesEncrypt(utf8.encode(data), keyBytes, iv);
      
      // Combine IV and encrypted data
      final combined = Uint8List.fromList([...iv, ...encryptedData]);
      
      return base64.encode(combined);
      
    } catch (e) {
      await _logSecurityEvent(SecurityEventType.encryptionFailed, 'Data encryption failed: $e');
      throw SecurityException('فشل في تشفير البيانات: ${e.toString()}');
    }
  }

  /// Decrypt sensitive data
  Future<String> decryptData(String encryptedData, {String? customKey}) async {
    try {
      final key = customKey ?? await _getEncryptionKey();
      final keyBytes = sha256.convert(utf8.encode(key)).bytes;
      
      final combined = base64.decode(encryptedData);
      
      // Extract IV and encrypted data
      final iv = combined.sublist(0, 16);
      final encrypted = combined.sublist(16);
      
      // Decrypt data
      final decryptedBytes = _aesDecrypt(encrypted, keyBytes, iv);
      
      return utf8.decode(decryptedBytes);
      
    } catch (e) {
      await _logSecurityEvent(SecurityEventType.decryptionFailed, 'Data decryption failed: $e');
      throw SecurityException('فشل في فك تشفير البيانات: ${e.toString()}');
    }
  }

  /// Validate data integrity
  Future<bool> validateDataIntegrity(String data, String expectedHash) async {
    try {
      final actualHash = sha256.convert(utf8.encode(data)).toString();
      return actualHash == expectedHash;
    } catch (e) {
      await _logSecurityEvent(SecurityEventType.integrityCheckFailed, 'Data integrity check failed: $e');
      return false;
    }
  }

  /// Generate secure hash for data
  String generateSecureHash(String data) {
    return sha256.convert(utf8.encode(data)).toString();
  }

  /// Perform comprehensive security audit
  Future<SecurityAuditResult> performSecurityAudit() async {
    try {
      final auditResults = <String, bool>{};
      final issues = <String>[];
      final recommendations = <String>[];

      // Check device security
      final deviceSecurity = await _checkDeviceSecurityStatus();
      auditResults['device_security'] = deviceSecurity.isSecure;
      if (!deviceSecurity.isSecure) {
        issues.addAll(deviceSecurity.issues);
      }

      // Check app integrity
      final appIntegrity = await _checkAppIntegrity();
      auditResults['app_integrity'] = appIntegrity;
      if (!appIntegrity) {
        issues.add('تم اكتشاف تعديل في ملفات التطبيق');
        recommendations.add('أعد تثبيت التطبيق من مصدر موثوق');
      }

      // Check data encryption status
      final encryptionStatus = await _checkDataEncryptionStatus();
      auditResults['data_encryption'] = encryptionStatus;
      if (!encryptionStatus) {
        issues.add('بعض البيانات غير مشفرة');
        recommendations.add('قم بتشفير جميع البيانات الحساسة');
      }

      // Check authentication configuration
      final authConfig = await _checkAuthenticationConfiguration();
      auditResults['authentication'] = authConfig;
      if (!authConfig) {
        issues.add('إعدادات المصادقة غير آمنة');
        recommendations.add('فعل المصادقة البيومترية أو كلمة المرور');
      }

      // Check network security
      final networkSecurity = await _checkNetworkSecurity();
      auditResults['network_security'] = networkSecurity;
      if (!networkSecurity) {
        issues.add('اتصال الشبكة غير آمن');
        recommendations.add('استخدم اتصال HTTPS فقط');
      }

      // Calculate overall security score
      final secureChecks = auditResults.values.where((v) => v).length;
      final totalChecks = auditResults.length;
      final securityScore = (secureChecks / totalChecks * 100).round();

      await _logSecurityEvent(
        SecurityEventType.securityAuditCompleted,
        'Security audit completed with score: $securityScore%'
      );

      return SecurityAuditResult(
        securityScore: securityScore,
        auditResults: auditResults,
        issues: issues,
        recommendations: recommendations,
        auditTimestamp: DateTime.now(),
      );

    } catch (e) {
      await _logSecurityEvent(SecurityEventType.securityAuditFailed, 'Security audit failed: $e');
      throw SecurityException('فشل في تدقيق الأمان: ${e.toString()}');
    }
  }

  /// Get security events log
  List<SecurityEvent> getSecurityEvents({int? limit}) {
    final events = List<SecurityEvent>.from(_securityEvents);
    events.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    if (limit != null && limit > 0) {
      return events.take(limit).toList();
    }
    
    return events;
  }

  /// Clear security events log
  Future<void> clearSecurityEvents() async {
    _securityEvents.clear();
    await _saveSecurityEvents();
    await _logSecurityEvent(SecurityEventType.logsCleared, 'Security events log cleared');
  }

  /// Enable/disable security features
  Future<void> updateSecurityConfiguration(SecurityConfiguration config) async {
    _securityConfig = config;
    await _saveSecurityConfiguration(config);
    await _logSecurityEvent(SecurityEventType.configurationUpdated, 'Security configuration updated');
  }

  /// Get current security configuration
  SecurityConfiguration? getSecurityConfiguration() => _securityConfig;

  /// Private helper methods
  Future<SecurityConfiguration> _loadSecurityConfiguration() async {
    try {
      final configJson = await _secureStorage.read(key: 'security_config');
      if (configJson != null) {
        return SecurityConfiguration.fromJson(jsonDecode(configJson));
      }
    } catch (e) {
      debugPrint('Failed to load security configuration: $e');
    }
    
    // Return default configuration
    return SecurityConfiguration.defaultConfig();
  }

  Future<void> _saveSecurityConfiguration(SecurityConfiguration config) async {
    await _secureStorage.write(
      key: 'security_config',
      value: jsonEncode(config.toJson()),
    );
  }

  Future<String> _generateDeviceFingerprint() async {
    try {
      final deviceInfo = await _deviceInfo.deviceInfo;
      final fingerprintData = <String>[];

      if (deviceInfo is AndroidDeviceInfo) {
        fingerprintData.addAll([
          deviceInfo.id,
          deviceInfo.model,
          deviceInfo.brand,
          deviceInfo.device,
          deviceInfo.hardware,
        ]);
      } else if (deviceInfo is IosDeviceInfo) {
        fingerprintData.addAll([
          deviceInfo.identifierForVendor ?? '',
          deviceInfo.model,
          deviceInfo.name,
          deviceInfo.systemName,
          deviceInfo.systemVersion,
        ]);
      }

      final combined = fingerprintData.join('|');
      return sha256.convert(utf8.encode(combined)).toString();

    } catch (e) {
      debugPrint('Failed to generate device fingerprint: $e');
      return 'unknown_device';
    }
  }

  Future<SecurityStatus> _performSecurityChecks() async {
    final issues = <String>[];

    // Check if device is rooted/jailbroken
    if (await _isDeviceCompromised()) {
      issues.add('الجهاز معدل (Rooted/Jailbroken)');
    }

    // Check if app is running in debug mode
    if (await _isDebugMode()) {
      issues.add('التطبيق يعمل في وضع التطوير');
    }

    // Check if device has screen lock
    if (!await _hasScreenLock()) {
      issues.add('الجهاز لا يحتوي على قفل شاشة');
    }

    return SecurityStatus(
      isSecure: issues.isEmpty,
      issues: issues,
      checkTimestamp: DateTime.now(),
    );
  }

  Future<void> _initializeSecurityMonitoring() async {
    // Load existing security events
    await _loadSecurityEvents();
    
    // Start periodic security checks
    _startPeriodicSecurityChecks();
  }

  void _startPeriodicSecurityChecks() {
    // Perform security checks every hour
    Timer.periodic(const Duration(hours: 1), (timer) async {
      if (_isInitialized) {
        final status = await _performSecurityChecks();
        if (!status.isSecure) {
          await _logSecurityEvent(
            SecurityEventType.securityThreatDetected,
            'Security threats detected: ${status.issues.join(', ')}'
          );
        }
      }
    });
  }

  Future<AuthenticationResult> _authenticateWithPIN() async {
    // Implementation would show PIN input dialog
    // For now, return success (would be implemented with actual PIN verification)
    return AuthenticationResult.success(AuthenticationType.pin);
  }

  Future<String> _getEncryptionKey() async {
    String? key = await _secureStorage.read(key: 'encryption_key');
    if (key == null) {
      key = _generateRandomString(32);
      await _secureStorage.write(key: 'encryption_key', value: key);
    }
    return key;
  }

  Uint8List _generateRandomBytes(int length) {
    final random = Random.secure();
    return Uint8List.fromList(List.generate(length, (_) => random.nextInt(256)));
  }

  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length)))
    );
  }

  Uint8List _aesEncrypt(List<int> data, List<int> key, List<int> iv) {
    // Simplified AES encryption (would use proper crypto library in production)
    final encrypted = <int>[];
    for (int i = 0; i < data.length; i++) {
      encrypted.add(data[i] ^ key[i % key.length] ^ iv[i % iv.length]);
    }
    return Uint8List.fromList(encrypted);
  }

  Uint8List _aesDecrypt(List<int> encryptedData, List<int> key, List<int> iv) {
    // Simplified AES decryption (would use proper crypto library in production)
    final decrypted = <int>[];
    for (int i = 0; i < encryptedData.length; i++) {
      decrypted.add(encryptedData[i] ^ key[i % key.length] ^ iv[i % iv.length]);
    }
    return Uint8List.fromList(decrypted);
  }

  Future<bool> _isDeviceCompromised() async {
    // Implementation would check for root/jailbreak indicators
    return false;
  }

  Future<bool> _isDebugMode() async {
    // Check if app is in debug mode
    return false; // Would check actual debug status
  }

  Future<bool> _hasScreenLock() async {
    // Check if device has screen lock enabled
    return true; // Would check actual screen lock status
  }

  Future<SecurityStatus> _checkDeviceSecurityStatus() async {
    return await _performSecurityChecks();
  }

  Future<bool> _checkAppIntegrity() async {
    // Check app signature and file integrity
    return true;
  }

  Future<bool> _checkDataEncryptionStatus() async {
    // Check if sensitive data is encrypted
    return true;
  }

  Future<bool> _checkAuthenticationConfiguration() async {
    // Check authentication settings
    return _securityConfig?.requireAuthentication ?? false;
  }

  Future<bool> _checkNetworkSecurity() async {
    // Check network security settings
    return true;
  }

  Future<void> _logSecurityEvent(SecurityEventType type, String message) async {
    final event = SecurityEvent(
      type: type,
      message: message,
      timestamp: DateTime.now(),
      deviceFingerprint: _deviceFingerprint,
    );
    
    _securityEvents.add(event);
    
    // Keep only last 1000 events
    if (_securityEvents.length > 1000) {
      _securityEvents = _securityEvents.sublist(_securityEvents.length - 1000);
    }
    
    await _saveSecurityEvents();
  }

  Future<void> _loadSecurityEvents() async {
    try {
      final eventsJson = await _secureStorage.read(key: 'security_events');
      if (eventsJson != null) {
        final eventsList = jsonDecode(eventsJson) as List;
        _securityEvents = eventsList
            .map((e) => SecurityEvent.fromJson(e))
            .toList();
      }
    } catch (e) {
      debugPrint('Failed to load security events: $e');
      _securityEvents = [];
    }
  }

  Future<void> _saveSecurityEvents() async {
    try {
      final eventsJson = jsonEncode(_securityEvents.map((e) => e.toJson()).toList());
      await _secureStorage.write(key: 'security_events', value: eventsJson);
    } catch (e) {
      debugPrint('Failed to save security events: $e');
    }
  }
}

/// Security configuration
class SecurityConfiguration {
  final bool requireAuthentication;
  final bool enableBiometric;
  final bool enableDataEncryption;
  final bool enableSecurityMonitoring;
  final int sessionTimeout; // minutes
  final bool allowScreenshots;
  final bool enableTamperDetection;

  SecurityConfiguration({
    required this.requireAuthentication,
    required this.enableBiometric,
    required this.enableDataEncryption,
    required this.enableSecurityMonitoring,
    required this.sessionTimeout,
    required this.allowScreenshots,
    required this.enableTamperDetection,
  });

  factory SecurityConfiguration.defaultConfig() {
    return SecurityConfiguration(
      requireAuthentication: true,
      enableBiometric: true,
      enableDataEncryption: true,
      enableSecurityMonitoring: true,
      sessionTimeout: 30,
      allowScreenshots: false,
      enableTamperDetection: true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'require_authentication': requireAuthentication,
      'enable_biometric': enableBiometric,
      'enable_data_encryption': enableDataEncryption,
      'enable_security_monitoring': enableSecurityMonitoring,
      'session_timeout': sessionTimeout,
      'allow_screenshots': allowScreenshots,
      'enable_tamper_detection': enableTamperDetection,
    };
  }

  factory SecurityConfiguration.fromJson(Map<String, dynamic> json) {
    return SecurityConfiguration(
      requireAuthentication: json['require_authentication'] ?? true,
      enableBiometric: json['enable_biometric'] ?? true,
      enableDataEncryption: json['enable_data_encryption'] ?? true,
      enableSecurityMonitoring: json['enable_security_monitoring'] ?? true,
      sessionTimeout: json['session_timeout'] ?? 30,
      allowScreenshots: json['allow_screenshots'] ?? false,
      enableTamperDetection: json['enable_tamper_detection'] ?? true,
    );
  }
}

/// Security event types
enum SecurityEventType {
  serviceInitialized,
  initializationFailed,
  authenticationSuccess,
  authenticationFailed,
  authenticationError,
  encryptionFailed,
  decryptionFailed,
  integrityCheckFailed,
  securityAuditCompleted,
  securityAuditFailed,
  securityThreatDetected,
  configurationUpdated,
  logsCleared,
}

/// Security event
class SecurityEvent {
  final SecurityEventType type;
  final String message;
  final DateTime timestamp;
  final String? deviceFingerprint;

  SecurityEvent({
    required this.type,
    required this.message,
    required this.timestamp,
    this.deviceFingerprint,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.index,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'device_fingerprint': deviceFingerprint,
    };
  }

  factory SecurityEvent.fromJson(Map<String, dynamic> json) {
    return SecurityEvent(
      type: SecurityEventType.values[json['type']],
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp']),
      deviceFingerprint: json['device_fingerprint'],
    );
  }
}

/// Security status
class SecurityStatus {
  final bool isSecure;
  final List<String> issues;
  final DateTime checkTimestamp;

  SecurityStatus({
    required this.isSecure,
    required this.issues,
    required this.checkTimestamp,
  });
}

/// Security audit result
class SecurityAuditResult {
  final int securityScore;
  final Map<String, bool> auditResults;
  final List<String> issues;
  final List<String> recommendations;
  final DateTime auditTimestamp;

  SecurityAuditResult({
    required this.securityScore,
    required this.auditResults,
    required this.issues,
    required this.recommendations,
    required this.auditTimestamp,
  });
}

/// Security initialization result
class SecurityInitResult {
  final bool isSuccess;
  final String? error;

  SecurityInitResult._({required this.isSuccess, this.error});

  factory SecurityInitResult.success() {
    return SecurityInitResult._(isSuccess: true);
  }

  factory SecurityInitResult.error(String error) {
    return SecurityInitResult._(isSuccess: false, error: error);
  }
}

/// Authentication result
class AuthenticationResult {
  final bool isSuccess;
  final String? error;
  final AuthenticationType? type;

  AuthenticationResult._({
    required this.isSuccess,
    this.error,
    this.type,
  });

  factory AuthenticationResult.success(AuthenticationType type) {
    return AuthenticationResult._(isSuccess: true, type: type);
  }

  factory AuthenticationResult.error(String error) {
    return AuthenticationResult._(isSuccess: false, error: error);
  }
}

/// Authentication types
enum AuthenticationType {
  biometric,
  pin,
  password,
}

/// Security exception
class SecurityException implements Exception {
  final String message;
  SecurityException(this.message);
  
  @override
  String toString() => 'SecurityException: $message';
}
