// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'prebuilt_content_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PrebuiltContent _$PrebuiltContentFromJson(Map<String, dynamic> json) =>
    PrebuiltContent(
      id: json['id'] as String,
      title: json['title'] as String,
      arabicText: json['arabicText'] as String,
      transliteration: json['transliteration'] as String?,
      translation: json['translation'] as String?,
      description: json['description'] as String?,
      category: $enumDecode(_$ContentCategoryEnumMap, json['category']),
      subcategory: json['subcategory'] as String?,
      difficulty: $enumDecodeNullable(_$DifficultyLevelEnumMap, json['difficulty']) ??
          DifficultyLevel.beginner,
      targetCount: (json['targetCount'] as num?)?.toInt(),
      source: json['source'] as String?,
      reference: json['reference'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      audioUrl: json['audioUrl'] as String?,
      colorHex: json['colorHex'] as String?,
      isPopular: json['isPopular'] as bool? ?? false,
      isFavorite: json['isFavorite'] as bool? ?? false,
      usageCount: (json['usageCount'] as num?)?.toInt(),
      steps: (json['steps'] as List<dynamic>?)
          ?.map((e) => PrebuiltStep.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$PrebuiltContentToJson(PrebuiltContent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'arabicText': instance.arabicText,
      'transliteration': instance.transliteration,
      'translation': instance.translation,
      'description': instance.description,
      'category': _$ContentCategoryEnumMap[instance.category]!,
      'subcategory': instance.subcategory,
      'difficulty': _$DifficultyLevelEnumMap[instance.difficulty]!,
      'targetCount': instance.targetCount,
      'source': instance.source,
      'reference': instance.reference,
      'tags': instance.tags,
      'audioUrl': instance.audioUrl,
      'colorHex': instance.colorHex,
      'isPopular': instance.isPopular,
      'isFavorite': instance.isFavorite,
      'usageCount': instance.usageCount,
      'steps': instance.steps,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$ContentCategoryEnumMap = {
  ContentCategory.athkar: 'athkar',
  ContentCategory.dua: 'dua',
  ContentCategory.tasbeeh: 'tasbeeh',
  ContentCategory.routine: 'routine',
};

const _$DifficultyLevelEnumMap = {
  DifficultyLevel.beginner: 'beginner',
  DifficultyLevel.intermediate: 'intermediate',
  DifficultyLevel.advanced: 'advanced',
};

PrebuiltStep _$PrebuiltStepFromJson(Map<String, dynamic> json) => PrebuiltStep(
      stepOrder: (json['stepOrder'] as num).toInt(),
      arabicText: json['arabicText'] as String,
      transliteration: json['transliteration'] as String?,
      translation: json['translation'] as String?,
      targetCount: (json['targetCount'] as num?)?.toInt() ?? 1,
      colorHex: json['colorHex'] as String?,
    );

Map<String, dynamic> _$PrebuiltStepToJson(PrebuiltStep instance) =>
    <String, dynamic>{
      'stepOrder': instance.stepOrder,
      'arabicText': instance.arabicText,
      'transliteration': instance.transliteration,
      'translation': instance.translation,
      'targetCount': instance.targetCount,
      'colorHex': instance.colorHex,
    };
