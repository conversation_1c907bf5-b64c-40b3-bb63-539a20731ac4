import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../services/sync_service.dart';
// import '../../theme/app_theme.dart'; // Unused import removed
import '../../screens/comprehensive_testing_screen.dart';

class SyncTestingWidget extends StatefulWidget {
  final Function(TestingStatus) onStatusChanged;

  const SyncTestingWidget({
    super.key,
    required this.onStatusChanged,
  });

  @override
  State<SyncTestingWidget> createState() => _SyncTestingWidgetState();
}

class _SyncTestingWidgetState extends State<SyncTestingWidget> {
  final SyncService _syncService = SyncService();
  
  final Map<String, TestResult> _testResults = {};
  bool _isRunningTests = false;
  int _currentTestRound = 0;
  final int _totalRounds = 5;

  final List<SyncTest> _syncTests = [
    SyncTest(
      id: 'real_time_sync',
      nameAr: 'المزامنة الفورية',
      nameEn: 'Real-time Sync',
      description: 'Test real-time data synchronization',
    ),
    SyncTest(
      id: 'conflict_resolution',
      nameAr: 'حل التعارضات',
      nameEn: 'Conflict Resolution',
      description: 'Test conflict resolution mechanisms',
    ),
    SyncTest(
      id: 'offline_sync',
      nameAr: 'المزامنة دون اتصال',
      nameEn: 'Offline Sync',
      description: 'Test offline data synchronization',
    ),
    SyncTest(
      id: 'cross_device_sync',
      nameAr: 'المزامنة عبر الأجهزة',
      nameEn: 'Cross-device Sync',
      description: 'Test synchronization across multiple devices',
    ),
    SyncTest(
      id: 'data_integrity',
      nameAr: 'سلامة البيانات',
      nameEn: 'Data Integrity',
      description: 'Test data integrity during sync',
    ),
    SyncTest(
      id: 'sync_performance',
      nameAr: 'أداء المزامنة',
      nameEn: 'Sync Performance',
      description: 'Test synchronization performance',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeTestResults();
  }

  void _initializeTestResults() {
    for (final test in _syncTests) {
      _testResults[test.id] = TestResult.notStarted;
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(languageService),
          const SizedBox(height: 24),
          _buildTestControls(languageService),
          const SizedBox(height: 24),
          _buildSyncStatus(languageService),
          const SizedBox(height: 24),
          _buildTestResults(languageService),
          const SizedBox(height: 24),
          if (_isRunningTests) _buildRoundProgress(languageService),
        ],
      ),
    );
  }

  Widget _buildHeader(LanguageService languageService) {
    final passedTests = _testResults.values.where((result) => result == TestResult.passed).length;
    final totalTests = _testResults.length;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.teal,
            Colors.teal.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.sync, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  languageService.isArabic ? 'اختبار المزامنة' : 'Synchronization Testing',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            languageService.isArabic 
                ? 'اختبار شامل للمزامنة عبر الأجهزة وحل التعارضات'
                : 'Comprehensive testing of cross-device sync and conflict resolution',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageService.isArabic ? 'التقدم' : 'Progress',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '$passedTests / $totalTests',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: passedTests / totalTests,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildTestControls(LanguageService languageService) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isRunningTests ? null : _runAllTests,
            icon: _isRunningTests 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(
              _isRunningTests
                  ? (languageService.isArabic ? 'جاري التشغيل...' : 'Running...')
                  : (languageService.isArabic ? 'تشغيل جميع الاختبارات' : 'Run All Tests'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: _isRunningTests ? null : _resetTests,
          icon: const Icon(Icons.refresh),
          label: Text(languageService.isArabic ? 'إعادة تعيين' : 'Reset'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildSyncStatus(LanguageService languageService) {
    return FutureBuilder<bool>(
      future: _syncService.isConnected(),
      builder: (context, snapshot) {
        final isConnected = snapshot.data ?? false;
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isConnected ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isConnected ? Colors.green : Colors.red,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isConnected ? Icons.cloud_done : Icons.cloud_off,
                color: isConnected ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languageService.isArabic ? 'حالة الاتصال' : 'Connection Status',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isConnected ? Colors.green : Colors.red,
                      ),
                    ),
                    Text(
                      isConnected
                          ? (languageService.isArabic ? 'متصل' : 'Connected')
                          : (languageService.isArabic ? 'غير متصل' : 'Disconnected'),
                      style: TextStyle(
                        color: isConnected ? Colors.green : Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              FutureBuilder<DateTime?>(
                future: _syncService.getLastSyncTime(),
                builder: (context, syncSnapshot) {
                  final lastSync = syncSnapshot.data;
                  return Column(
                    children: [
                      Text(
                        languageService.isArabic ? 'آخر مزامنة' : 'Last Sync',
                        style: const TextStyle(fontSize: 12),
                      ),
                      Text(
                        lastSync != null 
                            ? '${lastSync.hour}:${lastSync.minute.toString().padLeft(2, '0')}'
                            : (languageService.isArabic ? 'لا يوجد' : 'None'),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTestResults(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'نتائج الاختبارات' : 'Test Results',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.teal,
          ),
        ),
        const SizedBox(height: 12),
        ...(_syncTests.map((test) {
          final result = _testResults[test.id] ?? TestResult.notStarted;
          return _buildTestResultCard(test, result, languageService);
        }).toList()),
      ],
    );
  }

  Widget _buildTestResultCard(SyncTest test, TestResult result, LanguageService languageService) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (result) {
      case TestResult.passed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = languageService.isArabic ? 'نجح' : 'Passed';
        break;
      case TestResult.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = languageService.isArabic ? 'فشل' : 'Failed';
        break;
      case TestResult.inProgress:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = languageService.isArabic ? 'قيد التشغيل' : 'Running';
        break;
      case TestResult.notStarted:
        statusColor = Colors.grey;
        statusIcon = Icons.radio_button_unchecked;
        statusText = languageService.isArabic ? 'لم يبدأ' : 'Not Started';
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          languageService.isArabic ? test.nameAr : test.nameEn,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(test.description),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            if (result == TestResult.inProgress)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        onTap: () => _runSingleTest(test.id),
      ),
    );
  }

  Widget _buildRoundProgress(LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.teal.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.teal.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            languageService.isArabic 
                ? 'جولة الاختبار ${_currentTestRound + 1} من $_totalRounds'
                : 'Test Round ${_currentTestRound + 1} of $_totalRounds',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.teal,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentTestRound + 1) / _totalRounds,
            backgroundColor: Colors.teal.withValues(alpha: 0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.teal),
          ),
        ],
      ),
    );
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTests = true;
      _currentTestRound = 0;
    });

    widget.onStatusChanged(TestingStatus.inProgress);

    // Run 5 rounds of testing as per requirements
    for (int round = 0; round < _totalRounds; round++) {
      setState(() {
        _currentTestRound = round;
      });

      for (final test in _syncTests) {
        await _runSingleTestInternal(test.id);
        await Future.delayed(const Duration(milliseconds: 1000));
      }

      await Future.delayed(const Duration(seconds: 2));
    }

    setState(() {
      _isRunningTests = false;
    });

    final allPassed = _testResults.values.every((result) => result == TestResult.passed);
    widget.onStatusChanged(allPassed ? TestingStatus.passed : TestingStatus.failed);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            allPassed
                ? (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'جميع اختبارات المزامنة نجحت!'
                    : 'All sync tests passed!')
                : (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'بعض اختبارات المزامنة فشلت'
                    : 'Some sync tests failed'),
          ),
          backgroundColor: allPassed ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _runSingleTest(String testId) async {
    await _runSingleTestInternal(testId);
  }

  Future<void> _runSingleTestInternal(String testId) async {
    setState(() {
      _testResults[testId] = TestResult.inProgress;
    });

    try {
      bool testPassed = false;

      switch (testId) {
        case 'real_time_sync':
          testPassed = await _testRealTimeSync();
          break;
        case 'conflict_resolution':
          testPassed = await _testConflictResolution();
          break;
        case 'offline_sync':
          testPassed = await _testOfflineSync();
          break;
        case 'cross_device_sync':
          testPassed = await _testCrossDeviceSync();
          break;
        case 'data_integrity':
          testPassed = await _testDataIntegrity();
          break;
        case 'sync_performance':
          testPassed = await _testSyncPerformance();
          break;
      }

      setState(() {
        _testResults[testId] = testPassed ? TestResult.passed : TestResult.failed;
      });
    } catch (e) {
      setState(() {
        _testResults[testId] = TestResult.failed;
      });
    }
  }

  Future<bool> _testRealTimeSync() async {
    try {
      await _syncService.enableRealTimeSync();
      await _syncService.syncData({'test': 'real_time_data'});
      await Future.delayed(const Duration(seconds: 2));
      return await _syncService.verifySyncStatus();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testConflictResolution() async {
    try {
      await _syncService.createConflictScenario();
      await _syncService.resolveConflicts();
      await Future.delayed(const Duration(seconds: 1));
      return await _syncService.verifyConflictResolution();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testOfflineSync() async {
    try {
      await _syncService.simulateOfflineMode();
      await _syncService.syncOfflineData();
      await _syncService.goOnline();
      await Future.delayed(const Duration(seconds: 2));
      return await _syncService.verifyOfflineSync();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testCrossDeviceSync() async {
    try {
      await _syncService.simulateMultipleDevices();
      await _syncService.syncAcrossDevices();
      await Future.delayed(const Duration(seconds: 2));
      return await _syncService.verifyCrossDeviceSync();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testDataIntegrity() async {
    try {
      await _syncService.testDataIntegrity();
      await Future.delayed(const Duration(seconds: 1));
      return await _syncService.verifyDataIntegrity();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testSyncPerformance() async {
    try {
      final startTime = DateTime.now();
      await _syncService.performLargeDataSync();
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      return duration.inSeconds < 10; // Performance threshold
    } catch (e) {
      return false;
    }
  }

  void _resetTests() {
    setState(() {
      _initializeTestResults();
      _isRunningTests = false;
      _currentTestRound = 0;
    });
    widget.onStatusChanged(TestingStatus.notStarted);
  }
}

class SyncTest {
  final String id;
  final String nameAr;
  final String nameEn;
  final String description;

  SyncTest({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.description,
  });
}

enum TestResult {
  notStarted,
  inProgress,
  passed,
  failed,
}
