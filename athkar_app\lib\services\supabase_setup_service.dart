import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/app_config.dart';

class SupabaseSetupService {
  static SupabaseClient? _supabase;
  static bool _isSetup = false;

  /// Initialize and setup Supabase from scratch
  static Future<void> setupFromScratch() async {
    try {
      debugPrint('Setting up Supabase from scratch...');
      
      // Initialize Supabase
      await Supabase.initialize(
        url: AppConfig.supabaseUrl,
        anonKey: AppConfig.supabaseAnonKey,
        debug: !AppConfig.isProduction,
      );
      
      _supabase = Supabase.instance.client;
      
      debugPrint('Supabase initialized successfully');
      debugPrint('URL: ${AppConfig.supabaseUrl}');
      
      // Test connection
      await _testConnection();
      
      // Setup database schema
      await _setupDatabaseSchema();
      
      _isSetup = true;
      debugPrint('Supabase setup completed successfully');
      
    } catch (e) {
      debugPrint('Error setting up Supabase: $e');
      _isSetup = false;
      rethrow;
    }
  }

  /// Test Supabase connection
  static Future<void> _testConnection() async {
    try {
      debugPrint('Testing Supabase connection...');
      
      // Test basic connection
      await _supabase!.rpc('get_current_user_id');
      debugPrint('Connection test successful');
      
    } catch (e) {
      debugPrint('Connection test result: $e');
      // Don't throw error as this might be expected for new setup
    }
  }

  /// Setup database schema and policies
  static Future<void> _setupDatabaseSchema() async {
    try {
      debugPrint('Setting up database schema...');
      
      // Create user profiles table
      await _createUserProfilesTable();
      
      // Create athkar tables
      await _createAthkarTables();
      
      // Create progress and bookmarks tables
      await _createProgressTables();
      
      // Create settings tables
      await _createSettingsTables();
      
      // Create prayer times table
      await _createPrayerTimesTable();
      
      // Create prebuilt content table
      await _createPrebuiltContentTable();
      
      // Setup RLS policies
      await _setupRLSPolicies();
      
      // Insert sample data
      await _insertSampleData();
      
      debugPrint('Database schema setup completed');
      
    } catch (e) {
      debugPrint('Error setting up database schema: $e');
      // Don't throw as some operations might fail if tables already exist
    }
  }

  /// Create user profiles table
  static Future<void> _createUserProfilesTable() async {
    try {
      // Test if table exists by trying to query it
      await _supabase!.from('user_profiles').select('count').limit(1);
      debugPrint('User profiles table already exists');
    } catch (e) {
      debugPrint('User profiles table setup: $e');
      // Table creation should be done via Supabase dashboard or migration scripts
    }
  }

  /// Create athkar tables
  static Future<void> _createAthkarTables() async {
    try {
      // Test if tables exist
      await _supabase!.from('athkar_routines').select('count').limit(1);
      await _supabase!.from('athkar_steps').select('count').limit(1);
      debugPrint('Athkar tables already exist');
    } catch (e) {
      debugPrint('Athkar tables setup: $e');
      // Table creation should be done via Supabase dashboard
    }
  }

  /// Create progress tables
  static Future<void> _createProgressTables() async {
    try {
      // Test if tables exist
      await _supabase!.from('user_progress').select('count').limit(1);
      await _supabase!.from('bookmarks').select('count').limit(1);
      debugPrint('Progress tables already exist');
    } catch (e) {
      debugPrint('Progress tables setup: $e');
    }
  }

  /// Create settings tables
  static Future<void> _createSettingsTables() async {
    try {
      // Test if tables exist
      await _supabase!.from('user_settings').select('count').limit(1);
      await _supabase!.from('user_preferences').select('count').limit(1);
      debugPrint('Settings tables already exist');
    } catch (e) {
      debugPrint('Settings tables setup: $e');
    }
  }

  /// Create prayer times table
  static Future<void> _createPrayerTimesTable() async {
    try {
      // Test if table exists
      await _supabase!.from('prayer_times_jordan').select('count').limit(1);
      debugPrint('Prayer times table already exists');
    } catch (e) {
      debugPrint('Prayer times table setup: $e');
    }
  }

  /// Create prebuilt content table
  static Future<void> _createPrebuiltContentTable() async {
    try {
      // Test if table exists
      await _supabase!.from('prebuilt_content').select('count').limit(1);
      debugPrint('Prebuilt content table already exists');
    } catch (e) {
      debugPrint('Prebuilt content table setup: $e');
    }
  }

  /// Setup RLS policies
  static Future<void> _setupRLSPolicies() async {
    try {
      debugPrint('Setting up RLS policies...');
      // RLS policies are created with tables above
      debugPrint('RLS policies setup completed');
    } catch (e) {
      debugPrint('Error setting up RLS policies: $e');
    }
  }

  /// Insert sample data
  static Future<void> _insertSampleData() async {
    try {
      debugPrint('Inserting sample data...');
      
      // Insert sample prayer times
      await _supabase!.from('prayer_times_jordan').upsert([
        {
          'city': 'Amman',
          'prayer_date': '2024-01-01',
          'fajr': '05:45',
          'sunrise': '07:10',
          'dhuhr': '12:15',
          'asr': '15:05',
          'maghrib': '17:20',
          'isha': '18:45',
          'latitude': 31.9454,
          'longitude': 35.9284,
        },
        {
          'city': 'Amman',
          'prayer_date': '2024-06-15',
          'fajr': '04:15',
          'sunrise': '05:40',
          'dhuhr': '12:25',
          'asr': '16:15',
          'maghrib': '19:10',
          'isha': '20:35',
          'latitude': 31.9454,
          'longitude': 35.9284,
        },
      ]);
      
      // Insert sample prebuilt content
      await _supabase!.from('prebuilt_content').upsert([
        {
          'title': 'Morning Protection',
          'arabic_text': 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
          'transliteration': 'A\'udhu billahi min ash-shaytani\'r-rajim',
          'translation': 'I seek refuge in Allah from Satan, the accursed',
          'description': 'Seeking protection from Satan at the start of the day',
          'category': 'athkar',
          'subcategory': 'morning',
          'difficulty': 'beginner',
          'target_count': 3,
          'source': 'Quran',
          'reference': '16:98',
          'tags': ['morning', 'protection', 'daily'],
          'color_hex': '2E7D32',
          'is_popular': true,
        },
        {
          'title': 'Subhan Allah',
          'arabic_text': 'سُبْحَانَ اللَّهِ',
          'transliteration': 'Subhan Allah',
          'translation': 'Glory be to Allah',
          'description': 'Glorifying Allah, acknowledging His perfection',
          'category': 'tasbeeh',
          'subcategory': 'subhan_allah',
          'difficulty': 'beginner',
          'target_count': 33,
          'source': 'Hadith',
          'reference': 'Sahih Muslim',
          'tags': ['glorification', 'daily', 'remembrance'],
          'color_hex': '2E7D32',
          'is_popular': true,
        },
      ]);
      
      debugPrint('Sample data inserted successfully');
      
    } catch (e) {
      debugPrint('Error inserting sample data: $e');
      // Don't throw as this might fail if data already exists
    }
  }

  /// Check if setup is complete
  static bool get isSetup => _isSetup;

  /// Get Supabase client
  static SupabaseClient? get client => _supabase;

  /// Test authentication
  static Future<bool> testAuthentication() async {
    try {
      final user = _supabase?.auth.currentUser;
      debugPrint('Current user: ${user?.id}');
      return user != null;
    } catch (e) {
      debugPrint('Authentication test failed: $e');
      return false;
    }
  }

  /// Test data operations
  static Future<bool> testDataOperations() async {
    try {
      // Test reading prebuilt content
      final response = await _supabase!.from('prebuilt_content').select('*').limit(1);
      debugPrint('Data operations test successful: ${response.length} items');
      return true;
    } catch (e) {
      debugPrint('Data operations test failed: $e');
      return false;
    }
  }
}
