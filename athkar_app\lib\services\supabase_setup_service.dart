import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/app_config.dart';

class SupabaseSetupService {
  static SupabaseClient? _supabase;
  static bool _isSetup = false;

  /// Initialize and setup Supabase from scratch
  static Future<void> setupFromScratch() async {
    try {
      debugPrint('Setting up Supabase from scratch...');
      
      // Initialize Supabase
      await Supabase.initialize(
        url: AppConfig.supabaseUrl,
        anonKey: AppConfig.supabaseAnonKey,
        debug: !AppConfig.isProduction,
      );
      
      _supabase = Supabase.instance.client;
      
      debugPrint('Supabase initialized successfully');
      debugPrint('URL: ${AppConfig.supabaseUrl}');
      
      // Test connection
      await _testConnection();
      
      // Setup database schema
      await _setupDatabaseSchema();
      
      _isSetup = true;
      debugPrint('Supabase setup completed successfully');
      
    } catch (e) {
      debugPrint('Error setting up Supabase: $e');
      _isSetup = false;
      rethrow;
    }
  }

  /// Test Supabase connection
  static Future<void> _testConnection() async {
    try {
      debugPrint('Testing Supabase connection...');
      
      // Test basic connection
      final response = await _supabase!.rpc('get_current_user_id');
      debugPrint('Connection test successful');
      
    } catch (e) {
      debugPrint('Connection test result: $e');
      // Don't throw error as this might be expected for new setup
    }
  }

  /// Setup database schema and policies
  static Future<void> _setupDatabaseSchema() async {
    try {
      debugPrint('Setting up database schema...');
      
      // Create user profiles table
      await _createUserProfilesTable();
      
      // Create athkar tables
      await _createAthkarTables();
      
      // Create progress and bookmarks tables
      await _createProgressTables();
      
      // Create settings tables
      await _createSettingsTables();
      
      // Create prayer times table
      await _createPrayerTimesTable();
      
      // Create prebuilt content table
      await _createPrebuiltContentTable();
      
      // Setup RLS policies
      await _setupRLSPolicies();
      
      // Insert sample data
      await _insertSampleData();
      
      debugPrint('Database schema setup completed');
      
    } catch (e) {
      debugPrint('Error setting up database schema: $e');
      // Don't throw as some operations might fail if tables already exist
    }
  }

  /// Create user profiles table
  static Future<void> _createUserProfilesTable() async {
    try {
      await _supabase!.rpc('exec_sql', params: {
        'sql': '''
          CREATE TABLE IF NOT EXISTS user_profiles (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            full_name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            phone_number TEXT,
            avatar_url TEXT,
            location TEXT DEFAULT 'Amman, Jordan',
            language TEXT DEFAULT 'ar',
            theme TEXT DEFAULT 'light',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          
          ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
          
          CREATE POLICY IF NOT EXISTS "Users can view own profile" ON user_profiles
            FOR SELECT USING (auth.uid() = user_id);
          
          CREATE POLICY IF NOT EXISTS "Users can insert own profile" ON user_profiles
            FOR INSERT WITH CHECK (auth.uid() = user_id);
          
          CREATE POLICY IF NOT EXISTS "Users can update own profile" ON user_profiles
            FOR UPDATE USING (auth.uid() = user_id);
        '''
      });
      debugPrint('User profiles table created');
    } catch (e) {
      debugPrint('Error creating user profiles table: $e');
    }
  }

  /// Create athkar tables
  static Future<void> _createAthkarTables() async {
    try {
      await _supabase!.rpc('exec_sql', params: {
        'sql': '''
          CREATE TABLE IF NOT EXISTS athkar_routines (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            title TEXT NOT NULL,
            description TEXT,
            category TEXT DEFAULT 'custom',
            estimated_duration INTEGER,
            color_hex TEXT DEFAULT '2E7D32',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          
          CREATE TABLE IF NOT EXISTS athkar_steps (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            routine_id UUID REFERENCES athkar_routines(id) ON DELETE CASCADE,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            step_order INTEGER NOT NULL,
            arabic_text TEXT NOT NULL,
            transliteration TEXT,
            translation TEXT,
            target_count INTEGER DEFAULT 1,
            audio_url TEXT,
            color_hex TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          
          ALTER TABLE athkar_routines ENABLE ROW LEVEL SECURITY;
          ALTER TABLE athkar_steps ENABLE ROW LEVEL SECURITY;
          
          CREATE POLICY IF NOT EXISTS "Users can manage own routines" ON athkar_routines
            FOR ALL USING (auth.uid() = user_id);
          
          CREATE POLICY IF NOT EXISTS "Users can manage own steps" ON athkar_steps
            FOR ALL USING (auth.uid() = user_id);
        '''
      });
      debugPrint('Athkar tables created');
    } catch (e) {
      debugPrint('Error creating athkar tables: $e');
    }
  }

  /// Create progress tables
  static Future<void> _createProgressTables() async {
    try {
      await _supabase!.rpc('exec_sql', params: {
        'sql': '''
          CREATE TABLE IF NOT EXISTS user_progress (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            routine_id UUID REFERENCES athkar_routines(id) ON DELETE CASCADE,
            step_id UUID REFERENCES athkar_steps(id) ON DELETE CASCADE,
            current_count INTEGER DEFAULT 0,
            completed_at TIMESTAMP WITH TIME ZONE,
            session_date DATE DEFAULT CURRENT_DATE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          
          CREATE TABLE IF NOT EXISTS bookmarks (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            content_type TEXT NOT NULL,
            content_id TEXT NOT NULL,
            title TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          
          ALTER TABLE user_progress ENABLE ROW LEVEL SECURITY;
          ALTER TABLE bookmarks ENABLE ROW LEVEL SECURITY;
          
          CREATE POLICY IF NOT EXISTS "Users can manage own progress" ON user_progress
            FOR ALL USING (auth.uid() = user_id);
          
          CREATE POLICY IF NOT EXISTS "Users can manage own bookmarks" ON bookmarks
            FOR ALL USING (auth.uid() = user_id);
        '''
      });
      debugPrint('Progress tables created');
    } catch (e) {
      debugPrint('Error creating progress tables: $e');
    }
  }

  /// Create settings tables
  static Future<void> _createSettingsTables() async {
    try {
      await _supabase!.rpc('exec_sql', params: {
        'sql': '''
          CREATE TABLE IF NOT EXISTS user_settings (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            setting_key TEXT NOT NULL,
            setting_value TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(user_id, setting_key)
          );
          
          CREATE TABLE IF NOT EXISTS user_preferences (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            prayer_notifications BOOLEAN DEFAULT true,
            athkar_reminders BOOLEAN DEFAULT true,
            sound_enabled BOOLEAN DEFAULT true,
            vibration_enabled BOOLEAN DEFAULT true,
            auto_sync BOOLEAN DEFAULT true,
            preferred_language TEXT DEFAULT 'ar',
            theme_mode TEXT DEFAULT 'light',
            font_size TEXT DEFAULT 'medium',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          
          ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
          ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
          
          CREATE POLICY IF NOT EXISTS "Users can manage own settings" ON user_settings
            FOR ALL USING (auth.uid() = user_id);
          
          CREATE POLICY IF NOT EXISTS "Users can manage own preferences" ON user_preferences
            FOR ALL USING (auth.uid() = user_id);
        '''
      });
      debugPrint('Settings tables created');
    } catch (e) {
      debugPrint('Error creating settings tables: $e');
    }
  }

  /// Create prayer times table
  static Future<void> _createPrayerTimesTable() async {
    try {
      await _supabase!.rpc('exec_sql', params: {
        'sql': '''
          CREATE TABLE IF NOT EXISTS prayer_times_jordan (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            city TEXT NOT NULL,
            prayer_date DATE NOT NULL,
            fajr TIME NOT NULL,
            sunrise TIME NOT NULL,
            dhuhr TIME NOT NULL,
            asr TIME NOT NULL,
            maghrib TIME NOT NULL,
            isha TIME NOT NULL,
            latitude DECIMAL(10, 8),
            longitude DECIMAL(11, 8),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(city, prayer_date)
          );
          
          ALTER TABLE prayer_times_jordan ENABLE ROW LEVEL SECURITY;
          
          CREATE POLICY IF NOT EXISTS "Authenticated users can view prayer times" ON prayer_times_jordan
            FOR SELECT USING (auth.role() = 'authenticated');
        '''
      });
      debugPrint('Prayer times table created');
    } catch (e) {
      debugPrint('Error creating prayer times table: $e');
    }
  }

  /// Create prebuilt content table
  static Future<void> _createPrebuiltContentTable() async {
    try {
      await _supabase!.rpc('exec_sql', params: {
        'sql': '''
          CREATE TABLE IF NOT EXISTS prebuilt_content (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            title TEXT NOT NULL,
            arabic_text TEXT NOT NULL,
            transliteration TEXT,
            translation TEXT,
            description TEXT,
            category TEXT NOT NULL,
            subcategory TEXT,
            difficulty TEXT DEFAULT 'beginner',
            target_count INTEGER DEFAULT 1,
            source TEXT,
            reference TEXT,
            tags TEXT[],
            audio_url TEXT,
            color_hex TEXT DEFAULT '2E7D32',
            is_popular BOOLEAN DEFAULT false,
            usage_count INTEGER DEFAULT 0,
            steps JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          
          ALTER TABLE prebuilt_content ENABLE ROW LEVEL SECURITY;
          
          CREATE POLICY IF NOT EXISTS "Authenticated users can view prebuilt content" ON prebuilt_content
            FOR SELECT USING (auth.role() = 'authenticated');
        '''
      });
      debugPrint('Prebuilt content table created');
    } catch (e) {
      debugPrint('Error creating prebuilt content table: $e');
    }
  }

  /// Setup RLS policies
  static Future<void> _setupRLSPolicies() async {
    try {
      debugPrint('Setting up RLS policies...');
      // RLS policies are created with tables above
      debugPrint('RLS policies setup completed');
    } catch (e) {
      debugPrint('Error setting up RLS policies: $e');
    }
  }

  /// Insert sample data
  static Future<void> _insertSampleData() async {
    try {
      debugPrint('Inserting sample data...');
      
      // Insert sample prayer times
      await _supabase!.from('prayer_times_jordan').upsert([
        {
          'city': 'Amman',
          'prayer_date': '2024-01-01',
          'fajr': '05:45',
          'sunrise': '07:10',
          'dhuhr': '12:15',
          'asr': '15:05',
          'maghrib': '17:20',
          'isha': '18:45',
          'latitude': 31.9454,
          'longitude': 35.9284,
        },
        {
          'city': 'Amman',
          'prayer_date': '2024-06-15',
          'fajr': '04:15',
          'sunrise': '05:40',
          'dhuhr': '12:25',
          'asr': '16:15',
          'maghrib': '19:10',
          'isha': '20:35',
          'latitude': 31.9454,
          'longitude': 35.9284,
        },
      ]);
      
      // Insert sample prebuilt content
      await _supabase!.from('prebuilt_content').upsert([
        {
          'title': 'Morning Protection',
          'arabic_text': 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
          'transliteration': 'A\'udhu billahi min ash-shaytani\'r-rajim',
          'translation': 'I seek refuge in Allah from Satan, the accursed',
          'description': 'Seeking protection from Satan at the start of the day',
          'category': 'athkar',
          'subcategory': 'morning',
          'difficulty': 'beginner',
          'target_count': 3,
          'source': 'Quran',
          'reference': '16:98',
          'tags': ['morning', 'protection', 'daily'],
          'color_hex': '2E7D32',
          'is_popular': true,
        },
        {
          'title': 'Subhan Allah',
          'arabic_text': 'سُبْحَانَ اللَّهِ',
          'transliteration': 'Subhan Allah',
          'translation': 'Glory be to Allah',
          'description': 'Glorifying Allah, acknowledging His perfection',
          'category': 'tasbeeh',
          'subcategory': 'subhan_allah',
          'difficulty': 'beginner',
          'target_count': 33,
          'source': 'Hadith',
          'reference': 'Sahih Muslim',
          'tags': ['glorification', 'daily', 'remembrance'],
          'color_hex': '2E7D32',
          'is_popular': true,
        },
      ]);
      
      debugPrint('Sample data inserted successfully');
      
    } catch (e) {
      debugPrint('Error inserting sample data: $e');
      // Don't throw as this might fail if data already exists
    }
  }

  /// Check if setup is complete
  static bool get isSetup => _isSetup;

  /// Get Supabase client
  static SupabaseClient? get client => _supabase;

  /// Test authentication
  static Future<bool> testAuthentication() async {
    try {
      final user = _supabase?.auth.currentUser;
      debugPrint('Current user: ${user?.id}');
      return user != null;
    } catch (e) {
      debugPrint('Authentication test failed: $e');
      return false;
    }
  }

  /// Test data operations
  static Future<bool> testDataOperations() async {
    try {
      // Test reading prebuilt content
      final response = await _supabase!.from('prebuilt_content').select('*').limit(1);
      debugPrint('Data operations test successful: ${response.length} items');
      return true;
    } catch (e) {
      debugPrint('Data operations test failed: $e');
      return false;
    }
  }
}
