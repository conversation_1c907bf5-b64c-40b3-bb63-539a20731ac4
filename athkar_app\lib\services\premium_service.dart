import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/premium_models.dart';

class PremiumService {
  static const String _premiumKey = 'premium_status';

  
  static SharedPreferences? _prefs;
  static PremiumStatus? _currentStatus;
  static final StreamController<PremiumStatus> _statusController = 
      StreamController<PremiumStatus>.broadcast();

  // Premium feature flags
  static const Map<String, PremiumFeature> _premiumFeatures = {
    'unlimited_athkar': PremiumFeature(
      id: 'unlimited_athkar',
      name: 'Unlimited Athkar',
      description: 'Create unlimited custom athkar routines',
      category: PremiumFeatureCategory.content,
    ),
    'advanced_analytics': PremiumFeature(
      id: 'advanced_analytics',
      name: 'Advanced Analytics',
      description: 'Detailed progress tracking and insights',
      category: PremiumFeatureCategory.analytics,
    ),
    'cloud_backup': PremiumFeature(
      id: 'cloud_backup',
      name: 'Cloud Backup',
      description: 'Automatic cloud backup and sync across devices',
      category: PremiumFeatureCategory.sync,
    ),
    'custom_themes': PremiumFeature(
      id: 'custom_themes',
      name: 'Custom Themes',
      description: 'Create and customize app themes',
      category: PremiumFeatureCategory.customization,
    ),
    'offline_quran': PremiumFeature(
      id: 'offline_quran',
      name: 'Offline Quran',
      description: 'Download complete Quran for offline reading',
      category: PremiumFeatureCategory.content,
    ),
    'advanced_notifications': PremiumFeature(
      id: 'advanced_notifications',
      name: 'Smart Notifications',
      description: 'AI-powered prayer and dhikr reminders',
      category: PremiumFeatureCategory.notifications,
    ),
    'family_sharing': PremiumFeature(
      id: 'family_sharing',
      name: 'Family Sharing',
      description: 'Share premium features with family members',
      category: PremiumFeatureCategory.social,
    ),
    'priority_support': PremiumFeature(
      id: 'priority_support',
      name: 'Priority Support',
      description: '24/7 priority customer support',
      category: PremiumFeatureCategory.support,
    ),
  };

  // Subscription plans
  static const List<SubscriptionPlan> _subscriptionPlans = [
    SubscriptionPlan(
      id: 'monthly',
      name: 'Monthly Premium',
      description: 'All premium features for one month',
      price: 4.99,
      currency: 'USD',
      duration: Duration(days: 30),
      features: [
        'unlimited_athkar',
        'advanced_analytics',
        'cloud_backup',
        'custom_themes',
        'offline_quran',
        'advanced_notifications',
        'priority_support',
      ],
      isPopular: false,
    ),
    SubscriptionPlan(
      id: 'yearly',
      name: 'Yearly Premium',
      description: 'All premium features for one year (Save 50%)',
      price: 29.99,
      currency: 'USD',
      duration: Duration(days: 365),
      features: [
        'unlimited_athkar',
        'advanced_analytics',
        'cloud_backup',
        'custom_themes',
        'offline_quran',
        'advanced_notifications',
        'family_sharing',
        'priority_support',
      ],
      isPopular: true,
      discount: 0.5,
    ),
    SubscriptionPlan(
      id: 'lifetime',
      name: 'Lifetime Premium',
      description: 'One-time payment for lifetime access',
      price: 99.99,
      currency: 'USD',
      duration: null, // Lifetime
      features: [
        'unlimited_athkar',
        'advanced_analytics',
        'cloud_backup',
        'custom_themes',
        'offline_quran',
        'advanced_notifications',
        'family_sharing',
        'priority_support',
      ],
      isPopular: false,
      isLifetime: true,
    ),
  ];

  static Stream<PremiumStatus> get statusStream => _statusController.stream;
  static PremiumStatus get currentStatus => _currentStatus ?? PremiumStatus.free();

  static Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadPremiumStatus();
      await _validateSubscription();
    } catch (e) {
      debugPrint('Error initializing premium service: $e');
    }
  }

  static Future<void> _loadPremiumStatus() async {
    try {
      final statusJson = _prefs?.getString(_premiumKey);
      if (statusJson != null) {
        final statusData = json.decode(statusJson);
        _currentStatus = PremiumStatus.fromJson(statusData);
      } else {
        _currentStatus = PremiumStatus.free();
      }
      _statusController.add(_currentStatus!);
    } catch (e) {
      debugPrint('Error loading premium status: $e');
      _currentStatus = PremiumStatus.free();
    }
  }

  static Future<void> _savePremiumStatus() async {
    try {
      if (_currentStatus != null) {
        final statusJson = json.encode(_currentStatus!.toJson());
        await _prefs?.setString(_premiumKey, statusJson);
        _statusController.add(_currentStatus!);
      }
    } catch (e) {
      debugPrint('Error saving premium status: $e');
    }
  }

  static Future<void> _validateSubscription() async {
    if (_currentStatus?.isPremium == true && _currentStatus?.expiryDate != null) {
      if (_currentStatus!.expiryDate!.isBefore(DateTime.now())) {
        // Subscription expired
        await _expireSubscription();
      }
    }
  }

  static Future<void> _expireSubscription() async {
    _currentStatus = PremiumStatus.free();
    await _savePremiumStatus();
  }

  // Check if a specific feature is available
  static bool hasFeature(String featureId) {
    if (_currentStatus?.isPremium != true) return false;
    return _currentStatus?.features.contains(featureId) ?? false;
  }

  // Get all available premium features
  static List<PremiumFeature> getAvailableFeatures() {
    return _premiumFeatures.values.toList();
  }

  // Get subscription plans
  static List<SubscriptionPlan> getSubscriptionPlans() {
    return _subscriptionPlans;
  }

  // Purchase subscription
  static Future<bool> purchaseSubscription(String planId) async {
    try {
      final plan = _subscriptionPlans.firstWhere((p) => p.id == planId);
      
      // Implement actual payment processing
      // This would integrate with platform-specific payment systems
      final success = await _processPayment(plan);
      
      if (success) {
        await _activateSubscription(plan);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error purchasing subscription: $e');
      return false;
    }
  }

  static Future<bool> _processPayment(SubscriptionPlan plan) async {
    // Integrate with actual payment processors
    // - Google Play Billing for Android
    // - App Store Connect for iOS
    // - Stripe for web

    try {
      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 2));

      // In a real app, this would:
      // 1. Validate payment method
      // 2. Process payment with the platform store
      // 3. Handle payment confirmation
      // 4. Update user's subscription status

      debugPrint('Processing payment for plan: ${plan.name} - ${plan.price}');

      // Simulate successful payment for demo
      debugPrint('Payment processed successfully');
      return true;
    } catch (e) {
      debugPrint('Payment processing error: $e');
      return false;
    }
  }

  static Future<void> _activateSubscription(SubscriptionPlan plan) async {
    final now = DateTime.now();
    final expiryDate = plan.isLifetime ? null : now.add(plan.duration!);
    
    _currentStatus = PremiumStatus(
      isPremium: true,
      planId: plan.id,
      planName: plan.name,
      features: plan.features,
      purchaseDate: now,
      expiryDate: expiryDate,
      isLifetime: plan.isLifetime,
    );
    
    await _savePremiumStatus();
  }

  // Restore purchases
  static Future<bool> restorePurchases() async {
    try {
      // Implement platform-specific purchase restoration
      // This would query the platform stores for existing purchases

      try {
        // In a real app, this would:
        // 1. Query Google Play Billing for Android purchases
        // 2. Query App Store for iOS purchases
        // 3. Validate purchase receipts
        // 4. Restore active subscriptions

        debugPrint('Restoring purchases...');
        await Future.delayed(const Duration(seconds: 1));

        // For demo purposes, simulate finding previous purchases occasionally
        final hasPreviousPurchases = math.Random().nextDouble() > 0.8; // 20% chance

        if (hasPreviousPurchases) {
          // Simulate restoring a premium subscription
          final plan = _subscriptionPlans.first;
          await _activateSubscription(plan);
          debugPrint('Previous purchase restored successfully');
          return true;
        } else {
          debugPrint('No previous purchases found');
          return false;
        }
      } catch (e) {
        debugPrint('Error during purchase restoration: $e');
        return false;
      }
    } catch (e) {
      debugPrint('Error restoring purchases: $e');
      return false;
    }
  }

  // Cancel subscription
  static Future<bool> cancelSubscription() async {
    try {
      // Implement subscription cancellation
      // This would cancel the subscription with the platform store

      try {
        // In a real app, this would:
        // 1. Cancel subscription with Google Play Billing (Android)
        // 2. Cancel subscription with App Store (iOS)
        // 3. Handle cancellation confirmation
        // 4. Update local subscription status

        debugPrint('Cancelling subscription...');

        if (_currentStatus?.isPremium == true) {
          // Mark as cancelled but keep active until expiry
          _currentStatus = _currentStatus!.copyWith(isCancelled: true);
          await _savePremiumStatus();

          debugPrint('Subscription cancelled successfully');
          debugPrint('Premium features will remain active until expiry: ${_currentStatus!.expiryDate}');

          return true;
        } else {
          debugPrint('No active subscription to cancel');
          return false;
        }
      } catch (e) {
        debugPrint('Error in cancellation process: $e');
        return false;
      }
    } catch (e) {
      debugPrint('Error cancelling subscription: $e');
      return false;
    }
  }

  // Get usage limits for free users
  static UsageLimits getUsageLimits() {
    if (_currentStatus?.isPremium == true) {
      return UsageLimits.unlimited();
    }
    
    return const UsageLimits(
      maxCustomAthkar: 5,
      maxCustomDuas: 10,
      maxBackups: 1,
      maxThemes: 1,
      analyticsRetentionDays: 7,
    );
  }

  // Check if user has reached usage limit
  static bool hasReachedLimit(String limitType, int currentUsage) {
    if (_currentStatus?.isPremium == true) return false;
    
    final limits = getUsageLimits();
    switch (limitType) {
      case 'custom_athkar':
        return currentUsage >= limits.maxCustomAthkar;
      case 'custom_duas':
        return currentUsage >= limits.maxCustomDuas;
      case 'backups':
        return currentUsage >= limits.maxBackups;
      case 'themes':
        return currentUsage >= limits.maxThemes;
      default:
        return false;
    }
  }

  // Get premium benefits
  static List<String> getPremiumBenefits() {
    return [
      'Unlimited custom athkar and duas',
      'Advanced analytics and insights',
      'Cloud backup and sync',
      'Custom themes and colors',
      'Offline Quran access',
      'Smart AI-powered notifications',
      'Family sharing (yearly plan)',
      'Priority customer support',
      'Ad-free experience',
      'Early access to new features',
    ];
  }

  // Track premium feature usage
  static Future<void> trackFeatureUsage(String featureId) async {
    try {
      // Implement feature usage tracking for analytics
      final timestamp = DateTime.now().toIso8601String();
      final userId = await _getCurrentUserId();

      // In a real app, this would send analytics data to your backend
      final usageData = {
        'user_id': userId,
        'feature_id': featureId,
        'timestamp': timestamp,
        'subscription_type': _currentStatus?.planId ?? 'free',
        'platform': 'mobile',
      };

      debugPrint('Premium feature used: $featureId by user: $userId');
      debugPrint('Usage data: $usageData');

      // Store locally for later sync
      final prefs = await SharedPreferences.getInstance();
      final existingUsage = prefs.getStringList('premium_feature_usage') ?? [];
      existingUsage.add(json.encode(usageData));
      await prefs.setStringList('premium_feature_usage', existingUsage);

    } catch (e) {
      debugPrint('Error tracking feature usage: $e');
    }
  }

  static Future<String> _getCurrentUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('current_user_id') ?? 'anonymous_user';
    } catch (e) {
      return 'anonymous_user';
    }
  }

  // Get promotional offers
  static List<PromotionalOffer> getPromotionalOffers() {
    // Implement dynamic promotional offers
    final now = DateTime.now();
    final offers = <PromotionalOffer>[];

    // Ramadan special offer (during Ramadan month)
    final ramadanStart = DateTime(now.year, 3, 10); // Approximate Ramadan start
    final ramadanEnd = DateTime(now.year, 4, 10); // Approximate Ramadan end

    if (now.isAfter(ramadanStart) && now.isBefore(ramadanEnd)) {
      offers.add(PromotionalOffer(
        id: 'ramadan_special',
        title: 'Ramadan Kareem Special',
        description: '50% off yearly subscription during the holy month',
        discountPercentage: 0.5,
        validUntil: ramadanEnd,
        planIds: ['yearly'],
      ));
    }

    // New user offer
    offers.add(PromotionalOffer(
      id: 'new_user_welcome',
      title: 'Welcome Offer',
      description: '30% off your first subscription',
      discountPercentage: 0.3,
      validUntil: now.add(const Duration(days: 7)),
      planIds: ['monthly', 'yearly'],
    ));

    // Weekend special
    if (now.weekday == DateTime.friday || now.weekday == DateTime.saturday) {
      offers.add(PromotionalOffer(
        id: 'weekend_special',
        title: 'Jummah Special',
        description: '25% off all plans this weekend',
        discountPercentage: 0.25,
        validUntil: now.add(const Duration(days: 2)),
        planIds: ['monthly', 'yearly'],
      ));
    }

    return offers;
  }

  // Apply promotional code
  static Future<bool> applyPromotionalCode(String code) async {
    try {
      // Implement promotional code validation
      final normalizedCode = code.trim().toUpperCase();

      // Define valid promotional codes
      final validCodes = {
        'RAMADAN2024': {'discount': 0.5, 'description': 'Ramadan Special 50% off'},
        'WELCOME30': {'discount': 0.3, 'description': 'Welcome offer 30% off'},
        'JUMMAH25': {'discount': 0.25, 'description': 'Jummah special 25% off'},
        'STUDENT20': {'discount': 0.2, 'description': 'Student discount 20% off'},
        'FAMILY15': {'discount': 0.15, 'description': 'Family plan 15% off'},
      };

      if (validCodes.containsKey(normalizedCode)) {
        final codeData = validCodes[normalizedCode]!;
        final discount = codeData['discount'] as double;
        final description = codeData['description'] as String;

        // Store the applied promotional code
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('applied_promo_code', normalizedCode);
        await prefs.setDouble('promo_discount', discount);
        await prefs.setString('promo_description', description);

        debugPrint('Promotional code applied successfully: $normalizedCode');
        debugPrint('Discount: ${(discount * 100).round()}% - $description');

        return true;
      } else {
        debugPrint('Invalid promotional code: $normalizedCode');
        return false;
      }
    } catch (e) {
      debugPrint('Error applying promotional code: $e');
      return false;
    }
  }

  static void dispose() {
    _statusController.close();
  }
}
