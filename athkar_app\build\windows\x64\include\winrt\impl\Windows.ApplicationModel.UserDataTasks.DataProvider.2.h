// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_ApplicationModel_UserDataTasks_DataProvider_2_H
#define WINRT_Windows_ApplicationModel_UserDataTasks_DataProvider_2_H
#include "winrt/impl/Windows.ApplicationModel.UserDataTasks.DataProvider.1.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::UserDataTasks::DataProvider
{
    struct __declspec(empty_bases) UserDataTaskDataProviderConnection : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskDataProviderConnection
    {
        UserDataTaskDataProviderConnection(std::nullptr_t) noexcept {}
        UserDataTaskDataProviderConnection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskDataProviderConnection(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) UserDataTaskDataProviderTriggerDetails : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskDataProviderTriggerDetails
    {
        UserDataTaskDataProviderTriggerDetails(std::nullptr_t) noexcept {}
        UserDataTaskDataProviderTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskDataProviderTriggerDetails(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) UserDataTaskListCompleteTaskRequest : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListCompleteTaskRequest
    {
        UserDataTaskListCompleteTaskRequest(std::nullptr_t) noexcept {}
        UserDataTaskListCompleteTaskRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListCompleteTaskRequest(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) UserDataTaskListCompleteTaskRequestEventArgs : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListCompleteTaskRequestEventArgs
    {
        UserDataTaskListCompleteTaskRequestEventArgs(std::nullptr_t) noexcept {}
        UserDataTaskListCompleteTaskRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListCompleteTaskRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) UserDataTaskListCreateOrUpdateTaskRequest : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListCreateOrUpdateTaskRequest
    {
        UserDataTaskListCreateOrUpdateTaskRequest(std::nullptr_t) noexcept {}
        UserDataTaskListCreateOrUpdateTaskRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListCreateOrUpdateTaskRequest(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) UserDataTaskListCreateOrUpdateTaskRequestEventArgs : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListCreateOrUpdateTaskRequestEventArgs
    {
        UserDataTaskListCreateOrUpdateTaskRequestEventArgs(std::nullptr_t) noexcept {}
        UserDataTaskListCreateOrUpdateTaskRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListCreateOrUpdateTaskRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) UserDataTaskListDeleteTaskRequest : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListDeleteTaskRequest
    {
        UserDataTaskListDeleteTaskRequest(std::nullptr_t) noexcept {}
        UserDataTaskListDeleteTaskRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListDeleteTaskRequest(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) UserDataTaskListDeleteTaskRequestEventArgs : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListDeleteTaskRequestEventArgs
    {
        UserDataTaskListDeleteTaskRequestEventArgs(std::nullptr_t) noexcept {}
        UserDataTaskListDeleteTaskRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListDeleteTaskRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) UserDataTaskListSkipOccurrenceRequest : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListSkipOccurrenceRequest
    {
        UserDataTaskListSkipOccurrenceRequest(std::nullptr_t) noexcept {}
        UserDataTaskListSkipOccurrenceRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListSkipOccurrenceRequest(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) UserDataTaskListSkipOccurrenceRequestEventArgs : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListSkipOccurrenceRequestEventArgs
    {
        UserDataTaskListSkipOccurrenceRequestEventArgs(std::nullptr_t) noexcept {}
        UserDataTaskListSkipOccurrenceRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListSkipOccurrenceRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) UserDataTaskListSyncManagerSyncRequest : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListSyncManagerSyncRequest
    {
        UserDataTaskListSyncManagerSyncRequest(std::nullptr_t) noexcept {}
        UserDataTaskListSyncManagerSyncRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListSyncManagerSyncRequest(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) UserDataTaskListSyncManagerSyncRequestEventArgs : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListSyncManagerSyncRequestEventArgs
    {
        UserDataTaskListSyncManagerSyncRequestEventArgs(std::nullptr_t) noexcept {}
        UserDataTaskListSyncManagerSyncRequestEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::UserDataTasks::DataProvider::IUserDataTaskListSyncManagerSyncRequestEventArgs(ptr, take_ownership_from_abi) {}
    };
}
#endif
