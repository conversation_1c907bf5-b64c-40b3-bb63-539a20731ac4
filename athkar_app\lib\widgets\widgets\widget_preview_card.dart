import 'package:flutter/material.dart';
import '../../services/widget_manager.dart';
import '../../services/language_service.dart';
import '../../theme/app_theme.dart';

class WidgetPreviewCard extends StatelessWidget {
  final WidgetManager.WidgetConfig config;
  final LanguageService languageService;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final VoidCallback onRefresh;

  const WidgetPreviewCard({
    super.key,
    required this.config,
    required this.languageService,
    required this.onEdit,
    required this.onDelete,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.primaryGreen.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getWidgetIcon(config.type),
                    color: AppTheme.primaryGreen,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getWidgetTitle(config.type),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryGreen,
                        ),
                      ),
                      Text(
                        _getSizeText(config.size),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusIndicator(),
              ],
            ),
          ),
          
          // Preview
          Container(
            padding: const EdgeInsets.all(16),
            child: _buildWidgetPreview(),
          ),
          
          // Actions
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildActionButton(
                  icon: Icons.refresh,
                  label: languageService.isArabic ? 'تحديث' : 'Refresh',
                  onPressed: onRefresh,
                  color: AppTheme.primaryGreen,
                ),
                _buildActionButton(
                  icon: Icons.edit,
                  label: languageService.isArabic ? 'تعديل' : 'Edit',
                  onPressed: onEdit,
                  color: Colors.blue,
                ),
                _buildActionButton(
                  icon: Icons.delete,
                  label: languageService.isArabic ? 'حذف' : 'Delete',
                  onPressed: onDelete,
                  color: Colors.red,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: const BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            languageService.isArabic ? 'نشط' : 'Active',
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWidgetPreview() {
    switch (config.type) {
      case WidgetManager.WidgetType.routineCounter:
        return _buildRoutineCounterPreview();
      case WidgetManager.WidgetType.ayahOfDay:
        return _buildAyahOfDayPreview();
      case WidgetManager.WidgetType.athkarReminder:
        return _buildAthkarReminderPreview();
      case WidgetManager.WidgetType.quranProgress:
        return _buildQuranProgressPreview();
      case WidgetManager.WidgetType.prayerTimes:
        return _buildPrayerTimesPreview();
      case WidgetManager.WidgetType.islamicCalendar:
        return _buildIslamicCalendarPreview();
      case WidgetManager.WidgetType.dhikrCounter:
        return _buildDhikrCounterPreview();
      case WidgetManager.WidgetType.statistics:
        return _buildStatisticsPreview();
    }
  }

  Widget _buildRoutineCounterPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageService.isArabic ? 'أذكار الصباح' : 'Morning Athkar',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '15 / 33',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          CircularProgressIndicator(
            value: 0.45,
            backgroundColor: Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
            strokeWidth: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildAyahOfDayPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
            style: const TextStyle(
              fontSize: 14,
              fontFamily: 'Amiri',
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
            textDirection: TextDirection.rtl,
          ),
          const SizedBox(height: 8),
          Text(
            languageService.isArabic ? 'الطلاق - آية 2' : 'At-Talaq - Verse 2',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAthkarReminderPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.notifications_active,
            color: AppTheme.primaryGreen,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageService.isArabic ? 'أذكار المساء' : 'Evening Athkar',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  languageService.isArabic ? 'خلال 2:30:00' : 'In 2:30:00',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuranProgressPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageService.isArabic ? 'البقرة' : 'Al-Baqarah',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '12.5%',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: 0.125,
            backgroundColor: Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
          ),
        ],
      ),
    );
  }

  Widget _buildPrayerTimesPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                languageService.isArabic ? 'الصلاة القادمة' : 'Next Prayer',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                languageService.isArabic ? 'العصر' : 'Asr',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '15:30',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryGreen,
                ),
              ),
              Text(
                languageService.isArabic ? 'خلال 1:45' : 'In 1:45',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildIslamicCalendarPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            '15 رجب 1445',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              fontFamily: 'Amiri',
            ),
            textDirection: TextDirection.rtl,
          ),
          const SizedBox(height: 4),
          Text(
            languageService.isArabic ? 'الإسراء والمعراج' : 'Isra and Mi\'raj',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDhikrCounterPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'سبحان الله',
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              fontFamily: 'Amiri',
            ),
            textDirection: TextDirection.rtl,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppTheme.primaryGreen,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              '67',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('1250', languageService.isArabic ? 'أذكار' : 'Athkar'),
          _buildStatItem('15', languageService.isArabic ? 'أيام' : 'Days'),
          _buildStatItem('45', languageService.isArabic ? 'مكتمل' : 'Complete'),
        ],
      ),
    );
  }

  Widget _buildStatItem(String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 8,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: color,
              size: 18,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getWidgetIcon(WidgetManager.WidgetType type) {
    switch (type) {
      case WidgetManager.WidgetType.routineCounter:
        return Icons.track_changes;
      case WidgetManager.WidgetType.ayahOfDay:
        return Icons.menu_book;
      case WidgetManager.WidgetType.athkarReminder:
        return Icons.notifications_active;
      case WidgetManager.WidgetType.quranProgress:
        return Icons.auto_stories;
      case WidgetManager.WidgetType.prayerTimes:
        return Icons.access_time;
      case WidgetManager.WidgetType.islamicCalendar:
        return Icons.calendar_today;
      case WidgetManager.WidgetType.dhikrCounter:
        return Icons.add_circle;
      case WidgetManager.WidgetType.statistics:
        return Icons.bar_chart;
    }
  }

  String _getWidgetTitle(WidgetManager.WidgetType type) {
    if (languageService.isArabic) {
      switch (type) {
        case WidgetManager.WidgetType.routineCounter:
          return 'عداد الروتين';
        case WidgetManager.WidgetType.ayahOfDay:
          return 'آية اليوم';
        case WidgetManager.WidgetType.athkarReminder:
          return 'تذكير الأذكار';
        case WidgetManager.WidgetType.quranProgress:
          return 'تقدم القرآن';
        case WidgetManager.WidgetType.prayerTimes:
          return 'أوقات الصلاة';
        case WidgetManager.WidgetType.islamicCalendar:
          return 'التقويم الهجري';
        case WidgetManager.WidgetType.dhikrCounter:
          return 'عداد الذكر';
        case WidgetManager.WidgetType.statistics:
          return 'الإحصائيات';
      }
    } else {
      switch (type) {
        case WidgetManager.WidgetType.routineCounter:
          return 'Routine Counter';
        case WidgetManager.WidgetType.ayahOfDay:
          return 'Ayah of Day';
        case WidgetManager.WidgetType.athkarReminder:
          return 'Athkar Reminder';
        case WidgetManager.WidgetType.quranProgress:
          return 'Quran Progress';
        case WidgetManager.WidgetType.prayerTimes:
          return 'Prayer Times';
        case WidgetManager.WidgetType.islamicCalendar:
          return 'Islamic Calendar';
        case WidgetManager.WidgetType.dhikrCounter:
          return 'Dhikr Counter';
        case WidgetManager.WidgetType.statistics:
          return 'Statistics';
      }
    }
  }

  String _getSizeText(WidgetManager.WidgetSize size) {
    switch (size) {
      case WidgetManager.WidgetSize.small1x1:
        return languageService.isArabic ? 'صغير (1×1)' : 'Small (1×1)';
      case WidgetManager.WidgetSize.medium2x1:
        return languageService.isArabic ? 'متوسط (2×1)' : 'Medium (2×1)';
      case WidgetManager.WidgetSize.large2x2:
        return languageService.isArabic ? 'كبير (2×2)' : 'Large (2×2)';
      case WidgetManager.WidgetSize.extraLarge4x2:
        return languageService.isArabic ? 'كبير جداً (4×2)' : 'Extra Large (4×2)';
    }
  }
}
