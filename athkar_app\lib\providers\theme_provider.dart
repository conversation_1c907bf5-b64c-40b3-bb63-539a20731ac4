import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppThemeMode {
  light,
  dark,
  system,
}

class ThemeProvider extends ChangeNotifier {
  static const String _themeKey = 'app_theme_mode';
  static const String _fontSizeKey = 'app_font_size';
  
  AppThemeMode _themeMode = AppThemeMode.system;
  double _fontSize = 1.0; // Scale factor: 0.8 (small), 1.0 (medium), 1.2 (large)
  
  AppThemeMode get themeMode => _themeMode;
  double get fontSize => _fontSize;
  
  // Get the actual ThemeMode for MaterialApp
  ThemeMode get materialThemeMode {
    switch (_themeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }
  
  // Font size labels
  String get fontSizeLabel {
    if (_fontSize <= 0.8) return 'Small';
    if (_fontSize <= 1.0) return 'Medium';
    if (_fontSize <= 1.2) return 'Large';
    return 'Extra Large';
  }
  
  ThemeProvider() {
    _loadPreferences();
  }
  
  Future<void> _loadPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load theme mode
      final themeIndex = prefs.getInt(_themeKey) ?? AppThemeMode.system.index;
      _themeMode = AppThemeMode.values[themeIndex];
      
      // Load font size
      _fontSize = prefs.getDouble(_fontSizeKey) ?? 1.0;
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading theme preferences: $e');
    }
  }
  
  Future<void> setThemeMode(AppThemeMode mode) async {
    if (_themeMode == mode) return;
    
    _themeMode = mode;
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeKey, mode.index);
    } catch (e) {
      debugPrint('Error saving theme mode: $e');
    }
  }
  
  Future<void> setFontSize(double size) async {
    if (_fontSize == size) return;
    
    _fontSize = size.clamp(0.8, 1.4);
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_fontSizeKey, _fontSize);
    } catch (e) {
      debugPrint('Error saving font size: $e');
    }
  }
  
  // Convenience methods
  Future<void> toggleTheme() async {
    switch (_themeMode) {
      case AppThemeMode.light:
        await setThemeMode(AppThemeMode.dark);
        break;
      case AppThemeMode.dark:
        await setThemeMode(AppThemeMode.system);
        break;
      case AppThemeMode.system:
        await setThemeMode(AppThemeMode.light);
        break;
    }
  }
  
  Future<void> increaseFontSize() async {
    await setFontSize(_fontSize + 0.1);
  }
  
  Future<void> decreaseFontSize() async {
    await setFontSize(_fontSize - 0.1);
  }
  
  Future<void> resetFontSize() async {
    await setFontSize(1.0);
  }
}
