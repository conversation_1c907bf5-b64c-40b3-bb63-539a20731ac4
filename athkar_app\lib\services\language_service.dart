import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageService extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  static const String _rtlKey = 'rtl_enabled';
  
  Locale _currentLocale = const Locale('ar'); // Default to Arabic
  bool _isRTL = true; // Default to RTL for Arabic
  
  Locale get currentLocale => _currentLocale;
  bool get isRTL => _isRTL;
  bool get isArabic => _currentLocale.languageCode == 'ar';
  bool get isEnglish => _currentLocale.languageCode == 'en';
  
  static final LanguageService _instance = LanguageService._internal();
  factory LanguageService() => _instance;
  LanguageService._internal();

  /// Initialize language service
  Future<void> initialize() async {
    await _loadLanguagePreferences();
  }

  /// Load language preferences from storage
  Future<void> _loadLanguagePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final languageCode = prefs.getString(_languageKey) ?? 'ar';
      final rtlEnabled = prefs.getBool(_rtlKey) ?? true;
      
      _currentLocale = Locale(languageCode);
      _isRTL = rtlEnabled;
      
      debugPrint('Language loaded: $languageCode, RTL: $rtlEnabled');
    } catch (e) {
      debugPrint('Error loading language preferences: $e');
      // Use defaults
      _currentLocale = const Locale('ar');
      _isRTL = true;
    }
  }

  /// Change language to Arabic
  Future<void> setArabic() async {
    await _changeLanguage('ar', true);
  }

  /// Change language to English
  Future<void> setEnglish() async {
    await _changeLanguage('en', false);
  }

  /// Change language and RTL setting
  Future<void> _changeLanguage(String languageCode, bool rtl) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setString(_languageKey, languageCode);
      await prefs.setBool(_rtlKey, rtl);
      
      _currentLocale = Locale(languageCode);
      _isRTL = rtl;
      
      notifyListeners();
      
      debugPrint('Language changed to: $languageCode, RTL: $rtl');
    } catch (e) {
      debugPrint('Error changing language: $e');
    }
  }

  /// Toggle between Arabic and English
  Future<void> toggleLanguage() async {
    if (isArabic) {
      await setEnglish();
    } else {
      await setArabic();
    }
  }

  /// Get text direction based on current language
  TextDirection get textDirection => _isRTL ? TextDirection.rtl : TextDirection.ltr;

  /// Get alignment based on current language
  Alignment get startAlignment => _isRTL ? Alignment.centerRight : Alignment.centerLeft;
  Alignment get endAlignment => _isRTL ? Alignment.centerLeft : Alignment.centerRight;

  /// Get text align based on current language
  TextAlign get startTextAlign => _isRTL ? TextAlign.right : TextAlign.left;
  TextAlign get endTextAlign => _isRTL ? TextAlign.left : TextAlign.right;

  /// Get edge insets based on current language
  EdgeInsets getDirectionalPadding({
    double start = 0,
    double top = 0,
    double end = 0,
    double bottom = 0,
  }) {
    if (_isRTL) {
      return EdgeInsets.only(
        left: end,
        top: top,
        right: start,
        bottom: bottom,
      );
    } else {
      return EdgeInsets.only(
        left: start,
        top: top,
        right: end,
        bottom: bottom,
      );
    }
  }

  /// Get margin based on current language
  EdgeInsets getDirectionalMargin({
    double start = 0,
    double top = 0,
    double end = 0,
    double bottom = 0,
  }) {
    return getDirectionalPadding(
      start: start,
      top: top,
      end: end,
      bottom: bottom,
    );
  }

  /// Get icon based on direction (for back/forward buttons)
  IconData getDirectionalIcon({
    required IconData ltrIcon,
    required IconData rtlIcon,
  }) {
    return _isRTL ? rtlIcon : ltrIcon;
  }

  /// Get back icon based on current direction
  IconData get backIcon => _isRTL ? Icons.arrow_forward : Icons.arrow_back;

  /// Get forward icon based on current direction
  IconData get forwardIcon => _isRTL ? Icons.arrow_back : Icons.arrow_forward;

  /// Format number based on current language
  String formatNumber(int number) {
    if (isArabic) {
      // Convert to Arabic-Indic numerals
      return number.toString().replaceAllMapped(
        RegExp(r'[0-9]'),
        (match) {
          const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
          return arabicNumerals[int.parse(match.group(0)!)];
        },
      );
    }
    return number.toString();
  }

  /// Format time based on current language
  String formatTime(TimeOfDay time) {
    final hour = time.hour;
    final minute = time.minute;
    
    if (isArabic) {
      final arabicHour = formatNumber(hour);
      final arabicMinute = formatNumber(minute);
      return '$arabicHour:$arabicMinute';
    }
    
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  /// Get supported locales
  static List<Locale> get supportedLocales => const [
    Locale('ar'), // Arabic
    Locale('en'), // English
  ];

  /// Get language name for display
  String getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'ar':
        return isArabic ? 'العربية' : 'Arabic';
      case 'en':
        return isArabic ? 'الإنجليزية' : 'English';
      default:
        return languageCode;
    }
  }

  /// Get current language display name
  String get currentLanguageName => getLanguageName(_currentLocale.languageCode);

  /// Check if a string contains Arabic characters
  static bool containsArabic(String text) {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  /// Get appropriate font family for current language
  String? get fontFamily {
    if (isArabic) {
      return 'NotoSansArabic'; // You can add custom Arabic fonts
    }
    return null; // Use default font for English
  }

  /// Get text style with appropriate font for current language
  TextStyle getTextStyle(TextStyle baseStyle) {
    return baseStyle.copyWith(
      fontFamily: fontFamily,
    );
  }

  /// Reset to default language (Arabic)
  Future<void> resetToDefault() async {
    await setArabic();
  }
}
