import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../services/permissions_manager.dart';
import '../../services/language_service.dart';
import '../../theme/app_theme.dart';

class PermissionCardWidget extends StatefulWidget {
  final Map<String, dynamic> permission;
  final LanguageService languageService;
  final VoidCallback onPermissionGranted;
  final VoidCallback onSkip;

  const PermissionCardWidget({
    super.key,
    required this.permission,
    required this.languageService,
    required this.onPermissionGranted,
    required this.onSkip,
  });

  @override
  State<PermissionCardWidget> createState() => _PermissionCardWidgetState();
}

class _PermissionCardWidgetState extends State<PermissionCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  
  bool _isRequesting = false;
  PermissionStatus? _currentStatus;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _currentStatus = widget.permission['status'] as PermissionStatus;
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Permission icon and status
            _buildPermissionIcon(),
            
            const SizedBox(height: 32),
            
            // Permission title
            Text(
              widget.permission['title'] as String,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryGreen,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 16),
            
            // Permission description
            Text(
              widget.permission['description'] as String,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            // Benefits section
            _buildBenefitsSection(),
            
            const SizedBox(height: 32),
            
            // Action buttons
            _buildActionButtons(),
            
            const SizedBox(height: 16),
            
            // Status indicator
            _buildStatusIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionIcon() {
    final isGranted = _currentStatus?.isGranted ?? false;
    final icon = widget.permission['icon'] as IconData;
    
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isGranted 
            ? Colors.green.withValues(alpha: 0.1)
            : AppTheme.primaryGreen.withValues(alpha: 0.1),
        border: Border.all(
          color: isGranted ? Colors.green : AppTheme.primaryGreen,
          width: 3,
        ),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Icon(
            icon,
            size: 40,
            color: isGranted ? Colors.green : AppTheme.primaryGreen,
          ),
          if (isGranted)
            Positioned(
              bottom: 8,
              right: 8,
              child: Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBenefitsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.primaryGreen.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.primaryGreen.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.star,
                color: AppTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                widget.languageService.isArabic ? 'الفوائد' : 'Benefits',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryGreen,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildBenefitItem(
            Icons.check_circle_outline,
            _getBenefitText(),
          ),
          _buildBenefitItem(
            Icons.security,
            widget.languageService.isArabic 
                ? 'آمن ومحمي بالكامل'
                : 'Completely safe and secure',
          ),
          _buildBenefitItem(
            Icons.speed,
            widget.languageService.isArabic 
                ? 'تحسين أداء التطبيق'
                : 'Improved app performance',
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: AppTheme.primaryGreen,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getBenefitText() {
    final permission = widget.permission['permission'] as Permission;
    
    if (widget.languageService.isArabic) {
      switch (permission) {
        case Permission.notification:
          return 'تذكيرات في الوقت المناسب للأذكار والصلاة';
        case Permission.location:
          return 'تحديد دقيق لاتجاه القبلة وأوقات الصلاة';
        case Permission.storage:
          return 'استخدام التطبيق بدون إنترنت';
        case Permission.systemAlertWindow:
          return 'عد الأذكار أثناء استخدام تطبيقات أخرى';
        default:
          return 'تحسين تجربة استخدام التطبيق';
      }
    } else {
      switch (permission) {
        case Permission.notification:
          return 'Timely reminders for Athkar and prayers';
        case Permission.location:
          return 'Accurate Qibla direction and prayer times';
        case Permission.storage:
          return 'Use the app without internet connection';
        case Permission.systemAlertWindow:
          return 'Count Athkar while using other apps';
        default:
          return 'Enhanced app experience';
      }
    }
  }

  Widget _buildActionButtons() {
    final isGranted = _currentStatus?.isGranted ?? false;
    
    if (isGranted) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: Colors.green.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              widget.languageService.isArabic ? 'تم التفعيل' : 'Permission Granted',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
      );
    }
    
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isRequesting ? null : _requestPermission,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: _isRequesting
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    widget.languageService.isArabic ? 'السماح' : 'Allow Permission',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
        const SizedBox(height: 12),
        TextButton(
          onPressed: _isRequesting ? null : widget.onSkip,
          child: Text(
            widget.languageService.isArabic ? 'تخطي الآن' : 'Skip for Now',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusIndicator() {
    if (_currentStatus == null) return const SizedBox();
    
    Color statusColor;
    String statusText;
    IconData statusIcon;
    
    if (_currentStatus!.isGranted) {
      statusColor = Colors.green;
      statusText = widget.languageService.isArabic ? 'مفعل' : 'Granted';
      statusIcon = Icons.check_circle;
    } else if (_currentStatus!.isDenied) {
      statusColor = Colors.orange;
      statusText = widget.languageService.isArabic ? 'مرفوض' : 'Denied';
      statusIcon = Icons.cancel;
    } else if (_currentStatus!.isPermanentlyDenied) {
      statusColor = Colors.red;
      statusText = widget.languageService.isArabic ? 'مرفوض نهائياً' : 'Permanently Denied';
      statusIcon = Icons.block;
    } else {
      statusColor = Colors.grey;
      statusText = widget.languageService.isArabic ? 'غير محدد' : 'Not Determined';
      statusIcon = Icons.help_outline;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            statusIcon,
            size: 16,
            color: statusColor,
          ),
          const SizedBox(width: 6),
          Text(
            statusText,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: statusColor,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _requestPermission() async {
    if (_isRequesting) return;
    
    setState(() {
      _isRequesting = true;
    });
    
    try {
      final permission = widget.permission['permission'] as Permission;
      final manager = PermissionsManager();
      final newStatus = await manager.requestPermission(permission);
      
      if (mounted) {
        setState(() {
          _currentStatus = newStatus;
          _isRequesting = false;
        });
        
        if (newStatus.isGranted) {
          // Show success animation
          _showSuccessAnimation();
          widget.onPermissionGranted();
        } else if (newStatus.isPermanentlyDenied) {
          _showPermanentlyDeniedDialog();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isRequesting = false;
        });
        _showErrorDialog();
      }
    }
  }

  void _showSuccessAnimation() {
    // Add a success animation or feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          widget.languageService.isArabic 
              ? 'تم تفعيل الصلاحية بنجاح!'
              : 'Permission granted successfully!',
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showPermanentlyDeniedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          widget.languageService.isArabic ? 'صلاحية مطلوبة' : 'Permission Required',
        ),
        content: Text(
          widget.languageService.isArabic
              ? 'تم رفض هذه الصلاحية نهائياً. يرجى الذهاب إلى الإعدادات لتفعيلها يدوياً.'
              : 'This permission has been permanently denied. Please go to Settings to enable it manually.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(widget.languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              foregroundColor: Colors.white,
            ),
            child: Text(
              widget.languageService.isArabic ? 'فتح الإعدادات' : 'Open Settings',
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          widget.languageService.isArabic ? 'خطأ' : 'Error',
        ),
        content: Text(
          widget.languageService.isArabic
              ? 'حدث خطأ أثناء طلب الصلاحية. يرجى المحاولة مرة أخرى.'
              : 'An error occurred while requesting permission. Please try again.',
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              foregroundColor: Colors.white,
            ),
            child: Text(widget.languageService.isArabic ? 'حسناً' : 'OK'),
          ),
        ],
      ),
    );
  }
}
