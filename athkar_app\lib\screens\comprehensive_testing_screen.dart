import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/language_service.dart';
import '../theme/app_theme.dart';
import '../widgets/testing/notification_testing_widget.dart';
// import '../widgets/testing/floating_overlay_testing_widget.dart'; // Temporarily disabled
import '../widgets/testing/audio_system_testing_widget.dart';
import '../widgets/testing/widget_functionality_testing_widget.dart';
import '../widgets/testing/sync_testing_widget.dart';
import '../widgets/testing/permission_testing_widget.dart';
import '../widgets/testing/background_service_testing_widget.dart';
import '../widgets/testing/database_testing_widget.dart';

class ComprehensiveTestingScreen extends StatefulWidget {
  const ComprehensiveTestingScreen({super.key});

  @override
  State<ComprehensiveTestingScreen> createState() => _ComprehensiveTestingScreenState();
}

class _ComprehensiveTestingScreenState extends State<ComprehensiveTestingScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  final Map<String, TestingStatus> _testingResults = {};
  int _completedTests = 0;
  int _totalTests = 8;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 8, vsync: this);
    _initializeTestingResults();
  }

  void _initializeTestingResults() {
    _testingResults.addAll({
      'notifications': TestingStatus.notStarted,
      'floating_overlay': TestingStatus.notStarted,
      'audio_system': TestingStatus.notStarted,
      'widget_functionality': TestingStatus.notStarted,
      'sync_testing': TestingStatus.notStarted,
      'permissions': TestingStatus.notStarted,
      'background_services': TestingStatus.notStarted,
      'database_operations': TestingStatus.notStarted,
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _updateTestingStatus(String testKey, TestingStatus status) {
    setState(() {
      final oldStatus = _testingResults[testKey];
      _testingResults[testKey] = status;
      
      // Update completed tests count
      if (oldStatus != TestingStatus.passed && status == TestingStatus.passed) {
        _completedTests++;
      } else if (oldStatus == TestingStatus.passed && status != TestingStatus.passed) {
        _completedTests--;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          languageService.isArabic ? 'اختبار شامل للنظام' : 'Comprehensive System Testing',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryGreen,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetAllTests,
            tooltip: languageService.isArabic ? 'إعادة تعيين جميع الاختبارات' : 'Reset All Tests',
          ),
          IconButton(
            icon: const Icon(Icons.play_arrow),
            onPressed: _runAllTests,
            tooltip: languageService.isArabic ? 'تشغيل جميع الاختبارات' : 'Run All Tests',
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(120),
          child: Column(
            children: [
              // Progress indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          languageService.isArabic ? 'التقدم الإجمالي' : 'Overall Progress',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '$_completedTests / $_totalTests',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: _completedTests / _totalTests,
                      backgroundColor: Colors.white.withValues(alpha: 0.3),
                      valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ],
                ),
              ),
              
              // Tab bar
              TabBar(
                controller: _tabController,
                isScrollable: true,
                indicatorColor: Colors.white,
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white70,
                labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                tabs: [
                  _buildTab(
                    languageService.isArabic ? 'الإشعارات' : 'Notifications',
                    Icons.notifications,
                    'notifications',
                  ),
                  _buildTab(
                    languageService.isArabic ? 'العائم' : 'Overlay',
                    Icons.picture_in_picture,
                    'floating_overlay',
                  ),
                  _buildTab(
                    languageService.isArabic ? 'الصوت' : 'Audio',
                    Icons.volume_up,
                    'audio_system',
                  ),
                  _buildTab(
                    languageService.isArabic ? 'الودجت' : 'Widgets',
                    Icons.widgets,
                    'widget_functionality',
                  ),
                  _buildTab(
                    languageService.isArabic ? 'المزامنة' : 'Sync',
                    Icons.sync,
                    'sync_testing',
                  ),
                  _buildTab(
                    languageService.isArabic ? 'الصلاحيات' : 'Permissions',
                    Icons.security,
                    'permissions',
                  ),
                  _buildTab(
                    languageService.isArabic ? 'الخدمات' : 'Services',
                    Icons.settings,
                    'background_services',
                  ),
                  _buildTab(
                    languageService.isArabic ? 'قاعدة البيانات' : 'Database',
                    Icons.storage,
                    'database_operations',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          NotificationTestingWidget(
            onStatusChanged: (status) => _updateTestingStatus('notifications', status),
          ),
          // FloatingOverlayTestingWidget temporarily disabled
          Container(
            padding: const EdgeInsets.all(16),
            child: const Text('Floating Overlay Testing - Temporarily Disabled'),
          ),
          AudioSystemTestingWidget(
            onStatusChanged: (status) => _updateTestingStatus('audio_system', status),
          ),
          WidgetFunctionalityTestingWidget(
            onStatusChanged: (status) => _updateTestingStatus('widget_functionality', status),
          ),
          SyncTestingWidget(
            onStatusChanged: (status) => _updateTestingStatus('sync_testing', status),
          ),
          PermissionTestingWidget(
            onStatusChanged: (status) => _updateTestingStatus('permissions', status),
          ),
          BackgroundServiceTestingWidget(
            onStatusChanged: (status) => _updateTestingStatus('background_services', status),
          ),
          DatabaseTestingWidget(
            onStatusChanged: (status) => _updateTestingStatus('database_operations', status),
          ),
        ],
      ),
      floatingActionButton: _completedTests == _totalTests
          ? FloatingActionButton.extended(
              onPressed: _generateTestReport,
              backgroundColor: Colors.green,
              icon: const Icon(Icons.check_circle, color: Colors.white),
              label: Text(
                languageService.isArabic ? 'تقرير النتائج' : 'Test Report',
                style: const TextStyle(color: Colors.white),
              ),
            )
          : null,
    );
  }

  Widget _buildTab(String label, IconData icon, String testKey) {
    final status = _testingResults[testKey] ?? TestingStatus.notStarted;
    Color statusColor;
    
    switch (status) {
      case TestingStatus.passed:
        statusColor = Colors.green;
        break;
      case TestingStatus.failed:
        statusColor = Colors.red;
        break;
      case TestingStatus.inProgress:
        statusColor = Colors.orange;
        break;
      case TestingStatus.notStarted:
        statusColor = Colors.white70;
        break;
    }

    return Tab(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Icon(icon, size: 20),
              if (status != TestingStatus.notStarted)
                Positioned(
                  right: -2,
                  top: -2,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: statusColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: TextStyle(fontSize: 10),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _resetAllTests() {
    setState(() {
      _initializeTestingResults();
      _completedTests = 0;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          Provider.of<LanguageService>(context, listen: false).isArabic
              ? 'تم إعادة تعيين جميع الاختبارات'
              : 'All tests have been reset',
        ),
        backgroundColor: AppTheme.primaryGreen,
      ),
    );
  }

  void _runAllTests() {
    // This will trigger all testing widgets to start their tests
    // Implementation will be handled by individual testing widgets
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          Provider.of<LanguageService>(context, listen: false).isArabic
              ? 'بدء تشغيل جميع الاختبارات...'
              : 'Starting all tests...',
        ),
        backgroundColor: AppTheme.primaryGreen,
      ),
    );
  }

  void _generateTestReport() {
    final languageService = Provider.of<LanguageService>(context, listen: false);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          languageService.isArabic ? 'تقرير الاختبار الشامل' : 'Comprehensive Test Report',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Overall status
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.green, size: 32),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageService.isArabic ? 'جميع الاختبارات مكتملة' : 'All Tests Completed',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          Text(
                            languageService.isArabic 
                                ? 'النظام جاهز للإنتاج'
                                : 'System ready for production',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Test results summary
              ...(_testingResults.entries.map((entry) {
                return ListTile(
                  leading: Icon(
                    entry.value == TestingStatus.passed ? Icons.check_circle : Icons.error,
                    color: entry.value == TestingStatus.passed ? Colors.green : Colors.red,
                  ),
                  title: Text(_getTestDisplayName(entry.key, languageService.isArabic)),
                  trailing: Text(
                    _getStatusText(entry.value, languageService.isArabic),
                    style: TextStyle(
                      color: entry.value == TestingStatus.passed ? Colors.green : Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList()),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إغلاق' : 'Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Export report functionality can be added here
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              foregroundColor: Colors.white,
            ),
            child: Text(languageService.isArabic ? 'تصدير التقرير' : 'Export Report'),
          ),
        ],
      ),
    );
  }

  String _getTestDisplayName(String testKey, bool isArabic) {
    final names = {
      'notifications': isArabic ? 'نظام الإشعارات' : 'Notification System',
      'floating_overlay': isArabic ? 'النوافذ العائمة' : 'Floating Overlay',
      'audio_system': isArabic ? 'النظام الصوتي' : 'Audio System',
      'widget_functionality': isArabic ? 'وظائف الودجت' : 'Widget Functionality',
      'sync_testing': isArabic ? 'المزامنة عبر الأجهزة' : 'Cross-Device Sync',
      'permissions': isArabic ? 'نظام الصلاحيات' : 'Permission System',
      'background_services': isArabic ? 'الخدمات الخلفية' : 'Background Services',
      'database_operations': isArabic ? 'عمليات قاعدة البيانات' : 'Database Operations',
    };
    return names[testKey] ?? testKey;
  }

  String _getStatusText(TestingStatus status, bool isArabic) {
    switch (status) {
      case TestingStatus.passed:
        return isArabic ? 'نجح' : 'Passed';
      case TestingStatus.failed:
        return isArabic ? 'فشل' : 'Failed';
      case TestingStatus.inProgress:
        return isArabic ? 'قيد التشغيل' : 'In Progress';
      case TestingStatus.notStarted:
        return isArabic ? 'لم يبدأ' : 'Not Started';
    }
  }
}

enum TestingStatus {
  notStarted,
  inProgress,
  passed,
  failed,
}
