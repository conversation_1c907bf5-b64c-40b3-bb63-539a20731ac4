import 'package:supabase_flutter/supabase_flutter.dart';
import 'app_config.dart';

class SupabaseConfig {
  // Configuration values from AppConfig
  static String get supabaseUrl => AppConfig.supabaseUrl;
  static String get supabaseAnonKey => AppConfig.supabaseAnonKey;
  
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      debug: true, // Set to false in production
    );
  }
  
  static SupabaseClient get client => Supabase.instance.client;
  static GoTrueClient get auth => Supabase.instance.client.auth;

  // Database tables
  static const String userProfilesTable = 'user_profiles';
  static const String athkarRoutinesTable = 'athkar_routines';
  static const String athkarStepsTable = 'athkar_steps';
  static const String userProgressTable = 'user_progress';
  static const String bookmarksTable = 'bookmarks';
  static const String settingsTable = 'user_settings';
  static const String prebuiltContentTable = 'prebuilt_content';
  static const String prayerTimesTable = 'prayer_times_jordan';
  static const String userPreferencesTable = 'user_preferences';
}

// Supabase Database Schema
/*
-- Users table (handled by Supabase Auth)
-- Additional user profile information
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username TEXT UNIQUE,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Athkar Categories
CREATE TABLE athkar_categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  color TEXT,
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Athkar Routines
CREATE TABLE athkar_routines (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  category_id UUID REFERENCES athkar_categories(id),
  title TEXT NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT FALSE,
  is_favorite BOOLEAN DEFAULT FALSE,
  total_steps INTEGER DEFAULT 0,
  estimated_duration INTEGER, -- in minutes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Athkar Steps (individual dhikr within a routine)
CREATE TABLE athkar_steps (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  routine_id UUID REFERENCES athkar_routines(id) ON DELETE CASCADE,
  step_order INTEGER NOT NULL,
  arabic_text TEXT NOT NULL,
  transliteration TEXT,
  translation TEXT,
  target_count INTEGER DEFAULT 1,
  audio_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Progress Tracking
CREATE TABLE user_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  routine_id UUID REFERENCES athkar_routines(id),
  step_id UUID REFERENCES athkar_steps(id),
  current_count INTEGER DEFAULT 0,
  completed_at TIMESTAMP WITH TIME ZONE,
  session_date DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reminder Settings
CREATE TABLE reminder_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  routine_id UUID REFERENCES athkar_routines(id),
  is_enabled BOOLEAN DEFAULT TRUE,
  reminder_times TIME[],
  days_of_week INTEGER[], -- 0=Sunday, 1=Monday, etc.
  notification_title TEXT,
  notification_body TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Row Level Security (RLS) Policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE athkar_routines ENABLE ROW LEVEL SECURITY;
ALTER TABLE athkar_steps ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE reminder_settings ENABLE ROW LEVEL SECURITY;

-- Policies for user_profiles
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Policies for athkar_routines
CREATE POLICY "Users can view own routines" ON athkar_routines FOR SELECT USING (auth.uid() = user_id OR is_public = TRUE);
CREATE POLICY "Users can insert own routines" ON athkar_routines FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own routines" ON athkar_routines FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own routines" ON athkar_routines FOR DELETE USING (auth.uid() = user_id);

-- Policies for athkar_steps
CREATE POLICY "Users can view steps of accessible routines" ON athkar_steps FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM athkar_routines 
    WHERE athkar_routines.id = athkar_steps.routine_id 
    AND (athkar_routines.user_id = auth.uid() OR athkar_routines.is_public = TRUE)
  )
);
CREATE POLICY "Users can manage steps of own routines" ON athkar_steps FOR ALL USING (
  EXISTS (
    SELECT 1 FROM athkar_routines 
    WHERE athkar_routines.id = athkar_steps.routine_id 
    AND athkar_routines.user_id = auth.uid()
  )
);

-- Policies for user_progress
CREATE POLICY "Users can manage own progress" ON user_progress FOR ALL USING (auth.uid() = user_id);

-- Policies for reminder_settings
CREATE POLICY "Users can manage own reminders" ON reminder_settings FOR ALL USING (auth.uid() = user_id);

-- Insert default categories
INSERT INTO athkar_categories (name, description, icon, color, is_default) VALUES
('Morning Athkar', 'Supplications to be recited in the morning', 'sunrise', '#FFB74D', TRUE),
('Evening Athkar', 'Supplications to be recited in the evening', 'sunset', '#FF8A65', TRUE),
('Prayer Athkar', 'Supplications after the five daily prayers', 'prayer', '#81C784', TRUE),
('Sleep Athkar', 'Supplications before going to sleep', 'bedtime', '#9575CD', TRUE),
('General Dhikr', 'General remembrance and praise of Allah', 'dhikr', '#64B5F6', TRUE),
('Dua Collection', 'Various supplications for different occasions', 'dua', '#F06292', TRUE);
*/
