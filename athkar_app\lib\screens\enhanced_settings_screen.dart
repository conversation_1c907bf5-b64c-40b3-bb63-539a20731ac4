import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/language_service.dart';
import '../theme/app_theme.dart';
import '../widgets/settings_tabs/general_settings_tab.dart';
import '../widgets/settings_tabs/prayer_settings_tab.dart';
import '../widgets/settings_tabs/quran_settings_tab.dart';
import '../widgets/settings_tabs/hadith_settings_tab.dart';
import '../widgets/settings_tabs/advanced_settings_tab.dart';

class EnhancedSettingsScreen extends StatefulWidget {
  const EnhancedSettingsScreen({super.key});

  @override
  State<EnhancedSettingsScreen> createState() => _EnhancedSettingsScreenState();
}

class _EnhancedSettingsScreenState extends State<EnhancedSettingsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(languageService.isArabic ? 'الإعدادات' : 'Settings'),
          backgroundColor: AppTheme.primaryGreen,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: Icon(languageService.backIcon),
            onPressed: () => Navigator.pop(context),
          ),
          bottom: TabBar(
            controller: _tabController,
            isScrollable: true,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            labelStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w400,
            ),
            tabs: [
              Tab(
                icon: const Icon(Icons.settings, size: 20),
                text: languageService.isArabic ? 'عام' : 'General',
              ),
              Tab(
                icon: const Icon(Icons.access_time, size: 20),
                text: languageService.isArabic ? 'الصلاة' : 'Prayer',
              ),
              Tab(
                icon: const Icon(Icons.menu_book, size: 20),
                text: languageService.isArabic ? 'القرآن' : 'Quran',
              ),
              Tab(
                icon: const Icon(Icons.article, size: 20),
                text: languageService.isArabic ? 'الحديث' : 'Hadith',
              ),
              Tab(
                icon: const Icon(Icons.tune, size: 20),
                text: languageService.isArabic ? 'متقدم' : 'Advanced',
              ),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: const [
            GeneralSettingsTab(),
            PrayerSettingsTab(),
            QuranSettingsTab(),
            HadithSettingsTab(),
            AdvancedSettingsTab(),
          ],
        ),
      ),
    );
  }
}
