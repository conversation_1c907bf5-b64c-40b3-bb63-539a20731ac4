1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.islamicapps.athkar.athkar_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- Permissions for notifications -->
11    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
11-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:3:5-80
11-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:3:22-78
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:4:5-66
12-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:4:22-63
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:5:5-68
13-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:5:22-65
14    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
14-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:6:5-76
14-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:6:22-74
15
16    <!-- Permissions for floating window overlay -->
17    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
17-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:9:5-78
17-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:9:22-75
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:10:5-77
18-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:10:22-74
19
20    <!-- Internet permission for Supabase -->
21    <uses-permission android:name="android.permission.INTERNET" />
21-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:13:5-67
21-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:13:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:14:5-79
22-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:14:22-76
23    <!--
24         Required to query activities that can process text, see:
25         https://developer.android.com/training/package-visibility and
26         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
27
28         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
29    -->
30    <queries>
30-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:73:5-78:15
31        <intent>
31-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:74:9-77:18
32            <action android:name="android.intent.action.PROCESS_TEXT" />
32-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:75:13-72
32-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:75:21-70
33
34            <data android:mimeType="text/plain" />
34-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:76:13-50
34-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:76:19-48
35        </intent>
36        <intent>
36-->[:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
37            <action android:name="android.intent.action.GET_CONTENT" />
37-->[:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
37-->[:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-69
38
39            <data android:mimeType="*/*" />
39-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:76:13-50
39-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:76:19-48
40        </intent>
41        <intent>
41-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:13:9-15:18
42            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
42-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:13-91
42-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:21-88
43        </intent>
44        <intent>
44-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:16:9-18:18
45            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
45-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:13-116
45-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:21-113
46        </intent>
47    </queries>
48
49    <uses-feature android:name="android.hardware.camera.any" />
49-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-64
49-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:19-61
50
51    <uses-permission android:name="android.permission.CAMERA" />
51-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-65
51-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-62
52    <uses-permission android:name="android.permission.RECORD_AUDIO" />
52-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-71
52-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:22-68
53    <uses-permission
53-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-13:38
54        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
54-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-65
55        android:maxSdkVersion="28" />
55-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-35
56    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
57    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
57-->[:network_info_plus] D:\projects\12july\athkar\athkar_app\build\network_info_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-76
57-->[:network_info_plus] D:\projects\12july\athkar\athkar_app\build\network_info_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-73
58    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
58-->[:local_auth_android] D:\projects\12july\athkar\athkar_app\build\local_auth_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-72
58-->[:local_auth_android] D:\projects\12july\athkar\athkar_app\build\local_auth_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-69
59    <uses-permission android:name="com.android.vending.BILLING" /> <!-- suppress DeprecatedClassUsageInspection -->
59-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:5-67
59-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:22-64
60    <uses-permission android:name="android.permission.USE_FINGERPRINT" /> <!-- Required by older versions of Google Play services to create IID tokens -->
60-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
60-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
61    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
61-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
61-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
62    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
62-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
62-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
63    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
63-->[com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:27:5-79
63-->[com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:27:22-76
64    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
64-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
64-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
65    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
65-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
65-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
66
67    <permission
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
68        android:name="com.islamicapps.athkar.athkar_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
68-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
69        android:protectionLevel="signature" />
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
70
71    <uses-permission android:name="com.islamicapps.athkar.athkar_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
72
73    <application
74        android:name="android.app.Application"
74-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:18:9-42
75        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
75-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
76        android:extractNativeLibs="true"
77        android:icon="@mipmap/ic_launcher"
77-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:19:9-43
78        android:label="Athkar - Islamic Remembrance" >
78-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:17:9-53
79        <activity
79-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:20:9-41:20
80            android:name="com.islamicapps.athkar.athkar_app.MainActivity"
80-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:21:13-41
81            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
81-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:26:13-163
82            android:exported="true"
82-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:22:13-36
83            android:hardwareAccelerated="true"
83-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:27:13-47
84            android:launchMode="singleTop"
84-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:23:13-43
85            android:taskAffinity=""
85-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:24:13-36
86            android:theme="@style/LaunchTheme"
86-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:25:13-47
87            android:windowSoftInputMode="adjustResize" >
87-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:28:13-55
88
89            <!--
90                 Specifies an Android theme to apply to this Activity as soon as
91                 the Android process has started. This theme is visible to the user
92                 while the Flutter UI initializes. After that, this theme continues
93                 to determine the Window background behind the Flutter UI.
94            -->
95            <meta-data
95-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:33:13-36:17
96                android:name="io.flutter.embedding.android.NormalTheme"
96-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:34:15-70
97                android:resource="@style/NormalTheme" />
97-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:35:15-52
98
99            <intent-filter>
99-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:37:13-40:29
100                <action android:name="android.intent.action.MAIN" />
100-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:38:17-68
100-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:38:25-66
101
102                <category android:name="android.intent.category.LAUNCHER" />
102-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:39:17-76
102-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:39:27-74
103            </intent-filter>
104        </activity>
105        <!--
106             Don't delete the meta-data below.
107             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
108        -->
109        <meta-data
109-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:44:9-46:33
110            android:name="flutterEmbedding"
110-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:45:13-44
111            android:value="2" />
111-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:46:13-30
112
113        <!-- Notification receiver for boot completed -->
114        <receiver
114-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:49:9-57:20
115            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
115-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:49:19-107
116            android:exported="false" >
116-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:50:19-43
117            <intent-filter>
117-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:51:13-56:29
118                <action android:name="android.intent.action.BOOT_COMPLETED" />
118-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:52:17-78
118-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:52:25-76
119                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
119-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:53:17-83
119-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:53:25-81
120                <action android:name="android.intent.action.PACKAGE_REPLACED" />
120-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:54:17-80
120-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:54:25-78
121
122                <data android:scheme="package" />
122-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:76:13-50
122-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:55:23-47
123            </intent-filter>
124        </receiver>
125
126        <!-- Notification receiver -->
127        <receiver
127-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:60:9-61:46
128            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver"
128-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:60:19-103
129            android:exported="false" />
129-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:61:19-43
130
131        <!-- Floating window service -->
132        <service
132-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:64:9-66:40
133            android:name="flutter.overlay.window.OverlayService"
133-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:65:13-65
134            android:exported="false" />
134-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:66:13-37
135        <service
135-->[:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-16:19
136            android:name="com.google.firebase.components.ComponentDiscoveryService"
136-->[:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:18-89
137            android:directBootAware="true"
137-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
138            android:exported="false" >
138-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:56:13-37
139            <meta-data
139-->[:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:85
140                android:name="com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar"
140-->[:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-128
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-82
142            <meta-data
142-->[:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
143                android:name="com.google.firebase.components:io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar"
143-->[:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-130
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
145            <meta-data
145-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-38:85
146                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
146-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:17-128
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-82
148            <meta-data
148-->[:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
149                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
149-->[:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
151            <meta-data
151-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
152                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
152-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
154            <meta-data
154-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
155                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
155-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
157            <meta-data
157-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
158                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
158-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
160            <meta-data
160-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:18:13-20:85
161                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
161-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:19:17-115
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:20:17-82
163            <meta-data
163-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
164                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
164-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
165                android:value="com.google.firebase.components.ComponentRegistrar" />
165-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
166            <meta-data
166-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
167                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
167-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
169            <meta-data
169-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
170                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
170-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
171                android:value="com.google.firebase.components.ComponentRegistrar" />
171-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
172            <meta-data
172-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
173                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
173-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
175            <meta-data
175-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
176                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
176-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
178            <meta-data
178-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
179                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
179-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
181            <meta-data
181-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
182                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
182-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
184        </service>
185        <!--
186           Declares a provider which allows us to store files to share in
187           '.../caches/share_plus' and grant the receiving action access
188        -->
189        <provider
189-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-21:20
190            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
190-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-77
191            android:authorities="com.islamicapps.athkar.athkar_app.flutter.share_provider"
191-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-74
192            android:exported="false"
192-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
193            android:grantUriPermissions="true" >
193-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-47
194            <meta-data
194-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:68
195                android:name="android.support.FILE_PROVIDER_PATHS"
195-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-67
196                android:resource="@xml/flutter_share_file_paths" />
196-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-65
197        </provider>
198        <!--
199           This manifest declared broadcast receiver allows us to use an explicit
200           Intent when creating a PendingItent to be informed of the user's choice
201        -->
202        <receiver
202-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-32:20
203            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
203-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-82
204            android:exported="false" >
204-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-37
205            <intent-filter>
205-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-31:29
206                <action android:name="EXTRA_CHOSEN_COMPONENT" />
206-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-65
206-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:25-62
207            </intent-filter>
208        </receiver>
209
210        <service
210-->[:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:56
211            android:name="com.baseflow.geolocator.GeolocatorLocationService"
211-->[:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-77
212            android:enabled="true"
212-->[:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-35
213            android:exported="false"
213-->[:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
214            android:foregroundServiceType="location" />
214-->[:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-53
215
216        <provider
216-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
217            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
217-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
218            android:authorities="com.islamicapps.athkar.athkar_app.flutter.image_provider"
218-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
219            android:exported="false"
219-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
220            android:grantUriPermissions="true" >
220-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
221            <meta-data
221-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:68
222                android:name="android.support.FILE_PROVIDER_PATHS"
222-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-67
223                android:resource="@xml/flutter_image_picker_file_paths" />
223-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-65
224        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
225        <service
225-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
226            android:name="com.google.android.gms.metadata.ModuleDependencies"
226-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
227            android:enabled="false"
227-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
228            android:exported="false" >
228-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
229            <intent-filter>
229-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
230                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
230-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
230-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
231            </intent-filter>
232
233            <meta-data
233-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
234                android:name="photopicker_activity:0:required"
234-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
235                android:value="" />
235-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
236        </service>
237
238        <activity
238-->[:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
239            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
239-->[:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
240            android:exported="false"
240-->[:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
241            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
241-->[:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
242
243        <service
243-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-17:72
244            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
244-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-107
245            android:exported="false"
245-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
246            android:permission="android.permission.BIND_JOB_SERVICE" />
246-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-69
247        <service
247-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-24:19
248            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
248-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-97
249            android:exported="false" >
249-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-37
250            <intent-filter>
250-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-23:29
251                <action android:name="com.google.firebase.MESSAGING_EVENT" />
251-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:17-78
251-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:25-75
252            </intent-filter>
253        </service>
254
255        <receiver
255-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-33:20
256            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
256-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-98
257            android:exported="true"
257-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-36
258            android:permission="com.google.android.c2dm.permission.SEND" >
258-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-73
259            <intent-filter>
259-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-32:29
260                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
260-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
260-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
261            </intent-filter>
262        </receiver>
263
264        <provider
264-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:9-45:38
265            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
265-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:13-102
266            android:authorities="com.islamicapps.athkar.athkar_app.flutterfirebasemessaginginitprovider"
266-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-88
267            android:exported="false"
267-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-37
268            android:initOrder="99" />
268-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-35
269
270        <service
270-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
271            android:name="androidx.camera.core.impl.MetadataHolderService"
271-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
272            android:enabled="false"
272-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
273            android:exported="false" >
273-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
274            <meta-data
274-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
275                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
275-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
276                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
276-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
277        </service>
278
279        <meta-data
279-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:22:9-24:37
280            android:name="com.google.android.play.billingclient.version"
280-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:23:13-73
281            android:value="7.1.1" />
281-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:24:13-34
282
283        <activity
283-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:26:9-30:75
284            android:name="com.android.billingclient.api.ProxyBillingActivity"
284-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:27:13-78
285            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
285-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:28:13-96
286            android:exported="false"
286-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:29:13-37
287            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
287-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:30:13-72
288        <activity
288-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:31:9-35:75
289            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
289-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:32:13-80
290            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
290-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:33:13-96
291            android:exported="false"
291-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:34:13-37
292            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
292-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:35:13-72
293        <activity
293-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
294            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
294-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
295            android:excludeFromRecents="true"
295-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
296            android:exported="false"
296-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
297            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
297-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
298        <!--
299            Service handling Google Sign-In user revocation. For apps that do not integrate with
300            Google Sign-In, this service will never be started.
301        -->
302        <service
302-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
303            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
303-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
304            android:exported="true"
304-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
305            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
305-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
306            android:visibleToInstantApps="true" />
306-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
307
308        <receiver
308-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
309            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
309-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
310            android:exported="true"
310-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
311            android:permission="com.google.android.c2dm.permission.SEND" >
311-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
312            <intent-filter>
312-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-32:29
313                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
313-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
313-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
314            </intent-filter>
315
316            <meta-data
316-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
317                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
317-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
318                android:value="true" />
318-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
319        </receiver>
320        <!--
321             FirebaseMessagingService performs security checks at runtime,
322             but set to not exported to explicitly avoid allowing another app to call it.
323        -->
324        <service
324-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
325            android:name="com.google.firebase.messaging.FirebaseMessagingService"
325-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
326            android:directBootAware="true"
326-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
327            android:exported="false" >
327-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
328            <intent-filter android:priority="-500" >
328-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-23:29
329                <action android:name="com.google.firebase.MESSAGING_EVENT" />
329-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:17-78
329-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:25-75
330            </intent-filter>
331        </service>
332
333        <receiver
333-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
334            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
334-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
335            android:enabled="true"
335-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
336            android:exported="false" >
336-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
337        </receiver>
338
339        <service
339-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
340            android:name="com.google.android.gms.measurement.AppMeasurementService"
340-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
341            android:enabled="true"
341-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
342            android:exported="false" />
342-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
343        <service
343-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
344            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
344-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
345            android:enabled="true"
345-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
346            android:exported="false"
346-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
347            android:permission="android.permission.BIND_JOB_SERVICE" />
347-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
348
349        <activity
349-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
350            android:name="com.google.android.gms.common.api.GoogleApiActivity"
350-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
351            android:exported="false"
351-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
352            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
352-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
353
354        <service
354-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
355            android:name="com.google.firebase.sessions.SessionLifecycleService"
355-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
356            android:enabled="true"
356-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
357            android:exported="false" />
357-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
358
359        <provider
359-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
360            android:name="com.google.firebase.provider.FirebaseInitProvider"
360-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
361            android:authorities="com.islamicapps.athkar.athkar_app.firebaseinitprovider"
361-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
362            android:directBootAware="true"
362-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
363            android:exported="false"
363-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
364            android:initOrder="100" />
364-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
365        <provider
365-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
366            android:name="androidx.startup.InitializationProvider"
366-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
367            android:authorities="com.islamicapps.athkar.athkar_app.androidx-startup"
367-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
368            android:exported="false" >
368-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
369            <meta-data
369-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
370                android:name="androidx.work.WorkManagerInitializer"
370-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
371                android:value="androidx.startup" />
371-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
372            <meta-data
372-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
373                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
373-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
374                android:value="androidx.startup" />
374-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
375            <meta-data
375-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
376                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
376-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
377                android:value="androidx.startup" />
377-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
378        </provider>
379
380        <service
380-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
381            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
381-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
382            android:directBootAware="false"
382-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
383            android:enabled="@bool/enable_system_alarm_service_default"
383-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
384            android:exported="false" />
384-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
385        <service
385-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
386            android:name="androidx.work.impl.background.systemjob.SystemJobService"
386-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
387            android:directBootAware="false"
387-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
388            android:enabled="@bool/enable_system_job_service_default"
388-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
389            android:exported="true"
389-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
390            android:permission="android.permission.BIND_JOB_SERVICE" />
390-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
391        <service
391-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
392            android:name="androidx.work.impl.foreground.SystemForegroundService"
392-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
393            android:directBootAware="false"
393-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
394            android:enabled="@bool/enable_system_foreground_service_default"
394-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
395            android:exported="false" />
395-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
396
397        <receiver
397-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
398            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
398-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
399            android:directBootAware="false"
399-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
400            android:enabled="true"
400-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
401            android:exported="false" />
401-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
402        <receiver
402-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
403            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
403-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
404            android:directBootAware="false"
404-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
405            android:enabled="false"
405-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
406            android:exported="false" >
406-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
407            <intent-filter>
407-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
408                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
408-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
408-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
409                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
409-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
409-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
410            </intent-filter>
411        </receiver>
412        <receiver
412-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
413            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
413-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
414            android:directBootAware="false"
414-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
415            android:enabled="false"
415-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
416            android:exported="false" >
416-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
417            <intent-filter>
417-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
418                <action android:name="android.intent.action.BATTERY_OKAY" />
418-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
418-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
419                <action android:name="android.intent.action.BATTERY_LOW" />
419-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
419-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
420            </intent-filter>
421        </receiver>
422        <receiver
422-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
423            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
423-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
424            android:directBootAware="false"
424-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
425            android:enabled="false"
425-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
426            android:exported="false" >
426-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
427            <intent-filter>
427-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
428                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
428-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
428-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
429                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
429-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
429-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
430            </intent-filter>
431        </receiver>
432        <receiver
432-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
433            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
433-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
434            android:directBootAware="false"
434-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
435            android:enabled="false"
435-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
436            android:exported="false" >
436-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
437            <intent-filter>
437-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
438                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
438-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
438-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
439            </intent-filter>
440        </receiver>
441        <receiver
441-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
442            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
442-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
443            android:directBootAware="false"
443-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
444            android:enabled="false"
444-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
445            android:exported="false" >
445-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
446            <intent-filter>
446-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
447                <action android:name="android.intent.action.BOOT_COMPLETED" />
447-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:52:17-78
447-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:52:25-76
448                <action android:name="android.intent.action.TIME_SET" />
448-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
448-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
449                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
449-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
449-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
450            </intent-filter>
451        </receiver>
452        <receiver
452-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
453            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
453-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
454            android:directBootAware="false"
454-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
455            android:enabled="@bool/enable_system_alarm_service_default"
455-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
456            android:exported="false" >
456-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
457            <intent-filter>
457-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
458                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
458-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
458-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
459            </intent-filter>
460        </receiver>
461        <receiver
461-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
462            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
462-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
463            android:directBootAware="false"
463-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
464            android:enabled="true"
464-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
465            android:exported="true"
465-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
466            android:permission="android.permission.DUMP" >
466-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
467            <intent-filter>
467-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
468                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
468-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
468-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
469            </intent-filter>
470        </receiver>
471
472        <uses-library
472-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
473            android:name="androidx.window.extensions"
473-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
474            android:required="false" />
474-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
475        <uses-library
475-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
476            android:name="androidx.window.sidecar"
476-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
477            android:required="false" />
477-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
478        <uses-library
478-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
479            android:name="android.ext.adservices"
479-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
480            android:required="false" />
480-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
481
482        <meta-data
482-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
483            android:name="com.google.android.gms.version"
483-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
484            android:value="@integer/google_play_services_version" />
484-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
485
486        <service
486-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
487            android:name="androidx.room.MultiInstanceInvalidationService"
487-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
488            android:directBootAware="true"
488-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
489            android:exported="false" />
489-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
490
491        <receiver
491-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
492            android:name="androidx.profileinstaller.ProfileInstallReceiver"
492-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
493            android:directBootAware="false"
493-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
494            android:enabled="true"
494-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
495            android:exported="true"
495-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
496            android:permission="android.permission.DUMP" >
496-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
497            <intent-filter>
497-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
498                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
498-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
498-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
499            </intent-filter>
500            <intent-filter>
500-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
501                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
501-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
501-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
502            </intent-filter>
503            <intent-filter>
503-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
504                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
504-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
504-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
505            </intent-filter>
506            <intent-filter>
506-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
507                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
507-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
507-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
508            </intent-filter>
509        </receiver>
510
511        <service
511-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
512            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
512-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
513            android:exported="false" >
513-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
514            <meta-data
514-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
515                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
515-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
516                android:value="cct" />
516-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
517        </service>
518        <service
518-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
519            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
519-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
520            android:exported="false"
520-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
521            android:permission="android.permission.BIND_JOB_SERVICE" >
521-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
522        </service>
523
524        <receiver
524-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
525            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
525-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
526            android:exported="false" />
526-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
527    </application>
528
529</manifest>
