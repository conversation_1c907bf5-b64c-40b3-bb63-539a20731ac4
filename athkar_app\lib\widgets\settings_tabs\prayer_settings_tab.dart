import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../services/language_service.dart';

import '../../theme/app_theme.dart';

class PrayerSettingsTab extends StatefulWidget {
  const PrayerSettingsTab({super.key});

  @override
  State<PrayerSettingsTab> createState() => _PrayerSettingsTabState();
}

class _PrayerSettingsTabState extends State<PrayerSettingsTab> {
  String _selectedCalculationMethod = 'muslim_world_league';
  String _selectedMadhab = 'hanafi';
  bool _enableAthan = true;
  bool _enableVibration = true;
  double _athanVolume = 0.8;
  String _selectedAthanSound = 'default';

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Calculation Method Section
          _buildSectionHeader(
            languageService.isArabic ? 'طريقة الحساب' : 'Calculation Method',
            Icons.calculate,
          ),
          const SizedBox(height: 12),
          
          Card(
            elevation: 2,
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.public, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'طريقة الحساب' : 'Calculation Method'),
                  subtitle: Text(_getCalculationMethodName(_selectedCalculationMethod, languageService)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showCalculationMethodDialog(context, languageService),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.school, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'المذهب الفقهي' : 'Juristic Method'),
                  subtitle: Text(_getMadhabName(_selectedMadhab, languageService)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showMadhabDialog(context, languageService),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Location Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات الموقع' : 'Location Settings',
            Icons.location_on,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.my_location, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'الموقع الحالي' : 'Current Location'),
                  subtitle: Text(languageService.isArabic ? 'عمان، الأردن' : 'Amman, Jordan'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showLocationDialog(context, languageService),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.gps_fixed, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'تحديد الموقع التلقائي' : 'Auto Location Detection'),
                  subtitle: Text(languageService.isArabic ? 'استخدام GPS لتحديد الموقع' : 'Use GPS for location detection'),
                  value: true,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('auto_location_enabled', value);

                    if (value) {
                      // Request location permission and start auto location
                      final hasPermission = await _requestLocationPermission();
                      if (hasPermission) {
                        await _enableAutoLocation();
                      }
                    } else {
                      await _disableAutoLocation();
                    }

                    setState(() {});
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Athan Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات الأذان' : 'Athan Settings',
            Icons.volume_up,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.notifications_active, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'تفعيل الأذان' : 'Enable Athan'),
                  subtitle: Text(languageService.isArabic ? 'تشغيل الأذان عند دخول الوقت' : 'Play athan at prayer time'),
                  value: _enableAthan,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _enableAthan = value;
                    });
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.music_note, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'صوت الأذان' : 'Athan Sound'),
                  subtitle: Text(_getAthanSoundName(_selectedAthanSound, languageService)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showAthanSoundDialog(context, languageService),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.volume_up, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'مستوى الصوت' : 'Volume Level'),
                  subtitle: Slider(
                    value: _athanVolume,
                    min: 0.0,
                    max: 1.0,
                    divisions: 10,
                    activeColor: AppTheme.primaryGreen,
                    onChanged: (value) {
                      setState(() {
                        _athanVolume = value;
                      });
                    },
                  ),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.vibration, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'الاهتزاز' : 'Vibration'),
                  subtitle: Text(languageService.isArabic ? 'اهتزاز عند الأذان' : 'Vibrate during athan'),
                  value: _enableVibration,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _enableVibration = value;
                    });
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Prayer Reminders Section
          _buildSectionHeader(
            languageService.isArabic ? 'تذكيرات الصلاة' : 'Prayer Reminders',
            Icons.alarm,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.alarm_add, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'تذكير قبل الصلاة' : 'Pre-Prayer Reminder'),
                  subtitle: Text(languageService.isArabic ? 'تذكير قبل 15 دقيقة' : 'Reminder 15 minutes before'),
                  value: true,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    await _togglePrePrayerReminder(value);
                    setState(() {});
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.schedule, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'تذكير بعد الصلاة' : 'Post-Prayer Reminder'),
                  subtitle: Text(languageService.isArabic ? 'تذكير بالأذكار بعد الصلاة' : 'Reminder for post-prayer athkar'),
                  value: true,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    await _togglePostPrayerReminder(value);
                    setState(() {});
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Qibla Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات القبلة' : 'Qibla Settings',
            Icons.explore,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.compass_calibration, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'معايرة البوصلة' : 'Compass Calibration'),
                  subtitle: Text(languageService.isArabic ? 'معايرة تلقائية للبوصلة' : 'Automatic compass calibration'),
                  value: true,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    await _toggleCompassCalibration(value);
                    setState(() {});
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.vibration, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'اهتزاز عند اتجاه القبلة' : 'Vibrate on Qibla Direction'),
                  subtitle: Text(languageService.isArabic ? 'اهتزاز عند الوصول لاتجاه القبلة' : 'Vibrate when facing Qibla'),
                  value: false,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    await _toggleQiblaVibration(value);
                    setState(() {});
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: AppTheme.primaryGreen, size: 24),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
      ],
    );
  }

  String _getCalculationMethodName(String method, LanguageService languageService) {
    switch (method) {
      case 'muslim_world_league':
        return languageService.isArabic ? 'رابطة العالم الإسلامي' : 'Muslim World League';
      case 'egyptian':
        return languageService.isArabic ? 'الهيئة المصرية العامة للمساحة' : 'Egyptian General Authority of Survey';
      case 'karachi':
        return languageService.isArabic ? 'جامعة العلوم الإسلامية، كراتشي' : 'University of Islamic Sciences, Karachi';
      case 'umm_al_qura':
        return languageService.isArabic ? 'أم القرى، مكة المكرمة' : 'Umm Al-Qura, Makkah';
      case 'dubai':
        return languageService.isArabic ? 'دبي' : 'Dubai';
      default:
        return languageService.isArabic ? 'رابطة العالم الإسلامي' : 'Muslim World League';
    }
  }

  String _getMadhabName(String madhab, LanguageService languageService) {
    switch (madhab) {
      case 'hanafi':
        return languageService.isArabic ? 'حنفي' : 'Hanafi';
      case 'shafi':
        return languageService.isArabic ? 'شافعي' : 'Shafi';
      case 'maliki':
        return languageService.isArabic ? 'مالكي' : 'Maliki';
      case 'hanbali':
        return languageService.isArabic ? 'حنبلي' : 'Hanbali';
      default:
        return languageService.isArabic ? 'حنفي' : 'Hanafi';
    }
  }

  String _getAthanSoundName(String sound, LanguageService languageService) {
    switch (sound) {
      case 'default':
        return languageService.isArabic ? 'الافتراضي' : 'Default';
      case 'makkah':
        return languageService.isArabic ? 'مكة المكرمة' : 'Makkah';
      case 'madinah':
        return languageService.isArabic ? 'المدينة المنورة' : 'Madinah';
      case 'al_aqsa':
        return languageService.isArabic ? 'المسجد الأقصى' : 'Al-Aqsa';
      default:
        return languageService.isArabic ? 'الافتراضي' : 'Default';
    }
  }

  void _showCalculationMethodDialog(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'اختر طريقة الحساب' : 'Choose Calculation Method'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildCalculationMethodOption('muslim_world_league', languageService),
            _buildCalculationMethodOption('egyptian', languageService),
            _buildCalculationMethodOption('karachi', languageService),
            _buildCalculationMethodOption('umm_al_qura', languageService),
            _buildCalculationMethodOption('dubai', languageService),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationMethodOption(String method, LanguageService languageService) {
    return RadioListTile<String>(
      title: Text(_getCalculationMethodName(method, languageService)),
      value: method,
      groupValue: _selectedCalculationMethod,
      activeColor: AppTheme.primaryGreen,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedCalculationMethod = value;
          });
          Navigator.pop(context);
        }
      },
    );
  }

  void _showMadhabDialog(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'اختر المذهب الفقهي' : 'Choose Juristic Method'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMadhabOption('hanafi', languageService),
            _buildMadhabOption('shafi', languageService),
            _buildMadhabOption('maliki', languageService),
            _buildMadhabOption('hanbali', languageService),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildMadhabOption(String madhab, LanguageService languageService) {
    return RadioListTile<String>(
      title: Text(_getMadhabName(madhab, languageService)),
      value: madhab,
      groupValue: _selectedMadhab,
      activeColor: AppTheme.primaryGreen,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedMadhab = value;
          });
          Navigator.pop(context);
        }
      },
    );
  }

  void _showLocationDialog(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'إعدادات الموقع' : 'Location Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: Text(languageService.isArabic ? 'تحديد الموقع تلقائياً' : 'Auto-detect Location'),
              value: true,
              onChanged: (value) {
                // Implement auto location toggle
              },
            ),
            ListTile(
              leading: const Icon(Icons.location_city),
              title: Text(languageService.isArabic ? 'المدينة الحالية' : 'Current City'),
              subtitle: Text(languageService.isArabic ? 'عمان، الأردن' : 'Amman, Jordan'),
              trailing: const Icon(Icons.edit),
              onTap: () {
                // Implement city selection
              },
            ),
            ListTile(
              leading: const Icon(Icons.map),
              title: Text(languageService.isArabic ? 'تحديد الموقع يدوياً' : 'Set Location Manually'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // Implement manual location setting
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }

  void _showAthanSoundDialog(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'اختر صوت الأذان' : 'Choose Athan Sound'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildAthanSoundOption('default', languageService),
            _buildAthanSoundOption('makkah', languageService),
            _buildAthanSoundOption('madinah', languageService),
            _buildAthanSoundOption('al_aqsa', languageService),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildAthanSoundOption(String sound, LanguageService languageService) {
    return RadioListTile<String>(
      title: Text(_getAthanSoundName(sound, languageService)),
      value: sound,
      groupValue: _selectedAthanSound,
      activeColor: AppTheme.primaryGreen,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedAthanSound = value;
          });
          Navigator.pop(context);
        }
      },
    );
  }

  Future<bool> _requestLocationPermission() async {
    final status = await Permission.location.request();
    return status == PermissionStatus.granted;
  }

  Future<void> _enableAutoLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('auto_location_enabled', true);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Auto location enabled successfully'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error enabling auto location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _disableAutoLocation() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('auto_location_enabled', false);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Auto location disabled'),
          backgroundColor: AppTheme.primaryGreen,
        ),
      );
    }
  }

  Future<void> _togglePrePrayerReminder(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('pre_prayer_reminder_enabled', value);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(value ? 'Pre-prayer reminders enabled' : 'Pre-prayer reminders disabled'),
          backgroundColor: AppTheme.primaryGreen,
        ),
      );
    }
  }

  Future<void> _togglePostPrayerReminder(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('post_prayer_reminder_enabled', value);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(value ? 'Post-prayer reminders enabled' : 'Post-prayer reminders disabled'),
          backgroundColor: AppTheme.primaryGreen,
        ),
      );
    }
  }

  Future<void> _toggleCompassCalibration(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('compass_calibration_enabled', value);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(value ? 'Compass calibration enabled' : 'Compass calibration disabled'),
          backgroundColor: AppTheme.primaryGreen,
        ),
      );
    }
  }

  Future<void> _toggleQiblaVibration(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('qibla_vibration_enabled', value);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(value ? 'Qibla vibration enabled' : 'Qibla vibration disabled'),
          backgroundColor: AppTheme.primaryGreen,
        ),
      );
    }
  }
}
