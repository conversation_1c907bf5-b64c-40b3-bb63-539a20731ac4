// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Media_Devices_1_H
#define WINRT_Windows_Media_Devices_1_H
#include "winrt/impl/Windows.Media.Devices.0.h"
WINRT_EXPORT namespace winrt::Windows::Media::Devices
{
    struct __declspec(empty_bases) IAdvancedPhotoCaptureSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedPhotoCaptureSettings>
    {
        IAdvancedPhotoCaptureSettings(std::nullptr_t = nullptr) noexcept {}
        IAdvancedPhotoCaptureSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdvancedPhotoControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedPhotoControl>
    {
        IAdvancedPhotoControl(std::nullptr_t = nullptr) noexcept {}
        IAdvancedPhotoControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdvancedVideoCaptureDeviceController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController>
    {
        IAdvancedVideoCaptureDeviceController(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdvancedVideoCaptureDeviceController10 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController10>
    {
        IAdvancedVideoCaptureDeviceController10(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController10(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdvancedVideoCaptureDeviceController11 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController11>
    {
        IAdvancedVideoCaptureDeviceController11(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController11(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdvancedVideoCaptureDeviceController2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController2>
    {
        IAdvancedVideoCaptureDeviceController2(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdvancedVideoCaptureDeviceController3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController3>
    {
        IAdvancedVideoCaptureDeviceController3(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdvancedVideoCaptureDeviceController4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController4>
    {
        IAdvancedVideoCaptureDeviceController4(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdvancedVideoCaptureDeviceController5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController5>
    {
        IAdvancedVideoCaptureDeviceController5(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdvancedVideoCaptureDeviceController6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController6>
    {
        IAdvancedVideoCaptureDeviceController6(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdvancedVideoCaptureDeviceController7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController7>
    {
        IAdvancedVideoCaptureDeviceController7(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdvancedVideoCaptureDeviceController8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController8>
    {
        IAdvancedVideoCaptureDeviceController8(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAdvancedVideoCaptureDeviceController9 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAdvancedVideoCaptureDeviceController9>
    {
        IAdvancedVideoCaptureDeviceController9(std::nullptr_t = nullptr) noexcept {}
        IAdvancedVideoCaptureDeviceController9(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAudioDeviceController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAudioDeviceController>,
        impl::require<winrt::Windows::Media::Devices::IAudioDeviceController, winrt::Windows::Media::Devices::IMediaDeviceController>
    {
        IAudioDeviceController(std::nullptr_t = nullptr) noexcept {}
        IAudioDeviceController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAudioDeviceController2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAudioDeviceController2>
    {
        IAudioDeviceController2(std::nullptr_t = nullptr) noexcept {}
        IAudioDeviceController2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAudioDeviceModule :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAudioDeviceModule>
    {
        IAudioDeviceModule(std::nullptr_t = nullptr) noexcept {}
        IAudioDeviceModule(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAudioDeviceModuleNotificationEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAudioDeviceModuleNotificationEventArgs>
    {
        IAudioDeviceModuleNotificationEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAudioDeviceModuleNotificationEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAudioDeviceModulesManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAudioDeviceModulesManager>
    {
        IAudioDeviceModulesManager(std::nullptr_t = nullptr) noexcept {}
        IAudioDeviceModulesManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAudioDeviceModulesManagerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAudioDeviceModulesManagerFactory>
    {
        IAudioDeviceModulesManagerFactory(std::nullptr_t = nullptr) noexcept {}
        IAudioDeviceModulesManagerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICameraOcclusionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICameraOcclusionInfo>
    {
        ICameraOcclusionInfo(std::nullptr_t = nullptr) noexcept {}
        ICameraOcclusionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICameraOcclusionState :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICameraOcclusionState>
    {
        ICameraOcclusionState(std::nullptr_t = nullptr) noexcept {}
        ICameraOcclusionState(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICameraOcclusionStateChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICameraOcclusionStateChangedEventArgs>
    {
        ICameraOcclusionStateChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICameraOcclusionStateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDefaultAudioDeviceChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDefaultAudioDeviceChangedEventArgs>
    {
        IDefaultAudioDeviceChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDefaultAudioDeviceChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDigitalWindowBounds :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDigitalWindowBounds>
    {
        IDigitalWindowBounds(std::nullptr_t = nullptr) noexcept {}
        IDigitalWindowBounds(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDigitalWindowCapability :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDigitalWindowCapability>
    {
        IDigitalWindowCapability(std::nullptr_t = nullptr) noexcept {}
        IDigitalWindowCapability(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDigitalWindowControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDigitalWindowControl>
    {
        IDigitalWindowControl(std::nullptr_t = nullptr) noexcept {}
        IDigitalWindowControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IExposureCompensationControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExposureCompensationControl>
    {
        IExposureCompensationControl(std::nullptr_t = nullptr) noexcept {}
        IExposureCompensationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IExposureControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExposureControl>
    {
        IExposureControl(std::nullptr_t = nullptr) noexcept {}
        IExposureControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IExposurePriorityVideoControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExposurePriorityVideoControl>
    {
        IExposurePriorityVideoControl(std::nullptr_t = nullptr) noexcept {}
        IExposurePriorityVideoControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlashControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlashControl>
    {
        IFlashControl(std::nullptr_t = nullptr) noexcept {}
        IFlashControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlashControl2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlashControl2>
    {
        IFlashControl2(std::nullptr_t = nullptr) noexcept {}
        IFlashControl2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFocusControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusControl>
    {
        IFocusControl(std::nullptr_t = nullptr) noexcept {}
        IFocusControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFocusControl2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusControl2>
    {
        IFocusControl2(std::nullptr_t = nullptr) noexcept {}
        IFocusControl2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFocusSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFocusSettings>
    {
        IFocusSettings(std::nullptr_t = nullptr) noexcept {}
        IFocusSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHdrVideoControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHdrVideoControl>
    {
        IHdrVideoControl(std::nullptr_t = nullptr) noexcept {}
        IHdrVideoControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInfraredTorchControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInfraredTorchControl>
    {
        IInfraredTorchControl(std::nullptr_t = nullptr) noexcept {}
        IInfraredTorchControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsoSpeedControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsoSpeedControl>
    {
        IIsoSpeedControl(std::nullptr_t = nullptr) noexcept {}
        IIsoSpeedControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIsoSpeedControl2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIsoSpeedControl2>
    {
        IIsoSpeedControl2(std::nullptr_t = nullptr) noexcept {}
        IIsoSpeedControl2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILowLagPhotoControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILowLagPhotoControl>
    {
        ILowLagPhotoControl(std::nullptr_t = nullptr) noexcept {}
        ILowLagPhotoControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILowLagPhotoSequenceControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILowLagPhotoSequenceControl>
    {
        ILowLagPhotoSequenceControl(std::nullptr_t = nullptr) noexcept {}
        ILowLagPhotoSequenceControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaDeviceControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaDeviceControl>
    {
        IMediaDeviceControl(std::nullptr_t = nullptr) noexcept {}
        IMediaDeviceControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaDeviceControlCapabilities :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaDeviceControlCapabilities>
    {
        IMediaDeviceControlCapabilities(std::nullptr_t = nullptr) noexcept {}
        IMediaDeviceControlCapabilities(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaDeviceController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaDeviceController>
    {
        IMediaDeviceController(std::nullptr_t = nullptr) noexcept {}
        IMediaDeviceController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaDeviceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaDeviceStatics>
    {
        IMediaDeviceStatics(std::nullptr_t = nullptr) noexcept {}
        IMediaDeviceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IModuleCommandResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IModuleCommandResult>
    {
        IModuleCommandResult(std::nullptr_t = nullptr) noexcept {}
        IModuleCommandResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOpticalImageStabilizationControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOpticalImageStabilizationControl>
    {
        IOpticalImageStabilizationControl(std::nullptr_t = nullptr) noexcept {}
        IOpticalImageStabilizationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPanelBasedOptimizationControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPanelBasedOptimizationControl>
    {
        IPanelBasedOptimizationControl(std::nullptr_t = nullptr) noexcept {}
        IPanelBasedOptimizationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPhotoConfirmationControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoConfirmationControl>
    {
        IPhotoConfirmationControl(std::nullptr_t = nullptr) noexcept {}
        IPhotoConfirmationControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRegionOfInterest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRegionOfInterest>
    {
        IRegionOfInterest(std::nullptr_t = nullptr) noexcept {}
        IRegionOfInterest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRegionOfInterest2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRegionOfInterest2>
    {
        IRegionOfInterest2(std::nullptr_t = nullptr) noexcept {}
        IRegionOfInterest2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRegionsOfInterestControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRegionsOfInterestControl>
    {
        IRegionsOfInterestControl(std::nullptr_t = nullptr) noexcept {}
        IRegionsOfInterestControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneModeControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneModeControl>
    {
        ISceneModeControl(std::nullptr_t = nullptr) noexcept {}
        ISceneModeControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITorchControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITorchControl>
    {
        ITorchControl(std::nullptr_t = nullptr) noexcept {}
        ITorchControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVideoDeviceController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVideoDeviceController>,
        impl::require<winrt::Windows::Media::Devices::IVideoDeviceController, winrt::Windows::Media::Devices::IMediaDeviceController>
    {
        IVideoDeviceController(std::nullptr_t = nullptr) noexcept {}
        IVideoDeviceController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVideoDeviceControllerGetDevicePropertyResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVideoDeviceControllerGetDevicePropertyResult>
    {
        IVideoDeviceControllerGetDevicePropertyResult(std::nullptr_t = nullptr) noexcept {}
        IVideoDeviceControllerGetDevicePropertyResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVideoTemporalDenoisingControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVideoTemporalDenoisingControl>
    {
        IVideoTemporalDenoisingControl(std::nullptr_t = nullptr) noexcept {}
        IVideoTemporalDenoisingControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWhiteBalanceControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWhiteBalanceControl>
    {
        IWhiteBalanceControl(std::nullptr_t = nullptr) noexcept {}
        IWhiteBalanceControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IZoomControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IZoomControl>
    {
        IZoomControl(std::nullptr_t = nullptr) noexcept {}
        IZoomControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IZoomControl2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IZoomControl2>
    {
        IZoomControl2(std::nullptr_t = nullptr) noexcept {}
        IZoomControl2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IZoomSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IZoomSettings>
    {
        IZoomSettings(std::nullptr_t = nullptr) noexcept {}
        IZoomSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
