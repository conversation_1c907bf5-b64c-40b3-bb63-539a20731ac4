["D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\flutter_windows.dll", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\flutter_export.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\flutter_messenger.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\flutter_windows.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\icudtl.dat", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "D:\\projects\\12july\\athkar\\athkar_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\kernel_blob.bin", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\assets\\icons\\app_icon.svg", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\assets\\data\\sample_athkar.json", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\packages\\material_design_icons_flutter\\lib\\fonts\\materialdesignicons-webfont.ttf", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\packages\\wakelock_plus\\assets\\no_sleep.js", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\shaders\\ink_sparkle.frag", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\AssetManifest.json", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\AssetManifest.bin", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\FontManifest.json", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\NOTICES.Z", "D:\\projects\\12july\\athkar\\athkar_app\\build\\flutter_assets\\NativeAssetsManifest.json"]