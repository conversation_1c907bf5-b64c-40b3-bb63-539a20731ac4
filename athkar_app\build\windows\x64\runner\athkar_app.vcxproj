﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{9B0B964F-9E5D-3F64-AEBB-09A47546DA29}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>athkar_app</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\projects\12july\athkar\athkar_app\build\windows\x64\runner\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">athkar_app.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">athkar_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\projects\12july\athkar\athkar_app\build\windows\x64\runner\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">athkar_app.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">athkar_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\projects\12july\athkar\athkar_app\build\windows\x64\runner\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">athkar_app.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">athkar_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\projects\12july\athkar\athkar_app\windows;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\battery_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\local_auth_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/projects/12july/athkar/athkar_app/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\projects\12july\athkar\athkar_app\windows;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\battery_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\local_auth_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;D:\projects\12july\athkar\athkar_app\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\projects\12july\athkar\athkar_app\windows;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\battery_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\local_auth_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;D:\projects\12july\athkar\athkar_app\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Debug\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\app_links\Debug\app_links_plugin.lib;..\plugins\audioplayers_windows\Debug\audioplayers_windows_plugin.lib;..\plugins\battery_plus\Debug\battery_plus_plugin.lib;..\plugins\connectivity_plus\Debug\connectivity_plus_plugin.lib;..\plugins\file_selector_windows\Debug\file_selector_windows_plugin.lib;..\plugins\firebase_core\Debug\firebase_core_plugin.lib;..\plugins\flutter_secure_storage_windows\Debug\flutter_secure_storage_windows_plugin.lib;..\plugins\geolocator_windows\Debug\geolocator_windows_plugin.lib;..\plugins\local_auth_windows\Debug\local_auth_windows_plugin.lib;..\plugins\permission_handler_windows\Debug\permission_handler_windows_plugin.lib;..\plugins\share_plus\Debug\share_plus_plugin.lib;..\plugins\url_launcher_windows\Debug\url_launcher_windows_plugin.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_app.lib;advapi32.lib;ws2_32.lib;crypt32.lib;rpcrt4.lib;ole32.lib;icu.lib;..\flutter\Debug\flutter_wrapper_plugin.lib;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/Debug/athkar_app.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/Debug/athkar_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\projects\12july\athkar\athkar_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\projects\12july\athkar\athkar_app\windows;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\battery_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\local_auth_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/projects/12july/athkar/athkar_app/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR="Profile"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR=\"Profile\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\projects\12july\athkar\athkar_app\windows;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\battery_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\local_auth_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;D:\projects\12july\athkar\athkar_app\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\projects\12july\athkar\athkar_app\windows;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\battery_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\local_auth_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;D:\projects\12july\athkar\athkar_app\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Profile\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\app_links\Profile\app_links_plugin.lib;..\plugins\audioplayers_windows\Profile\audioplayers_windows_plugin.lib;..\plugins\battery_plus\Profile\battery_plus_plugin.lib;..\plugins\connectivity_plus\Profile\connectivity_plus_plugin.lib;..\plugins\file_selector_windows\Profile\file_selector_windows_plugin.lib;..\plugins\firebase_core\Profile\firebase_core_plugin.lib;..\plugins\flutter_secure_storage_windows\Profile\flutter_secure_storage_windows_plugin.lib;..\plugins\geolocator_windows\Profile\geolocator_windows_plugin.lib;..\plugins\local_auth_windows\Profile\local_auth_windows_plugin.lib;..\plugins\permission_handler_windows\Profile\permission_handler_windows_plugin.lib;..\plugins\share_plus\Profile\share_plus_plugin.lib;..\plugins\url_launcher_windows\Profile\url_launcher_windows_plugin.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Debug\firebase_app.lib;advapi32.lib;ws2_32.lib;crypt32.lib;rpcrt4.lib;ole32.lib;icu.lib;..\flutter\Profile\flutter_wrapper_plugin.lib;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/Profile/athkar_app.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/Profile/athkar_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\projects\12july\athkar\athkar_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\projects\12july\athkar\athkar_app\windows;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\battery_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\local_auth_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/projects/12july/athkar/athkar_app/build/windows/x64/extracted/firebase_cpp_sdk_windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;FLUTTER_PLUGIN_IMPL;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\projects\12july\athkar\athkar_app\windows;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\battery_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\local_auth_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;D:\projects\12july\athkar\athkar_app\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\projects\12july\athkar\athkar_app\windows;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\app_links\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\audioplayers_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\battery_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\local_auth_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\share_plus\windows\include;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;D:\projects\12july\athkar\athkar_app\build\windows\x64\extracted\firebase_cpp_sdk_windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Release\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\app_links\Release\app_links_plugin.lib;..\plugins\audioplayers_windows\Release\audioplayers_windows_plugin.lib;..\plugins\battery_plus\Release\battery_plus_plugin.lib;..\plugins\connectivity_plus\Release\connectivity_plus_plugin.lib;..\plugins\file_selector_windows\Release\file_selector_windows_plugin.lib;..\plugins\firebase_core\Release\firebase_core_plugin.lib;..\plugins\flutter_secure_storage_windows\Release\flutter_secure_storage_windows_plugin.lib;..\plugins\geolocator_windows\Release\geolocator_windows_plugin.lib;..\plugins\local_auth_windows\Release\local_auth_windows_plugin.lib;..\plugins\permission_handler_windows\Release\permission_handler_windows_plugin.lib;..\plugins\share_plus\Release\share_plus_plugin.lib;..\plugins\url_launcher_windows\Release\url_launcher_windows_plugin.lib;..\extracted\firebase_cpp_sdk_windows\libs\windows\VS2019\MD\x64\Release\firebase_app.lib;advapi32.lib;ws2_32.lib;crypt32.lib;rpcrt4.lib;ole32.lib;icu.lib;..\flutter\Release\flutter_wrapper_plugin.lib;D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/Release/athkar_app.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/Release/athkar_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\projects\12july\athkar\athkar_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\projects\12july\athkar\athkar_app\windows\runner\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/projects/12july/athkar/athkar_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/projects/12july/athkar/athkar_app/windows -BD:/projects/12july/athkar/athkar_app/build/windows/x64 --check-stamp-file D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\projects\12july\athkar\athkar_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule D:/projects/12july/athkar/athkar_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/projects/12july/athkar/athkar_app/windows -BD:/projects/12july/athkar/athkar_app/build/windows/x64 --check-stamp-file D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\projects\12july\athkar\athkar_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/projects/12july/athkar/athkar_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/projects/12july/athkar/athkar_app/windows -BD:/projects/12july/athkar/athkar_app/build/windows/x64 --check-stamp-file D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\projects\12july\athkar\athkar_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\projects\12july\athkar\athkar_app\windows\runner\flutter_window.cpp" />
    <ClCompile Include="D:\projects\12july\athkar\athkar_app\windows\runner\main.cpp" />
    <ClCompile Include="D:\projects\12july\athkar\athkar_app\windows\runner\utils.cpp" />
    <ClCompile Include="D:\projects\12july\athkar\athkar_app\windows\runner\win32_window.cpp" />
    <ClCompile Include="D:\projects\12july\athkar\athkar_app\windows\flutter\generated_plugin_registrant.cc" />
    <ResourceCompile Include="D:\projects\12july\athkar\athkar_app\windows\runner\Runner.rc" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{1C9A04CE-B521-3F10-9853-ECEA216A6078}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\plugins\app_links\app_links_plugin.vcxproj">
      <Project>{B1E729A4-9506-30E3-A7B8-F620082A8AB4}</Project>
      <Name>app_links_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\plugins\audioplayers_windows\audioplayers_windows_plugin.vcxproj">
      <Project>{C6816DFC-45A1-302C-884F-4BBB2C642E56}</Project>
      <Name>audioplayers_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\plugins\battery_plus\battery_plus_plugin.vcxproj">
      <Project>{B95909B6-C01A-3911-993D-192CCA9A59BD}</Project>
      <Name>battery_plus_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\plugins\connectivity_plus\connectivity_plus_plugin.vcxproj">
      <Project>{447F2E6A-00CF-3337-B9E8-7870668D4876}</Project>
      <Name>connectivity_plus_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\plugins\file_selector_windows\file_selector_windows_plugin.vcxproj">
      <Project>{09BFB79B-0F4D-3611-8481-65EBA5D007BC}</Project>
      <Name>file_selector_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\plugins\firebase_core\firebase_core_plugin.vcxproj">
      <Project>{B56CE881-DFC9-3360-AB48-A217D3E94381}</Project>
      <Name>firebase_core_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{A1E5DC38-EC07-396C-9451-1BA08EC76A07}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\plugins\flutter_secure_storage_windows\flutter_secure_storage_windows_plugin.vcxproj">
      <Project>{C2E8FEEB-3F94-32FE-8C26-10B04D472405}</Project>
      <Name>flutter_secure_storage_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\flutter\flutter_wrapper_app.vcxproj">
      <Project>{AA9D0B55-2DEE-30EE-A172-45D43F91D97F}</Project>
      <Name>flutter_wrapper_app</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\flutter\flutter_wrapper_plugin.vcxproj">
      <Project>{67915962-D970-3935-936B-151B711292DB}</Project>
      <Name>flutter_wrapper_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\plugins\geolocator_windows\geolocator_windows_plugin.vcxproj">
      <Project>{E317AE5B-0218-384C-BF57-B1F9C14F1A2E}</Project>
      <Name>geolocator_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\plugins\local_auth_windows\local_auth_windows_plugin.vcxproj">
      <Project>{F1A31C63-FB3E-3DEA-A13C-D7A81BDB1772}</Project>
      <Name>local_auth_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\plugins\permission_handler_windows\permission_handler_windows_plugin.vcxproj">
      <Project>{004D2623-F7D6-3E54-B10F-1B399A1B9BD1}</Project>
      <Name>permission_handler_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\plugins\share_plus\share_plus_plugin.vcxproj">
      <Project>{9746F648-EFEF-3F39-8726-F2583C5E8C60}</Project>
      <Name>share_plus_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\plugins\url_launcher_windows\url_launcher_windows_plugin.vcxproj">
      <Project>{F6ED9633-4E2C-3A81-AFF1-A66A329948E0}</Project>
      <Name>url_launcher_windows_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>