import 'package:flutter/material.dart';
import 'dart:math';

/// Complete Quran Khatma (completion) service with daily Wird calculations
/// Provides comprehensive Quran reading plans and progress tracking
class QuranKhatmaService {
  static final QuranKhatmaService _instance = QuranKhatmaService._internal();
  factory QuranKhatmaService() => _instance;
  QuranKhatmaService._internal();

  // Quran statistics
  static const int totalSurahs = 114;
  static const int totalAyahs = 6236;
  static const int totalPages = 604;
  static const int totalJuz = 30;
  static const int totalHizb = 60;
  static const int totalRubAlHizb = 240;

  // Current Khatma data
  KhatmaPlan? _currentPlan;
  List<ReadingSession> _readingSessions = [];
  Map<int, bool> _completedSurahs = {};
  Map<int, bool> _completedJuz = {};
  
  bool _isInitialized = false;

  /// Initialize the Quran Khatma service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadKhatmaData();
      _isInitialized = true;
      debugPrint('Quran Khatma Service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Quran Khatma Service: $e');
      rethrow;
    }
  }

  /// Create a new Khatma plan
  Future<KhatmaPlan> createKhatmaPlan({
    required KhatmaPlanType planType,
    required DateTime startDate,
    DateTime? endDate,
    int? customDays,
    List<int>? preferredTimes, // Hours of day (0-23)
    bool includeWeekends = true,
  }) async {
    if (!_isInitialized) await initialize();

    final plan = KhatmaPlan(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      planType: planType,
      startDate: startDate,
      endDate: endDate ?? _calculateEndDate(planType, startDate, customDays),
      customDays: customDays,
      preferredTimes: preferredTimes ?? [6, 14, 20], // Fajr, Dhuhr, Maghrib
      includeWeekends: includeWeekends,
      dailyTargets: _calculateDailyTargets(planType, customDays),
      createdAt: DateTime.now(),
    );

    _currentPlan = plan;
    await _saveKhatmaData();
    
    return plan;
  }

  /// Get current active plan
  KhatmaPlan? getCurrentPlan() => _currentPlan;

  /// Get today's reading target
  DailyTarget? getTodayTarget() {
    if (_currentPlan == null) return null;

    final today = DateTime.now();
    final daysSinceStart = today.difference(_currentPlan!.startDate).inDays;
    
    if (daysSinceStart < 0 || daysSinceStart >= _currentPlan!.dailyTargets.length) {
      return null;
    }

    return _currentPlan!.dailyTargets[daysSinceStart];
  }

  /// Record a reading session
  Future<void> recordReadingSession({
    required int surahNumber,
    required int fromAyah,
    required int toAyah,
    required Duration readingTime,
    String? notes,
  }) async {
    final session = ReadingSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      surahNumber: surahNumber,
      fromAyah: fromAyah,
      toAyah: toAyah,
      readingTime: readingTime,
      timestamp: DateTime.now(),
      notes: notes,
    );

    _readingSessions.add(session);
    
    // Update completion status
    await _updateCompletionStatus(session);
    await _saveKhatmaData();
  }

  /// Get reading progress
  KhatmaProgress getProgress() {
    if (_currentPlan == null) {
      return KhatmaProgress(
        totalProgress: 0.0,
        completedSurahs: 0,
        completedJuz: 0,
        completedAyahs: 0,
        daysCompleted: 0,
        averageReadingTime: Duration.zero,
        currentStreak: 0,
        longestStreak: 0,
      );
    }

    final completedSurahsCount = _completedSurahs.values.where((completed) => completed).length;
    final completedJuzCount = _completedJuz.values.where((completed) => completed).length;
    final completedAyahs = _calculateCompletedAyahs();
    
    final totalProgress = completedAyahs / totalAyahs;
    final daysCompleted = _calculateDaysCompleted();
    final averageReadingTime = _calculateAverageReadingTime();
    final currentStreak = _calculateCurrentStreak();
    final longestStreak = _calculateLongestStreak();

    return KhatmaProgress(
      totalProgress: totalProgress,
      completedSurahs: completedSurahsCount,
      completedJuz: completedJuzCount,
      completedAyahs: completedAyahs,
      daysCompleted: daysCompleted,
      averageReadingTime: averageReadingTime,
      currentStreak: currentStreak,
      longestStreak: longestStreak,
    );
  }

  /// Get reading statistics
  ReadingStatistics getStatistics() {
    final totalReadingTime = _readingSessions.fold<Duration>(
      Duration.zero,
      (total, session) => total + session.readingTime,
    );

    final sessionsPerDay = <DateTime, int>{};
    for (final session in _readingSessions) {
      final date = DateTime(session.timestamp.year, session.timestamp.month, session.timestamp.day);
      sessionsPerDay[date] = (sessionsPerDay[date] ?? 0) + 1;
    }

    final mostActiveDay = sessionsPerDay.entries.isEmpty
        ? null
        : sessionsPerDay.entries.reduce((a, b) => a.value > b.value ? a : b).key;

    final favoriteTime = _calculateFavoriteReadingTime();
    final readingSpeed = _calculateReadingSpeed();

    return ReadingStatistics(
      totalSessions: _readingSessions.length,
      totalReadingTime: totalReadingTime,
      averageSessionTime: _readingSessions.isEmpty
          ? Duration.zero
          : Duration(milliseconds: totalReadingTime.inMilliseconds ~/ _readingSessions.length),
      mostActiveDay: mostActiveDay,
      favoriteReadingTime: favoriteTime,
      readingSpeed: readingSpeed,
      sessionsThisWeek: _getSessionsThisWeek(),
      sessionsThisMonth: _getSessionsThisMonth(),
    );
  }

  /// Get recommended daily target based on remaining time
  DailyTarget getRecommendedTarget() {
    if (_currentPlan == null) {
      return DailyTarget(
        day: 1,
        date: DateTime.now(),
        targetSurahs: [1],
        targetAyahs: 20,
        targetPages: 1,
        estimatedTime: const Duration(minutes: 15),
      );
    }

    final remainingDays = _currentPlan!.endDate.difference(DateTime.now()).inDays;
    final remainingAyahs = totalAyahs - _calculateCompletedAyahs();
    
    if (remainingDays <= 0) {
      return getTodayTarget() ?? _getDefaultTarget();
    }

    final dailyAyahs = (remainingAyahs / remainingDays).ceil();
    final dailyPages = (dailyAyahs / 10).ceil(); // Approximate 10 ayahs per page
    final estimatedTime = Duration(minutes: dailyAyahs * 2); // 2 minutes per ayah

    return DailyTarget(
      day: DateTime.now().difference(_currentPlan!.startDate).inDays + 1,
      date: DateTime.now(),
      targetSurahs: _getRecommendedSurahs(dailyAyahs),
      targetAyahs: dailyAyahs,
      targetPages: dailyPages,
      estimatedTime: estimatedTime,
    );
  }

  /// Calculate daily targets based on plan type
  List<DailyTarget> _calculateDailyTargets(KhatmaPlanType planType, int? customDays) {
    final targets = <DailyTarget>[];
    final totalDays = _getPlanDuration(planType, customDays);
    final dailyAyahs = (totalAyahs / totalDays).ceil();

    for (int day = 1; day <= totalDays; day++) {
      final date = _currentPlan!.startDate.add(Duration(days: day - 1));
      final targetSurahs = _getSurahsForDay(day, totalDays);
      
      targets.add(DailyTarget(
        day: day,
        date: date,
        targetSurahs: targetSurahs,
        targetAyahs: dailyAyahs,
        targetPages: (dailyAyahs / 10).ceil(),
        estimatedTime: Duration(minutes: dailyAyahs * 2),
      ));
    }

    return targets;
  }

  /// Calculate end date based on plan type
  DateTime _calculateEndDate(KhatmaPlanType planType, DateTime startDate, int? customDays) {
    final duration = _getPlanDuration(planType, customDays);
    return startDate.add(Duration(days: duration));
  }

  /// Get plan duration in days
  int _getPlanDuration(KhatmaPlanType planType, int? customDays) {
    switch (planType) {
      case KhatmaPlanType.oneWeek:
        return 7;
      case KhatmaPlanType.twoWeeks:
        return 14;
      case KhatmaPlanType.oneMonth:
        return 30;
      case KhatmaPlanType.twoMonths:
        return 60;
      case KhatmaPlanType.threeMonths:
        return 90;
      case KhatmaPlanType.sixMonths:
        return 180;
      case KhatmaPlanType.oneYear:
        return 365;
      case KhatmaPlanType.custom:
        return customDays ?? 30;
    }
  }

  /// Get surahs for a specific day
  List<int> _getSurahsForDay(int day, int totalDays) {
    // Distribute surahs evenly across days
    final surahsPerDay = (totalSurahs / totalDays).ceil();
    final startSurah = ((day - 1) * surahsPerDay) + 1;
    final endSurah = min(startSurah + surahsPerDay - 1, totalSurahs);
    
    return List.generate(endSurah - startSurah + 1, (index) => startSurah + index);
  }

  /// Get recommended surahs based on ayah count
  List<int> _getRecommendedSurahs(int targetAyahs) {
    // This would use actual surah data to recommend appropriate surahs
    // For now, return a simple calculation
    return [1]; // Placeholder
  }

  /// Calculate completed ayahs
  int _calculateCompletedAyahs() {
    return _readingSessions.fold(0, (total, session) {
      return total + (session.toAyah - session.fromAyah + 1);
    });
  }

  /// Calculate days completed
  int _calculateDaysCompleted() {
    final uniqueDays = <DateTime>{};
    for (final session in _readingSessions) {
      final date = DateTime(session.timestamp.year, session.timestamp.month, session.timestamp.day);
      uniqueDays.add(date);
    }
    return uniqueDays.length;
  }

  /// Calculate average reading time
  Duration _calculateAverageReadingTime() {
    if (_readingSessions.isEmpty) return Duration.zero;
    
    final totalTime = _readingSessions.fold<Duration>(
      Duration.zero,
      (total, session) => total + session.readingTime,
    );
    
    return Duration(milliseconds: totalTime.inMilliseconds ~/ _readingSessions.length);
  }

  /// Calculate current reading streak
  int _calculateCurrentStreak() {
    if (_readingSessions.isEmpty) return 0;

    final sortedSessions = _readingSessions.toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    int streak = 0;
    DateTime? lastDate;

    for (final session in sortedSessions) {
      final sessionDate = DateTime(session.timestamp.year, session.timestamp.month, session.timestamp.day);
      
      if (lastDate == null) {
        lastDate = sessionDate;
        streak = 1;
      } else {
        final difference = lastDate.difference(sessionDate).inDays;
        
        if (difference == 1) {
          streak++;
          lastDate = sessionDate;
        } else if (difference > 1) {
          break;
        }
      }
    }

    return streak;
  }

  /// Calculate longest reading streak
  int _calculateLongestStreak() {
    if (_readingSessions.isEmpty) return 0;

    final sessionDates = _readingSessions
        .map((session) => DateTime(session.timestamp.year, session.timestamp.month, session.timestamp.day))
        .toSet()
        .toList()
      ..sort();

    int longestStreak = 1;
    int currentStreak = 1;

    for (int i = 1; i < sessionDates.length; i++) {
      if (sessionDates[i].difference(sessionDates[i - 1]).inDays == 1) {
        currentStreak++;
        longestStreak = max(longestStreak, currentStreak);
      } else {
        currentStreak = 1;
      }
    }

    return longestStreak;
  }

  /// Calculate favorite reading time
  int _calculateFavoriteReadingTime() {
    final hourCounts = <int, int>{};
    
    for (final session in _readingSessions) {
      final hour = session.timestamp.hour;
      hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
    }

    if (hourCounts.isEmpty) return 6; // Default to Fajr time

    return hourCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// Calculate reading speed (ayahs per minute)
  double _calculateReadingSpeed() {
    if (_readingSessions.isEmpty) return 0.0;

    final totalAyahs = _readingSessions.fold(0, (total, session) {
      return total + (session.toAyah - session.fromAyah + 1);
    });

    final totalMinutes = _readingSessions.fold(0.0, (total, session) {
      return total + session.readingTime.inMilliseconds / 60000;
    });

    return totalMinutes > 0 ? totalAyahs / totalMinutes : 0.0;
  }

  /// Get sessions this week
  int _getSessionsThisWeek() {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    
    return _readingSessions.where((session) {
      return session.timestamp.isAfter(weekStart);
    }).length;
  }

  /// Get sessions this month
  int _getSessionsThisMonth() {
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);
    
    return _readingSessions.where((session) {
      return session.timestamp.isAfter(monthStart);
    }).length;
  }

  /// Update completion status
  Future<void> _updateCompletionStatus(ReadingSession session) async {
    // Mark surah as completed if all ayahs are read
    // This would need actual surah data to implement properly
    _completedSurahs[session.surahNumber] = true;
    
    // Update Juz completion based on surah completion
    // This would need actual Juz mapping to implement properly
  }

  /// Get default target
  DailyTarget _getDefaultTarget() {
    return DailyTarget(
      day: 1,
      date: DateTime.now(),
      targetSurahs: [1],
      targetAyahs: 20,
      targetPages: 2,
      estimatedTime: const Duration(minutes: 30),
    );
  }

  /// Load Khatma data from storage
  Future<void> _loadKhatmaData() async {
    // In a real implementation, this would load from persistent storage
    debugPrint('Loading Khatma data...');
  }

  /// Save Khatma data to storage
  Future<void> _saveKhatmaData() async {
    // In a real implementation, this would save to persistent storage
    debugPrint('Saving Khatma data...');
  }
}

/// Khatma plan types
enum KhatmaPlanType {
  oneWeek,
  twoWeeks,
  oneMonth,
  twoMonths,
  threeMonths,
  sixMonths,
  oneYear,
  custom,
}

/// Khatma plan model
class KhatmaPlan {
  final String id;
  final KhatmaPlanType planType;
  final DateTime startDate;
  final DateTime endDate;
  final int? customDays;
  final List<int> preferredTimes;
  final bool includeWeekends;
  final List<DailyTarget> dailyTargets;
  final DateTime createdAt;

  KhatmaPlan({
    required this.id,
    required this.planType,
    required this.startDate,
    required this.endDate,
    this.customDays,
    required this.preferredTimes,
    required this.includeWeekends,
    required this.dailyTargets,
    required this.createdAt,
  });

  int get totalDays => endDate.difference(startDate).inDays + 1;
  
  double get progressPercentage {
    final daysPassed = DateTime.now().difference(startDate).inDays;
    return daysPassed / totalDays;
  }
}

/// Daily target model
class DailyTarget {
  final int day;
  final DateTime date;
  final List<int> targetSurahs;
  final int targetAyahs;
  final int targetPages;
  final Duration estimatedTime;

  DailyTarget({
    required this.day,
    required this.date,
    required this.targetSurahs,
    required this.targetAyahs,
    required this.targetPages,
    required this.estimatedTime,
  });
}

/// Reading session model
class ReadingSession {
  final String id;
  final int surahNumber;
  final int fromAyah;
  final int toAyah;
  final Duration readingTime;
  final DateTime timestamp;
  final String? notes;

  ReadingSession({
    required this.id,
    required this.surahNumber,
    required this.fromAyah,
    required this.toAyah,
    required this.readingTime,
    required this.timestamp,
    this.notes,
  });

  int get ayahsRead => toAyah - fromAyah + 1;
}

/// Khatma progress model
class KhatmaProgress {
  final double totalProgress;
  final int completedSurahs;
  final int completedJuz;
  final int completedAyahs;
  final int daysCompleted;
  final Duration averageReadingTime;
  final int currentStreak;
  final int longestStreak;

  KhatmaProgress({
    required this.totalProgress,
    required this.completedSurahs,
    required this.completedJuz,
    required this.completedAyahs,
    required this.daysCompleted,
    required this.averageReadingTime,
    required this.currentStreak,
    required this.longestStreak,
  });
}

/// Reading statistics model
class ReadingStatistics {
  final int totalSessions;
  final Duration totalReadingTime;
  final Duration averageSessionTime;
  final DateTime? mostActiveDay;
  final int favoriteReadingTime;
  final double readingSpeed;
  final int sessionsThisWeek;
  final int sessionsThisMonth;

  ReadingStatistics({
    required this.totalSessions,
    required this.totalReadingTime,
    required this.averageSessionTime,
    this.mostActiveDay,
    required this.favoriteReadingTime,
    required this.readingSpeed,
    required this.sessionsThisWeek,
    required this.sessionsThisMonth,
  });
}

/// Extension for plan type display names
extension KhatmaPlanTypeExtension on KhatmaPlanType {
  String get displayNameArabic {
    switch (this) {
      case KhatmaPlanType.oneWeek:
        return 'أسبوع واحد';
      case KhatmaPlanType.twoWeeks:
        return 'أسبوعان';
      case KhatmaPlanType.oneMonth:
        return 'شهر واحد';
      case KhatmaPlanType.twoMonths:
        return 'شهران';
      case KhatmaPlanType.threeMonths:
        return 'ثلاثة أشهر';
      case KhatmaPlanType.sixMonths:
        return 'ستة أشهر';
      case KhatmaPlanType.oneYear:
        return 'سنة واحدة';
      case KhatmaPlanType.custom:
        return 'مخصص';
    }
  }

  String get displayNameEnglish {
    switch (this) {
      case KhatmaPlanType.oneWeek:
        return 'One Week';
      case KhatmaPlanType.twoWeeks:
        return 'Two Weeks';
      case KhatmaPlanType.oneMonth:
        return 'One Month';
      case KhatmaPlanType.twoMonths:
        return 'Two Months';
      case KhatmaPlanType.threeMonths:
        return 'Three Months';
      case KhatmaPlanType.sixMonths:
        return 'Six Months';
      case KhatmaPlanType.oneYear:
        return 'One Year';
      case KhatmaPlanType.custom:
        return 'Custom';
    }
  }
}
