// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Automation_Text_H
#define WINRT_Windows_UI_Xaml_Automation_Text_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.210806.1"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.210806.1"
#include "winrt/Windows.UI.Xaml.Automation.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Text.2.h"
namespace winrt::impl
{
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Automation::Text
{
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
#endif
}
#endif
