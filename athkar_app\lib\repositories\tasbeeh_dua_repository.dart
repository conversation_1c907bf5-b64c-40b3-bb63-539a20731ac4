import '../database/database_helper.dart';
import '../models/athkar_models.dart';

class TasbeehDuaRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Tasbeeh Items
  Future<List<TasbeehItem>> getAllTasbeehItems() async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasbeeh_items',
      orderBy: 'is_default DESC, created_at ASC',
    );

    return List.generate(maps.length, (i) {
      return TasbeehItem.fromJson(maps[i]);
    });
  }

  Future<TasbeehItem?> getTasbeehItemById(String id) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasbeeh_items',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return TasbeehItem.from<PERSON>son(maps.first);
    }
    return null;
  }

  Future<List<TasbeehItem>> getTasbeehItemsByCategory(String category) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasbeeh_items',
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'is_default DESC, created_at ASC',
    );

    return List.generate(maps.length, (i) {
      return TasbeehItem.fromJson(maps[i]);
    });
  }

  Future<String> insertTasbeehItem(TasbeehItem item) async {
    final db = await _dbHelper.database;
    await db.insert('tasbeeh_items', item.toJson());
    return item.id;
  }

  Future<void> updateTasbeehItem(TasbeehItem item) async {
    final db = await _dbHelper.database;
    await db.update(
      'tasbeeh_items',
      item.toJson(),
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  Future<void> deleteTasbeehItem(String id) async {
    final db = await _dbHelper.database;
    await db.delete(
      'tasbeeh_items',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Dua Items
  Future<List<DuaItem>> getAllDuaItems() async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'dua_items',
      orderBy: 'is_default DESC, created_at ASC',
    );

    return List.generate(maps.length, (i) {
      return DuaItem.fromJson(maps[i]);
    });
  }

  Future<DuaItem?> getDuaItemById(String id) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'dua_items',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return DuaItem.fromJson(maps.first);
    }
    return null;
  }

  Future<List<DuaItem>> getDuaItemsByCategory(String category) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'dua_items',
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'is_default DESC, created_at ASC',
    );

    return List.generate(maps.length, (i) {
      return DuaItem.fromJson(maps[i]);
    });
  }

  Future<String> insertDuaItem(DuaItem item) async {
    final db = await _dbHelper.database;
    await db.insert('dua_items', item.toJson());
    return item.id;
  }

  Future<void> updateDuaItem(DuaItem item) async {
    final db = await _dbHelper.database;
    await db.update(
      'dua_items',
      item.toJson(),
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  Future<void> deleteDuaItem(String id) async {
    final db = await _dbHelper.database;
    await db.delete(
      'dua_items',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Tasbeeh Sessions
  Future<List<TasbeehSession>> getAllTasbeehSessions() async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasbeeh_sessions',
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return TasbeehSession.fromJson(maps[i]);
    });
  }

  Future<List<TasbeehSession>> getUserTasbeehSessions(String userId) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasbeeh_sessions',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return TasbeehSession.fromJson(maps[i]);
    });
  }

  Future<TasbeehSession?> getTasbeehSessionById(String id) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasbeeh_sessions',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return TasbeehSession.fromJson(maps.first);
    }
    return null;
  }

  Future<String> insertTasbeehSession(TasbeehSession session) async {
    final db = await _dbHelper.database;
    await db.insert('tasbeeh_sessions', session.toJson());
    return session.id;
  }

  Future<void> updateTasbeehSession(TasbeehSession session) async {
    final db = await _dbHelper.database;
    await db.update(
      'tasbeeh_sessions',
      session.toJson(),
      where: 'id = ?',
      whereArgs: [session.id],
    );
  }

  Future<void> completeTasbeehSession(String sessionId) async {
    final db = await _dbHelper.database;
    await db.update(
      'tasbeeh_sessions',
      {
        'is_completed': 1,
        'end_time': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [sessionId],
    );
  }

  Future<void> deleteTasbeehSession(String id) async {
    final db = await _dbHelper.database;
    await db.delete(
      'tasbeeh_sessions',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Search functionality
  Future<List<TasbeehItem>> searchTasbeehItems(String query) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasbeeh_items',
      where: 'arabic_text LIKE ? OR transliteration LIKE ? OR translation LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'is_default DESC, created_at ASC',
    );

    return List.generate(maps.length, (i) {
      return TasbeehItem.fromJson(maps[i]);
    });
  }

  Future<List<DuaItem>> searchDuaItems(String query) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'dua_items',
      where: 'title LIKE ? OR arabic_text LIKE ? OR transliteration LIKE ? OR translation LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%', '%$query%'],
      orderBy: 'is_default DESC, created_at ASC',
    );

    return List.generate(maps.length, (i) {
      return DuaItem.fromJson(maps[i]);
    });
  }

  // Statistics
  Future<Map<String, int>> getTasbeehStats(String userId) async {
    final db = await _dbHelper.database;
    
    final totalSessionsResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM tasbeeh_sessions WHERE user_id = ?',
      [userId],
    );
    
    final completedSessionsResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM tasbeeh_sessions WHERE user_id = ? AND is_completed = 1',
      [userId],
    );
    
    final totalCountResult = await db.rawQuery(
      'SELECT SUM(current_count) as total FROM tasbeeh_sessions WHERE user_id = ?',
      [userId],
    );

    return {
      'totalSessions': totalSessionsResult.first['count'] as int,
      'completedSessions': completedSessionsResult.first['count'] as int,
      'totalCount': (totalCountResult.first['total'] as int?) ?? 0,
    };
  }
}
