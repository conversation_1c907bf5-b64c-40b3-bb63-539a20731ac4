import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../services/background_service.dart';
// import '../../theme/app_theme.dart'; // Unused import removed
import '../../screens/comprehensive_testing_screen.dart';

class BackgroundServiceTestingWidget extends StatefulWidget {
  final Function(TestingStatus) onStatusChanged;

  const BackgroundServiceTestingWidget({
    super.key,
    required this.onStatusChanged,
  });

  @override
  State<BackgroundServiceTestingWidget> createState() => _BackgroundServiceTestingWidgetState();
}

class _BackgroundServiceTestingWidgetState extends State<BackgroundServiceTestingWidget> {
  final BackgroundService _backgroundService = BackgroundService();
  
  final Map<String, TestResult> _testResults = {};
  bool _isRunningTests = false;
  int _currentTestRound = 0;
  final int _totalRounds = 5;

  final List<ServiceTest> _serviceTests = [
    ServiceTest(
      id: 'service_initialization',
      nameAr: 'تهيئة الخدمة',
      nameEn: 'Service Initialization',
      description: 'Test background service initialization',
    ),
    ServiceTest(
      id: 'notification_service',
      nameAr: 'خدمة الإشعارات',
      nameEn: 'Notification Service',
      description: 'Test background notification service',
    ),
    ServiceTest(
      id: 'sync_service',
      nameAr: 'خدمة المزامنة',
      nameEn: 'Sync Service',
      description: 'Test background synchronization service',
    ),
    ServiceTest(
      id: 'prayer_reminder_service',
      nameAr: 'خدمة تذكير الصلاة',
      nameEn: 'Prayer Reminder Service',
      description: 'Test prayer time reminder service',
    ),
    ServiceTest(
      id: 'athkar_reminder_service',
      nameAr: 'خدمة تذكير الأذكار',
      nameEn: 'Athkar Reminder Service',
      description: 'Test Athkar reminder service',
    ),
    ServiceTest(
      id: 'service_persistence',
      nameAr: 'استمرارية الخدمة',
      nameEn: 'Service Persistence',
      description: 'Test service persistence across app states',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeTestResults();
  }

  void _initializeTestResults() {
    for (final test in _serviceTests) {
      _testResults[test.id] = TestResult.notStarted;
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(languageService),
          const SizedBox(height: 24),
          _buildTestControls(languageService),
          const SizedBox(height: 24),
          _buildServiceStatus(languageService),
          const SizedBox(height: 24),
          _buildTestResults(languageService),
          const SizedBox(height: 24),
          if (_isRunningTests) _buildRoundProgress(languageService),
        ],
      ),
    );
  }

  Widget _buildHeader(LanguageService languageService) {
    final passedTests = _testResults.values.where((result) => result == TestResult.passed).length;
    final totalTests = _testResults.length;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.deepPurple,
            Colors.deepPurple.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.settings, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  languageService.isArabic ? 'اختبار الخدمات الخلفية' : 'Background Services Testing',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            languageService.isArabic 
                ? 'اختبار شامل لجميع الخدمات الخلفية الإسلامية'
                : 'Comprehensive testing of all Islamic background services',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageService.isArabic ? 'التقدم' : 'Progress',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '$passedTests / $totalTests',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: passedTests / totalTests,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildTestControls(LanguageService languageService) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isRunningTests ? null : _runAllTests,
            icon: _isRunningTests 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(
              _isRunningTests
                  ? (languageService.isArabic ? 'جاري التشغيل...' : 'Running...')
                  : (languageService.isArabic ? 'تشغيل جميع الاختبارات' : 'Run All Tests'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.deepPurple,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: _isRunningTests ? null : _resetTests,
          icon: const Icon(Icons.refresh),
          label: Text(languageService.isArabic ? 'إعادة تعيين' : 'Reset'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildServiceStatus(LanguageService languageService) {
    return FutureBuilder<bool>(
      future: _backgroundService.isRunning(),
      builder: (context, snapshot) {
        final isRunning = snapshot.data ?? false;
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isRunning ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isRunning ? Colors.green : Colors.red,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isRunning ? Icons.play_circle : Icons.pause_circle,
                color: isRunning ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languageService.isArabic ? 'حالة الخدمات الخلفية' : 'Background Services Status',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isRunning ? Colors.green : Colors.red,
                      ),
                    ),
                    Text(
                      isRunning
                          ? (languageService.isArabic ? 'تعمل' : 'Running')
                          : (languageService.isArabic ? 'متوقفة' : 'Stopped'),
                      style: TextStyle(
                        color: isRunning ? Colors.green : Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              FutureBuilder<int>(
                future: _backgroundService.getActiveServicesCount(),
                builder: (context, countSnapshot) {
                  final count = countSnapshot.data ?? 0;
                  return Column(
                    children: [
                      Text(
                        languageService.isArabic ? 'الخدمات النشطة' : 'Active Services',
                        style: const TextStyle(fontSize: 12),
                      ),
                      Text(
                        '$count',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTestResults(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'نتائج الاختبارات' : 'Test Results',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.deepPurple,
          ),
        ),
        const SizedBox(height: 12),
        ...(_serviceTests.map((test) {
          final result = _testResults[test.id] ?? TestResult.notStarted;
          return _buildTestResultCard(test, result, languageService);
        }).toList()),
      ],
    );
  }

  Widget _buildTestResultCard(ServiceTest test, TestResult result, LanguageService languageService) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (result) {
      case TestResult.passed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = languageService.isArabic ? 'نجح' : 'Passed';
        break;
      case TestResult.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = languageService.isArabic ? 'فشل' : 'Failed';
        break;
      case TestResult.inProgress:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = languageService.isArabic ? 'قيد التشغيل' : 'Running';
        break;
      case TestResult.notStarted:
        statusColor = Colors.grey;
        statusIcon = Icons.radio_button_unchecked;
        statusText = languageService.isArabic ? 'لم يبدأ' : 'Not Started';
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          languageService.isArabic ? test.nameAr : test.nameEn,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(test.description),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            if (result == TestResult.inProgress)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        onTap: () => _runSingleTest(test.id),
      ),
    );
  }

  Widget _buildRoundProgress(LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.deepPurple.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.deepPurple.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            languageService.isArabic 
                ? 'جولة الاختبار ${_currentTestRound + 1} من $_totalRounds'
                : 'Test Round ${_currentTestRound + 1} of $_totalRounds',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.deepPurple,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentTestRound + 1) / _totalRounds,
            backgroundColor: Colors.deepPurple.withValues(alpha: 0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.deepPurple),
          ),
        ],
      ),
    );
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTests = true;
      _currentTestRound = 0;
    });

    widget.onStatusChanged(TestingStatus.inProgress);

    // Run 5 rounds of testing as per requirements
    for (int round = 0; round < _totalRounds; round++) {
      setState(() {
        _currentTestRound = round;
      });

      for (final test in _serviceTests) {
        await _runSingleTestInternal(test.id);
        await Future.delayed(const Duration(milliseconds: 800));
      }

      await Future.delayed(const Duration(seconds: 2));
    }

    setState(() {
      _isRunningTests = false;
    });

    final allPassed = _testResults.values.every((result) => result == TestResult.passed);
    widget.onStatusChanged(allPassed ? TestingStatus.passed : TestingStatus.failed);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            allPassed
                ? (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'جميع اختبارات الخدمات الخلفية نجحت!'
                    : 'All background service tests passed!')
                : (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'بعض اختبارات الخدمات الخلفية فشلت'
                    : 'Some background service tests failed'),
          ),
          backgroundColor: allPassed ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _runSingleTest(String testId) async {
    await _runSingleTestInternal(testId);
  }

  Future<void> _runSingleTestInternal(String testId) async {
    setState(() {
      _testResults[testId] = TestResult.inProgress;
    });

    try {
      bool testPassed = false;

      switch (testId) {
        case 'service_initialization':
          testPassed = await _testServiceInitialization();
          break;
        case 'notification_service':
          testPassed = await _testNotificationService();
          break;
        case 'sync_service':
          testPassed = await _testSyncService();
          break;
        case 'prayer_reminder_service':
          testPassed = await _testPrayerReminderService();
          break;
        case 'athkar_reminder_service':
          testPassed = await _testAthkarReminderService();
          break;
        case 'service_persistence':
          testPassed = await _testServicePersistence();
          break;
      }

      setState(() {
        _testResults[testId] = testPassed ? TestResult.passed : TestResult.failed;
      });
    } catch (e) {
      setState(() {
        _testResults[testId] = TestResult.failed;
      });
    }
  }

  Future<bool> _testServiceInitialization() async {
    try {
      await _backgroundService.initialize();
      await Future.delayed(const Duration(seconds: 1));
      return await _backgroundService.isRunning();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testNotificationService() async {
    try {
      await _backgroundService.startNotificationService();
      await Future.delayed(const Duration(seconds: 1));
      return await _backgroundService.isServiceRunning('notification');
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testSyncService() async {
    try {
      await _backgroundService.startSyncService();
      await Future.delayed(const Duration(seconds: 1));
      return await _backgroundService.isServiceRunning('sync');
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testPrayerReminderService() async {
    try {
      await _backgroundService.startPrayerReminderService();
      await Future.delayed(const Duration(seconds: 1));
      return await _backgroundService.isServiceRunning('prayer_reminder');
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testAthkarReminderService() async {
    try {
      await _backgroundService.startAthkarReminderService();
      await Future.delayed(const Duration(seconds: 1));
      return await _backgroundService.isServiceRunning('athkar_reminder');
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testServicePersistence() async {
    try {
      await _backgroundService.testServicePersistence();
      await Future.delayed(const Duration(seconds: 2));
      return await _backgroundService.verifyServicePersistence();
    } catch (e) {
      return false;
    }
  }

  void _resetTests() {
    setState(() {
      _initializeTestResults();
      _isRunningTests = false;
      _currentTestRound = 0;
    });
    widget.onStatusChanged(TestingStatus.notStarted);
  }
}

class ServiceTest {
  final String id;
  final String nameAr;
  final String nameEn;
  final String description;

  ServiceTest({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.description,
  });
}

enum TestResult {
  notStarted,
  inProgress,
  passed,
  failed,
}
