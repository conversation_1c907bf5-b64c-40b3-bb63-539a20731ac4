^D:\PROJECTS\12JULY\ATHKAR\ATHKAR_APP\BUILD\WINDOWS\X64\CMAKEFILES\0AB914E4DB428086201772BDA52E0FDE\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/projects/12july/athkar/athkar_app/windows -BD:/projects/12july/athkar/athkar_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/projects/12july/athkar/athkar_app/build/windows/x64/athkar_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
