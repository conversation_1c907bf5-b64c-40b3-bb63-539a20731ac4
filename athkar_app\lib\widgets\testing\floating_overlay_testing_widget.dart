import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../services/floating_overlay_service.dart';
import '../../theme/app_theme.dart';
import '../../screens/comprehensive_testing_screen.dart';

class FloatingOverlayTestingWidget extends StatefulWidget {
  final Function(TestingStatus) onStatusChanged;

  const FloatingOverlayTestingWidget({
    super.key,
    required this.onStatusChanged,
  });

  @override
  State<FloatingOverlayTestingWidget> createState() => _FloatingOverlayTestingWidgetState();
}

class _FloatingOverlayTestingWidgetState extends State<FloatingOverlayTestingWidget> {
  final FloatingOverlayService _overlayService = FloatingOverlayService();
  
  final Map<String, TestResult> _testResults = {};
  bool _isRunningTests = false;
  int _currentTestRound = 0;
  final int _totalRounds = 5;

  final List<OverlayTest> _overlayTests = [
    OverlayTest(
      id: 'basic_overlay',
      nameAr: 'النافذة العائمة الأساسية',
      nameEn: 'Basic Floating Overlay',
      description: 'Test basic floating overlay display',
    ),
    OverlayTest(
      id: 'dhikr_counter_overlay',
      nameAr: 'عداد الذكر العائم',
      nameEn: 'Floating Dhikr Counter',
      description: 'Test floating dhikr counter functionality',
    ),
    OverlayTest(
      id: 'prayer_time_overlay',
      nameAr: 'نافذة أوقات الصلاة',
      nameEn: 'Prayer Times Overlay',
      description: 'Test prayer times floating display',
    ),
    OverlayTest(
      id: 'athkar_reminder_overlay',
      nameAr: 'تذكير الأذكار العائم',
      nameEn: 'Floating Athkar Reminder',
      description: 'Test floating athkar reminder',
    ),
    OverlayTest(
      id: 'overlay_positioning',
      nameAr: 'تموضع النافذة',
      nameEn: 'Overlay Positioning',
      description: 'Test overlay positioning and movement',
    ),
    OverlayTest(
      id: 'overlay_persistence',
      nameAr: 'استمرارية النافذة',
      nameEn: 'Overlay Persistence',
      description: 'Test overlay persistence across app states',
    ),
    OverlayTest(
      id: 'multiple_overlays',
      nameAr: 'النوافذ المتعددة',
      nameEn: 'Multiple Overlays',
      description: 'Test multiple overlays simultaneously',
    ),
    OverlayTest(
      id: 'overlay_interactions',
      nameAr: 'تفاعلات النافذة',
      nameEn: 'Overlay Interactions',
      description: 'Test overlay touch interactions',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeTestResults();
  }

  void _initializeTestResults() {
    for (final test in _overlayTests) {
      _testResults[test.id] = TestResult.notStarted;
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with progress
          _buildHeader(languageService),
          
          const SizedBox(height: 24),
          
          // Test controls
          _buildTestControls(languageService),
          
          const SizedBox(height: 24),
          
          // Permission status
          _buildPermissionStatus(languageService),
          
          const SizedBox(height: 24),
          
          // Test results
          _buildTestResults(languageService),
          
          const SizedBox(height: 24),
          
          // Round progress
          if (_isRunningTests) _buildRoundProgress(languageService),
        ],
      ),
    );
  }

  Widget _buildHeader(LanguageService languageService) {
    final passedTests = _testResults.values.where((result) => result == TestResult.passed).length;
    final totalTests = _testResults.length;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.purple,
            Colors.purple.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.picture_in_picture, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  languageService.isArabic ? 'اختبار النوافذ العائمة' : 'Floating Overlay Testing',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            languageService.isArabic 
                ? 'اختبار شامل للنوافذ العائمة وعدادات الذكر والتذكيرات'
                : 'Comprehensive testing of floating overlays, dhikr counters, and reminders',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageService.isArabic ? 'التقدم' : 'Progress',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '$passedTests / $totalTests',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: passedTests / totalTests,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildTestControls(LanguageService languageService) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isRunningTests ? null : _runAllTests,
            icon: _isRunningTests 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(
              _isRunningTests
                  ? (languageService.isArabic ? 'جاري التشغيل...' : 'Running...')
                  : (languageService.isArabic ? 'تشغيل جميع الاختبارات' : 'Run All Tests'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: _isRunningTests ? null : _resetTests,
          icon: const Icon(Icons.refresh),
          label: Text(languageService.isArabic ? 'إعادة تعيين' : 'Reset'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildPermissionStatus(LanguageService languageService) {
    return FutureBuilder<bool>(
      future: _overlayService.hasOverlayPermission(),
      builder: (context, snapshot) {
        final hasPermission = snapshot.data ?? false;
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: hasPermission ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: hasPermission ? Colors.green : Colors.red,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                hasPermission ? Icons.check_circle : Icons.error,
                color: hasPermission ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languageService.isArabic ? 'صلاحية النوافذ العائمة' : 'Overlay Permission',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: hasPermission ? Colors.green : Colors.red,
                      ),
                    ),
                    Text(
                      hasPermission
                          ? (languageService.isArabic ? 'مُمنوحة' : 'Granted')
                          : (languageService.isArabic ? 'مطلوبة' : 'Required'),
                      style: TextStyle(
                        color: hasPermission ? Colors.green : Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              if (!hasPermission)
                ElevatedButton(
                  onPressed: () => _overlayService.requestOverlayPermission(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(languageService.isArabic ? 'طلب الصلاحية' : 'Request'),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTestResults(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'نتائج الاختبارات' : 'Test Results',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.purple,
          ),
        ),
        const SizedBox(height: 12),
        ...(_overlayTests.map((test) {
          final result = _testResults[test.id] ?? TestResult.notStarted;
          return _buildTestResultCard(test, result, languageService);
        }).toList()),
      ],
    );
  }

  Widget _buildTestResultCard(OverlayTest test, TestResult result, LanguageService languageService) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (result) {
      case TestResult.passed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = languageService.isArabic ? 'نجح' : 'Passed';
        break;
      case TestResult.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = languageService.isArabic ? 'فشل' : 'Failed';
        break;
      case TestResult.inProgress:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = languageService.isArabic ? 'قيد التشغيل' : 'Running';
        break;
      case TestResult.notStarted:
        statusColor = Colors.grey;
        statusIcon = Icons.radio_button_unchecked;
        statusText = languageService.isArabic ? 'لم يبدأ' : 'Not Started';
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          languageService.isArabic ? test.nameAr : test.nameEn,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(test.description),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            if (result == TestResult.inProgress)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        onTap: () => _runSingleTest(test.id),
      ),
    );
  }

  Widget _buildRoundProgress(LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            languageService.isArabic 
                ? 'جولة الاختبار ${_currentTestRound + 1} من $_totalRounds'
                : 'Test Round ${_currentTestRound + 1} of $_totalRounds',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.purple,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentTestRound + 1) / _totalRounds,
            backgroundColor: Colors.purple.withValues(alpha: 0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.purple),
          ),
        ],
      ),
    );
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTests = true;
      _currentTestRound = 0;
    });

    widget.onStatusChanged(TestingStatus.inProgress);

    // Check overlay permission first
    final hasPermission = await _overlayService.hasOverlayPermission();
    if (!hasPermission) {
      setState(() {
        _isRunningTests = false;
      });
      widget.onStatusChanged(TestingStatus.failed);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LanguageService>(context, listen: false).isArabic
                  ? 'صلاحية النوافذ العائمة مطلوبة'
                  : 'Overlay permission required',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // Run 5 rounds of testing as per requirements
    for (int round = 0; round < _totalRounds; round++) {
      setState(() {
        _currentTestRound = round;
      });

      for (final test in _overlayTests) {
        await _runSingleTestInternal(test.id);
        await Future.delayed(const Duration(milliseconds: 800)); // Brief delay between tests
      }

      await Future.delayed(const Duration(seconds: 2)); // Delay between rounds
    }

    setState(() {
      _isRunningTests = false;
    });

    // Check if all tests passed
    final allPassed = _testResults.values.every((result) => result == TestResult.passed);
    widget.onStatusChanged(allPassed ? TestingStatus.passed : TestingStatus.failed);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            allPassed
                ? (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'جميع اختبارات النوافذ العائمة نجحت!'
                    : 'All overlay tests passed!')
                : (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'بعض اختبارات النوافذ العائمة فشلت'
                    : 'Some overlay tests failed'),
          ),
          backgroundColor: allPassed ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _runSingleTest(String testId) async {
    await _runSingleTestInternal(testId);
  }

  Future<void> _runSingleTestInternal(String testId) async {
    setState(() {
      _testResults[testId] = TestResult.inProgress;
    });

    try {
      bool testPassed = false;

      switch (testId) {
        case 'basic_overlay':
          testPassed = await _testBasicOverlay();
          break;
        case 'dhikr_counter_overlay':
          testPassed = await _testDhikrCounterOverlay();
          break;
        case 'prayer_time_overlay':
          testPassed = await _testPrayerTimeOverlay();
          break;
        case 'athkar_reminder_overlay':
          testPassed = await _testAthkarReminderOverlay();
          break;
        case 'overlay_positioning':
          testPassed = await _testOverlayPositioning();
          break;
        case 'overlay_persistence':
          testPassed = await _testOverlayPersistence();
          break;
        case 'multiple_overlays':
          testPassed = await _testMultipleOverlays();
          break;
        case 'overlay_interactions':
          testPassed = await _testOverlayInteractions();
          break;
      }

      setState(() {
        _testResults[testId] = testPassed ? TestResult.passed : TestResult.failed;
      });
    } catch (e) {
      setState(() {
        _testResults[testId] = TestResult.failed;
      });
    }
  }

  Future<bool> _testBasicOverlay() async {
    try {
      await _overlayService.showBasicOverlay(
        content: 'Test Overlay',
        position: const Offset(100, 100),
      );
      await Future.delayed(const Duration(seconds: 2));
      await _overlayService.hideOverlay();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testDhikrCounterOverlay() async {
    try {
      await _overlayService.showDhikrCounterOverlay(
        dhikrText: 'سبحان الله',
        currentCount: 0,
        targetCount: 33,
      );
      await Future.delayed(const Duration(seconds: 2));
      await _overlayService.hideOverlay();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testPrayerTimeOverlay() async {
    try {
      await _overlayService.showPrayerTimeOverlay(
        nextPrayer: 'الفجر',
        timeRemaining: '2:30:00',
      );
      await Future.delayed(const Duration(seconds: 2));
      await _overlayService.hideOverlay();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testAthkarReminderOverlay() async {
    try {
      await _overlayService.showAthkarReminderOverlay(
        athkarName: 'أذكار الصباح',
        reminderText: 'حان وقت أذكار الصباح',
      );
      await Future.delayed(const Duration(seconds: 2));
      await _overlayService.hideOverlay();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testOverlayPositioning() async {
    try {
      await _overlayService.showBasicOverlay(
        content: 'Position Test',
        position: const Offset(50, 50),
      );
      await Future.delayed(const Duration(seconds: 1));
      
      await _overlayService.updateOverlayPosition(const Offset(200, 200));
      await Future.delayed(const Duration(seconds: 1));
      
      await _overlayService.hideOverlay();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testOverlayPersistence() async {
    try {
      await _overlayService.showPersistentOverlay(
        content: 'Persistent Test',
        position: const Offset(150, 150),
      );
      await Future.delayed(const Duration(seconds: 2));
      
      // Test if overlay persists across app state changes
      final isVisible = await _overlayService.isOverlayVisible();
      await _overlayService.hideOverlay();
      
      return isVisible;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testMultipleOverlays() async {
    try {
      await _overlayService.showMultipleOverlays([
        OverlayConfig(
          id: 'overlay1',
          content: 'Overlay 1',
          position: const Offset(100, 100),
        ),
        OverlayConfig(
          id: 'overlay2',
          content: 'Overlay 2',
          position: const Offset(200, 200),
        ),
      ]);
      
      await Future.delayed(const Duration(seconds: 2));
      await _overlayService.hideAllOverlays();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testOverlayInteractions() async {
    try {
      await _overlayService.showInteractiveOverlay(
        content: 'Interactive Test',
        position: const Offset(100, 100),
        onTap: () => debugPrint('Overlay tapped'),
        onLongPress: () => debugPrint('Overlay long pressed'),
      );
      
      await Future.delayed(const Duration(seconds: 2));
      await _overlayService.hideOverlay();
      return true;
    } catch (e) {
      return false;
    }
  }

  void _resetTests() {
    setState(() {
      _initializeTestResults();
      _isRunningTests = false;
      _currentTestRound = 0;
    });
    widget.onStatusChanged(TestingStatus.notStarted);
  }
}

class OverlayTest {
  final String id;
  final String nameAr;
  final String nameEn;
  final String description;

  OverlayTest({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.description,
  });
}

enum TestResult {
  notStarted,
  inProgress,
  passed,
  failed,
}
