import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

class PermissionProgressWidget extends StatefulWidget {
  final int current;
  final int total;
  final int granted;

  const PermissionProgressWidget({
    super.key,
    required this.current,
    required this.total,
    required this.granted,
  });

  @override
  State<PermissionProgressWidget> createState() => _PermissionProgressWidgetState();
}

class _PermissionProgressWidgetState extends State<PermissionProgressWidget>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;
  
  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.current / widget.total,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
    
    _progressController.forward();
  }

  @override
  void didUpdateWidget(PermissionProgressWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.current != widget.current || oldWidget.total != widget.total) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value,
        end: widget.current / widget.total,
      ).animate(CurvedAnimation(
        parent: _progressController,
        curve: Curves.easeInOut,
      ));
      _progressController.forward();
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        children: [
          // Progress indicators
          Row(
            children: List.generate(widget.total, (index) {
              return Expanded(
                child: Container(
                  margin: EdgeInsets.only(
                    right: index < widget.total - 1 ? 8 : 0,
                  ),
                  child: _buildProgressStep(index),
                ),
              );
            }),
          ),
          
          const SizedBox(height: 16),
          
          // Progress bar
          _buildProgressBar(),
          
          const SizedBox(height: 12),
          
          // Progress text
          _buildProgressText(),
        ],
      ),
    );
  }

  Widget _buildProgressStep(int index) {
    final isCompleted = index < widget.current;
    final isCurrent = index == widget.current - 1;
    final isGranted = index < widget.granted;
    
    Color stepColor;
    IconData stepIcon;
    
    if (isGranted) {
      stepColor = Colors.green;
      stepIcon = Icons.check;
    } else if (isCompleted) {
      stepColor = AppTheme.primaryGreen;
      stepIcon = Icons.check;
    } else if (isCurrent) {
      stepColor = AppTheme.primaryGreen;
      stepIcon = Icons.radio_button_unchecked;
    } else {
      stepColor = Colors.grey[300]!;
      stepIcon = Icons.radio_button_unchecked;
    }
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: 4,
      decoration: BoxDecoration(
        color: stepColor,
        borderRadius: BorderRadius.circular(2),
      ),
      child: isCurrent
          ? Container(
              decoration: BoxDecoration(
                color: stepColor,
                borderRadius: BorderRadius.circular(2),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(2),
                child: LinearProgressIndicator(
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    stepColor.withValues(alpha: 0.3),
                  ),
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildProgressBar() {
    return Container(
      height: 8,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(4),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            return LinearProgressIndicator(
              value: _progressAnimation.value,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(
                _getProgressColor(),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProgressText() {
    final percentage = ((widget.current / widget.total) * 100).round();
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '${widget.current} / ${widget.total}',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        Row(
          children: [
            Icon(
              Icons.check_circle,
              size: 16,
              color: Colors.green,
            ),
            const SizedBox(width: 4),
            Text(
              '${widget.granted}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.green,
              ),
            ),
          ],
        ),
        Text(
          '$percentage%',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: _getProgressColor(),
          ),
        ),
      ],
    );
  }

  Color _getProgressColor() {
    final progress = widget.current / widget.total;
    
    if (progress >= 1.0) {
      return Colors.green;
    } else if (progress >= 0.7) {
      return AppTheme.primaryGreen;
    } else if (progress >= 0.4) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}

/// Circular progress indicator for permissions
class CircularPermissionProgress extends StatefulWidget {
  final int granted;
  final int total;
  final double size;

  const CircularPermissionProgress({
    super.key,
    required this.granted,
    required this.total,
    this.size = 80,
  });

  @override
  State<CircularPermissionProgress> createState() => _CircularPermissionProgressState();
}

class _CircularPermissionProgressState extends State<CircularPermissionProgress>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: widget.granted / widget.total,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _controller.forward();
  }

  @override
  void didUpdateWidget(CircularPermissionProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.granted != widget.granted || oldWidget.total != widget.total) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.granted / widget.total,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ));
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background circle
          SizedBox(
            width: widget.size,
            height: widget.size,
            child: CircularProgressIndicator(
              value: 1.0,
              strokeWidth: 6,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[200]!),
            ),
          ),
          
          // Progress circle
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return SizedBox(
                width: widget.size,
                height: widget.size,
                child: CircularProgressIndicator(
                  value: _animation.value,
                  strokeWidth: 6,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getProgressColor(),
                  ),
                ),
              );
            },
          ),
          
          // Center text
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${widget.granted}',
                style: TextStyle(
                  fontSize: widget.size * 0.25,
                  fontWeight: FontWeight.bold,
                  color: _getProgressColor(),
                ),
              ),
              Text(
                '/ ${widget.total}',
                style: TextStyle(
                  fontSize: widget.size * 0.15,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getProgressColor() {
    final progress = widget.granted / widget.total;
    
    if (progress >= 1.0) {
      return Colors.green;
    } else if (progress >= 0.7) {
      return AppTheme.primaryGreen;
    } else if (progress >= 0.4) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}

/// Mini progress indicator for quick status
class MiniPermissionIndicator extends StatelessWidget {
  final int granted;
  final int total;

  const MiniPermissionIndicator({
    super.key,
    required this.granted,
    required this.total,
  });

  @override
  Widget build(BuildContext context) {
    final progress = granted / total;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getProgressColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getProgressColor().withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getProgressIcon(),
            size: 14,
            color: _getProgressColor(),
          ),
          const SizedBox(width: 4),
          Text(
            '$granted/$total',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: _getProgressColor(),
            ),
          ),
        ],
      ),
    );
  }

  Color _getProgressColor() {
    final progress = granted / total;
    
    if (progress >= 1.0) {
      return Colors.green;
    } else if (progress >= 0.7) {
      return AppTheme.primaryGreen;
    } else if (progress >= 0.4) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  IconData _getProgressIcon() {
    final progress = granted / total;
    
    if (progress >= 1.0) {
      return Icons.check_circle;
    } else if (progress >= 0.5) {
      return Icons.schedule;
    } else {
      return Icons.warning;
    }
  }
}
