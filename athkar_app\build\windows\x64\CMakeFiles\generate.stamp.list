D:/projects/12july/athkar/athkar_app/build/windows/x64/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/app_links/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/audioplayers_windows/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/battery_plus/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/connectivity_plus/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/file_selector_windows/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/firebase_core/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/firebase_core/bin/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/flutter_secure_storage_windows/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/geolocator_windows/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/local_auth_windows/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/permission_handler_windows/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/rive_common/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/share_plus/CMakeFiles/generate.stamp
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/url_launcher_windows/CMakeFiles/generate.stamp
