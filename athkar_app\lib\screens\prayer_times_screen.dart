import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:geolocator/geolocator.dart';
import '../services/prayer_times_service.dart';
import '../theme/app_theme.dart';
import '../models/prayer_times_models.dart';
import 'prayer_times_settings_screen.dart';

class PrayerTimesScreen extends StatefulWidget {
  const PrayerTimesScreen({super.key});

  @override
  State<PrayerTimesScreen> createState() => _PrayerTimesScreenState();
}

class _PrayerTimesScreenState extends State<PrayerTimesScreen> {
  PrayerTimes? _prayerTimes;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadPrayerTimes();
  }

  Future<void> _loadPrayerTimes() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Try to get prayer times using the enhanced Adhan package first
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      final adhanTimes = await PrayerTimesService.getPrayerTimesWithAdhan(
        position.latitude,
        position.longitude,
        DateTime.now(),
      );

      // Fallback to original method if Adhan fails
      final prayerTimes = adhanTimes ?? await PrayerTimesService.getAdjustedPrayerTimes();
      
      setState(() {
        _prayerTimes = prayerTimes;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _showCalculationComparison() async {
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      final enhancedTimes = await PrayerTimesService.getEnhancedPrayerTimes(
        position.latitude,
        position.longitude,
        DateTime.now(),
      );

      final comparison = PrayerTimesService.comparePrayerTimes(enhancedTimes);

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('مقارنة طرق حساب أوقات الصلاة'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'مقارنة بين حزمة Adhan وطريقة الحساب الأصلية:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  if (comparison.isNotEmpty) ...[
                    for (final entry in comparison.entries)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Text('${entry.key}: ${entry.value}'),
                      ),
                  ] else
                    const Text('لا توجد بيانات للمقارنة'),
                  const SizedBox(height: 16),
                  const Text(
                    'ملاحظة: حزمة Adhan تستخدم حسابات فلكية دقيقة من كتاب "Astronomical Algorithms" لجان ميوس.',
                    style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في المقارنة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Prayer Times'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.compare),
            onPressed: _showCalculationComparison,
            tooltip: 'مقارنة طرق الحساب',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showPrayerSettings,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPrayerTimes,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryGreen,
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading prayer times',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPrayerTimes,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_prayerTimes == null) {
      return const Center(
        child: Text('No prayer times available'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildLocationCard(),
          const SizedBox(height: 20),
          _buildCurrentPrayerCard(),
          const SizedBox(height: 20),
          _buildPrayerTimesList(),
        ],
      ),
    );
  }

  Widget _buildLocationCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.location_on,
              color: AppTheme.primaryGreen,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _prayerTimes!.location,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _prayerTimes!.date,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentPrayerCard() {
    final currentPrayer = _getCurrentPrayer();
    final nextPrayer = _getNextPrayer();

    return Card(
      elevation: 4,
      color: AppTheme.primaryGreen,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  MdiIcons.clockOutline,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Prayer',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        currentPrayer,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (nextPrayer != null) ...[
              const SizedBox(height: 12),
              const Divider(color: Colors.white54),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    MdiIcons.clockTimeEightOutline,
                    color: Colors.white70,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Next: $nextPrayer',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPrayerTimesList() {
    final prayers = [
      ('Fajr', _prayerTimes!.fajr, MdiIcons.weatherSunset),
      ('Sunrise', _prayerTimes!.sunrise, MdiIcons.weatherSunny),
      ('Dhuhr', _prayerTimes!.dhuhr, MdiIcons.weatherSunny),
      ('Asr', _prayerTimes!.asr, MdiIcons.weatherPartlyCloudy),
      ('Maghrib', _prayerTimes!.maghrib, MdiIcons.weatherSunset),
      ('Isha', _prayerTimes!.isha, MdiIcons.weatherNight),
    ];

    return Card(
      elevation: 4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Prayer Times',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...prayers.map((prayer) => _buildPrayerTimeItem(
            prayer.$1,
            prayer.$2,
            prayer.$3,
          )),
        ],
      ),
    );
  }

  Widget _buildPrayerTimeItem(String name, String time, IconData icon) {
    final isCurrentPrayer = _getCurrentPrayer() == name;
    
    return Container(
      decoration: BoxDecoration(
        color: isCurrentPrayer ? AppTheme.primaryGreen.withValues(alpha: 0.1) : null,
        border: Border(
          left: BorderSide(
            color: isCurrentPrayer ? AppTheme.primaryGreen : Colors.transparent,
            width: 4,
          ),
        ),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isCurrentPrayer ? AppTheme.primaryGreen : Colors.grey[600],
        ),
        title: Text(
          name,
          style: TextStyle(
            fontWeight: isCurrentPrayer ? FontWeight.bold : FontWeight.normal,
            color: isCurrentPrayer ? AppTheme.primaryGreen : null,
          ),
        ),
        trailing: Text(
          time,
          style: TextStyle(
            fontSize: 16,
            fontWeight: isCurrentPrayer ? FontWeight.bold : FontWeight.normal,
            color: isCurrentPrayer ? AppTheme.primaryGreen : null,
          ),
        ),
      ),
    );
  }

  String _getCurrentPrayer() {
    // Simple implementation - in real app, calculate based on current time
    final now = DateTime.now();
    final hour = now.hour;
    
    if (hour < 6) return 'Isha';
    if (hour < 7) return 'Fajr';
    if (hour < 12) return 'Sunrise';
    if (hour < 15) return 'Dhuhr';
    if (hour < 18) return 'Asr';
    if (hour < 20) return 'Maghrib';
    return 'Isha';
  }

  String? _getNextPrayer() {
    final current = _getCurrentPrayer();
    switch (current) {
      case 'Fajr': return 'Dhuhr';
      case 'Dhuhr': return 'Asr';
      case 'Asr': return 'Maghrib';
      case 'Maghrib': return 'Isha';
      case 'Isha': return 'Fajr';
      default: return null;
    }
  }

  void _showPrayerSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PrayerTimesSettingsScreen(
          onSettingsChanged: _loadPrayerTimes,
        ),
      ),
    );
  }
}
