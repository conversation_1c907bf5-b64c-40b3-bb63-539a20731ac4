import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/athkar_provider.dart';
import '../providers/auth_provider.dart';
import '../models/athkar_models.dart';
import '../widgets/color_picker_widget.dart';
import '../theme/app_theme.dart';
import 'package:uuid/uuid.dart';

class CreateRoutineScreen extends StatefulWidget {
  final AthkarRoutine? routine; // For editing existing routine

  const CreateRoutineScreen({super.key, this.routine});

  @override
  State<CreateRoutineScreen> createState() => _CreateRoutineScreenState();
}

class _CreateRoutineScreenState extends State<CreateRoutineScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _uuid = const Uuid();

  String? _selectedCategoryId;
  int _estimatedDuration = 5;
  bool _isPublic = false;
  Color _selectedColor = AppTheme.primaryGreen;
  List<AthkarStepData> _steps = [];

  bool get _isEditing => widget.routine != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadExistingRoutine();
    } else {
      _addNewStep();
    }
  }

  void _loadExistingRoutine() {
    final routine = widget.routine!;
    _titleController.text = routine.title;
    _descriptionController.text = routine.description ?? '';
    _selectedCategoryId = routine.categoryId;
    _estimatedDuration = routine.estimatedDuration ?? 5;
    _isPublic = routine.isPublic;

    // Load color from hex string
    if (routine.colorHex != null) {
      try {
        _selectedColor = Color(int.parse('FF${routine.colorHex!}', radix: 16));
      } catch (e) {
        _selectedColor = AppTheme.primaryGreen;
      }
    }

    if (routine.steps != null) {
      _steps = routine.steps!.map((step) => AthkarStepData.fromStep(step)).toList();
    }

    if (_steps.isEmpty) {
      _addNewStep();
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _addNewStep() {
    setState(() {
      _steps.add(AthkarStepData(
        stepOrder: _steps.length + 1,
        arabicText: _steps.isEmpty ? 'سُبْحَانَ اللهِ' : '', // Add default for first step
        transliteration: _steps.isEmpty ? 'Subhan Allah' : '',
        translation: _steps.isEmpty ? 'Glory be to Allah' : '',
        targetCount: _steps.isEmpty ? 33 : 1, // Common count for first step
      ));
    });
  }

  void _removeStep(int index) {
    if (_steps.length > 1) {
      setState(() {
        _steps.removeAt(index);
        // Reorder remaining steps
        for (int i = 0; i < _steps.length; i++) {
          _steps[i].stepOrder = i + 1;
        }
      });
    }
  }

  void _reorderSteps(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final step = _steps.removeAt(oldIndex);
      _steps.insert(newIndex, step);
      
      // Update step orders
      for (int i = 0; i < _steps.length; i++) {
        _steps[i].stepOrder = i + 1;
      }
    });
  }

  Future<void> _saveRoutine() async {
    debugPrint('=== _saveRoutine called ===');

    if (!_formKey.currentState!.validate()) {
      debugPrint('Form validation failed');
      return;
    }

    if (_steps.isEmpty) {
      debugPrint('Steps validation failed: no steps');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one step to your routine')),
      );
      return;
    }

    // Check for empty Arabic text in any step
    final emptySteps = _steps.where((step) => step.arabicText.trim().isEmpty).toList();
    if (emptySteps.isNotEmpty) {
      debugPrint('Steps validation failed: ${emptySteps.length} steps have missing Arabic text');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please fill in Arabic text for all steps (${emptySteps.length} step${emptySteps.length > 1 ? 's' : ''} missing)'),
          duration: const Duration(seconds: 4),
        ),
      );
      return;
    }

    debugPrint('Validation passed, proceeding with save...');

    final authProvider = context.read<AuthProvider>();
    final athkarProvider = context.read<AthkarProvider>();

    try {
      debugPrint('Creating routine object...');
      final now = DateTime.now();
      final routineId = _isEditing ? widget.routine!.id : _uuid.v4();
      debugPrint('Routine ID: $routineId');

      final routine = AthkarRoutine(
        id: routineId,
        userId: authProvider.user?.id,
        categoryId: _selectedCategoryId,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null : _descriptionController.text.trim(),
        isPublic: _isPublic,
        isFavorite: _isEditing ? widget.routine!.isFavorite : false,
        totalSteps: _steps.length,
        estimatedDuration: _estimatedDuration,
        colorHex: _selectedColor.toARGB32().toRadixString(16).substring(2).toUpperCase(),
        createdAt: _isEditing ? widget.routine!.createdAt : now,
        updatedAt: now,
      );

      String? savedRoutineId;
      if (_isEditing) {
        debugPrint('Updating existing routine...');
        await athkarProvider.updateRoutine(routine);
        savedRoutineId = routineId;
      } else {
        debugPrint('Adding new routine...');
        savedRoutineId = await athkarProvider.addRoutine(routine);
        debugPrint('Saved routine ID: $savedRoutineId');
      }

      if (savedRoutineId != null) {
        debugPrint('Saving ${_steps.length} steps...');
        // Save steps
        for (final stepData in _steps) {
          final step = AthkarStep(
            id: stepData.id ?? _uuid.v4(),
            routineId: savedRoutineId,
            stepOrder: stepData.stepOrder,
            arabicText: stepData.arabicText,
            transliteration: stepData.transliteration.isEmpty ? null : stepData.transliteration,
            translation: stepData.translation.isEmpty ? null : stepData.translation,
            targetCount: stepData.targetCount,
            audioUrl: null,
            createdAt: now,
            updatedAt: now,
          );

          if (_isEditing && stepData.id != null) {
            await athkarProvider.updateStep(step);
          } else {
            await athkarProvider.addStep(step);
          }
        }

        if (mounted) {
          Navigator.pop(context, true);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_isEditing ? 'Routine updated successfully' : 'Routine created successfully'),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving routine: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Routine' : 'Create Routine'),
        actions: [
          TextButton(
            onPressed: () {
              debugPrint('Save button pressed in AppBar');
              _saveRoutine();
            },
            child: Text(
              _isEditing ? 'Update' : 'Save',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicInfo(),
                    const SizedBox(height: 24),
                    _buildStepsSection(),
                  ],
                ),
              ),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Title
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Routine Title *',
                hintText: 'e.g., Morning Athkar',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a title';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // Description
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Brief description of this routine',
              ),
              maxLines: 3,
            ),
            
            const SizedBox(height: 16),
            
            // Category
            Consumer<AthkarProvider>(
              builder: (context, athkarProvider, child) {
                return DropdownButtonFormField<String>(
                  value: _selectedCategoryId,
                  decoration: const InputDecoration(
                    labelText: 'Category',
                  ),
                  items: athkarProvider.categories.map((category) {
                    return DropdownMenuItem(
                      value: category.id,
                      child: Text(category.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategoryId = value;
                    });
                  },
                );
              },
            ),

            const SizedBox(height: 16),

            // Color Picker
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[400]!),
                borderRadius: BorderRadius.circular(4),
              ),
              child: ListTile(
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _selectedColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                ),
                title: const Text('Routine Color'),
                subtitle: Text('Tap to change color'),
                trailing: const Icon(Icons.color_lens),
                onTap: () async {
                  final color = await showColorPickerDialog(
                    context,
                    initialColor: _selectedColor,
                    title: 'Choose Routine Color',
                    presetColors: [
                      AppTheme.primaryGreen,
                      AppTheme.lightGreen,
                      AppTheme.accentGold,
                      AppTheme.lightGold,
                      AppTheme.darkBlue,
                      AppTheme.lightBlue,
                      const Color(0xFF8E24AA), // Purple
                      const Color(0xFFD32F2F), // Red
                      const Color(0xFFFF6F00), // Orange
                      const Color(0xFF388E3C), // Green
                      const Color(0xFF1976D2), // Blue
                      const Color(0xFF7B1FA2), // Deep Purple
                      const Color(0xFF5D4037), // Brown
                      const Color(0xFF455A64), // Blue Grey
                    ],
                  );
                  if (color != null) {
                    setState(() {
                      _selectedColor = color;
                    });
                  }
                },
              ),
            ),

            const SizedBox(height: 16),

            // Duration
            Row(
              children: [
                const Text('Estimated Duration: '),
                Expanded(
                  child: Slider(
                    value: _estimatedDuration.toDouble(),
                    min: 1,
                    max: 60,
                    divisions: 59,
                    label: '$_estimatedDuration min',
                    onChanged: (value) {
                      setState(() {
                        _estimatedDuration = value.round();
                      });
                    },
                  ),
                ),
                Text('$_estimatedDuration min'),
              ],
            ),
            
            // Public toggle
            SwitchListTile(
              title: const Text('Make Public'),
              subtitle: const Text('Allow other users to see and use this routine'),
              value: _isPublic,
              onChanged: (value) {
                setState(() {
                  _isPublic = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Athkar Steps',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: _addNewStep,
                  icon: const Icon(Icons.add),
                  tooltip: 'Add Step',
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            ReorderableListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _steps.length,
              onReorder: _reorderSteps,
              itemBuilder: (context, index) {
                return _buildStepCard(index);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepCard(int index) {
    final step = _steps[index];
    
    return Card(
      key: ValueKey(step.stepOrder),
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(MdiIcons.reorderHorizontal, color: Colors.grey),
                const SizedBox(width: 8),
                Text(
                  'Step ${step.stepOrder}',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                if (_steps.length > 1)
                  IconButton(
                    onPressed: () => _removeStep(index),
                    icon: const Icon(Icons.delete, color: Colors.red),
                    iconSize: 20,
                  ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Arabic text
            TextFormField(
              initialValue: step.arabicText,
              decoration: const InputDecoration(
                labelText: 'Arabic Text *',
                hintText: 'Enter the dhikr in Arabic',
              ),
              textDirection: TextDirection.rtl,
              onChanged: (value) {
                step.arabicText = value;
              },
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Arabic text is required';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 12),
            
            // Transliteration
            TextFormField(
              initialValue: step.transliteration,
              decoration: const InputDecoration(
                labelText: 'Transliteration',
                hintText: 'Phonetic pronunciation',
              ),
              onChanged: (value) {
                step.transliteration = value;
              },
            ),
            
            const SizedBox(height: 12),
            
            // Translation
            TextFormField(
              initialValue: step.translation,
              decoration: const InputDecoration(
                labelText: 'Translation',
                hintText: 'English translation',
              ),
              onChanged: (value) {
                step.translation = value;
              },
            ),
            
            const SizedBox(height: 12),
            
            // Target count
            Row(
              children: [
                const Text('Repeat: '),
                Expanded(
                  child: Slider(
                    value: step.targetCount.toDouble(),
                    min: 1,
                    max: 1000,
                    divisions: 999,
                    label: '${step.targetCount}x',
                    onChanged: (value) {
                      setState(() {
                        step.targetCount = value.round();
                      });
                    },
                  ),
                ),
                Text('${step.targetCount}x'),
              ],
            ),

            const SizedBox(height: 16),

            // Individual Step Color Picker
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[400]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListTile(
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: step.colorHex != null
                        ? Color(int.parse('FF${step.colorHex!}', radix: 16))
                        : AppTheme.primaryGreen,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                ),
                title: const Text('Step Color'),
                subtitle: const Text('Tap to change background color'),
                trailing: const Icon(Icons.palette),
                onTap: () async {
                  final currentColor = step.colorHex != null
                      ? Color(int.parse('FF${step.colorHex!}', radix: 16))
                      : AppTheme.primaryGreen;

                  final color = await showColorPickerDialog(
                    context,
                    initialColor: currentColor,
                    title: 'Choose Step Background Color',
                    presetColors: [
                      AppTheme.primaryGreen,
                      AppTheme.accentGold,
                      Colors.blue,
                      Colors.purple,
                      Colors.teal,
                      Colors.orange,
                      Colors.pink,
                      Colors.indigo,
                      Colors.cyan,
                      Colors.amber,
                      Colors.deepOrange,
                      Colors.lightGreen,
                    ],
                  );

                  if (color != null) {
                    setState(() {
                      step.colorHex = color.value.toRadixString(16).substring(2);
                    });
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                debugPrint('Create/Update button pressed at bottom');
                _saveRoutine();
              },
              child: Text(_isEditing ? 'Update Routine' : 'Create Routine'),
            ),
          ),
        ],
      ),
    );
  }
}

class AthkarStepData {
  String? id;
  int stepOrder;
  String arabicText;
  String transliteration;
  String translation;
  int targetCount;
  String? colorHex;

  AthkarStepData({
    this.id,
    required this.stepOrder,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.targetCount,
    this.colorHex,
  });

  factory AthkarStepData.fromStep(AthkarStep step) {
    return AthkarStepData(
      id: step.id,
      stepOrder: step.stepOrder,
      arabicText: step.arabicText,
      transliteration: step.transliteration ?? '',
      translation: step.translation ?? '',
      targetCount: step.targetCount,
      colorHex: step.colorHex,
    );
  }
}
