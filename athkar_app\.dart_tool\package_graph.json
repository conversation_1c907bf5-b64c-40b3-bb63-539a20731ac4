{"roots": ["athkar_app"], "packages": [{"name": "athkar_app", "version": "1.0.0+1", "dependencies": ["app_settings", "audioplayers", "battery_plus", "cached_network_image", "camera", "connectivity_plus", "crypto", "csv", "cupertino_icons", "device_info_plus", "excel", "expandable", "file_picker", "firebase_analytics", "firebase_core", "firebase_crashlytics", "firebase_messaging", "fl_chart", "flutter", "flutter_html", "flutter_keyboard_visibility", "flutter_launcher_icons", "flutter_local_notifications", "flutter_markdown", "flutter_native_splash", "flutter_overlay_window", "flutter_screenutil", "flutter_secure_storage", "flutter_staggered_animations", "flutter_svg", "geolocator", "google_sign_in", "http", "image", "image_picker", "in_app_purchase", "infinite_scroll_pagination", "intl", "json_annotation", "local_auth", "lottie", "material_design_icons_flutter", "modal_bottom_sheet", "network_info_plus", "package_info_plus", "path", "path_provider", "pdf", "permission_handler", "provider", "pull_to_refresh", "qr_flutter", "sensors_plus", "share_plus", "shared_preferences", "shimmer", "sign_in_with_apple", "sliding_up_panel", "smooth_page_indicator", "sqflite", "sqflite_common_ffi", "supabase_flutter", "table_calendar", "timezone", "url_launcher", "uuid", "vibration", "video_player", "wakelock_plus", "webview_flutter", "workmanager"], "devDependencies": ["build_runner", "flutter_lints", "flutter_test", "json_serializable"]}, {"name": "json_serializable", "version": "6.9.5", "dependencies": ["analyzer", "async", "build", "build_config", "collection", "dart_style", "json_annotation", "meta", "path", "pub_semver", "pubspec_parse", "source_gen", "source_helper"]}, {"name": "build_runner", "version": "2.5.4", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web", "web_socket_channel", "yaml"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "flutter_native_splash", "version": "2.4.4", "dependencies": ["ansicolor", "args", "flutter", "flutter_web_plugins", "html", "image", "meta", "path", "universal_io", "xml", "yaml"]}, {"name": "flutter_launcher_icons", "version": "0.13.1", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "app_settings", "version": "5.2.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "network_info_plus", "version": "5.0.3", "dependencies": ["collection", "ffi", "flutter", "flutter_web_plugins", "meta", "network_info_plus_platform_interface", "nm", "win32"]}, {"name": "battery_plus", "version": "6.2.2", "dependencies": ["battery_plus_platform_interface", "flutter", "flutter_web_plugins", "meta", "upower", "web"]}, {"name": "wakelock_plus", "version": "1.3.2", "dependencies": ["dbus", "flutter", "flutter_web_plugins", "meta", "package_info_plus", "wakelock_plus_platform_interface", "web", "win32"]}, {"name": "workmanager", "version": "0.8.0", "dependencies": ["flutter", "workmanager_android", "workmanager_apple", "workmanager_platform_interface"]}, {"name": "flutter_keyboard_visibility", "version": "6.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_platform_interface", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows", "meta"]}, {"name": "flutter_screenutil", "version": "5.9.3", "dependencies": ["flutter"]}, {"name": "vibration", "version": "3.1.3", "dependencies": ["flutter", "plugin_platform_interface", "vibration_platform_interface"]}, {"name": "flutter_staggered_animations", "version": "1.1.1", "dependencies": ["flutter"]}, {"name": "modal_bottom_sheet", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "sliding_up_panel", "version": "2.0.0+1", "dependencies": ["flutter"]}, {"name": "expandable", "version": "5.0.1", "dependencies": ["flutter"]}, {"name": "smooth_page_indicator", "version": "1.2.1", "dependencies": ["flutter"]}, {"name": "infinite_scroll_pagination", "version": "4.1.0", "dependencies": ["flutter", "flutter_staggered_grid_view", "sliver_tools"]}, {"name": "pull_to_refresh", "version": "2.0.0", "dependencies": ["flutter"]}, {"name": "shimmer", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "flutter_svg", "version": "2.2.0", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "cached_network_image", "version": "3.4.1", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "flutter_html", "version": "3.0.0", "dependencies": ["collection", "csslib", "flutter", "html", "list_counter"]}, {"name": "flutter_markdown", "version": "0.7.7+1", "dependencies": ["flutter", "markdown", "meta", "path"]}, {"name": "webview_flutter", "version": "4.13.0", "dependencies": ["flutter", "webview_flutter_android", "webview_flutter_platform_interface", "webview_flutter_wkwebview"]}, {"name": "sign_in_with_apple", "version": "6.1.4", "dependencies": ["flutter", "meta", "sign_in_with_apple_platform_interface", "sign_in_with_apple_web"]}, {"name": "google_sign_in", "version": "6.3.0", "dependencies": ["flutter", "google_sign_in_android", "google_sign_in_ios", "google_sign_in_platform_interface", "google_sign_in_web"]}, {"name": "firebase_messaging", "version": "15.2.9", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_messaging_platform_interface", "firebase_messaging_web", "flutter", "meta"]}, {"name": "firebase_crashlytics", "version": "4.3.9", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_crashlytics_platform_interface", "flutter", "stack_trace"]}, {"name": "firebase_analytics", "version": "11.5.2", "dependencies": ["firebase_analytics_platform_interface", "firebase_analytics_web", "firebase_core", "firebase_core_platform_interface", "flutter"]}, {"name": "firebase_core", "version": "3.15.1", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "in_app_purchase", "version": "3.2.3", "dependencies": ["flutter", "in_app_purchase_android", "in_app_purchase_platform_interface", "in_app_purchase_storekit"]}, {"name": "flutter_secure_storage", "version": "9.2.4", "dependencies": ["flutter", "flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_platform_interface", "flutter_secure_storage_web", "flutter_secure_storage_windows", "meta"]}, {"name": "local_auth", "version": "2.3.0", "dependencies": ["flutter", "local_auth_android", "local_auth_darwin", "local_auth_platform_interface", "local_auth_windows"]}, {"name": "qr_flutter", "version": "4.1.0", "dependencies": ["flutter", "qr"]}, {"name": "csv", "version": "6.0.0", "dependencies": []}, {"name": "excel", "version": "4.0.6", "dependencies": ["archive", "collection", "equatable", "xml"]}, {"name": "pdf", "version": "3.11.3", "dependencies": ["archive", "barcode", "bidi", "crypto", "image", "meta", "path_parsing", "vector_math", "xml"]}, {"name": "file_picker", "version": "8.3.7", "dependencies": ["cross_file", "ffi", "flutter", "flutter_plugin_android_lifecycle", "flutter_web_plugins", "path", "plugin_platform_interface", "web", "win32"]}, {"name": "image_picker", "version": "1.1.2", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "camera", "version": "0.11.2", "dependencies": ["camera_android_camerax", "camera_avfoundation", "camera_platform_interface", "camera_web", "flutter", "flutter_plugin_android_lifecycle"]}, {"name": "video_player", "version": "2.10.0", "dependencies": ["flutter", "html", "video_player_android", "video_player_avfoundation", "video_player_platform_interface", "video_player_web"]}, {"name": "audioplayers", "version": "6.5.0", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_platform_interface", "audioplayers_web", "audioplayers_windows", "file", "flutter", "http", "meta", "path_provider", "synchronized", "uuid"]}, {"name": "image", "version": "4.3.0", "dependencies": ["archive", "meta", "xml"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "package_info_plus", "version": "8.3.0", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "device_info_plus", "version": "10.1.2", "dependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "web", "win32", "win32_registry"]}, {"name": "url_launcher", "version": "6.3.2", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "sensors_plus", "version": "5.0.1", "dependencies": ["flutter", "flutter_web_plugins", "sensors_plus_platform_interface"]}, {"name": "geolocator", "version": "12.0.0", "dependencies": ["flutter", "geolocator_android", "geolocator_apple", "geolocator_platform_interface", "geolocator_web", "geolocator_windows"]}, {"name": "table_calendar", "version": "3.1.3", "dependencies": ["flutter", "intl", "simple_gesture_detector"]}, {"name": "fl_chart", "version": "0.68.0", "dependencies": ["equatable", "flutter"]}, {"name": "lottie", "version": "3.3.0", "dependencies": ["archive", "flutter", "http", "path", "vector_math"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "intl", "version": "0.19.0", "dependencies": ["clock", "meta", "path"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "share_plus", "version": "10.1.4", "dependencies": ["cross_file", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "mime", "share_plus_platform_interface", "url_launcher_linux", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows", "web", "win32"]}, {"name": "connectivity_plus", "version": "6.1.4", "dependencies": ["collection", "connectivity_plus_platform_interface", "flutter", "flutter_web_plugins", "meta", "nm", "web"]}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "flutter_overlay_window", "version": "0.5.0", "dependencies": ["flutter"]}, {"name": "timezone", "version": "0.9.4", "dependencies": ["path"]}, {"name": "flutter_local_notifications", "version": "17.2.4", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "timezone"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "sqflite_common_ffi", "version": "2.3.6", "dependencies": ["meta", "path", "sqflite_common", "sqlite3", "synchronized"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "supabase_flutter", "version": "2.9.1", "dependencies": ["app_links", "async", "crypto", "flutter", "http", "logging", "meta", "path_provider", "shared_preferences", "supabase", "url_launcher", "web"]}, {"name": "material_design_icons_flutter", "version": "7.0.7296", "dependencies": ["flutter"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "source_helper", "version": "1.3.6", "dependencies": ["analyzer", "source_gen"]}, {"name": "source_gen", "version": "2.0.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "pub_semver", "source_span", "yaml"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "dart_style", "version": "3.1.0", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "build", "version": "2.5.4", "dependencies": ["analyzer", "async", "build_runner_core", "built_collection", "built_value", "convert", "crypto", "glob", "graphs", "logging", "meta", "package_config", "path", "pool"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "analyzer", "version": "7.5.6", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "watcher", "version": "1.1.2", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "build_runner_core", "version": "9.1.2", "dependencies": ["analyzer", "async", "build", "build_config", "build_resolvers", "build_runner", "built_collection", "built_value", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_resolvers", "version": "2.5.4", "dependencies": ["analyzer", "async", "build", "build_runner_core", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "ansicolor", "version": "2.0.3", "dependencies": []}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "universal_io", "version": "2.2.2", "dependencies": ["collection", "meta", "typed_data"]}, {"name": "html", "version": "0.15.6", "dependencies": ["csslib", "source_span"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "network_info_plus_platform_interface", "version": "2.0.2", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "upower", "version": "0.7.0", "dependencies": ["dbus"]}, {"name": "battery_plus_platform_interface", "version": "2.0.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "wakelock_plus_platform_interface", "version": "1.2.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "workmanager_apple", "version": "0.8.0", "dependencies": ["flutter", "workmanager_platform_interface"]}, {"name": "workmanager_android", "version": "0.8.0", "dependencies": ["flutter", "workmanager_platform_interface"]}, {"name": "workmanager_platform_interface", "version": "0.8.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "flutter_keyboard_visibility_windows", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_web", "version": "2.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface", "flutter_web_plugins"]}, {"name": "flutter_keyboard_visibility_macos", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_linux", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_platform_interface", "version": "2.0.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "vibration_platform_interface", "version": "0.1.0", "dependencies": ["device_info_plus", "flutter", "plugin_platform_interface"]}, {"name": "sliver_tools", "version": "0.2.12", "dependencies": ["flutter"]}, {"name": "flutter_staggered_grid_view", "version": "0.7.0", "dependencies": ["flutter"]}, {"name": "vector_graphics_compiler", "version": "1.1.17", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "vector_graphics_codec", "version": "1.1.13", "dependencies": []}, {"name": "vector_graphics", "version": "1.1.19", "dependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "octo_image", "version": "2.1.0", "dependencies": ["flutter"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "cached_network_image_web", "version": "1.3.1", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager", "web"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "list_counter", "version": "1.0.2", "dependencies": []}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "markdown", "version": "7.3.0", "dependencies": ["args", "meta"]}, {"name": "webview_flutter_wkwebview", "version": "3.22.0", "dependencies": ["flutter", "meta", "path", "webview_flutter_platform_interface"]}, {"name": "webview_flutter_platform_interface", "version": "2.13.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "webview_flutter_android", "version": "4.7.0", "dependencies": ["flutter", "meta", "webview_flutter_platform_interface"]}, {"name": "sign_in_with_apple_web", "version": "2.1.1", "dependencies": ["flutter", "flutter_web_plugins", "sign_in_with_apple_platform_interface"]}, {"name": "sign_in_with_apple_platform_interface", "version": "1.1.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "google_sign_in_web", "version": "0.12.4+4", "dependencies": ["flutter", "flutter_web_plugins", "google_identity_services_web", "google_sign_in_platform_interface", "http", "web"]}, {"name": "google_sign_in_platform_interface", "version": "2.5.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "google_sign_in_ios", "version": "5.9.0", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "google_sign_in_android", "version": "6.2.1", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "firebase_messaging_web", "version": "3.10.9", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_messaging_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_messaging_platform_interface", "version": "4.6.9", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_platform_interface", "version": "6.0.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "firebase_crashlytics_platform_interface", "version": "3.8.9", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_analytics_web", "version": "0.5.10+15", "dependencies": ["_flutterfire_internals", "firebase_analytics_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins"]}, {"name": "firebase_analytics_platform_interface", "version": "4.4.2", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_web", "version": "2.24.1", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "in_app_purchase_storekit", "version": "0.4.3", "dependencies": ["collection", "flutter", "in_app_purchase_platform_interface", "json_annotation"]}, {"name": "in_app_purchase_platform_interface", "version": "1.4.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "in_app_purchase_android", "version": "0.4.0+2", "dependencies": ["collection", "flutter", "in_app_purchase_platform_interface"]}, {"name": "flutter_secure_storage_windows", "version": "3.1.2", "dependencies": ["ffi", "flutter", "flutter_secure_storage_platform_interface", "path", "path_provider", "win32"]}, {"name": "flutter_secure_storage_web", "version": "1.2.1", "dependencies": ["flutter", "flutter_secure_storage_platform_interface", "flutter_web_plugins", "js"]}, {"name": "flutter_secure_storage_platform_interface", "version": "1.1.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_secure_storage_macos", "version": "3.1.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "flutter_secure_storage_linux", "version": "1.2.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "local_auth_windows", "version": "1.0.11", "dependencies": ["flutter", "local_auth_platform_interface"]}, {"name": "local_auth_platform_interface", "version": "1.0.10", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "local_auth_darwin", "version": "1.5.0", "dependencies": ["flutter", "intl", "local_auth_platform_interface"]}, {"name": "local_auth_android", "version": "1.0.49", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "intl", "local_auth_platform_interface"]}, {"name": "qr", "version": "3.0.2", "dependencies": ["meta"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "archive", "version": "3.6.1", "dependencies": ["crypto", "path"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "bidi", "version": "2.0.13", "dependencies": []}, {"name": "barcode", "version": "2.2.9", "dependencies": ["meta", "qr"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.28", "dependencies": ["flutter"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.1", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.1+2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.12+2", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.0.6", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.12+23", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "camera_web", "version": "0.3.5", "dependencies": ["camera_platform_interface", "flutter", "flutter_web_plugins", "stream_transform", "web"]}, {"name": "camera_platform_interface", "version": "2.10.0", "dependencies": ["cross_file", "flutter", "plugin_platform_interface", "stream_transform"]}, {"name": "camera_avfoundation", "version": "0.9.20+2", "dependencies": ["camera_platform_interface", "flutter", "stream_transform"]}, {"name": "camera_android_camerax", "version": "0.6.19", "dependencies": ["async", "camera_platform_interface", "flutter", "meta", "stream_transform"]}, {"name": "video_player_web", "version": "2.4.0", "dependencies": ["flutter", "flutter_web_plugins", "video_player_platform_interface", "web"]}, {"name": "video_player_platform_interface", "version": "6.4.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "video_player_avfoundation", "version": "2.8.0", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "video_player_android", "version": "2.8.7", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "audioplayers_windows", "version": "4.2.1", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_web", "version": "5.1.1", "dependencies": ["audioplayers_platform_interface", "flutter", "flutter_web_plugins", "web"]}, {"name": "audioplayers_platform_interface", "version": "7.1.1", "dependencies": ["collection", "flutter", "meta", "plugin_platform_interface"]}, {"name": "audioplayers_linux", "version": "4.2.1", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_darwin", "version": "6.3.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_android", "version": "5.2.1", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "win32_registry", "version": "1.1.5", "dependencies": ["ffi", "win32"]}, {"name": "device_info_plus_platform_interface", "version": "7.0.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.16", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "sensors_plus_platform_interface", "version": "1.2.0", "dependencies": ["flutter", "logging", "meta", "plugin_platform_interface"]}, {"name": "geolocator_windows", "version": "0.2.5", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_web", "version": "4.1.3", "dependencies": ["flutter", "flutter_web_plugins", "geolocator_platform_interface", "web"]}, {"name": "geolocator_apple", "version": "2.3.13", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_android", "version": "4.6.2", "dependencies": ["flutter", "geolocator_platform_interface", "meta", "uuid"]}, {"name": "geolocator_platform_interface", "version": "4.2.6", "dependencies": ["flutter", "meta", "plugin_platform_interface", "vector_math"]}, {"name": "simple_gesture_detector", "version": "0.2.1", "dependencies": ["flutter"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "share_plus_platform_interface", "version": "5.0.2", "dependencies": ["cross_file", "flutter", "meta", "mime", "path_provider", "plugin_platform_interface", "uuid"]}, {"name": "connectivity_plus_platform_interface", "version": "2.0.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "flutter_local_notifications_platform_interface", "version": "7.2.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_local_notifications_linux", "version": "4.0.1", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqlite3", "version": "2.7.6", "dependencies": ["collection", "ffi", "meta", "path", "typed_data", "web"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "supabase", "version": "2.8.0", "dependencies": ["functions_client", "gotrue", "http", "logging", "postgrest", "realtime_client", "rxdart", "storage_client", "yet_another_json_isolate"]}, {"name": "app_links", "version": "6.4.0", "dependencies": ["app_links_linux", "app_links_platform_interface", "app_links_web", "flutter"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "built_value", "version": "8.10.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "_fe_analyzer_shared", "version": "85.0.0", "dependencies": ["meta"]}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "google_identity_services_web", "version": "0.3.3+1", "dependencies": ["meta", "web"]}, {"name": "_flutterfire_internals", "version": "1.3.58", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "file_selector_macos", "version": "0.9.4+3", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "yet_another_json_isolate", "version": "2.1.0", "dependencies": ["async"]}, {"name": "storage_client", "version": "2.4.0", "dependencies": ["http", "http_parser", "logging", "meta", "mime", "retry"]}, {"name": "realtime_client", "version": "2.5.1", "dependencies": ["collection", "http", "logging", "meta", "web_socket_channel"]}, {"name": "postgrest", "version": "2.4.2", "dependencies": ["http", "logging", "meta", "yet_another_json_isolate"]}, {"name": "gotrue", "version": "2.13.0", "dependencies": ["collection", "crypto", "http", "jwt_decode", "logging", "meta", "retry", "rxdart", "web"]}, {"name": "functions_client", "version": "2.4.3", "dependencies": ["http", "logging", "yet_another_json_isolate"]}, {"name": "app_links_web", "version": "1.0.4", "dependencies": ["app_links_platform_interface", "flutter", "flutter_web_plugins", "web"]}, {"name": "app_links_platform_interface", "version": "2.0.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "app_links_linux", "version": "1.0.3", "dependencies": ["app_links_platform_interface", "flutter", "gtk"]}, {"name": "retry", "version": "3.1.2", "dependencies": []}, {"name": "jwt_decode", "version": "0.3.1", "dependencies": []}, {"name": "gtk", "version": "2.1.0", "dependencies": ["ffi", "flutter", "meta"]}], "configVersion": 1}