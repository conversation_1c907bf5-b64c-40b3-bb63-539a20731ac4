import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quran_khatma_models.dart';
import '../providers/quran_provider.dart';
import '../services/new_supabase_service.dart';

class QuranKhatmaProvider extends ChangeNotifier {
  List<QuranKhatma> _khatmas = [];
  List<QuranMemorization> _memorizations = [];
  bool _isLoaded = false;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<QuranKhatma> get khatmas => List.unmodifiable(_khatmas);
  List<QuranMemorization> get memorizations => List.unmodifiable(_memorizations);
  bool get isLoaded => _isLoaded;
  bool get isLoading => _isLoading;
  String? get error => _error;

  QuranKhatma? get activeKhatma => _khatmas.where((k) => !k.isCompleted).isNotEmpty 
      ? _khatmas.where((k) => !k.isCompleted).first 
      : null;

  /// Initialize provider
  Future<void> initialize() async {
    if (_isLoaded || _isLoading) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _loadKhatmas();
      await _loadMemorizations();
      _isLoaded = true;
      debugPrint('QuranKhatmaProvider initialized successfully');
    } catch (e) {
      _error = 'Failed to load Quran Khatma data: $e';
      debugPrint('Error initializing QuranKhatmaProvider: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Create new Khatma
  Future<QuranKhatma> createKhatma({
    required String name,
    required String arabicName,
    required KhatmaType type,
    required int totalDays,
    KhatmaSettings? settings,
  }) async {
    try {
      final startDate = DateTime.now();
      final endDate = startDate.add(Duration(days: totalDays));
      
      final khatma = QuranKhatma(
        id: 'khatma_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        arabicName: arabicName,
        startDate: startDate,
        targetEndDate: endDate,
        totalDays: totalDays,
        type: type,
        dailyWirds: await _generateDailyWirds(startDate, totalDays),
        settings: settings ?? KhatmaSettings(),
      );

      _khatmas.add(khatma);
      await _saveKhatmas();
      await _syncKhatmaToSupabase(khatma);
      
      notifyListeners();
      debugPrint('Created new Khatma: ${khatma.name}');
      return khatma;
    } catch (e) {
      debugPrint('Error creating Khatma: $e');
      rethrow;
    }
  }

  /// Generate daily wirds for Khatma
  Future<List<DailyWird>> _generateDailyWirds(DateTime startDate, int totalDays) async {
    final quranProvider = QuranProvider();
    final surahs = quranProvider.surahs;
    
    if (surahs.isEmpty) {
      throw Exception('Quran data not loaded');
    }

    final dailyWirds = <DailyWird>[];
    final totalAyahs = surahs.fold<int>(0, (sum, surah) => sum + surah.numberOfAyahs);
    final ayahsPerDay = (totalAyahs / totalDays).ceil();

    int currentAyahIndex = 0;
    
    for (int day = 1; day <= totalDays; day++) {
      final date = startDate.add(Duration(days: day - 1));
      final sections = <WirdSection>[];
      int dayAyahCount = 0;

      while (dayAyahCount < ayahsPerDay && currentAyahIndex < totalAyahs) {
        // Find current surah and ayah
        int ayahSum = 0;
        int surahIndex = 0;
        
        for (int i = 0; i < surahs.length; i++) {
          if (ayahSum + surahs[i].numberOfAyahs > currentAyahIndex) {
            surahIndex = i;
            break;
          }
          ayahSum += surahs[i].numberOfAyahs;
        }

        final surah = surahs[surahIndex];
        final ayahInSurah = currentAyahIndex - ayahSum + 1;
        final remainingInSurah = surah.numberOfAyahs - ayahInSurah + 1;
        final remainingForDay = ayahsPerDay - dayAyahCount;
        
        final ayahsToRead = remainingInSurah < remainingForDay 
            ? remainingInSurah 
            : remainingForDay;

        sections.add(WirdSection(
          surahId: surah.id,
          surahName: surah.englishName,
          surahArabicName: surah.arabicName,
          fromAyah: ayahInSurah,
          toAyah: ayahInSurah + ayahsToRead - 1,
          totalAyahs: ayahsToRead,
        ));

        currentAyahIndex += ayahsToRead;
        dayAyahCount += ayahsToRead;
      }

      dailyWirds.add(DailyWird(
        day: day,
        date: date,
        sections: sections,
      ));
    }

    return dailyWirds;
  }

  /// Mark daily wird as completed
  Future<void> completeDailyWird(String khatmaId, int day) async {
    try {
      final khatmaIndex = _khatmas.indexWhere((k) => k.id == khatmaId);
      if (khatmaIndex == -1) return;

      final khatma = _khatmas[khatmaIndex];
      final wirdIndex = khatma.dailyWirds.indexWhere((w) => w.day == day);
      if (wirdIndex == -1) return;

      final updatedWird = DailyWird(
        day: khatma.dailyWirds[wirdIndex].day,
        date: khatma.dailyWirds[wirdIndex].date,
        sections: khatma.dailyWirds[wirdIndex].sections,
        isCompleted: true,
        completedAt: DateTime.now(),
        readingTimeMinutes: khatma.dailyWirds[wirdIndex].readingTimeMinutes,
        notes: khatma.dailyWirds[wirdIndex].notes,
      );

      final updatedWirds = List<DailyWird>.from(khatma.dailyWirds);
      updatedWirds[wirdIndex] = updatedWird;

      final completedWirds = updatedWirds.where((w) => w.isCompleted).length;
      final progress = completedWirds / updatedWirds.length;
      final isCompleted = progress >= 1.0;

      final updatedKhatma = QuranKhatma(
        id: khatma.id,
        name: khatma.name,
        arabicName: khatma.arabicName,
        startDate: khatma.startDate,
        targetEndDate: khatma.targetEndDate,
        totalDays: khatma.totalDays,
        type: khatma.type,
        dailyWirds: updatedWirds,
        progress: progress,
        isCompleted: isCompleted,
        completedDate: isCompleted ? DateTime.now() : null,
        notes: khatma.notes,
        settings: khatma.settings,
      );

      _khatmas[khatmaIndex] = updatedKhatma;
      await _saveKhatmas();
      await _syncKhatmaToSupabase(updatedKhatma);
      
      notifyListeners();
      debugPrint('Completed daily wird $day for Khatma ${khatma.name}');
    } catch (e) {
      debugPrint('Error completing daily wird: $e');
    }
  }

  /// Create memorization plan
  Future<QuranMemorization> createMemorizationPlan({
    required String name,
    required List<int> surahIds,
    MemorizationSettings? settings,
  }) async {
    try {
      final quranProvider = QuranProvider();
      final sections = <MemorizationSection>[];

      for (final surahId in surahIds) {
        final surah = quranProvider.getSurah(surahId);
        if (surah != null) {
          sections.add(MemorizationSection(
            surahId: surahId,
            surahName: surah.englishName,
            fromAyah: 1,
            toAyah: surah.numberOfAyahs,
          ));
        }
      }

      final memorization = QuranMemorization(
        id: 'memorization_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        sections: sections,
        startDate: DateTime.now(),
        settings: settings ?? MemorizationSettings(),
      );

      _memorizations.add(memorization);
      await _saveMemorizations();
      
      notifyListeners();
      debugPrint('Created memorization plan: $name');
      return memorization;
    } catch (e) {
      debugPrint('Error creating memorization plan: $e');
      rethrow;
    }
  }

  /// Load Khatmas from storage
  Future<void> _loadKhatmas() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final khatmasJson = prefs.getStringList('quran_khatmas') ?? [];
      
      _khatmas = khatmasJson.map((jsonString) {
        try {
          final Map<String, dynamic> data = {};
          final parts = jsonString.split('&');
          for (final part in parts) {
            final keyValue = part.split('=');
            if (keyValue.length == 2) {
              final key = Uri.decodeComponent(keyValue[0]);
              final value = Uri.decodeComponent(keyValue[1]);
              data[key] = value;
            }
          }
          return QuranKhatma.fromJson(data);
        } catch (e) {
          debugPrint('Error parsing Khatma: $e');
          return null;
        }
      }).where((k) => k != null).cast<QuranKhatma>().toList();

      debugPrint('Loaded ${_khatmas.length} Khatmas from storage');
    } catch (e) {
      debugPrint('Error loading Khatmas: $e');
      _khatmas = [];
    }
  }

  /// Save Khatmas to storage
  Future<void> _saveKhatmas() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final khatmasJson = _khatmas.map((khatma) {
        final json = khatma.toJson();
        final parts = <String>[];
        json.forEach((key, value) {
          if (value != null) {
            String valueStr = value.toString();
            parts.add('${Uri.encodeComponent(key)}=${Uri.encodeComponent(valueStr)}');
          }
        });
        return parts.join('&');
      }).toList();

      await prefs.setStringList('quran_khatmas', khatmasJson);
      debugPrint('Saved ${_khatmas.length} Khatmas to storage');
    } catch (e) {
      debugPrint('Error saving Khatmas: $e');
    }
  }

  /// Load memorizations from storage
  Future<void> _loadMemorizations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final memorizationsJson = prefs.getStringList('quran_memorizations') ?? [];
      
      _memorizations = memorizationsJson.map((jsonString) {
        try {
          final Map<String, dynamic> data = {};
          final parts = jsonString.split('&');
          for (final part in parts) {
            final keyValue = part.split('=');
            if (keyValue.length == 2) {
              final key = Uri.decodeComponent(keyValue[0]);
              final value = Uri.decodeComponent(keyValue[1]);
              data[key] = value;
            }
          }
          return QuranMemorization.fromJson(data);
        } catch (e) {
          debugPrint('Error parsing memorization: $e');
          return null;
        }
      }).where((m) => m != null).cast<QuranMemorization>().toList();

      debugPrint('Loaded ${_memorizations.length} memorizations from storage');
    } catch (e) {
      debugPrint('Error loading memorizations: $e');
      _memorizations = [];
    }
  }

  /// Save memorizations to storage
  Future<void> _saveMemorizations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final memorizationsJson = _memorizations.map((memorization) {
        final json = memorization.toJson();
        final parts = <String>[];
        json.forEach((key, value) {
          if (value != null) {
            String valueStr = value.toString();
            parts.add('${Uri.encodeComponent(key)}=${Uri.encodeComponent(valueStr)}');
          }
        });
        return parts.join('&');
      }).toList();

      await prefs.setStringList('quran_memorizations', memorizationsJson);
      debugPrint('Saved ${_memorizations.length} memorizations to storage');
    } catch (e) {
      debugPrint('Error saving memorizations: $e');
    }
  }

  /// Sync Khatma to Supabase
  Future<void> _syncKhatmaToSupabase(QuranKhatma khatma) async {
    try {
      if (!NewSupabaseService.isAuthenticated) return;

      await NewSupabaseService.client.from('quran_khatmas').upsert({
        'id': khatma.id,
        'user_id': NewSupabaseService.client.auth.currentUser?.id,
        'name': khatma.name,
        'arabic_name': khatma.arabicName,
        'start_date': khatma.startDate.toIso8601String(),
        'target_end_date': khatma.targetEndDate.toIso8601String(),
        'total_days': khatma.totalDays,
        'type': khatma.type.index,
        'progress': khatma.progress,
        'is_completed': khatma.isCompleted,
        'completed_date': khatma.completedDate?.toIso8601String(),
        'notes': khatma.notes,
        'updated_at': DateTime.now().toIso8601String(),
      });

      debugPrint('Synced Khatma to Supabase: ${khatma.id}');
    } catch (e) {
      debugPrint('Error syncing Khatma to Supabase: $e');
    }
  }

  /// Get Khatma statistics
  Map<String, dynamic> getKhatmaStatistics() {
    final completed = _khatmas.where((k) => k.isCompleted).length;
    final active = _khatmas.where((k) => !k.isCompleted).length;
    final totalDaysRead = _khatmas.fold<int>(0, (sum, k) => 
        sum + k.dailyWirds.where((w) => w.isCompleted).length);

    return {
      'total_khatmas': _khatmas.length,
      'completed_khatmas': completed,
      'active_khatmas': active,
      'total_days_read': totalDaysRead,
      'average_progress': _khatmas.isNotEmpty 
          ? _khatmas.fold<double>(0, (sum, k) => sum + k.progress) / _khatmas.length
          : 0.0,
    };
  }
}
