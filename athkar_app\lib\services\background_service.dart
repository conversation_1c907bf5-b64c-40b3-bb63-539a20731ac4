import 'package:flutter/material.dart';

class BackgroundService {
  static final BackgroundService _instance = BackgroundService._internal();
  factory BackgroundService() => _instance;
  BackgroundService._internal();

  bool _isRunning = false;
  final Map<String, bool> _services = {};

  Future<bool> isRunning() async {
    return _isRunning;
  }

  Future<int> getActiveServicesCount() async {
    return _services.values.where((isActive) => isActive).length;
  }

  Future<void> initialize() async {
    try {
      _isRunning = true;
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      debugPrint('Error initializing background service: $e');
    }
  }

  Future<void> startNotificationService() async {
    try {
      _services['notification'] = true;
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e) {
      debugPrint('Error starting notification service: $e');
    }
  }

  Future<void> startSyncService() async {
    try {
      _services['sync'] = true;
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e) {
      debugPrint('Error starting sync service: $e');
    }
  }

  Future<void> startPrayerReminderService() async {
    try {
      _services['prayer_reminder'] = true;
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e) {
      debugPrint('Error starting prayer reminder service: $e');
    }
  }

  Future<void> startAthkarReminderService() async {
    try {
      _services['athkar_reminder'] = true;
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e) {
      debugPrint('Error starting athkar reminder service: $e');
    }
  }

  Future<bool> isServiceRunning(String serviceName) async {
    return _services[serviceName] ?? false;
  }

  Future<void> testServicePersistence() async {
    try {
      await Future.delayed(const Duration(seconds: 1));
    } catch (e) {
      debugPrint('Error testing service persistence: $e');
    }
  }

  Future<bool> verifyServicePersistence() async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      return _isRunning;
    } catch (e) {
      return false;
    }
  }

  Future<void> stopService(String serviceName) async {
    try {
      _services[serviceName] = false;
      await Future.delayed(const Duration(milliseconds: 100));
    } catch (e) {
      debugPrint('Error stopping service $serviceName: $e');
    }
  }

  Future<void> stopAllServices() async {
    try {
      _services.clear();
      _isRunning = false;
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e) {
      debugPrint('Error stopping all services: $e');
    }
  }
}
