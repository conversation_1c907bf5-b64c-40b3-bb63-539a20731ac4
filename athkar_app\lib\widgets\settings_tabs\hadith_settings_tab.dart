import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/language_service.dart';
import '../../theme/app_theme.dart';

class HadithSettingsTab extends StatefulWidget {
  const HadithSettingsTab({super.key});

  @override
  State<HadithSettingsTab> createState() => _HadithSettingsTabState();
}

class _HadithSettingsTabState extends State<HadithSettingsTab> {
  final Map<String, bool> _enabledCollections = {
    'bukhari': true,
    'muslim': true,
    'abudawud': true,
    'tirmidhi': true,
    'nasai': true,
    'ibnmajah': true,
  };

  double _arabicFontSize = 16.0;
  double _translationFontSize = 14.0;
  bool _showTranslation = true;
  bool _showNarrator = true;
  bool _showGrading = true;
  bool _showReference = true;
  String _defaultLanguage = 'arabic';
  bool _searchInArabic = true;
  bool _searchInTranslation = true;
  bool _searchInNarrator = true;
  bool _syncFavorites = true;
  bool _backupFavorites = true;
  bool _dailyHadithEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    // Load settings from SharedPreferences
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      _searchInArabic = prefs.getBool('hadith_search_arabic') ?? true;
      _searchInTranslation = prefs.getBool('hadith_search_translation') ?? true;
      _searchInNarrator = prefs.getBool('hadith_search_narrator') ?? true;
      _syncFavorites = prefs.getBool('hadith_sync_favorites') ?? true;
      _backupFavorites = prefs.getBool('hadith_backup_favorites') ?? true;
      _dailyHadithEnabled = prefs.getBool('hadith_daily_enabled') ?? true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Collections Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'مجموعات الأحاديث' : 'Hadith Collections',
            Icons.library_books,
          ),
          const SizedBox(height: 12),
          
          Card(
            elevation: 2,
            child: Column(
              children: [
                _buildCollectionTile('bukhari', languageService.isArabic ? 'صحيح البخاري' : 'Sahih Bukhari'),
                const Divider(height: 1),
                _buildCollectionTile('muslim', languageService.isArabic ? 'صحيح مسلم' : 'Sahih Muslim'),
                const Divider(height: 1),
                _buildCollectionTile('abudawud', languageService.isArabic ? 'سنن أبي داود' : 'Sunan Abu Dawud'),
                const Divider(height: 1),
                _buildCollectionTile('tirmidhi', languageService.isArabic ? 'جامع الترمذي' : 'Jami\' at-Tirmidhi'),
                const Divider(height: 1),
                _buildCollectionTile('nasai', languageService.isArabic ? 'سنن النسائي' : 'Sunan an-Nasai'),
                const Divider(height: 1),
                _buildCollectionTile('ibnmajah', languageService.isArabic ? 'سنن ابن ماجه' : 'Sunan Ibn Majah'),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Display Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات العرض' : 'Display Settings',
            Icons.visibility,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.format_size, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'حجم الخط العربي' : 'Arabic Font Size'),
                  subtitle: Slider(
                    value: _arabicFontSize,
                    min: 12.0,
                    max: 24.0,
                    divisions: 12,
                    activeColor: AppTheme.primaryGreen,
                    label: _arabicFontSize.round().toString(),
                    onChanged: (value) {
                      setState(() {
                        _arabicFontSize = value;
                      });
                    },
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.translate, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'حجم خط الترجمة' : 'Translation Font Size'),
                  subtitle: Slider(
                    value: _translationFontSize,
                    min: 10.0,
                    max: 20.0,
                    divisions: 10,
                    activeColor: AppTheme.primaryGreen,
                    label: _translationFontSize.round().toString(),
                    onChanged: (value) {
                      setState(() {
                        _translationFontSize = value;
                      });
                    },
                  ),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.translate, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'إظهار الترجمة' : 'Show Translation'),
                  subtitle: Text(languageService.isArabic ? 'عرض ترجمة الأحاديث' : 'Display hadith translations'),
                  value: _showTranslation,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _showTranslation = value;
                    });
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.person, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'إظهار الراوي' : 'Show Narrator'),
                  subtitle: Text(languageService.isArabic ? 'عرض اسم راوي الحديث' : 'Display hadith narrator'),
                  value: _showNarrator,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _showNarrator = value;
                    });
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.grade, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'إظهار درجة الحديث' : 'Show Grading'),
                  subtitle: Text(languageService.isArabic ? 'عرض درجة صحة الحديث' : 'Display hadith authenticity grade'),
                  value: _showGrading,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _showGrading = value;
                    });
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.link, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'إظهار المرجع' : 'Show Reference'),
                  subtitle: Text(languageService.isArabic ? 'عرض مرجع الحديث' : 'Display hadith reference'),
                  value: _showReference,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _showReference = value;
                    });
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Search Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات البحث' : 'Search Settings',
            Icons.search,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.language, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'لغة البحث الافتراضية' : 'Default Search Language'),
                  subtitle: Text(_getLanguageName(_defaultLanguage, languageService)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showLanguageDialog(context, languageService),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.search, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'البحث في النص العربي' : 'Search in Arabic Text'),
                  subtitle: Text(languageService.isArabic ? 'تضمين النص العربي في البحث' : 'Include Arabic text in search'),
                  value: _searchInArabic,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    setState(() {
                      _searchInArabic = value;
                    });

                    // Save to SharedPreferences
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('hadith_search_arabic', value);
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.translate, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'البحث في الترجمة' : 'Search in Translation'),
                  subtitle: Text(languageService.isArabic ? 'تضمين الترجمة في البحث' : 'Include translation in search'),
                  value: _searchInTranslation,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    setState(() {
                      _searchInTranslation = value;
                    });

                    // Save to SharedPreferences
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('hadith_search_translation', value);
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.person_search, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'البحث في أسماء الرواة' : 'Search in Narrator Names'),
                  subtitle: Text(languageService.isArabic ? 'تضمين أسماء الرواة في البحث' : 'Include narrator names in search'),
                  value: _searchInNarrator,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    setState(() {
                      _searchInNarrator = value;
                    });

                    // Save to SharedPreferences
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('hadith_search_narrator', value);
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Favorites Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات المفضلة' : 'Favorites Settings',
            Icons.favorite,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.sync, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'مزامنة المفضلة' : 'Sync Favorites'),
                  subtitle: Text(languageService.isArabic ? 'مزامنة المفضلة عبر الأجهزة' : 'Sync favorites across devices'),
                  value: _syncFavorites,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final messenger = ScaffoldMessenger.of(context);
                    final isArabic = languageService.isArabic;

                    setState(() {
                      _syncFavorites = value;
                    });

                    // Save to SharedPreferences
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('hadith_sync_favorites', value);

                    if (mounted) {
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            isArabic
                                ? (value ? 'تم تفعيل مزامنة المفضلة' : 'تم إيقاف مزامنة المفضلة')
                                : (value ? 'Favorites sync enabled' : 'Favorites sync disabled'),
                          ),
                          backgroundColor: AppTheme.primaryGreen,
                        ),
                      );
                    }
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.backup, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'نسخ احتياطي للمفضلة' : 'Backup Favorites'),
                  subtitle: Text(languageService.isArabic ? 'إنشاء نسخة احتياطية تلقائية' : 'Create automatic backup'),
                  value: _backupFavorites,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final messenger = ScaffoldMessenger.of(context);
                    final isArabic = languageService.isArabic;

                    setState(() {
                      _backupFavorites = value;
                    });

                    // Save to SharedPreferences
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('hadith_backup_favorites', value);

                    if (mounted) {
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            isArabic
                                ? (value ? 'تم تفعيل النسخ الاحتياطي للمفضلة' : 'تم إيقاف النسخ الاحتياطي للمفضلة')
                                : (value ? 'Favorites backup enabled' : 'Favorites backup disabled'),
                          ),
                          backgroundColor: AppTheme.primaryGreen,
                        ),
                      );
                    }
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.folder_open, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'تصدير المفضلة' : 'Export Favorites'),
                  subtitle: Text(languageService.isArabic ? 'تصدير قائمة الأحاديث المفضلة' : 'Export favorite hadiths list'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _exportFavorites(context, languageService),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.folder_open, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'استيراد المفضلة' : 'Import Favorites'),
                  subtitle: Text(languageService.isArabic ? 'استيراد قائمة أحاديث مفضلة' : 'Import favorite hadiths list'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _importFavorites(context, languageService),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Notification Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات الإشعارات' : 'Notification Settings',
            Icons.notifications,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.today, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'حديث اليوم' : 'Daily Hadith'),
                  subtitle: Text(languageService.isArabic ? 'إشعار يومي بحديث مختار' : 'Daily notification with selected hadith'),
                  value: _dailyHadithEnabled,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final messenger = ScaffoldMessenger.of(context);
                    final isArabic = languageService.isArabic;

                    setState(() {
                      _dailyHadithEnabled = value;
                    });

                    // Save to SharedPreferences
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('hadith_daily_enabled', value);

                    if (mounted) {
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            isArabic
                                ? (value ? 'تم تفعيل حديث اليوم' : 'تم إيقاف حديث اليوم')
                                : (value ? 'Daily hadith enabled' : 'Daily hadith disabled'),
                          ),
                          backgroundColor: AppTheme.primaryGreen,
                        ),
                      );
                    }
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.schedule, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'وقت الإشعار اليومي' : 'Daily Notification Time'),
                  subtitle: Text(languageService.isArabic ? '8:00 صباحاً' : '8:00 AM'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showTimePickerDialog(context, languageService),
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: AppTheme.primaryGreen, size: 24),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
      ],
    );
  }

  Widget _buildCollectionTile(String collectionId, String name) {
    return SwitchListTile(
      secondary: const Icon(Icons.book, color: AppTheme.primaryGreen),
      title: Text(name),
      subtitle: Text(_getCollectionDescription(collectionId)),
      value: _enabledCollections[collectionId] ?? false,
      activeColor: AppTheme.primaryGreen,
      onChanged: (value) {
        setState(() {
          _enabledCollections[collectionId] = value;
        });
      },
    );
  }

  String _getCollectionDescription(String collectionId) {
    switch (collectionId) {
      case 'bukhari':
        return 'أصح كتاب بعد كتاب الله';
      case 'muslim':
        return 'ثاني أصح الكتب بعد البخاري';
      case 'abudawud':
        return 'من كتب السنن المعتمدة';
      case 'tirmidhi':
        return 'جامع الأحاديث والآثار';
      case 'nasai':
        return 'من أدق كتب السنن';
      case 'ibnmajah':
        return 'آخر الكتب الستة';
      default:
        return '';
    }
  }

  String _getLanguageName(String language, LanguageService languageService) {
    switch (language) {
      case 'arabic':
        return languageService.isArabic ? 'العربية' : 'Arabic';
      case 'english':
        return languageService.isArabic ? 'الإنجليزية' : 'English';
      case 'both':
        return languageService.isArabic ? 'كلاهما' : 'Both';
      default:
        return languageService.isArabic ? 'العربية' : 'Arabic';
    }
  }

  void _showLanguageDialog(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'اختر لغة البحث' : 'Choose Search Language'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildLanguageOption('arabic', languageService),
            _buildLanguageOption('english', languageService),
            _buildLanguageOption('both', languageService),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageOption(String language, LanguageService languageService) {
    return RadioListTile<String>(
      title: Text(_getLanguageName(language, languageService)),
      value: language,
      groupValue: _defaultLanguage,
      activeColor: AppTheme.primaryGreen,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _defaultLanguage = value;
          });
          Navigator.pop(context);
        }
      },
    );
  }

  void _showTimePickerDialog(BuildContext context, LanguageService languageService) {
    showTimePicker(
      context: context,
      initialTime: const TimeOfDay(hour: 8, minute: 0),
    ).then((time) {
      if (time != null) {
        _saveSelectedTime(time, languageService);
      }
    });
  }

  Future<void> _saveSelectedTime(TimeOfDay time, LanguageService languageService) async {
    // Save selected time to SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('hadith_daily_reminder_time', '${time.hour}:${time.minute}');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            languageService.isArabic
                ? 'تم تحديد الوقت: ${time.hour}:${time.minute.toString().padLeft(2, '0')}'
                : 'Time set: ${time.hour}:${time.minute.toString().padLeft(2, '0')}',
          ),
          backgroundColor: AppTheme.primaryGreen,
        ),
      );
    }
  }

  void _exportFavorites(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'تصدير المفضلة' : 'Export Favorites'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.file_download),
              title: Text(languageService.isArabic ? 'تصدير الأحاديث المفضلة' : 'Export Favorite Hadiths'),
              onTap: () {
                // Implement favorite hadiths export
              },
            ),
            ListTile(
              leading: const Icon(Icons.bookmark),
              title: Text(languageService.isArabic ? 'تصدير الإشارات المرجعية' : 'Export Bookmarks'),
              onTap: () {
                // Implement bookmarks export
              },
            ),
            ListTile(
              leading: const Icon(Icons.note),
              title: Text(languageService.isArabic ? 'تصدير الملاحظات' : 'Export Notes'),
              onTap: () {
                // Implement notes export
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }

  void _importFavorites(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'استيراد المفضلة' : 'Import Favorites'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.file_upload),
              title: Text(languageService.isArabic ? 'استيراد الأحاديث' : 'Import Hadiths'),
              onTap: () {
                // Implement hadiths import
              },
            ),
            ListTile(
              leading: const Icon(Icons.bookmark_add),
              title: Text(languageService.isArabic ? 'استيراد الإشارات المرجعية' : 'Import Bookmarks'),
              onTap: () {
                // Implement bookmarks import
              },
            ),
            ListTile(
              leading: const Icon(Icons.note_add),
              title: Text(languageService.isArabic ? 'استيراد الملاحظات' : 'Import Notes'),
              onTap: () {
                // Implement notes import
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }
}
