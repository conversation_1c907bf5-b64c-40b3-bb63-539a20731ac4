import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'athkar_app.db');
    
    return await openDatabase(
      path,
      version: 5,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create athkar_categories table
    await db.execute('''
      CREATE TABLE athkar_categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        icon TEXT,
        color TEXT,
        is_default INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create athkar_routines table
    await db.execute('''
      CREATE TABLE athkar_routines (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        category_id TEXT,
        title TEXT NOT NULL,
        description TEXT,
        is_public INTEGER DEFAULT 0,
        is_favorite INTEGER DEFAULT 0,
        total_steps INTEGER DEFAULT 0,
        estimated_duration INTEGER,
        color_hex TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (category_id) REFERENCES athkar_categories (id)
      )
    ''');

    // Create athkar_steps table
    await db.execute('''
      CREATE TABLE athkar_steps (
        id TEXT PRIMARY KEY,
        routine_id TEXT NOT NULL,
        step_order INTEGER NOT NULL,
        arabic_text TEXT NOT NULL,
        transliteration TEXT,
        translation TEXT,
        target_count INTEGER DEFAULT 1,
        audio_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (routine_id) REFERENCES athkar_routines (id) ON DELETE CASCADE
      )
    ''');

    // Create user_progress table
    await db.execute('''
      CREATE TABLE user_progress (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        routine_id TEXT NOT NULL,
        step_id TEXT NOT NULL,
        current_count INTEGER DEFAULT 0,
        completed_at TEXT,
        session_date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (routine_id) REFERENCES athkar_routines (id),
        FOREIGN KEY (step_id) REFERENCES athkar_steps (id)
      )
    ''');

    // Create reminder_settings table
    await db.execute('''
      CREATE TABLE reminder_settings (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        routine_id TEXT NOT NULL,
        is_enabled INTEGER DEFAULT 1,
        reminder_times TEXT, -- JSON array of time strings
        days_of_week TEXT, -- JSON array of integers
        notification_title TEXT,
        notification_body TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (routine_id) REFERENCES athkar_routines (id)
      )
    ''');

    // Create user_profiles table
    await db.execute('''
      CREATE TABLE user_profiles (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE,
        full_name TEXT,
        avatar_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create tasbeeh_items table
    await db.execute('''
      CREATE TABLE tasbeeh_items (
        id TEXT PRIMARY KEY,
        arabic_text TEXT NOT NULL,
        transliteration TEXT,
        translation TEXT,
        category TEXT,
        color_hex TEXT,
        is_default INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create tasbeeh_sessions table
    await db.execute('''
      CREATE TABLE tasbeeh_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        tasbeeh_id TEXT NOT NULL,
        target_count INTEGER NOT NULL,
        current_count INTEGER DEFAULT 0,
        start_time TEXT NOT NULL,
        end_time TEXT,
        is_completed INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        FOREIGN KEY (tasbeeh_id) REFERENCES tasbeeh_items (id)
      )
    ''');

    // Create dua_items table
    await db.execute('''
      CREATE TABLE dua_items (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        arabic_text TEXT NOT NULL,
        transliteration TEXT,
        translation TEXT,
        category TEXT,
        source TEXT,
        reference TEXT,
        color_hex TEXT,
        is_default INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_routines_user_id ON athkar_routines(user_id)');
    await db.execute('CREATE INDEX idx_routines_category_id ON athkar_routines(category_id)');
    await db.execute('CREATE INDEX idx_steps_routine_id ON athkar_steps(routine_id)');
    await db.execute('CREATE INDEX idx_progress_user_id ON user_progress(user_id)');
    await db.execute('CREATE INDEX idx_progress_routine_id ON user_progress(routine_id)');
    await db.execute('CREATE INDEX idx_progress_session_date ON user_progress(session_date)');
    await db.execute('CREATE INDEX idx_tasbeeh_sessions_user_id ON tasbeeh_sessions(user_id)');
    await db.execute('CREATE INDEX idx_tasbeeh_sessions_tasbeeh_id ON tasbeeh_sessions(tasbeeh_id)');
    await db.execute('CREATE INDEX idx_tasbeeh_items_category ON tasbeeh_items(category)');
    await db.execute('CREATE INDEX idx_dua_items_category ON dua_items(category)');

    // Insert default categories
    await _insertDefaultCategories(db);

    // Insert default tasbeeh and dua items
    await _insertDefaultTasbeehAndDuas(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add tasbeeh and dua tables
      await db.execute('''
        CREATE TABLE tasbeeh_items (
          id TEXT PRIMARY KEY,
          arabic_text TEXT NOT NULL,
          transliteration TEXT,
          translation TEXT,
          category TEXT,
          color_hex TEXT,
          is_default INTEGER DEFAULT 0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE tasbeeh_sessions (
          id TEXT PRIMARY KEY,
          user_id TEXT NOT NULL,
          tasbeeh_id TEXT NOT NULL,
          target_count INTEGER NOT NULL,
          current_count INTEGER DEFAULT 0,
          start_time TEXT NOT NULL,
          end_time TEXT,
          is_completed INTEGER DEFAULT 0,
          created_at TEXT NOT NULL,
          FOREIGN KEY (tasbeeh_id) REFERENCES tasbeeh_items (id)
        )
      ''');

      await db.execute('''
        CREATE TABLE dua_items (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          arabic_text TEXT NOT NULL,
          transliteration TEXT,
          translation TEXT,
          category TEXT,
          source TEXT,
          reference TEXT,
          color_hex TEXT,
          is_default INTEGER DEFAULT 0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      // Add indexes
      await db.execute('CREATE INDEX idx_tasbeeh_sessions_user_id ON tasbeeh_sessions(user_id)');
      await db.execute('CREATE INDEX idx_tasbeeh_sessions_tasbeeh_id ON tasbeeh_sessions(tasbeeh_id)');
      await db.execute('CREATE INDEX idx_tasbeeh_items_category ON tasbeeh_items(category)');
      await db.execute('CREATE INDEX idx_dua_items_category ON dua_items(category)');

      // Insert default tasbeeh and dua items
      await _insertDefaultTasbeehAndDuas(db);
    }

    if (oldVersion < 3) {
      // Add color fields to existing tables
      await db.execute('ALTER TABLE athkar_routines ADD COLUMN color_hex TEXT');
      await db.execute('ALTER TABLE tasbeeh_items ADD COLUMN color_hex TEXT');
      await db.execute('ALTER TABLE dua_items ADD COLUMN color_hex TEXT');
    }

    if (oldVersion < 4) {
      // Add analytics tables
      await _createAnalyticsTables(db);
    }

    if (oldVersion < 5) {
      // Add enterprise and community tables
      await _createEnterpriseTables(db);
      await _createCommunityTables(db);
    }
  }

  Future<void> _insertDefaultCategories(Database db) async {
    final now = DateTime.now().toIso8601String();
    
    final defaultCategories = [
      {
        'id': 'morning-athkar',
        'name': 'Morning Athkar',
        'description': 'Supplications to be recited in the morning',
        'icon': 'sunrise',
        'color': '#FFB74D',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
      {
        'id': 'evening-athkar',
        'name': 'Evening Athkar',
        'description': 'Supplications to be recited in the evening',
        'icon': 'sunset',
        'color': '#FF8A65',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
      {
        'id': 'prayer-athkar',
        'name': 'Prayer Athkar',
        'description': 'Supplications after the five daily prayers',
        'icon': 'prayer',
        'color': '#81C784',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
      {
        'id': 'sleep-athkar',
        'name': 'Sleep Athkar',
        'description': 'Supplications before going to sleep',
        'icon': 'bedtime',
        'color': '#9575CD',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
      {
        'id': 'general-dhikr',
        'name': 'General Dhikr',
        'description': 'General remembrance and praise of Allah',
        'icon': 'dhikr',
        'color': '#64B5F6',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
      {
        'id': 'dua-collection',
        'name': 'Dua Collection',
        'description': 'Various supplications for different occasions',
        'icon': 'dua',
        'color': '#F06292',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
    ];

    for (final category in defaultCategories) {
      await db.insert('athkar_categories', category);
    }
  }

  Future<void> _insertDefaultTasbeehAndDuas(Database db) async {
    final now = DateTime.now().toIso8601String();

    // Insert default tasbeeh items
    final defaultTasbeeh = [
      {
        'id': 'subhan-allah',
        'arabic_text': 'سُبْحَانَ اللهِ',
        'transliteration': 'Subhan Allah',
        'translation': 'Glory be to Allah',
        'category': 'Praise',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
      {
        'id': 'alhamdulillah',
        'arabic_text': 'الْحَمْدُ لِلَّهِ',
        'transliteration': 'Alhamdulillah',
        'translation': 'Praise be to Allah',
        'category': 'Praise',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
      {
        'id': 'allahu-akbar',
        'arabic_text': 'اللهُ أَكْبَرُ',
        'transliteration': 'Allahu Akbar',
        'translation': 'Allah is Greatest',
        'category': 'Glorification',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
      {
        'id': 'la-ilaha-illa-allah',
        'arabic_text': 'لَا إِلَهَ إِلَّا اللهُ',
        'transliteration': 'La ilaha illa Allah',
        'translation': 'There is no god but Allah',
        'category': 'Declaration of Faith',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
      {
        'id': 'astaghfirullah',
        'arabic_text': 'أَسْتَغْفِرُ اللهَ',
        'transliteration': 'Astaghfirullah',
        'translation': 'I seek forgiveness from Allah',
        'category': 'Seeking Forgiveness',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
    ];

    for (final tasbeeh in defaultTasbeeh) {
      await db.insert('tasbeeh_items', tasbeeh);
    }

    // Insert default dua items
    final defaultDuas = [
      {
        'id': 'dua-waking-up',
        'title': 'Dua when waking up',
        'arabic_text': 'الْحَمْدُ لِلَّهِ الَّذِي أَحْيَانَا بَعْدَ مَا أَمَاتَنَا وَإِلَيْهِ النُّشُورُ',
        'transliteration': 'Alhamdu lillahil-ladhi ahyana ba\'da ma amatana wa ilayhin-nushur',
        'translation': 'Praise be to Allah who gave us life after having taken it from us and unto Him is the resurrection.',
        'category': 'Daily Duas',
        'source': 'Hadith',
        'reference': 'Bukhari',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
      {
        'id': 'dua-before-eating',
        'title': 'Dua before eating',
        'arabic_text': 'بِسْمِ اللهِ',
        'transliteration': 'Bismillah',
        'translation': 'In the name of Allah',
        'category': 'Daily Duas',
        'source': 'Hadith',
        'reference': 'Abu Dawud',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
      {
        'id': 'dua-guidance',
        'title': 'Dua for guidance',
        'arabic_text': 'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ',
        'transliteration': 'Rabbana atina fi\'d-dunya hasanatan wa fi\'l-akhirati hasanatan wa qina \'adhab an-nar',
        'translation': 'Our Lord, give us good in this world and good in the next world, and save us from the punishment of the Fire.',
        'category': 'Quranic Duas',
        'source': 'Quran',
        'reference': '2:201',
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      },
    ];

    for (final dua in defaultDuas) {
      await db.insert('dua_items', dua);
    }
  }

  // Create analytics tables
  Future<void> _createAnalyticsTables(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS user_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        start_time INTEGER,
        end_time INTEGER,
        duration INTEGER,
        screen_views INTEGER,
        actions_count INTEGER,
        device_info TEXT,
        app_version TEXT,
        created_at INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS user_actions (
        id TEXT PRIMARY KEY,
        session_id TEXT,
        user_id TEXT,
        action_type TEXT,
        action_name TEXT,
        screen_name TEXT,
        parameters TEXT,
        timestamp INTEGER,
        created_at INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS app_performance (
        id TEXT PRIMARY KEY,
        session_id TEXT,
        metric_name TEXT,
        metric_value REAL,
        timestamp INTEGER,
        metadata TEXT,
        created_at INTEGER
      )
    ''');
  }

  // Create enterprise tables
  Future<void> _createEnterpriseTables(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS organizations (
        id TEXT PRIMARY KEY,
        name TEXT,
        description TEXT,
        type TEXT,
        logo_url TEXT,
        website TEXT,
        contact_email TEXT,
        phone TEXT,
        address TEXT,
        subscription_plan TEXT,
        max_users INTEGER,
        current_users INTEGER DEFAULT 0,
        features TEXT,
        settings TEXT,
        created_at INTEGER,
        updated_at INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS organization_users (
        id TEXT PRIMARY KEY,
        organization_id TEXT,
        user_id TEXT,
        role TEXT,
        permissions TEXT,
        department TEXT,
        position TEXT,
        is_active BOOLEAN DEFAULT 1,
        invited_by TEXT,
        joined_at INTEGER,
        last_active INTEGER
      )
    ''');
  }

  // Create community tables
  Future<void> _createCommunityTables(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS user_profiles (
        id TEXT PRIMARY KEY,
        user_id TEXT UNIQUE,
        display_name TEXT,
        bio TEXT,
        avatar_url TEXT,
        level INTEGER DEFAULT 1,
        experience_points INTEGER DEFAULT 0,
        total_athkar_completed INTEGER DEFAULT 0,
        streak_days INTEGER DEFAULT 0,
        badges TEXT,
        privacy_settings TEXT,
        created_at INTEGER,
        updated_at INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS communities (
        id TEXT PRIMARY KEY,
        name TEXT,
        description TEXT,
        category TEXT,
        image_url TEXT,
        member_count INTEGER DEFAULT 0,
        is_private BOOLEAN DEFAULT 0,
        admin_user_id TEXT,
        created_at INTEGER,
        updated_at INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS challenges (
        id TEXT PRIMARY KEY,
        title TEXT,
        description TEXT,
        type TEXT,
        target_value INTEGER,
        duration_days INTEGER,
        start_date INTEGER,
        end_date INTEGER,
        reward_points INTEGER,
        badge_id TEXT,
        is_global BOOLEAN DEFAULT 0,
        community_id TEXT,
        created_by TEXT,
        participant_count INTEGER DEFAULT 0,
        created_at INTEGER
      )
    ''');
  }

  // Raw query method
  Future<List<Map<String, dynamic>>> rawQuery(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    return await db.rawQuery(sql, arguments);
  }

  // Execute method for DDL statements
  Future<void> execute(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    await db.execute(sql, arguments);
  }

  // Helper method to close database
  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  // Helper method to delete database (for testing)
  Future<void> deleteDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'athkar_app.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }

  // Generic CRUD operations
  Future<int> insert(String table, Map<String, dynamic> data) async {
    final db = await database;
    return await db.insert(table, data);
  }

  Future<List<Map<String, dynamic>>> query(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    return await db.query(
      table,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  Future<int> update(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    return await db.update(table, data, where: where, whereArgs: whereArgs);
  }

  Future<int> delete(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    return await db.delete(table, where: where, whereArgs: whereArgs);
  }

  /// Get all routines with their steps
  Future<List<dynamic>> getAllRoutines() async {
    final db = await database;

    // Get all routines
    final routineResults = await db.query(
      'athkar_routines',
      orderBy: 'created_at DESC',
    );

    final routines = <dynamic>[];

    for (final routineData in routineResults) {
      // Get steps for this routine
      final stepResults = await db.query(
        'athkar_steps',
        where: 'routine_id = ?',
        whereArgs: [routineData['id']],
        orderBy: 'step_order ASC',
      );

      // Convert to AthkarRoutine object (simplified for sync)
      final routine = {
        'id': routineData['id'],
        'title': routineData['title'],
        'description': routineData['description'],
        'categoryId': routineData['category_id'],
        'estimatedDuration': routineData['estimated_duration'],
        'colorHex': routineData['color_hex'],
        'createdAt': routineData['created_at'],
        'updatedAt': routineData['updated_at'],
        'steps': stepResults,
      };

      routines.add(routine);
    }

    return routines;
  }
}
