import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../theme/app_theme.dart';

class FontSizeSettingsScreen extends StatelessWidget {
  const FontSizeSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Font Size'),
      ),
      body: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Choose your preferred font size',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),
                
                // Preview text
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Preview',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: (Theme.of(context).textTheme.titleMedium?.fontSize ?? 16) * themeProvider.fontSize,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ',
                        style: TextStyle(
                          fontSize: 18 * themeProvider.fontSize,
                          fontWeight: FontWeight.w500,
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Bismillah hir-Rahman nir-Raheem',
                        style: TextStyle(
                          fontSize: 14 * themeProvider.fontSize,
                          fontStyle: FontStyle.italic,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'In the name of Allah, the Most Gracious, the Most Merciful',
                        style: TextStyle(
                          fontSize: 14 * themeProvider.fontSize,
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Font size options
                Text(
                  'Font Size: ${themeProvider.fontSizeLabel}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Slider
                Slider(
                  value: themeProvider.fontSize,
                  min: 0.8,
                  max: 1.4,
                  divisions: 6,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    themeProvider.setFontSize(value);
                  },
                ),
                
                // Size labels
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Small', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                    Text('Medium', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                    Text('Large', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                    Text('Extra Large', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                  ],
                ),
                
                const SizedBox(height: 32),
                
                // Quick action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => themeProvider.decreaseFontSize(),
                      icon: const Icon(Icons.remove),
                      label: const Text('Smaller'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[200],
                        foregroundColor: Colors.black87,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () => themeProvider.resetFontSize(),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Reset'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.accentGold,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () => themeProvider.increaseFontSize(),
                      icon: const Icon(Icons.add),
                      label: const Text('Larger'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryGreen,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
                
                const Spacer(),
                
                // Info text
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: AppTheme.primaryGreen, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Font size changes will apply to all Arabic text, transliterations, and translations throughout the app.',
                          style: TextStyle(
                            fontSize: 12 * themeProvider.fontSize,
                            color: AppTheme.primaryGreen,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
