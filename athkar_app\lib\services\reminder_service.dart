import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import '../models/athkar_models.dart';
import 'notification_service.dart';
import 'package:uuid/uuid.dart';

class ReminderService {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final _uuid = const Uuid();

  // Create a new reminder
  Future<String> createReminder({
    required String userId,
    required String routineId,
    required List<String> reminderTimes,
    List<int>? daysOfWeek,
    String? notificationTitle,
    String? notificationBody,
  }) async {
    try {
      final now = DateTime.now();
      final reminderId = _uuid.v4();
      
      final reminderData = {
        'id': reminderId,
        'user_id': userId,
        'routine_id': routineId,
        'is_enabled': 1,
        'reminder_times': _encodeStringList(reminderTimes),
        'days_of_week': daysOfWeek != null ? _encodeIntList(daysOfWeek) : '[]',
        'notification_title': notificationTitle,
        'notification_body': notificationBody,
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      await _dbHelper.insert('reminder_settings', reminderData);
      
      // Schedule notifications
      await _scheduleNotifications(reminderId, routineId, reminderTimes, 
                                 daysOfWeek, notificationTitle, notificationBody);
      
      return reminderId;
    } catch (e) {
      debugPrint('Error creating reminder: $e');
      rethrow;
    }
  }

  // Get all reminders for a user
  Future<List<ReminderSetting>> getUserReminders(String userId) async {
    try {
      final maps = await _dbHelper.query(
        'reminder_settings',
        where: 'user_id = ?',
        whereArgs: [userId],
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => _mapToReminderSetting(map)).toList();
    } catch (e) {
      debugPrint('Error getting user reminders: $e');
      return [];
    }
  }

  // Get reminders for a specific routine
  Future<List<ReminderSetting>> getRoutineReminders(String routineId) async {
    try {
      final maps = await _dbHelper.query(
        'reminder_settings',
        where: 'routine_id = ?',
        whereArgs: [routineId],
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => _mapToReminderSetting(map)).toList();
    } catch (e) {
      debugPrint('Error getting routine reminders: $e');
      return [];
    }
  }

  // Update reminder
  Future<void> updateReminder(ReminderSetting reminder) async {
    try {
      final updateData = {
        'is_enabled': reminder.isEnabled ? 1 : 0,
        'reminder_times': _encodeStringList(reminder.reminderTimes),
        'days_of_week': _encodeIntList(reminder.daysOfWeek),
        'notification_title': reminder.notificationTitle,
        'notification_body': reminder.notificationBody,
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _dbHelper.update(
        'reminder_settings',
        updateData,
        where: 'id = ?',
        whereArgs: [reminder.id],
      );

      // Cancel existing notifications
      await NotificationService.cancelAthkarReminder(reminder.routineId);

      // Reschedule if enabled
      if (reminder.isEnabled) {
        await _scheduleNotifications(
          reminder.id,
          reminder.routineId,
          reminder.reminderTimes,
          reminder.daysOfWeek,
          reminder.notificationTitle,
          reminder.notificationBody,
        );
      }
    } catch (e) {
      debugPrint('Error updating reminder: $e');
      rethrow;
    }
  }

  // Delete reminder
  Future<void> deleteReminder(String reminderId) async {
    try {
      // Get reminder to cancel notifications
      final maps = await _dbHelper.query(
        'reminder_settings',
        where: 'id = ?',
        whereArgs: [reminderId],
      );

      if (maps.isNotEmpty) {
        final reminder = _mapToReminderSetting(maps.first);
        await NotificationService.cancelAthkarReminder(reminder.routineId);
      }

      await _dbHelper.delete(
        'reminder_settings',
        where: 'id = ?',
        whereArgs: [reminderId],
      );
    } catch (e) {
      debugPrint('Error deleting reminder: $e');
      rethrow;
    }
  }

  // Toggle reminder enabled/disabled
  Future<void> toggleReminder(String reminderId) async {
    try {
      final maps = await _dbHelper.query(
        'reminder_settings',
        where: 'id = ?',
        whereArgs: [reminderId],
      );

      if (maps.isNotEmpty) {
        final reminder = _mapToReminderSetting(maps.first);
        final updatedReminder = ReminderSetting(
          id: reminder.id,
          userId: reminder.userId,
          routineId: reminder.routineId,
          isEnabled: !reminder.isEnabled,
          reminderTimes: reminder.reminderTimes,
          daysOfWeek: reminder.daysOfWeek,
          notificationTitle: reminder.notificationTitle,
          notificationBody: reminder.notificationBody,
          createdAt: reminder.createdAt,
          updatedAt: DateTime.now(),
        );

        await updateReminder(updatedReminder);
      }
    } catch (e) {
      debugPrint('Error toggling reminder: $e');
      rethrow;
    }
  }

  // Schedule notifications for a reminder
  Future<void> _scheduleNotifications(
    String reminderId,
    String routineId,
    List<String> reminderTimes,
    List<int>? daysOfWeek,
    String? notificationTitle,
    String? notificationBody,
  ) async {
    try {
      for (int i = 0; i < reminderTimes.length; i++) {
        final timeString = reminderTimes[i];
        final timeParts = timeString.split(':');
        if (timeParts.length != 2) continue;

        final hour = int.tryParse(timeParts[0]);
        final minute = int.tryParse(timeParts[1]);
        if (hour == null || minute == null) continue;

        final time = TimeOfDay(hour: hour, minute: minute);
        final id = (reminderId.hashCode + i).abs();

        if (daysOfWeek != null && daysOfWeek.isNotEmpty) {
          await NotificationService.scheduleWeeklyNotification(
            id: id,
            title: notificationTitle ?? 'Athkar Reminder',
            body: notificationBody ?? 'Time for your dhikr and remembrance',
            time: time,
            daysOfWeek: daysOfWeek,
            payload: 'athkar_routine:$routineId',
          );
        } else {
          await NotificationService.scheduleDailyNotification(
            id: id,
            title: notificationTitle ?? 'Athkar Reminder',
            body: notificationBody ?? 'Time for your dhikr and remembrance',
            time: time,
            payload: 'athkar_routine:$routineId',
          );
        }
      }
    } catch (e) {
      debugPrint('Error scheduling notifications: $e');
    }
  }

  // Helper methods
  String _encodeStringList(List<String> list) {
    return list.map((s) => '"$s"').join(',');
  }

  String _encodeIntList(List<int> list) {
    return list.join(',');
  }

  List<String> _decodeStringList(String encoded) {
    if (encoded.isEmpty || encoded == '[]') return [];
    return encoded.split(',').map((s) => s.replaceAll('"', '')).toList();
  }

  List<int> _decodeIntList(String encoded) {
    if (encoded.isEmpty || encoded == '[]') return [];
    return encoded.split(',').map((s) => int.tryParse(s) ?? 0).toList();
  }

  ReminderSetting _mapToReminderSetting(Map<String, dynamic> map) {
    return ReminderSetting(
      id: map['id'],
      userId: map['user_id'],
      routineId: map['routine_id'],
      isEnabled: map['is_enabled'] == 1,
      reminderTimes: _decodeStringList(map['reminder_times'] ?? ''),
      daysOfWeek: _decodeIntList(map['days_of_week'] ?? ''),
      notificationTitle: map['notification_title'],
      notificationBody: map['notification_body'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }
}
