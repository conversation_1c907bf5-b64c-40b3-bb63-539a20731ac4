import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/view_models.dart';

/// Provider for managing view configuration across the app
class ViewConfigurationProvider extends ChangeNotifier {
  ViewConfiguration _configuration = const ViewConfiguration();
  QuranViewConfiguration _quranConfiguration = const QuranViewConfiguration();
  
  // Getters
  ViewConfiguration get configuration => _configuration;
  QuranViewConfiguration get quranConfiguration => _quranConfiguration;
  
  // Quick access getters
  ViewMode get viewMode => _configuration.viewMode;
  LayoutDensity get layoutDensity => _configuration.layoutDensity;
  double get fontSize => _configuration.fontSize;
  FontFamily get fontFamily => _configuration.fontFamily;
  Color get primaryColor => _configuration.primaryColor;
  Color get secondaryColor => _configuration.secondaryColor;
  Color get backgroundColor => _configuration.backgroundColor;
  Color get textColor => _configuration.textColor;
  bool get showArabic => _configuration.showArabic;
  bool get showTranslation => _configuration.showTranslation;
  bool get enableAnimations => _configuration.enableAnimations;
  
  /// Initialize the provider and load saved configurations
  Future<void> initialize() async {
    await _loadConfiguration();
    await _loadQuranConfiguration();
  }
  
  /// Load general view configuration from SharedPreferences
  Future<void> _loadConfiguration() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configMap = prefs.getString('view_configuration');
      
      if (configMap != null) {
        // Parse JSON string to Map
        final Map<String, dynamic> map = {};
        // Simple parsing for now - in production, use proper JSON parsing
        _configuration = ViewConfiguration.fromMap(map);
      }
    } catch (e) {
      debugPrint('Error loading view configuration: $e');
    }
  }
  
  /// Load Quran-specific view configuration from SharedPreferences
  Future<void> _loadQuranConfiguration() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configMap = prefs.getString('quran_view_configuration');
      
      if (configMap != null) {
        // Parse JSON string to Map
        final Map<String, dynamic> map = {};
        // Simple parsing for now - in production, use proper JSON parsing
        _quranConfiguration = QuranViewConfiguration.fromMap(map);
      }
    } catch (e) {
      debugPrint('Error loading Quran view configuration: $e');
    }
  }
  
  /// Save general view configuration to SharedPreferences
  Future<void> _saveConfiguration() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configMap = _configuration.toMap();
      // Convert to JSON string - in production, use proper JSON encoding
      await prefs.setString('view_configuration', configMap.toString());
    } catch (e) {
      debugPrint('Error saving view configuration: $e');
    }
  }
  
  /// Save Quran-specific view configuration to SharedPreferences
  Future<void> _saveQuranConfiguration() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configMap = _quranConfiguration.toMap();
      // Convert to JSON string - in production, use proper JSON encoding
      await prefs.setString('quran_view_configuration', configMap.toString());
    } catch (e) {
      debugPrint('Error saving Quran view configuration: $e');
    }
  }
  
  /// Update view mode
  Future<void> updateViewMode(ViewMode viewMode) async {
    _configuration = _configuration.copyWith(viewMode: viewMode);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update layout density
  Future<void> updateLayoutDensity(LayoutDensity density) async {
    _configuration = _configuration.copyWith(layoutDensity: density);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update font size
  Future<void> updateFontSize(double fontSize) async {
    _configuration = _configuration.copyWith(fontSize: fontSize);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update font family
  Future<void> updateFontFamily(FontFamily fontFamily) async {
    _configuration = _configuration.copyWith(fontFamily: fontFamily);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update primary color
  Future<void> updatePrimaryColor(Color color) async {
    _configuration = _configuration.copyWith(primaryColor: color);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update secondary color
  Future<void> updateSecondaryColor(Color color) async {
    _configuration = _configuration.copyWith(secondaryColor: color);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update background color
  Future<void> updateBackgroundColor(Color color) async {
    _configuration = _configuration.copyWith(backgroundColor: color);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update text color
  Future<void> updateTextColor(Color color) async {
    _configuration = _configuration.copyWith(textColor: color);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Toggle Arabic text display
  Future<void> toggleArabicDisplay() async {
    _configuration = _configuration.copyWith(showArabic: !_configuration.showArabic);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Toggle translation display
  Future<void> toggleTranslationDisplay() async {
    _configuration = _configuration.copyWith(showTranslation: !_configuration.showTranslation);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Toggle animations
  Future<void> toggleAnimations() async {
    _configuration = _configuration.copyWith(enableAnimations: !_configuration.enableAnimations);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update animation speed
  Future<void> updateAnimationSpeed(double speed) async {
    _configuration = _configuration.copyWith(animationSpeed: speed);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update grid columns
  Future<void> updateGridColumns(int columns) async {
    _configuration = _configuration.copyWith(gridColumns: columns);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update card elevation
  Future<void> updateCardElevation(double elevation) async {
    _configuration = _configuration.copyWith(cardElevation: elevation);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update border radius
  Future<void> updateBorderRadius(double radius) async {
    _configuration = _configuration.copyWith(borderRadius: radius);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update content padding
  Future<void> updateContentPadding(EdgeInsets padding) async {
    _configuration = _configuration.copyWith(contentPadding: padding);
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update entire configuration
  Future<void> updateConfiguration(ViewConfiguration newConfiguration) async {
    _configuration = newConfiguration;
    await _saveConfiguration();
    notifyListeners();
  }
  
  /// Update Quran view mode
  Future<void> updateQuranViewMode(QuranViewMode viewMode) async {
    _quranConfiguration = _quranConfiguration.copyWithQuran(quranViewMode: viewMode);
    await _saveQuranConfiguration();
    notifyListeners();
  }
  
  /// Update Arabic font size for Quran
  Future<void> updateQuranArabicFontSize(double fontSize) async {
    _quranConfiguration = _quranConfiguration.copyWithQuran(arabicFontSize: fontSize);
    await _saveQuranConfiguration();
    notifyListeners();
  }

  /// Update translation font size for Quran
  Future<void> updateQuranTranslationFontSize(double fontSize) async {
    _quranConfiguration = _quranConfiguration.copyWithQuran(translationFontSize: fontSize);
    await _saveQuranConfiguration();
    notifyListeners();
  }

  /// Toggle verse numbers display
  Future<void> toggleVerseNumbers() async {
    _quranConfiguration = _quranConfiguration.copyWithQuran(showVerseNumbers: !_quranConfiguration.showVerseNumbers);
    await _saveQuranConfiguration();
    notifyListeners();
  }

  /// Toggle Surah headers display
  Future<void> toggleSurahHeaders() async {
    _quranConfiguration = _quranConfiguration.copyWithQuran(showSurahHeaders: !_quranConfiguration.showSurahHeaders);
    await _saveQuranConfiguration();
    notifyListeners();
  }

  /// Toggle Tafseer display
  Future<void> toggleTafseer() async {
    _quranConfiguration = _quranConfiguration.copyWithQuran(showTafseer: !_quranConfiguration.showTafseer);
    await _saveQuranConfiguration();
    notifyListeners();
  }

  /// Toggle word-by-word translation
  Future<void> toggleWordByWordTranslation() async {
    _quranConfiguration = _quranConfiguration.copyWithQuran(enableWordByWordTranslation: !_quranConfiguration.enableWordByWordTranslation);
    await _saveQuranConfiguration();
    notifyListeners();
  }
  
  /// Reset to default configuration
  Future<void> resetToDefaults() async {
    _configuration = const ViewConfiguration();
    _quranConfiguration = const QuranViewConfiguration();
    await _saveConfiguration();
    await _saveQuranConfiguration();
    notifyListeners();
  }
  
  /// Get text style based on current configuration
  TextStyle getTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
  }) {
    return TextStyle(
      fontSize: fontSize ?? _configuration.fontSize,
      fontFamily: _configuration.fontFamily.fontName,
      fontWeight: fontWeight,
      color: color ?? _configuration.textColor,
    );
  }
  
  /// Get Arabic text style based on current configuration
  TextStyle getArabicTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
  }) {
    return TextStyle(
      fontSize: fontSize ?? _quranConfiguration.arabicFontSize,
      fontFamily: 'Amiri', // Always use Arabic font for Arabic text
      fontWeight: fontWeight,
      color: color ?? _configuration.textColor,
      height: 1.8, // Better line height for Arabic text
    );
  }
  
  /// Get theme data based on current configuration
  ThemeData getThemeData() {
    return ThemeData(
      primarySwatch: MaterialColor(
        _configuration.primaryColor.toARGB32(),
        <int, Color>{
          50: _configuration.primaryColor.withValues(alpha: 0.1),
          100: _configuration.primaryColor.withValues(alpha: 0.2),
          200: _configuration.primaryColor.withValues(alpha: 0.3),
          300: _configuration.primaryColor.withValues(alpha: 0.4),
          400: _configuration.primaryColor.withValues(alpha: 0.5),
          500: _configuration.primaryColor,
          600: _configuration.primaryColor.withValues(alpha: 0.7),
          700: _configuration.primaryColor.withValues(alpha: 0.8),
          800: _configuration.primaryColor.withValues(alpha: 0.9),
          900: _configuration.primaryColor,
        },
      ),
      scaffoldBackgroundColor: _configuration.backgroundColor,
      textTheme: TextTheme(
        bodyLarge: getTextStyle(),
        bodyMedium: getTextStyle(),
        titleLarge: getTextStyle(fontSize: _configuration.fontSize + 4, fontWeight: FontWeight.bold),
        titleMedium: getTextStyle(fontSize: _configuration.fontSize + 2, fontWeight: FontWeight.w600),
      ),
      cardTheme: CardThemeData(
        elevation: _configuration.cardElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_configuration.borderRadius),
        ),
      ),
    );
  }
}
