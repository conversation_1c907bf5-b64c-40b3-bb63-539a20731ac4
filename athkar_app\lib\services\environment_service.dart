import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../config/app_config.dart';

class EnvironmentService {
  static late PackageInfo _packageInfo;
  static late DeviceInfoPlugin _deviceInfo;
  static Map<String, dynamic>? _environmentData;
  
  static Future<void> initialize() async {
    try {
      _packageInfo = await PackageInfo.fromPlatform();
      _deviceInfo = DeviceInfoPlugin();
      await _loadEnvironmentData();
    } catch (e) {
      debugPrint('Error initializing environment service: $e');
    }
  }
  
  static Future<void> _loadEnvironmentData() async {
    try {
      final deviceData = await _getDeviceInfo();
      final appData = await _getAppInfo();
      final systemData = await _getSystemInfo();
      
      _environmentData = {
        'device': deviceData,
        'app': appData,
        'system': systemData,
        'config': AppConfig.getEnvironmentInfo(),
      };
    } catch (e) {
      debugPrint('Error loading environment data: $e');
      _environmentData = {};
    }
  }
  
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return {
          'platform': 'android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
          'product': androidInfo.product,
          'hardware': androidInfo.hardware,
          'android_version': androidInfo.version.release,
          'sdk_int': androidInfo.version.sdkInt,
          'security_patch': androidInfo.version.securityPatch,
          'fingerprint': androidInfo.fingerprint,
          'is_physical_device': androidInfo.isPhysicalDevice,
          'supported_abis': androidInfo.supportedAbis,
          'system_features': androidInfo.systemFeatures,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return {
          'platform': 'ios',
          'name': iosInfo.name,
          'model': iosInfo.model,
          'localized_model': iosInfo.localizedModel,
          'system_name': iosInfo.systemName,
          'system_version': iosInfo.systemVersion,
          'identifier_for_vendor': iosInfo.identifierForVendor,
          'is_physical_device': iosInfo.isPhysicalDevice,
          'utsname': {
            'machine': iosInfo.utsname.machine,
            'nodename': iosInfo.utsname.nodename,
            'release': iosInfo.utsname.release,
            'sysname': iosInfo.utsname.sysname,
            'version': iosInfo.utsname.version,
          },
        };
      } else {
        return {
          'platform': Platform.operatingSystem,
          'version': Platform.operatingSystemVersion,
        };
      }
    } catch (e) {
      debugPrint('Error getting device info: $e');
      return {
        'platform': Platform.operatingSystem,
        'error': e.toString(),
      };
    }
  }
  
  static Future<Map<String, dynamic>> _getAppInfo() async {
    return {
      'app_name': _packageInfo.appName,
      'package_name': _packageInfo.packageName,
      'version': _packageInfo.version,
      'build_number': _packageInfo.buildNumber,
      'build_signature': _packageInfo.buildSignature,
      'installer_store': _packageInfo.installerStore,
    };
  }
  
  static Future<Map<String, dynamic>> _getSystemInfo() async {
    return {
      'dart_version': Platform.version,
      'operating_system': Platform.operatingSystem,
      'operating_system_version': Platform.operatingSystemVersion,
      'locale': Platform.localeName,
      'number_of_processors': Platform.numberOfProcessors,
      'path_separator': Platform.pathSeparator,
      'executable': Platform.executable,
      'executable_arguments': Platform.executableArguments,
      'environment': Platform.environment,
      'is_debug_mode': kDebugMode,
      'is_profile_mode': kProfileMode,
      'is_release_mode': kReleaseMode,
      'is_web': kIsWeb,
    };
  }
  
  // Getters for environment data
  static Map<String, dynamic> get environmentData => _environmentData ?? {};
  static Map<String, dynamic> get deviceInfo => environmentData['device'] ?? {};
  static Map<String, dynamic> get appInfo => environmentData['app'] ?? {};
  static Map<String, dynamic> get systemInfo => environmentData['system'] ?? {};
  static Map<String, dynamic> get configInfo => environmentData['config'] ?? {};
  
  // Platform checks
  static bool get isAndroid => Platform.isAndroid;
  static bool get isIOS => Platform.isIOS;
  static bool get isWeb => kIsWeb;
  static bool get isMobile => Platform.isAndroid || Platform.isIOS;
  static bool get isDesktop => Platform.isWindows || Platform.isMacOS || Platform.isLinux;
  
  // App version info
  static String get appName => _packageInfo.appName;
  static String get packageName => _packageInfo.packageName;
  static String get version => _packageInfo.version;
  static String get buildNumber => _packageInfo.buildNumber;
  static String get fullVersion => '$version+$buildNumber';
  
  // Environment checks
  static bool get isProduction => AppConfig.isProduction;
  static bool get isDevelopment => AppConfig.isDevelopment;
  static bool get isDebugMode => kDebugMode;
  static bool get isReleaseMode => kReleaseMode;
  static bool get isProfileMode => kProfileMode;
  
  // Device capabilities
  static bool get supportsHapticFeedback => Platform.isIOS || Platform.isAndroid;
  static bool get supportsBiometrics => Platform.isIOS || Platform.isAndroid;
  static bool get supportsCamera => Platform.isIOS || Platform.isAndroid;
  static bool get supportsLocation => Platform.isIOS || Platform.isAndroid;
  static bool get supportsSensors => Platform.isIOS || Platform.isAndroid;
  static bool get supportsNotifications => Platform.isIOS || Platform.isAndroid;
  static bool get supportsInAppPurchases => Platform.isIOS || Platform.isAndroid;
  
  // Network and connectivity
  static bool get supportsNetworking => !kIsWeb || Platform.isIOS || Platform.isAndroid;
  static bool get supportsBackgroundTasks => Platform.isIOS || Platform.isAndroid;
  
  // Storage capabilities
  static bool get supportsSecureStorage => Platform.isIOS || Platform.isAndroid;
  static bool get supportsFileSystem => !kIsWeb;
  static bool get supportsDatabase => true;
  
  // Feature availability based on platform
  static bool isFeatureSupported(String feature) {
    switch (feature) {
      case 'qibla_finder':
        return supportsLocation && supportsSensors;
      case 'biometric_auth':
        return supportsBiometrics;
      case 'push_notifications':
        return supportsNotifications;
      case 'in_app_purchases':
        return supportsInAppPurchases;
      case 'camera':
        return supportsCamera;
      case 'haptic_feedback':
        return supportsHapticFeedback;
      case 'background_tasks':
        return supportsBackgroundTasks;
      case 'secure_storage':
        return supportsSecureStorage;
      case 'file_system':
        return supportsFileSystem;
      case 'offline_mode':
        return supportsDatabase && supportsFileSystem;
      default:
        return true;
    }
  }
  
  // Performance and memory info
  static Future<Map<String, dynamic>> getPerformanceInfo() async {
    try {
      return {
        'memory_usage': await _getMemoryUsage(),
        'cpu_usage': await _getCpuUsage(),
        'battery_level': await _getBatteryLevel(),
        'storage_info': await _getStorageInfo(),
        'network_info': await _getNetworkInfo(),
      };
    } catch (e) {
      debugPrint('Error getting performance info: $e');
      return {};
    }
  }
  
  static Future<Map<String, dynamic>> _getMemoryUsage() async {
    // Implement memory usage tracking
    try {
      // In a real app, you would use platform-specific APIs or packages like device_info_plus
      // For now, return mock data
      final usagePercent = 50.0 + (math.Random().nextDouble() * 30.0); // 50-80% usage
      final totalMemoryMB = 4096 + math.Random().nextInt(4096); // 4-8GB
      final usedMemoryMB = (totalMemoryMB * usagePercent / 100).round();
      final availableMemoryMB = totalMemoryMB - usedMemoryMB;

      return {
        'total_memory': '${totalMemoryMB}MB',
        'available_memory': '${availableMemoryMB}MB',
        'used_memory': '${usedMemoryMB}MB',
        'usage_percent': usagePercent.round(),
      };
    } catch (e) {
      return {
        'total_memory': 'unknown',
        'available_memory': 'unknown',
        'used_memory': 'unknown',
        'usage_percent': 0,
      };
    }
  }
  
  static Future<Map<String, dynamic>> _getCpuUsage() async {
    // Implement CPU usage tracking
    try {
      // In a real app, you would use platform-specific APIs
      // For now, return mock data
      final cpuUsage = 20.0 + (math.Random().nextDouble() * 60.0); // 20-80% usage

      return {
        'cpu_usage': '${cpuUsage.round()}%',
        'cpu_cores': Platform.numberOfProcessors,
        'cpu_usage_percent': cpuUsage.round(),
      };
    } catch (e) {
      return {
        'cpu_usage': 'unknown',
        'cpu_cores': Platform.numberOfProcessors,
        'cpu_usage_percent': 0,
      };
    }
  }
  
  static Future<Map<String, dynamic>> _getBatteryLevel() async {
    // Implement battery level tracking
    try {
      // In a real app, you would use battery_plus package
      // For now, return mock data
      final batteryLevel = 20 + math.Random().nextInt(80); // 20-100%
      final isCharging = math.Random().nextBool();

      return {
        'battery_level': '$batteryLevel%',
        'is_charging': isCharging,
        'battery_level_percent': batteryLevel,
      };
    } catch (e) {
      return {
        'battery_level': 'unknown',
        'is_charging': false,
        'battery_level_percent': 0,
      };
    }
  }

  static Future<Map<String, dynamic>> _getStorageInfo() async {
    // Implement storage info tracking
    try {
      // In a real app, you would use platform-specific APIs
      // For now, return mock data
      final totalStorageGB = 64 + math.Random().nextInt(192); // 64-256GB
      final usedStorageGB = (totalStorageGB * (0.3 + math.Random().nextDouble() * 0.5)).round(); // 30-80% used
      final availableStorageGB = totalStorageGB - usedStorageGB;

      return {
        'total_storage': '${totalStorageGB}GB',
        'available_storage': '${availableStorageGB}GB',
        'used_storage': '${usedStorageGB}GB',
        'usage_percent': (usedStorageGB / totalStorageGB * 100).round(),
      };
    } catch (e) {
      return {
        'total_storage': 'unknown',
        'available_storage': 'unknown',
        'used_storage': 'unknown',
        'usage_percent': 0,
      };
    }
  }

  static Future<Map<String, dynamic>> _getNetworkInfo() async {
    // Implement network info tracking
    try {
      // In a real app, you would use connectivity_plus package
      // For now, return mock data
      final connectionTypes = ['WiFi', 'Mobile', 'Ethernet'];
      final connectionType = connectionTypes[math.Random().nextInt(connectionTypes.length)];
      final isConnected = math.Random().nextBool();
      final signalStrength = isConnected ? (1 + math.Random().nextInt(4)) : 0; // 0-4 bars

      return {
        'connection_type': connectionType,
        'is_connected': isConnected,
        'signal_strength': '$signalStrength/4',
        'signal_strength_bars': signalStrength,
      };
    } catch (e) {
      return {
        'connection_type': 'unknown',
        'is_connected': false,
        'signal_strength': 'unknown',
        'signal_strength_bars': 0,
      };
    }
  }
  
  // Debug and logging helpers
  static void logEnvironmentInfo() {
    if (isDevelopment) {
      debugPrint('=== Environment Information ===');
      debugPrint('App: $appName v$fullVersion');
      debugPrint('Platform: ${Platform.operatingSystem}');
      debugPrint('Debug Mode: $isDebugMode');
      debugPrint('Production: $isProduction');
      debugPrint('Device Info: ${deviceInfo.toString()}');
      debugPrint('==============================');
    }
  }
  
  // Configuration validation
  static bool validateConfiguration() {
    try {
      // Check required configuration values
      final requiredConfigs = [
        AppConfig.appName,
        AppConfig.appVersion,
        AppConfig.baseApiUrl,
        AppConfig.supabaseUrl,
        AppConfig.supabaseAnonKey,
      ];
      
      for (final config in requiredConfigs) {
        if (config.isEmpty) {
          debugPrint('Missing required configuration');
          return false;
        }
      }
      
      // Validate URLs
      final urls = [
        AppConfig.baseApiUrl,
        AppConfig.supabaseUrl,
        AppConfig.quranApiUrl,
        AppConfig.prayerTimesApiUrl,
      ];
      
      for (final url in urls) {
        final uri = Uri.tryParse(url);
        if (uri == null || !uri.hasAbsolutePath) {
          debugPrint('Invalid URL configuration: $url');
          return false;
        }
      }
      
      return true;
    } catch (e) {
      debugPrint('Configuration validation error: $e');
      return false;
    }
  }
  
  // Export environment data for debugging
  static Map<String, dynamic> exportEnvironmentData() {
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'environment': environmentData,
      'configuration_valid': validateConfiguration(),
      'supported_features': {
        'qibla_finder': isFeatureSupported('qibla_finder'),
        'biometric_auth': isFeatureSupported('biometric_auth'),
        'push_notifications': isFeatureSupported('push_notifications'),
        'in_app_purchases': isFeatureSupported('in_app_purchases'),
        'camera': isFeatureSupported('camera'),
        'haptic_feedback': isFeatureSupported('haptic_feedback'),
        'background_tasks': isFeatureSupported('background_tasks'),
        'secure_storage': isFeatureSupported('secure_storage'),
        'file_system': isFeatureSupported('file_system'),
        'offline_mode': isFeatureSupported('offline_mode'),
      },
    };
  }
}
