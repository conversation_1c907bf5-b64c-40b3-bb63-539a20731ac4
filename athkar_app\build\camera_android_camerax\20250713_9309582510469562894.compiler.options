"-Xallow-no-source-files" "-classpath" "D:\\projects\\12july\\athkar\\athkar_app\\build\\camera_android_camerax\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\93898e8b22930619b2b7ae97a351e7ee\\transformed\\jetified-camera-video-1.5.0-beta01-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7fef6396bec23677ed507863a607a1e5\\transformed\\jetified-camera-lifecycle-1.5.0-beta01-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2b7cfe0d632ab62c8d215acd4afeec8f\\transformed\\jetified-camera-camera2-1.5.0-beta01-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3c89df64981f3e3416989e5abb2e66f5\\transformed\\jetified-camera-core-1.5.0-beta01-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b8b5cb48081050c95cd4cbdf5ce957e7\\transformed\\jetified-flutter_embedding_debug-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ec85a195f3d8caf00ca4b869cdb885fa\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\98271631e4f1a87237f04152c4c985d9\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\96c394fd60607c7dd5d7bcc0d47530e0\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\477756d298b6e925e25a98275dcc5fe9\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0a3134edf157ab3aba720d954425e31e\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\568a70bb3f483facf4d2d7d43ae6008c\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d33fd142c93f006da693497115ea39b8\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\baddc2ab01e31011d9a085c5afd8f90c\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\69a8da1683c718e294212bb079617d82\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\20cfe6ef4b0a124cd0e1462452b12421\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c7fd0003b4c5f32b6401622484f58d3\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\94352ef56a2c0eb3126c4155ddbf4938\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cfe908072914cae0c03c4c0f407f54eb\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a369d4793d95f1cf7a87081c7b8c9fdd\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c50491eb7fc85750cb06a9f9ac15ed5\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\80030f81c5c11797c21d6361ad2e793b\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\897fcbd93910d32a9413b6785dfde8f4\\transformed\\jetified-annotation-experimental-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1da56407fdb3c6ef1988316e93bb5572\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7462755296592f32eb623f96e43bc6cf\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\21d7c1738b8ba5d31307b2e344230070\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\51b14c9819ef974d3fe90e08e4564be0\\transformed\\jetified-annotation-jvm-1.8.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fe6afea4c06fdc6586ace11b1927dacc\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\20006cb2cbdc8e092d2d9a80d3af9f10\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1d7b96f62486d85fa5db6ade63609594\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fda9eaacc96b6b08a14a95252385c2ea\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4de03bc1c75b6023ecb44cfea59ebdda\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\14712948e900ccb770108d48c533d8a8\\transformed\\jetified-guava-33.4.0-android.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6fe12c0a4100349fb6dd80cc0b5d1eee\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0fc41274109b850884ac253c794e002b\\transformed\\jetified-listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a5fc419f8a3a9500db8bf0a8a5eec4e4\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00392a538534e18478a88d40e194cb43\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\831e9ba9d971996df52ca0cbff2f8ca8\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2428a6d9e35ed5178c2fc5a5f96f07c8\\transformed\\jetified-jspecify-1.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\82cf244aa0fccda7150edde6aca1179d\\transformed\\jetified-failureaccess-1.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\007ad5faafb1071d1e723bb57a2b3208\\transformed\\jetified-jsr305-3.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7d270a0fb6a6e96c59146edb6317be0b\\transformed\\jetified-checker-qual-3.43.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\440a2a770e9b1c92348d7e0b3030c4dc\\transformed\\jetified-error_prone_annotations-2.36.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3c4b07d9bb9d0342c2152ea0db956b31\\transformed\\jetified-j2objc-annotations-3.0.0.jar;d:\\Sdk\\platforms\\android-35\\android.jar;d:\\Sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\projects\\12july\\athkar\\athkar_app\\build\\camera_android_camerax\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "camera_android_camerax_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\AnalyzerProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\AspectRatioStrategyProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\Camera2CameraControlProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\Camera2CameraInfoProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraAndroidCameraxPlugin.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraCharacteristicsProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraControlProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraInfoProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraIntegerRangeProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraPermissionsError.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraPermissionsErrorProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraPermissionsManager.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraSelectorProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraSizeProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraStateProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraStateStateErrorProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CaptureRequestOptionsProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CaptureRequestProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\DeviceOrientationManager.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\DeviceOrientationManagerProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\DisplayOrientedMeteringPointFactoryProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ExposureStateProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\FallbackStrategyProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\FocusMeteringActionBuilderProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\FocusMeteringActionProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\FocusMeteringResultProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\GeneratedCameraXLibrary.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ImageAnalysisProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ImageCaptureProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ImageProxyProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\LiveDataProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\MeteringPointFactoryProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\MeteringPointProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ObserverProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\PendingRecordingProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\PlaneProxyProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\PreviewProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ProcessCameraProviderProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ProxyApiRegistrar.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ProxyLifecycleProvider.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\QualitySelectorProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\RecorderProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\RecordingProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ResolutionFilterProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ResolutionInfoProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ResolutionSelectorProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ResolutionStrategyProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\SystemServicesManager.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\SystemServicesManagerProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\VideoCaptureProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\VideoRecordEventListener.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\VideoRecordEventListenerProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ZoomStateProxyApi.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\CameraXLibrary.g.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\camera_android_camerax-0.6.19\\android\\src\\main\\java\\io\\flutter\\plugins\\camerax\\ResultCompat.kt"