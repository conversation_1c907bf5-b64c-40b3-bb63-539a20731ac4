// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Text_Core_1_H
#define WINRT_Windows_UI_Text_Core_1_H
#include "winrt/impl/Windows.UI.Text.Core.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Text::Core
{
    struct __declspec(empty_bases) ICoreTextCompositionCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextCompositionCompletedEventArgs>
    {
        ICoreTextCompositionCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreTextCompositionCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextCompositionSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextCompositionSegment>
    {
        ICoreTextCompositionSegment(std::nullptr_t = nullptr) noexcept {}
        ICoreTextCompositionSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextCompositionStartedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextCompositionStartedEventArgs>
    {
        ICoreTextCompositionStartedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreTextCompositionStartedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextEditContext :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextEditContext>
    {
        ICoreTextEditContext(std::nullptr_t = nullptr) noexcept {}
        ICoreTextEditContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextEditContext2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextEditContext2>
    {
        ICoreTextEditContext2(std::nullptr_t = nullptr) noexcept {}
        ICoreTextEditContext2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextFormatUpdatingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextFormatUpdatingEventArgs>
    {
        ICoreTextFormatUpdatingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreTextFormatUpdatingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextLayoutBounds :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextLayoutBounds>
    {
        ICoreTextLayoutBounds(std::nullptr_t = nullptr) noexcept {}
        ICoreTextLayoutBounds(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextLayoutRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextLayoutRequest>
    {
        ICoreTextLayoutRequest(std::nullptr_t = nullptr) noexcept {}
        ICoreTextLayoutRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextLayoutRequest2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextLayoutRequest2>
    {
        ICoreTextLayoutRequest2(std::nullptr_t = nullptr) noexcept {}
        ICoreTextLayoutRequest2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextLayoutRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextLayoutRequestedEventArgs>
    {
        ICoreTextLayoutRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreTextLayoutRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextSelectionRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextSelectionRequest>
    {
        ICoreTextSelectionRequest(std::nullptr_t = nullptr) noexcept {}
        ICoreTextSelectionRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextSelectionRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextSelectionRequestedEventArgs>
    {
        ICoreTextSelectionRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreTextSelectionRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextSelectionUpdatingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextSelectionUpdatingEventArgs>
    {
        ICoreTextSelectionUpdatingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreTextSelectionUpdatingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextServicesManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextServicesManager>
    {
        ICoreTextServicesManager(std::nullptr_t = nullptr) noexcept {}
        ICoreTextServicesManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextServicesManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextServicesManagerStatics>
    {
        ICoreTextServicesManagerStatics(std::nullptr_t = nullptr) noexcept {}
        ICoreTextServicesManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextServicesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextServicesStatics>
    {
        ICoreTextServicesStatics(std::nullptr_t = nullptr) noexcept {}
        ICoreTextServicesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextTextRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextTextRequest>
    {
        ICoreTextTextRequest(std::nullptr_t = nullptr) noexcept {}
        ICoreTextTextRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextTextRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextTextRequestedEventArgs>
    {
        ICoreTextTextRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreTextTextRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICoreTextTextUpdatingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreTextTextUpdatingEventArgs>
    {
        ICoreTextTextUpdatingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreTextTextUpdatingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
