import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../services/audio_service.dart';
// import '../../theme/app_theme.dart'; // Unused import removed
import '../../screens/comprehensive_testing_screen.dart';

class AudioSystemTestingWidget extends StatefulWidget {
  final Function(TestingStatus) onStatusChanged;

  const AudioSystemTestingWidget({
    super.key,
    required this.onStatusChanged,
  });

  @override
  State<AudioSystemTestingWidget> createState() => _AudioSystemTestingWidgetState();
}

class _AudioSystemTestingWidgetState extends State<AudioSystemTestingWidget> {
  final AudioService _audioService = AudioService();
  
  final Map<String, TestResult> _testResults = {};
  bool _isRunningTests = false;
  int _currentTestRound = 0;
  final int _totalRounds = 5;

  final List<AudioTest> _audioTests = [
    AudioTest(
      id: 'notification_sound',
      nameAr: 'صوت الإشعار',
      nameEn: 'Notification Sound',
      description: 'Test notification sound playback',
    ),
    AudioTest(
      id: 'quran_recitation',
      nameAr: 'تلاوة القرآن',
      nameEn: 'Quran Recitation',
      description: 'Test Quran recitation audio playback',
    ),
    AudioTest(
      id: 'athkar_audio',
      nameAr: 'صوت الأذكار',
      nameEn: 'Athkar Audio',
      description: 'Test Athkar audio playback',
    ),
    AudioTest(
      id: 'athan_sound',
      nameAr: 'صوت الأذان',
      nameEn: 'Athan Sound',
      description: 'Test Athan (call to prayer) audio',
    ),
    AudioTest(
      id: 'background_audio',
      nameAr: 'الصوت الخلفي',
      nameEn: 'Background Audio',
      description: 'Test background audio playback',
    ),
    AudioTest(
      id: 'audio_controls',
      nameAr: 'تحكم الصوت',
      nameEn: 'Audio Controls',
      description: 'Test audio playback controls',
    ),
    AudioTest(
      id: 'volume_control',
      nameAr: 'التحكم في الصوت',
      nameEn: 'Volume Control',
      description: 'Test volume control functionality',
    ),
    AudioTest(
      id: 'audio_focus',
      nameAr: 'تركيز الصوت',
      nameEn: 'Audio Focus',
      description: 'Test audio focus management',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeTestResults();
  }

  void _initializeTestResults() {
    for (final test in _audioTests) {
      _testResults[test.id] = TestResult.notStarted;
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with progress
          _buildHeader(languageService),
          
          const SizedBox(height: 24),
          
          // Test controls
          _buildTestControls(languageService),
          
          const SizedBox(height: 24),
          
          // Audio status
          _buildAudioStatus(languageService),
          
          const SizedBox(height: 24),
          
          // Test results
          _buildTestResults(languageService),
          
          const SizedBox(height: 24),
          
          // Round progress
          if (_isRunningTests) _buildRoundProgress(languageService),
        ],
      ),
    );
  }

  Widget _buildHeader(LanguageService languageService) {
    final passedTests = _testResults.values.where((result) => result == TestResult.passed).length;
    final totalTests = _testResults.length;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.orange,
            Colors.orange.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.volume_up, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  languageService.isArabic ? 'اختبار النظام الصوتي' : 'Audio System Testing',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            languageService.isArabic 
                ? 'اختبار شامل لجميع الأصوات الإسلامية والتحكم الصوتي'
                : 'Comprehensive testing of all Islamic audio and sound controls',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageService.isArabic ? 'التقدم' : 'Progress',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '$passedTests / $totalTests',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: passedTests / totalTests,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildTestControls(LanguageService languageService) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isRunningTests ? null : _runAllTests,
            icon: _isRunningTests 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(
              _isRunningTests
                  ? (languageService.isArabic ? 'جاري التشغيل...' : 'Running...')
                  : (languageService.isArabic ? 'تشغيل جميع الاختبارات' : 'Run All Tests'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: _isRunningTests ? null : _resetTests,
          icon: const Icon(Icons.refresh),
          label: Text(languageService.isArabic ? 'إعادة تعيين' : 'Reset'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildAudioStatus(LanguageService languageService) {
    return FutureBuilder<bool>(
      future: _audioService.isAudioAvailable(),
      builder: (context, snapshot) {
        final isAvailable = snapshot.data ?? false;
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isAvailable ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isAvailable ? Colors.green : Colors.red,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isAvailable ? Icons.volume_up : Icons.volume_off,
                color: isAvailable ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languageService.isArabic ? 'حالة النظام الصوتي' : 'Audio System Status',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isAvailable ? Colors.green : Colors.red,
                      ),
                    ),
                    Text(
                      isAvailable
                          ? (languageService.isArabic ? 'متاح' : 'Available')
                          : (languageService.isArabic ? 'غير متاح' : 'Unavailable'),
                      style: TextStyle(
                        color: isAvailable ? Colors.green : Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              // Volume indicator
              FutureBuilder<double>(
                future: _audioService.getCurrentVolume(),
                builder: (context, volumeSnapshot) {
                  final volume = volumeSnapshot.data ?? 0.0;
                  return Column(
                    children: [
                      Text(
                        languageService.isArabic ? 'الصوت' : 'Volume',
                        style: const TextStyle(fontSize: 12),
                      ),
                      Text(
                        '${(volume * 100).round()}%',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTestResults(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'نتائج الاختبارات' : 'Test Results',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.orange,
          ),
        ),
        const SizedBox(height: 12),
        ...(_audioTests.map((test) {
          final result = _testResults[test.id] ?? TestResult.notStarted;
          return _buildTestResultCard(test, result, languageService);
        }).toList()),
      ],
    );
  }

  Widget _buildTestResultCard(AudioTest test, TestResult result, LanguageService languageService) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (result) {
      case TestResult.passed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = languageService.isArabic ? 'نجح' : 'Passed';
        break;
      case TestResult.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = languageService.isArabic ? 'فشل' : 'Failed';
        break;
      case TestResult.inProgress:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = languageService.isArabic ? 'قيد التشغيل' : 'Running';
        break;
      case TestResult.notStarted:
        statusColor = Colors.grey;
        statusIcon = Icons.radio_button_unchecked;
        statusText = languageService.isArabic ? 'لم يبدأ' : 'Not Started';
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          languageService.isArabic ? test.nameAr : test.nameEn,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(test.description),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            if (result == TestResult.inProgress)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        onTap: () => _runSingleTest(test.id),
      ),
    );
  }

  Widget _buildRoundProgress(LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            languageService.isArabic 
                ? 'جولة الاختبار ${_currentTestRound + 1} من $_totalRounds'
                : 'Test Round ${_currentTestRound + 1} of $_totalRounds',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentTestRound + 1) / _totalRounds,
            backgroundColor: Colors.orange.withValues(alpha: 0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
          ),
        ],
      ),
    );
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTests = true;
      _currentTestRound = 0;
    });

    widget.onStatusChanged(TestingStatus.inProgress);

    // Check audio availability first
    final isAvailable = await _audioService.isAudioAvailable();
    if (!isAvailable) {
      setState(() {
        _isRunningTests = false;
      });
      widget.onStatusChanged(TestingStatus.failed);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LanguageService>(context, listen: false).isArabic
                  ? 'النظام الصوتي غير متاح'
                  : 'Audio system unavailable',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // Run 5 rounds of testing as per requirements
    for (int round = 0; round < _totalRounds; round++) {
      setState(() {
        _currentTestRound = round;
      });

      for (final test in _audioTests) {
        await _runSingleTestInternal(test.id);
        await Future.delayed(const Duration(milliseconds: 1000)); // Brief delay between tests
      }

      await Future.delayed(const Duration(seconds: 2)); // Delay between rounds
    }

    setState(() {
      _isRunningTests = false;
    });

    // Check if all tests passed
    final allPassed = _testResults.values.every((result) => result == TestResult.passed);
    widget.onStatusChanged(allPassed ? TestingStatus.passed : TestingStatus.failed);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            allPassed
                ? (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'جميع اختبارات النظام الصوتي نجحت!'
                    : 'All audio system tests passed!')
                : (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'بعض اختبارات النظام الصوتي فشلت'
                    : 'Some audio system tests failed'),
          ),
          backgroundColor: allPassed ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _runSingleTest(String testId) async {
    await _runSingleTestInternal(testId);
  }

  Future<void> _runSingleTestInternal(String testId) async {
    setState(() {
      _testResults[testId] = TestResult.inProgress;
    });

    try {
      bool testPassed = false;

      switch (testId) {
        case 'notification_sound':
          testPassed = await _testNotificationSound();
          break;
        case 'quran_recitation':
          testPassed = await _testQuranRecitation();
          break;
        case 'athkar_audio':
          testPassed = await _testAthkarAudio();
          break;
        case 'athan_sound':
          testPassed = await _testAthanSound();
          break;
        case 'background_audio':
          testPassed = await _testBackgroundAudio();
          break;
        case 'audio_controls':
          testPassed = await _testAudioControls();
          break;
        case 'volume_control':
          testPassed = await _testVolumeControl();
          break;
        case 'audio_focus':
          testPassed = await _testAudioFocus();
          break;
      }

      setState(() {
        _testResults[testId] = testPassed ? TestResult.passed : TestResult.failed;
      });
    } catch (e) {
      setState(() {
        _testResults[testId] = TestResult.failed;
      });
    }
  }

  Future<bool> _testNotificationSound() async {
    try {
      await _audioService.playNotificationSound('assets/sounds/notification.mp3');
      await Future.delayed(const Duration(seconds: 2));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testQuranRecitation() async {
    try {
      await _audioService.playQuranRecitation(
        surahNumber: 1,
        ayahNumber: 1,
        reciterName: 'Abdul Rahman Al-Sudais',
      );
      await Future.delayed(const Duration(seconds: 3));
      await AudioService.stopAudio();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testAthkarAudio() async {
    try {
      await _audioService.playAthkarAudio(
        athkarText: 'سبحان الله وبحمده',
        audioPath: 'assets/sounds/athkar/subhan_allah.mp3',
      );
      await Future.delayed(const Duration(seconds: 2));
      await AudioService.stopAudio();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testAthanSound() async {
    try {
      await _audioService.playAthanSound(
        prayerName: 'الفجر',
        athanPath: 'assets/sounds/athan/fajr.mp3',
      );
      await Future.delayed(const Duration(seconds: 3));
      await AudioService.stopAudio();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testBackgroundAudio() async {
    try {
      await _audioService.playBackgroundAudio(
        audioPath: 'assets/sounds/background/quran_background.mp3',
        volume: 0.3,
      );
      await Future.delayed(const Duration(seconds: 2));
      await _audioService.stopBackgroundAudio();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testAudioControls() async {
    try {
      await _audioService.playAudio('assets/sounds/test.mp3');
      await Future.delayed(const Duration(seconds: 1));
      
      await _audioService.pauseAudio();
      await Future.delayed(const Duration(milliseconds: 500));
      
      await _audioService.resumeAudio();
      await Future.delayed(const Duration(seconds: 1));
      
      await AudioService.stopAudio();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testVolumeControl() async {
    try {
      await _audioService.setVolume(0.5);
      await Future.delayed(const Duration(milliseconds: 500));
      
      await _audioService.setVolume(0.8);
      await Future.delayed(const Duration(milliseconds: 500));
      
      await _audioService.setVolume(0.6);
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testAudioFocus() async {
    try {
      await _audioService.requestAudioFocus();
      await Future.delayed(const Duration(seconds: 1));
      
      await _audioService.abandonAudioFocus();
      return true;
    } catch (e) {
      return false;
    }
  }

  void _resetTests() {
    setState(() {
      _initializeTestResults();
      _isRunningTests = false;
      _currentTestRound = 0;
    });
    widget.onStatusChanged(TestingStatus.notStarted);
  }
}

class AudioTest {
  final String id;
  final String nameAr;
  final String nameEn;
  final String description;

  AudioTest({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.description,
  });
}

enum TestResult {
  notStarted,
  inProgress,
  passed,
  failed,
}
