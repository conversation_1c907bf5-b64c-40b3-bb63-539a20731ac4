androidx.recyclerview.widget.RecyclerView
androidx.work.impl.WorkDatabase_Impl
androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
androidx.appcompat.widget.ButtonBarLayout
dev.fluttercommunity.workmanager.BackgroundWorker
io.flutter.plugins.firebase.core.FlutterFirebaseCorePlugin
androidx.camera.camera2.internal.compat.quirk.CaptureSessionStuckWhenCreatingBeforeClosingCameraQuirk
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback
androidx.startup.InitializationProvider
androidx.lifecycle.ReportFragment$LifecycleCallbacks
io.flutter.plugins.imagepicker.ImagePickerPlugin
com.google.android.gms.internal.play_billing.zzad
org.chromium.support_lib_boundary.PrefetchParamsBoundaryInterface
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface
androidx.camera.core.internal.compat.quirk.LargeJpegImageQuirk
io.flutter.plugins.pathprovider.PathProviderPlugin
android.support.v4.app.INotificationSideChannel$Stub
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
com.google.android.gms.internal.fido.zzl
androidx.camera.video.internal.compat.quirk.AudioEncoderIgnoresInputTimestampQuirk
com.google.firebase.messaging.FirebaseMessagingService
android.support.v4.media.session.PlaybackStateCompat$CustomAction
com.google.android.gms.internal.auth-api-phone.zzi
io.flutter.plugins.camerax.CameraAndroidCameraxPlugin
com.google.android.gms.common.api.internal.StatusCallback
com.google.firebase.datatransport.TransportRegistrar
androidx.appcompat.widget.Toolbar
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails
io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
androidx.browser.browseractions.BrowserActionsFallbackMenuView
com.google.android.gms.common.api.Status
androidx.camera.video.internal.compat.quirk.AudioTimestampFramePositionIncorrectQuirk
androidx.camera.video.internal.compat.quirk.SignalEosOutputBufferNotComeQuirk
androidx.media.AudioAttributesCompat
com.google.android.gms.auth.api.signin.SignInAccount
com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
androidx.appcompat.widget.SearchView
com.google.android.gms.internal.measurement.zzcw
androidx.preference.CheckBoxPreference
kotlinx.coroutines.android.AndroidDispatcherFactory
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface
android.support.v4.graphics.drawable.IconCompatParcelizer
com.google.android.gms.internal.measurement.zzco
com.google.android.gms.measurement.AppMeasurementJobService
com.google.android.gms.internal.fido.zzd
androidx.work.OverwritingInputMerger
io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback
androidx.camera.core.internal.compat.quirk.CaptureFailedRetryQuirk
com.google.android.gms.internal.play_billing.zzz
androidx.preference.Preference
android.support.v4.media.session.PlaybackStateCompat
org.chromium.support_lib_boundary.ServiceWorkerClientBoundaryInterface
com.dexterous.flutterlocalnotifications.models.BitmapSource
androidx.media.AudioAttributesImplApi26
com.google.android.gms.internal.auth-api-phone.zzd
androidx.room.MultiInstanceInvalidationService
android.support.v4.media.AudioAttributesImplApi21Parcelizer
androidx.camera.video.internal.compat.quirk.PreviewFreezeAfterHighSpeedRecordingQuirk
com.google.android.gms.internal.fido.zzb
com.google.android.gms.location.zzs
dev.fluttercommunity.plus.network_info.NetworkInfoPlusPlugin
dev.fluttercommunity.plus.battery.BatteryPlusPlugin
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface
androidx.work.impl.background.systemalarm.RescheduleReceiver
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback
androidx.work.WorkManagerInitializer
com.google.android.gms.measurement.internal.zzgf
androidx.preference.PreferenceGroup
com.google.android.gms.auth.api.signin.internal.zbt
com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry
com.dexterous.flutterlocalnotifications.models.ScheduleMode
com.dexterous.flutterlocalnotifications.models.Time
com.google.android.gms.internal.play_billing.zzah
androidx.media3.common.BundleListRetriever
com.google.android.gms.internal.ads_identifier.zzb
androidx.camera.camera2.internal.compat.quirk.Preview3AThreadCrashQuirk
com.google.android.gms.common.SupportErrorDialogFragment
android.support.v4.media.session.ParcelableVolumeInfo
io.flutter.plugins.localauth.LocalAuthPlugin
androidx.appcompat.widget.FitWindowsFrameLayout
com.dexterous.flutterlocalnotifications.models.RepeatInterval
com.dexterous.flutterlocalnotifications.models.SoundSource
com.google.android.gms.internal.fido.zzu
androidx.annotation.Keep
io.flutter.embedding.engine.FlutterJNI
com.google.android.gms.internal.auth.zzj
androidx.camera.video.internal.compat.quirk.SizeCannotEncodeVideoQuirk
androidx.work.impl.WorkDatabase
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory
com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver
androidx.window.extensions.core.util.function.Predicate
androidx.work.multiprocess.IWorkManagerImplCallback$Stub
com.google.android.gms.common.moduleinstall.internal.zaa
com.android.billingclient.api.ProxyBillingActivityV2
io.flutter.view.TextureRegistry$SurfaceTextureEntry
com.google.android.gms.dynamic.FragmentWrapper
android.support.v4.app.RemoteActionCompatParcelizer
com.google.android.gms.auth.api.signin.internal.zbo
androidx.versionedparcelable.CustomVersionedParcelable
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface
androidx.work.ArrayCreatingInputMerger
androidx.appcompat.app.AlertController$RecycleListView
androidx.recyclerview.widget.StaggeredGridLayoutManager
androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
com.google.android.gms.common.GooglePlayServicesManifestException
com.google.firebase.installations.ktx.FirebaseInstallationsKtxRegistrar
androidx.appcompat.view.menu.ListMenuItemView
androidx.media.AudioAttributesImplApi21Parcelizer
android.support.v4.media.session.MediaSessionCompat$ResultReceiverWrapper
com.google.android.gms.common.api.internal.zza
androidx.camera.camera2.internal.compat.quirk.CaptureSessionShouldUseMrirQuirk
com.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin
androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportService$Stub
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback
androidx.media.AudioAttributesImpl
com.google.firebase.components.ComponentDiscoveryService
androidx.preference.PreferenceScreen
com.google.android.gms.internal.auth.zzn
androidx.core.graphics.drawable.IconCompat
com.google.android.gms.internal.auth.zze
android.support.v4.media.session.MediaSessionCompat$Token
com.google.android.gms.auth.api.signin.internal.SignInHubActivity
com.jrai.flutter_keyboard_visibility.FlutterKeyboardVisibilityPlugin
com.google.android.gms.common.ErrorDialogFragment
androidx.camera.camera2.internal.compat.quirk.CaptureSessionOnClosedNotCalledQuirk
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
io.flutter.plugins.googlesignin.GoogleSignInPlugin
com.google.android.gms.internal.auth-api.zbb
androidx.preference.SwitchPreferenceCompat
com.google.android.gms.internal.auth.zzbd
androidx.camera.video.internal.compat.quirk.ExtraSupportedQualityQuirk
org.chromium.support_lib_boundary.WebResourceRequestBoundaryInterface
com.google.android.gms.internal.auth.zzas
androidx.camera.core.internal.compat.quirk.SoftwareJpegEncodingPreferredQuirk
androidx.camera.camera2.internal.compat.quirk.CrashWhenTakingPhotoWithAutoFlashAEModeQuirk
androidx.camera.camera2.internal.compat.quirk.CaptureNoResponseQuirk
com.google.android.gms.location.zzy
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants$ProcessGlobalConfigMapKey
com.google.android.gms.common.moduleinstall.internal.zad
android.support.customtabs.ICustomTabsCallback$Stub
androidx.camera.video.internal.compat.quirk.ExcludeStretchedVideoQualityQuirk
androidx.biometric.FingerprintDialogFragment
com.google.android.datatransport.cct.CctBackendFactory
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface
android.support.v4.media.MediaBrowserCompat$CustomActionResultReceiver
com.google.android.gms.internal.auth-api-phone.zzb
android.support.v4.media.MediaBrowserCompat$SearchResultReceiver
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface
androidx.work.CoroutineWorker
android.support.v4.os.IResultReceiver2$Stub
com.google.android.gms.auth.UserRecoverableAuthException
io.flutter.plugins.firebase.crashlytics.FlutterFirebaseCrashlyticsPlugin
com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver
com.google.firebase.crashlytics.CrashlyticsRegistrar
io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar
androidx.biometric.BiometricViewModel
io.flutter.plugin.text.ProcessTextPlugin
androidx.camera.camera2.internal.compat.quirk.SmallDisplaySizeQuirk
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport
androidx.profileinstaller.ProfileInstallReceiver
androidx.appcompat.widget.AlertDialogLayout
androidx.camera.video.internal.compat.quirk.StopCodecAfterSurfaceRemovalCrashMediaServerQuirk
androidx.camera.camera2.internal.compat.quirk.ImageCaptureFlashNotFireQuirk
org.chromium.support_lib_boundary.ProfileBoundaryInterface
androidx.core.app.RemoteActionCompat
androidx.camera.camera2.internal.compat.quirk.ImageCaptureWithFlashUnderexposureQuirk
androidx.media.AudioAttributesCompatParcelizer
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface
com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper
io.flutter.plugins.urllauncher.WebViewActivity
net.jonhanson.flutter_native_splash.FlutterNativeSplashPlugin
androidx.camera.camera2.internal.compat.quirk.TextureViewIsClosedQuirk
com.google.android.gms.dynamic.SupportFragmentWrapper
com.google.android.gms.internal.play_billing.zzaw
com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior
androidx.core.graphics.drawable.IconCompatParcelizer
com.google.android.gms.internal.play_billing.zzaf
androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus
androidx.appcompat.widget.DialogTitle
androidx.appcompat.view.menu.ExpandedMenuView
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface
com.google.firebase.messaging.FirebaseMessagingRegistrar
androidx.preference.SwitchPreference
com.google.android.gms.internal.auth.zzl
com.dexterous.flutterlocalnotifications.models.IconSource
androidx.camera.core.internal.compat.quirk.OnePixelShiftQuirk
com.google.android.gms.internal.play_billing.zzaj
android.support.v4.media.MediaBrowserCompat$MediaItem
androidx.work.impl.workers.DiagnosticsWorker
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
androidx.appcompat.widget.ActionMenuView
androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportCallback$Stub
androidx.camera.core.internal.compat.quirk.ImageCaptureFailedForSpecificCombinationQuirk
androidx.camera.camera2.internal.compat.quirk.ImageCaptureWashedOutImageQuirk
com.tekartik.sqflite.SqflitePlugin
com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation
androidx.camera.video.internal.compat.quirk.VideoQualityQuirk
com.google.android.gms.common.internal.ReflectedParcelable
androidx.fragment.app.DialogFragment
com.google.firebase.FirebaseCommonKtxRegistrar
com.google.android.gms.internal.ads_identifier.zze
androidx.preference.EditTextPreference
android.support.customtabs.IEngagementSignalsCallback$Stub
androidx.camera.camera2.internal.compat.quirk.FlashTooSlowQuirk
androidx.room.IMultiInstanceInvalidationCallback$Stub
androidx.camera.camera2.internal.compat.quirk.LegacyCameraOutputConfigNullPointerQuirk
androidx.camera.camera2.internal.compat.quirk.LegacyCameraSurfaceCleanupQuirk
androidx.work.Worker
androidx.lifecycle.ProcessLifecycleInitializer
androidx.lifecycle.ReportFragment
androidx.camera.camera2.internal.compat.quirk.PreviewPixelHDRnetQuirk
io.flutter.plugins.GeneratedPluginRegistrant
androidx.recyclerview.widget.LinearLayoutManager
androidx.profileinstaller.ProfileInstallerInitializer
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation
androidx.appcompat.widget.ActivityChooserView$InnerLayout
com.google.android.gms.common.GooglePlayServicesMissingManifestValueException
com.android.billingclient.api.ProxyBillingActivity
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack
org.chromium.support_lib_boundary.PrefetchStatusCodeBoundaryInterface
androidx.preference.DialogPreference
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType
androidx.camera.video.internal.compat.quirk.PrematureEndOfStreamVideoQuirk
androidx.camera.camera2.internal.compat.quirk.StillCaptureFlashStopRepeatingQuirk
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface
androidx.camera.camera2.internal.compat.quirk.CaptureSessionStuckQuirk
com.google.android.gms.common.internal.ICancelToken$Stub
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior
com.google.android.gms.common.api.internal.zzd
io.flutter.view.FlutterCallbackInformation
io.flutter.plugins.firebase.crashlytics.FirebaseCrashlyticsTestCrash
androidx.camera.camera2.internal.compat.quirk.InvalidVideoProfilesQuirk
androidx.media.AudioAttributesImplApi26Parcelizer
androidx.camera.camera2.internal.compat.quirk.ExcludedSupportedSizesQuirk
com.dexterous.flutterlocalnotifications.models.DateTimeComponents
android.support.customtabs.ICustomTabsService$Stub
com.google.android.gms.internal.measurement.zzcz
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation
io.flutter.view.AccessibilityViewEmbedder
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry
io.flutter.plugins.firebase.analytics.FlutterFirebaseAnalyticsPlugin
androidx.camera.core.internal.compat.quirk.LowMemoryQuirk
com.google.android.gms.common.util.DynamiteApi
androidx.preference.ListPreference
dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin
androidx.camera.camera2.internal.compat.quirk.AutoFlashUnderExposedQuirk
io.flutter.view.TextureRegistry$ImageConsumer
com.google.android.gms.internal.auth-api.zbs
androidx.camera.camera2.internal.compat.quirk.YuvImageOnePixelShiftQuirk
com.google.android.gms.common.internal.IAccountAccessor$Stub
androidx.preference.SeekBarPreference
com.google.android.gms.common.internal.zzac
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface
com.google.firebase.provider.FirebaseInitProvider
com.dexterous.flutterlocalnotifications.utils.BooleanUtils
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
androidx.camera.video.internal.compat.quirk.CodecStuckOnFlushQuirk
io.flutter.plugins.inapppurchase.InAppPurchasePlugin
com.google.android.gms.cloudmessaging.IMessengerCompat$Impl
com.dexterous.flutterlocalnotifications.models.styles.StyleInformation
com.google.android.gms.common.internal.zzd
com.baseflow.geolocator.GeolocatorLocationService
com.google.android.gms.internal.play_billing.zzar
androidx.work.impl.background.systemalarm.SystemAlarmService
io.flutter.view.TextureRegistry$ImageTextureEntry
io.flutter.plugins.firebase.crashlytics.FlutterError
androidx.camera.video.internal.compat.quirk.HdrRepeatingRequestFailureQuirk
androidx.camera.camera2.internal.compat.quirk.UseTorchAsFlashQuirk
com.google.android.gms.common.api.Scope
com.google.android.gms.internal.location.zzy
com.google.android.gms.measurement.internal.zzgc
androidx.camera.camera2.Camera2Config$DefaultProvider
com.google.android.gms.signin.internal.zad
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin
com.google.firebase.components.ComponentRegistrar
com.google.android.gms.common.internal.AccountAccessor
androidx.camera.camera2.internal.compat.quirk.PreviewOrientationIncorrectQuirk
org.chromium.support_lib_boundary.WebViewRendererBoundaryInterface
com.google.firebase.messaging.ktx.FirebaseMessagingKtxRegistrar
com.google.android.gms.location.LocationResult
com.google.android.gms.internal.auth-api.zbi
androidx.camera.camera2.internal.compat.quirk.AspectRatioLegacyApi21Quirk
kotlinx.coroutines.internal.StackTraceRecoveryKt
androidx.camera.camera2.internal.compat.quirk.AfRegionFlipHorizontallyQuirk
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface
dev.fluttercommunity.plus.share.SharePlusPlugin
org.chromium.support_lib_boundary.ScriptHandlerBoundaryInterface
com.google.android.gms.common.internal.zzz
com.google.android.gms.common.internal.service.zaj
com.google.android.gms.measurement.internal.zzjc
androidx.camera.core.ImageProcessingUtil
androidx.preference.MultiSelectListPreference
android.support.v4.media.session.MediaSessionCompat$QueueItem
com.it_nomads.fluttersecurestorage.FlutterSecureStoragePlugin
androidx.appcompat.widget.ActionBarContextView
androidx.camera.camera2.internal.compat.quirk.PreviewUnderExposureQuirk
androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
dev.fluttercommunity.plus.sensors.SensorsPlugin
androidx.appcompat.view.menu.ActionMenuItemView
com.google.firebase.iid.FirebaseInstanceIdReceiver
androidx.camera.camera2.internal.compat.quirk.Nexus4AndroidLTargetAspectRatioQuirk
android.support.v4.media.session.IMediaControllerCallback$Stub
androidx.appcompat.widget.ActionBarContainer
com.dexterous.flutterlocalnotifications.models.MessageDetails
androidx.camera.core.impl.MetadataHolderService
androidx.work.multiprocess.IListenableWorkerImpl$Stub
androidx.appcompat.widget.ContentFrameLayout
androidx.camera.core.internal.compat.quirk.ImageCaptureRotationOptionQuirk
androidx.work.impl.background.systemjob.SystemJobService
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
com.google.android.gms.common.internal.zzaf
androidx.work.impl.foreground.SystemForegroundService
org.chromium.support_lib_boundary.WebMessageListenerBoundaryInterface
androidx.core.widget.NestedScrollView
com.google.android.gms.auth.account.zzd
androidx.appcompat.widget.SearchView$SearchAutoComplete
dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin
androidx.biometric.BiometricFragment
org.chromium.support_lib_boundary.WebMessageCallbackBoundaryInterface
kotlin.coroutines.jvm.internal.BaseContinuationImpl
android.support.customtabs.trusted.ITrustedWebActivityService$Stub
com.google.android.gms.internal.auth-api.zbo
androidx.camera.camera2.internal.compat.quirk.CamcorderProfileResolutionQuirk
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer
flutter.overlay.window.flutter_overlay_window.FlutterOverlayWindowPlugin
androidx.camera.video.internal.compat.quirk.VideoEncoderCrashQuirk
android.support.v4.media.session.MediaControllerCompat$MediaControllerImplApi21$ExtraBinderRequestResultReceiver
androidx.camera.video.internal.compat.quirk.NegativeLatLongSavesIncorrectlyQuirk
org.chromium.support_lib_boundary.FeatureFlagHolderBoundaryInterface
androidx.media.AudioAttributesImplApi21
androidx.camera.video.internal.compat.quirk.VideoEncoderSuspendDoesNotIncludeSuspendTimeQuirk
com.google.android.gms.auth.api.signin.GoogleSignInOptions
com.google.android.gms.internal.measurement.zzct
android.support.customtabs.IPostMessageService$Stub
com.google.android.gms.common.annotation.KeepName
com.google.firebase.concurrent.ExecutorsRegistrar
com.dexterous.flutterlocalnotifications.models.NotificationAction
com.google.android.gms.auth.api.signin.GoogleSignInAccount
androidx.camera.core.internal.compat.quirk.SurfaceOrderQuirk
androidx.camera.camera2.internal.compat.quirk.JpegCaptureDownsizingQuirk
androidx.lifecycle.SavedStateHandlesVM
com.islamicapps.athkar.athkar_app.MainActivity
com.google.firebase.FirebaseCommonRegistrar
androidx.room.MultiInstanceInvalidationClient$callback$1
androidx.camera.video.internal.compat.quirk.ReportedVideoQualityNotSupportedQuirk
io.flutter.plugins.urllauncher.UrlLauncherPlugin
com.google.firebase.analytics.FirebaseAnalytics
androidx.camera.camera2.internal.compat.quirk.ImageCaptureFailedForVideoSnapshotQuirk
androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin
com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
org.chromium.support_lib_boundary.WebViewCookieManagerBoundaryInterface
android.support.v4.media.AudioAttributesImplApi26Parcelizer
com.google.android.gms.internal.auth-api.zbl
androidx.room.MultiInstanceInvalidationService$binder$1
androidx.media3.exoplayer.hls.HlsMediaSource$Factory
com.google.android.gms.internal.location.zzb
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
androidx.versionedparcelable.ParcelImpl
com.dexterous.flutterlocalnotifications.models.styles.MessagingStyleInformation
androidx.lifecycle.ProcessLifecycleOwner$attach$1
org.chromium.support_lib_boundary.PrefetchOperationResultBoundaryInterface
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface
com.google.android.gms.signin.internal.zac
com.google.android.gms.internal.location.zzw
androidx.appcompat.widget.FitWindowsLinearLayout
io.flutter.plugins.firebase.core.FlutterFirebasePlugin
androidx.preference.internal.PreferenceImageView
androidx.camera.camera2.internal.compat.quirk.AbnormalStreamWhenImageAnalysisBindWithTemplateRecordQuirk
androidx.camera.camera2.internal.compat.quirk.RepeatingStreamConstraintForVideoRecordingQuirk
android.support.v4.media.MediaDescriptionCompat
androidx.preference.DropDownPreference
com.google.android.gms.internal.auth.zzbf
com.google.firebase.installations.FirebaseInstallationsRegistrar
androidx.webkit.WebViewClientCompat
com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
androidx.media3.exoplayer.dash.DashMediaSource$Factory
com.google.firebase.ktx.FirebaseCommonKtxRegistrar
androidx.work.multiprocess.IWorkManagerImpl$Stub
androidx.camera.core.internal.compat.quirk.AeFpsRangeQuirk
androidx.camera.camera2.internal.compat.quirk.CameraNoResponseWhenEnablingFlashQuirk
androidx.camera.video.internal.compat.quirk.DeactivateEncoderSurfaceBeforeStopEncoderQuirk
org.chromium.support_lib_boundary.IsomorphicObjectBoundaryInterface
org.chromium.support_lib_boundary.StaticsBoundaryInterface
com.google.android.gms.internal.measurement.zzbp
androidx.lifecycle.DefaultLifecycleObserver
com.baseflow.permissionhandler.PermissionHandlerPlugin
dev.fluttercommunity.plus.share.ShareFileProvider
xyz.luan.audioplayers.AudioplayersPlugin
androidx.camera.camera2.internal.compat.quirk.ZslDisablerQuirk
com.google.android.gms.internal.auth-api.zbu
androidx.work.impl.workers.ConstraintTrackingWorker
com.dexterous.flutterlocalnotifications.models.NotificationDetails
io.flutter.plugins.imagepicker.ImagePickerFileProvider
dev.fluttercommunity.workmanager.WorkmanagerPlugin
com.google.android.gms.internal.location.zzaa
com.mr.flutter.plugin.filepicker.FilePickerPlugin
com.benjaminabel.vibration.VibrationPlugin
dev.fluttercommunity.plus.connectivity.ConnectivityPlugin
androidx.preference.PreferenceCategory
androidx.camera.camera2.internal.compat.quirk.ExtraCroppingQuirk
com.google.firebase.sessions.FirebaseSessionsRegistrar
androidx.recyclerview.widget.GridLayoutManager
androidx.camera.camera2.internal.compat.quirk.ImageCaptureFailWithAutoFlashQuirk
com.google.android.gms.internal.base.zab
com.google.android.gms.internal.location.zzn
androidx.media.AudioAttributesImplBase
androidx.camera.video.internal.compat.quirk.MediaCodecInfoReportIncorrectInfoQuirk
com.google.android.gms.internal.location.zzs
com.google.android.gms.auth.api.signin.internal.SignInConfiguration
io.flutter.plugins.videoplayer.VideoPlayerPlugin
androidx.camera.core.internal.compat.quirk.PreviewGreenTintQuirk
com.google.android.gms.common.api.internal.zact
androidx.camera.core.impl.utils.SurfaceUtil
androidx.camera.camera2.internal.compat.quirk.AeFpsRangeLegacyQuirk
io.flutter.embedding.engine.FlutterOverlaySurface
androidx.appcompat.widget.ViewStubCompat
androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory
android.support.v4.media.session.IMediaSession$Stub
com.google.android.gms.internal.play_billing.zzao
com.llfbandit.app_links.AppLinksPlugin
com.google.android.gms.common.internal.FallbackServiceBroker
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface
io.flutter.plugins.flutter_plugin_android_lifecycle.FlutterAndroidLifecyclePlugin
android.support.v4.media.AudioAttributesCompatParcelizer
com.google.android.gms.measurement.AppMeasurementService
dev.fluttercommunity.plus.share.SharePlusPendingIntent
com.google.android.gms.common.internal.IGmsServiceBroker$Stub
com.google.firebase.sessions.SessionLifecycleService
com.google.android.gms.dynamic.IObjectWrapper$Stub
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction
com.google.android.gms.dynamic.ObjectWrapper
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation
org.chromium.support_lib_boundary.VisualStateCallbackBoundaryInterface
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty
com.google.android.gms.internal.play_billing.zzam
com.google.android.gms.location.LocationRequest
androidx.camera.core.internal.compat.quirk.SurfaceProcessingQuirk
androidx.camera.camera2.internal.compat.quirk.JpegHalCorruptImageQuirk
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface
androidx.appcompat.widget.ActionBarOverlayLayout
androidx.camera.camera2.internal.compat.quirk.ImageCaptureFailedWhenVideoCaptureIsBoundQuirk
com.google.android.gms.internal.common.zzb
com.google.android.gms.auth.api.signin.RevocationBoundService
io.flutter.view.TextureRegistry$GLTextureConsumer
com.google.android.gms.auth.TokenData
dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency
android.support.v4.media.RatingCompat
com.google.android.gms.measurement.internal.zzfz
androidx.camera.camera2.internal.compat.quirk.ExtraSupportedOutputSizeQuirk
androidx.preference.UnPressableLinearLayout
com.google.android.gms.internal.auth.zzb
androidx.window.extensions.core.util.function.Function
android.support.v4.media.MediaBrowserCompat$ItemReceiver
com.google.android.gms.internal.auth-api.zbq
androidx.camera.camera2.internal.compat.quirk.TorchFlashRequiredFor3aUpdateQuirk
com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar
io.flutter.plugins.webviewflutter.WebViewFlutterPlugin
android.support.v4.os.IResultReceiver$Stub
com.google.android.gms.auth.api.signin.internal.zbq
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin
androidx.work.impl.diagnostics.DiagnosticsReceiver
androidx.camera.camera2.internal.compat.quirk.IncorrectCaptureStateQuirk
android.support.v4.media.AudioAttributesImplBaseParcelizer
com.google.android.gms.internal.location.zzq
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface
com.google.android.gms.internal.fido.zzq
androidx.camera.core.internal.compat.quirk.IncorrectJpegMetadataQuirk
androidx.camera.camera2.internal.compat.quirk.ConfigureSurfaceToSecondarySessionFailQuirk
com.google.android.gms.internal.play_billing.zzx
com.google.android.gms.common.api.internal.BasePendingResult
androidx.work.WorkerParameters
androidx.core.app.RemoteActionCompatParcelizer
com.google.android.gms.dynamite.descriptors.com.google.android.gms.measurement.dynamite.ModuleDescriptor
androidx.camera.video.internal.compat.quirk.MediaFormatMustNotUseFrameRateToFindEncoderQuirk
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
androidx.camera.camera2.internal.compat.quirk.PreviewDelayWhenVideoCaptureIsBoundQuirk
androidx.camera.camera2.internal.compat.quirk.ExtraSupportedSurfaceCombinationsQuirk
io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference
androidx.camera.camera2.internal.compat.quirk.PreviewStretchWhenVideoCaptureIsBoundQuirk
androidx.camera.camera2.internal.compat.quirk.TorchIsClosedAfterImageCapturingQuirk
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$SpeculativeLoadingStatus
com.google.android.gms.internal.measurement.zzcq
com.google.android.gms.location.LocationAvailability
com.google.android.gms.common.api.GoogleApiActivity
com.google.android.gms.internal.play_billing.zzau
com.google.android.gms.common.internal.service.zaa
androidx.camera.video.internal.compat.quirk.ExtraSupportedResolutionQuirk
org.chromium.support_lib_boundary.WebMessageBoundaryInterface
com.google.android.gms.auth.api.signin.internal.zba
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingPlugin
com.google.firebase.messaging.FirebaseMessaging
com.google.android.gms.internal.measurement.zzbm
com.dexterous.flutterlocalnotifications.utils.StringUtils
com.spencerccf.app_settings.AppSettingsPlugin
androidx.window.extensions.core.util.function.Consumer
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback
androidx.core.app.CoreComponentFactory
io.flutter.view.TextureRegistry$SurfaceProducer
com.google.android.gms.common.GooglePlayServicesIncorrectManifestValueException
androidx.camera.video.internal.compat.quirk.StretchedVideoResolutionQuirk
androidx.camera.video.internal.compat.quirk.MediaCodecDefaultDataSpaceQuirk
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation
com.google.android.gms.internal.auth-api-phone.zzf
android.support.v4.media.MediaMetadataCompat
com.baseflow.geolocator.GeolocatorPlugin
androidx.camera.camera2.internal.compat.quirk.TemporalNoiseQuirk
com.google.android.gms.measurement.AppMeasurement
androidx.preference.TwoStatePreference
com.dexterous.flutterlocalnotifications.ActionBroadcastReceiver
androidx.camera.camera2.internal.compat.quirk.CaptureIntentPreviewQuirk
androidx.camera.video.internal.compat.quirk.EncoderNotUsePersistentInputSurfaceQuirk
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader
io.flutter.plugin.platform.SingleViewPresentation
androidx.loader.app.LoaderManagerImpl$LoaderViewModel
androidx.camera.video.internal.compat.quirk.PreviewBlackScreenQuirk
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface
android.support.customtabs.trusted.ITrustedWebActivityCallback$Stub
com.google.android.gms.common.moduleinstall.internal.zag
androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
com.google.android.gms.internal.auth.zzan
com.dexterous.flutterlocalnotifications.models.NotificationStyle
androidx.work.impl.workers.CombineContinuationsWorker
com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
com.google.android.gms.dynamic.IFragmentWrapper$Stub
androidx.appcompat.widget.SwitchCompat
com.google.android.gms.internal.play_billing.zzab
com.google.android.gms.auth.account.zza
com.google.android.gms.internal.fido.zzf
androidx.media.AudioAttributesImplBaseParcelizer
com.google.android.gms.measurement.AppMeasurementReceiver
androidx.camera.video.internal.compat.quirk.MediaStoreVideoCannotWrite
androidx.transition.FragmentTransitionSupport
androidx.camera.camera2.internal.compat.quirk.FlashAvailabilityBufferUnderflowQuirk
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails
androidx.camera.video.internal.compat.quirk.CameraUseInconsistentTimebaseQuirk
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants
com.google.android.gms.location.zzv
com.dexterous.flutterlocalnotifications.models.PersonDetails
com.google.android.gms.common.api.internal.IStatusCallback$Stub
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface
androidx.room.IMultiInstanceInvalidationService$Stub
androidx.camera.camera2.internal.compat.quirk.ImageCapturePixelHDRPlusQuirk
com.google.crypto.tink.proto.AesGcmSivKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
io.flutter.embedding.engine.FlutterOverlaySurface: int id
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String VISIBILITY
com.google.android.gms.auth.TokenData: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.HmacParams: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HIDE_EXPANDED_LARGE_ICON
com.google.android.gms.internal.measurement.zzgh: java.lang.String zzd
com.google.android.gms.internal.measurement.zzig: com.google.android.gms.internal.measurement.zzig zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String BIG_TEXT
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String PLAY_SOUND
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String BIG_PICTURE_BITMAP_SOURCE
com.google.crypto.tink.proto.AesGcmKeyFormat: int keySize_
com.google.crypto.tink.proto.EncryptedKeyset: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.android.gms.location.zzal: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.Keyset: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String channelDescription
com.google.android.gms.internal.play_billing.zzlb: int zze
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval Daily
kotlinx.coroutines.CompletedExceptionally: int _handled
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HTML_FORMAT_BIG_TEXT
io.flutter.plugins.GeneratedPluginRegistrant: java.lang.String TAG
com.google.android.gms.internal.measurement.zzii: com.google.android.gms.internal.measurement.zzii zzg
io.flutter.embedding.engine.FlutterJNI: java.lang.String TAG
com.google.android.gms.internal.measurement.zzgn: com.google.android.gms.internal.measurement.zzmn zzb
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Parser PARSER
com.google.android.gms.internal.play_billing.zzln: int zzd
com.google.android.gms.internal.measurement.zzid: int zzI
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SCHEDULE_MODE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String PAYLOAD
com.google.android.gms.common.internal.zzal: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.play_billing.zzki: int zzd
com.google.crypto.tink.proto.Keyset: com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList key_
com.dexterous.flutterlocalnotifications.models.Time: java.lang.Integer minute
com.google.android.gms.internal.measurement.zzid: java.lang.String zzp
kotlinx.coroutines.android.HandlerContext: kotlinx.coroutines.android.HandlerContext _immediate
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$SpeculativeLoadingStatus: int DISABLED
com.google.android.gms.internal.measurement.zzib: int zzg
com.google.android.gms.internal.measurement.zzig: com.google.android.gms.internal.measurement.zzmn zze
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CANCEL_ALL_METHOD
com.google.android.gms.internal.measurement.zzhs: long zzf
com.google.crypto.tink.proto.KeysetInfo: int PRIMARY_KEY_ID_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzgf: boolean zzg
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader: java.lang.ClassLoader sClassLoader
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String TITLE_COLOR_RED
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: java.lang.String name
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer day
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean attached
io.flutter.embedding.engine.FlutterJNI: java.util.Set flutterUiDisplayListeners
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource DrawableResource
com.dexterous.flutterlocalnotifications.models.SoundSource: com.dexterous.flutterlocalnotifications.models.SoundSource[] $VALUES
com.google.crypto.tink.proto.AesSivKeyFormat: com.google.crypto.tink.proto.AesSivKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesEaxKey: com.google.crypto.tink.proto.AesEaxKey DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzgr: java.lang.String zze
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TEXT
kotlinx.coroutines.JobSupport: java.lang.Object _state
com.google.android.gms.internal.measurement.zzfn: boolean zzi
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ENABLE_LIGHTS
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String PROGRESS
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval Weekly
com.google.crypto.tink.proto.XChaCha20Poly1305Key: int KEY_VALUE_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzgt: java.lang.String zzd
com.google.crypto.tink.proto.AesCmacKey: int PARAMS_FIELD_NUMBER
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
com.dexterous.flutterlocalnotifications.models.Time: java.lang.String MINUTE
com.google.crypto.tink.proto.AesGcmKeyFormat: int version_
com.google.android.gms.internal.measurement.zzis: int zzb
com.google.android.gms.internal.auth.zzdq: int zza
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String icon
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ticker
com.google.android.gms.internal.measurement.zzgr: int zzb
com.google.crypto.tink.proto.AesCtrHmacAeadKey: com.google.crypto.tink.proto.AesCtrHmacAeadKey DEFAULT_INSTANCE
com.google.firebase.datatransport.TransportRegistrar: java.lang.String LIBRARY_NAME
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String GROUP_CONVERSATION
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ICON
com.google.android.gms.common.internal.RootTelemetryConfiguration: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.AesSivKey: int KEY_VALUE_FIELD_NUMBER
com.google.android.gms.measurement.internal.zzah: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TITLE
com.google.crypto.tink.proto.AesCtrKeyFormat: com.google.crypto.tink.proto.AesCtrParams params_
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims
com.google.android.gms.common.api.Scope: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.location.zze: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.AesGcmKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.AesGcmKeyFormat: int KEY_SIZE_FIELD_NUMBER
androidx.media3.common.Metadata: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.play_billing.zzkd: int zzg
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean newFrameAvailable
com.google.android.gms.internal.measurement.zzff: java.lang.String zze
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat: int VERSION_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents Time
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified appContext
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
com.google.crypto.tink.proto.AesCtrHmacAeadKey: int AES_CTR_KEY_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String AUDIO_ATTRIBUTES_USAGE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency scheduledNotificationRepeatFrequency
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ONGOING
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents DayOfMonthAndTime
com.google.android.gms.internal.measurement.zzhu: long zze
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean notifiedDestroy
com.google.crypto.tink.proto.AesCmacParams: int TAG_SIZE_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzfr: com.google.android.gms.internal.measurement.zzfr zzh
com.dexterous.flutterlocalnotifications.models.MessageDetails: java.lang.Long timestamp
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_NOTIFICATION_CHANNELS_METHOD
com.google.android.gms.internal.measurement.zzfw: int zzb
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String DELETE_NOTIFICATION_CHANNEL_GROUP_METHOD
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CREATE_NOTIFICATION_CHANNEL_GROUP_METHOD
com.google.android.gms.internal.measurement.zzgj: boolean zze
androidx.media3.extractor.metadata.mp4.SlowMotionData: android.os.Parcelable$Creator CREATOR
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType: int TYPE_STRING
com.google.android.gms.internal.measurement.zzhq: int zzb
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource ByteArray
com.google.android.gms.internal.play_billing.zzkx: com.google.android.gms.internal.play_billing.zzkx zzb
com.google.crypto.tink.proto.RegistryConfig: int ENTRY_FIELD_NUMBER
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state
com.google.android.gms.internal.measurement.zzhc: com.google.android.gms.internal.measurement.zzhc zzk
kotlinx.coroutines.DispatchedCoroutine: int _decision
com.google.android.gms.internal.measurement.zzid: java.lang.String zzt
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int status_
com.google.android.gms.internal.measurement.zzja: com.google.android.gms.internal.measurement.zziw zze
androidx.datastore.preferences.PreferencesProto$Value: int BOOLEAN_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzhc: java.lang.String zzf
com.dexterous.flutterlocalnotifications.models.NotificationAction: com.dexterous.flutterlocalnotifications.models.IconSource iconSource
io.flutter.embedding.engine.FlutterJNI: float displayWidth
com.google.android.gms.signin.internal.zai: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.AesCtrKey: com.google.crypto.tink.proto.AesCtrParams params_
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat DEFAULT_INSTANCE
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
com.google.android.gms.common.api.internal.BasePendingResult: com.google.android.gms.common.api.internal.zas resultGuardian
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LARGE_ICON_BITMAP_SOURCE
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List mutators
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String GROUP_ALERT_BEHAVIOR
com.google.android.gms.auth.api.identity.AuthorizationResult: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgv: com.google.android.gms.internal.measurement.zzgv zzi
androidx.datastore.preferences.PreferencesProto$StringSet: int STRINGS_FIELD_NUMBER
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object tail
com.google.android.gms.internal.measurement.zzid: java.lang.String zzN
com.google.android.gms.internal.measurement.zzid: com.google.android.gms.internal.measurement.zzid zzat
com.google.android.gms.internal.measurement.zzfr: int zzd
com.google.crypto.tink.proto.AesEaxKeyFormat: int KEY_SIZE_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_IMAGES
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager deferredComponentManager
com.google.crypto.tink.proto.HmacKey: com.google.crypto.tink.proto.HmacKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.HmacParams: com.google.crypto.tink.proto.HmacParams DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesCtrKeyFormat: int keySize_
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: java.lang.String ID
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.app.Activity mainActivity
com.google.android.gms.common.zzs: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean enableLights
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ICON_SOURCE
com.google.crypto.tink.proto.KeyTemplate: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle
com.google.crypto.tink.proto.KeyData: int TYPE_URL_FIELD_NUMBER
com.google.android.gms.internal.play_billing.zzlx: com.google.android.gms.internal.play_billing.zzlx zzb
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.Boolean allowGeneratedReplies
com.google.android.gms.internal.location.zzeg: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String EXACT_ALARMS_PERMISSION_ERROR_CODE
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: int HMAC_KEY_FORMAT_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TICKER
com.google.android.gms.internal.measurement.zzhe: boolean zzh
com.google.android.gms.internal.measurement.zzii: com.google.android.gms.internal.measurement.zzmm zzb
com.google.android.gms.internal.measurement.zzfn: boolean zzh
com.google.android.gms.internal.measurement.zzfr: boolean zzf
com.google.android.gms.internal.measurement.zzfl: int zzb
com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormat: com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeyTypeEntry: java.lang.String catalogueName_
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String groupId
com.google.android.gms.internal.play_billing.zzjz: com.google.android.gms.internal.play_billing.zzjz zzb
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$SpeculativeLoadingStatus: int PRERENDER_ENABLED
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: float finalOpacity
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: java.lang.Class baseType
com.google.android.gms.common.server.converter.zac: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfn: java.lang.String zze
com.google.android.gms.internal.measurement.zzha: com.google.android.gms.internal.measurement.zznf zzm
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
com.google.crypto.tink.proto.AesCmacKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.ScheduleMode scheduleMode
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: com.google.crypto.tink.proto.AesCtrKeyFormat aesCtrKeyFormat_
com.google.android.gms.internal.play_billing.zzkl: com.google.android.gms.internal.play_billing.zzkl zzb
com.google.android.gms.internal.play_billing.zzdy: com.google.android.gms.internal.play_billing.zzdy$zzd listeners
io.flutter.plugin.platform.SingleViewPresentation: int viewId
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SILENT
com.google.crypto.tink.proto.HmacKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner
com.google.android.gms.auth.AccountChangeEvent: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String CANCEL_NOTIFICATION
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String DRAWABLE
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: java.util.ArrayList lines
com.google.crypto.tink.proto.AesCtrHmacAeadKey: int version_
com.google.android.gms.internal.measurement.zzgh: int zzb
com.google.android.gms.common.moduleinstall.ModuleInstallIntentResponse: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
com.google.android.gms.internal.measurement.zzfh: int zzb
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev
com.google.android.gms.internal.play_billing.zzkx: boolean zze
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.localization.LocalizationPlugin localizationPlugin
com.google.crypto.tink.proto.AesEaxKeyFormat: com.google.crypto.tink.proto.AesEaxParams params_
com.google.android.gms.internal.measurement.zzhw: java.lang.String zzd
com.google.android.gms.internal.measurement.zzgc: java.lang.String zze
com.google.crypto.tink.proto.EncryptedKeyset: com.google.crypto.tink.shaded.protobuf.ByteString encryptedKeyset_
com.google.android.gms.internal.play_billing.zzdy$zzk: java.lang.Thread thread
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Internal$ProtobufList strings_
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified firebaseSessionsComponent
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedWidth
kotlinx.coroutines.internal.AtomicOp: java.lang.Object _consensus
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource DrawableResource
com.google.android.gms.internal.measurement.zzha: int zzb
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHANNEL_NAME
com.google.crypto.tink.proto.KeyTemplate: int outputPrefixType_
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String SOUND_SOURCE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String IMPORTANT
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer
com.google.crypto.tink.proto.HmacParams: int hash_
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String REQUEST_FULL_SCREEN_INTENT_PERMISSION_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String LED_COLOR_GREEN
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INPUT
androidx.media3.extractor.metadata.vorbis.VorbisComment: android.os.Parcelable$Creator CREATOR
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int DISABLED
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer maxProgress
androidx.media3.extractor.metadata.emsg.EventMessage: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat DEFAULT_INSTANCE
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String ALLOW_GENERATED_REPLIES
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
com.google.android.gms.internal.measurement.zzis: int zzd
androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem: android.os.Parcelable$Creator CREATOR
androidx.work.impl.utils.futures.AbstractFuture: androidx.work.impl.utils.futures.AbstractFuture$Waiter waiters
com.google.android.gms.internal.measurement.zzff: com.google.android.gms.internal.measurement.zzfl zzh
com.google.crypto.tink.proto.AesSivKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.KeyTypeEntry: java.lang.String primitiveName_
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mAppId
com.google.android.gms.measurement.internal.zzog: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzho: com.google.android.gms.internal.measurement.zzmn zzb
com.google.android.gms.dynamite.descriptors.com.google.android.gms.measurement.dynamite.ModuleDescriptor: java.lang.String MODULE_ID
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean autoCancel
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String IMPORTANCE
com.google.android.gms.internal.measurement.zzgr: com.google.android.gms.internal.measurement.zzgr zzf
com.google.android.gms.location.LocationRequest: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.location.zzei: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.KeysetInfo: com.google.crypto.tink.proto.KeysetInfo DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeyTemplate: com.google.crypto.tink.proto.KeyTemplate DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzja: int zzb
com.google.android.gms.internal.measurement.zzik: int zzd
com.google.android.gms.internal.measurement.zzgh: com.google.android.gms.internal.measurement.zzgh zzg
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: java.lang.String summaryText
com.google.android.gms.internal.measurement.zzid: long zzv
androidx.fragment.app.FragmentManagerState: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.platform.SingleViewPresentation: java.lang.String TAG
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer groupAlertBehavior
com.google.crypto.tink.proto.Keyset$Key: int OUTPUT_PREFIX_TYPE_FIELD_NUMBER
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: android.os.Bundle mExpiredEventParams
com.google.android.gms.internal.play_billing.zzki: com.google.android.gms.internal.play_billing.zzki zzb
androidx.media3.extractor.metadata.scte35.SpliceNullCommand: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzid: boolean zzaf
com.google.crypto.tink.proto.AesEaxKey: int version_
com.google.android.gms.internal.measurement.zzid: int zzd
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_NOTIFICATION_CHANNELS_ERROR_CODE
android.support.v4.media.session.MediaSessionCompat$ResultReceiverWrapper: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_RAW_RESOURCE_ERROR_MESSAGE
io.flutter.plugin.platform.SingleViewPresentation: android.widget.FrameLayout container
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String scheduledDateTime
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CATEGORY
com.google.android.gms.auth.api.identity.SaveAccountLinkingTokenResult: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzhe: boolean zzj
com.google.crypto.tink.proto.AesSivKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCtrParams: int IV_SIZE_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.Time: java.lang.Integer hour
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: java.util.Map registeredPlugins
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String PENDING_NOTIFICATION_REQUESTS_METHOD
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets lastWindowInsets
com.google.android.gms.auth.api.accounttransfer.zzu: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.play_billing.zzkr: int zzf
com.google.android.gms.common.moduleinstall.ModuleInstallResponse: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzid: int zzb
com.google.android.gms.internal.play_billing.zzkx: int zzd
com.google.android.gms.internal.measurement.zzdd: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: int BYTES_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean showWhen
com.google.android.gms.internal.measurement.zzhq: com.google.android.gms.internal.measurement.zzhq zzf
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.String summaryText
io.flutter.embedding.engine.FlutterJNI: float displayHeight
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String METHOD_CHANNEL
com.google.android.gms.internal.measurement.zzid: com.google.android.gms.internal.measurement.zzmn zzE
com.google.android.gms.internal.play_billing.zzkr: boolean zzk
androidx.media3.exoplayer.hls.HlsTrackMetadataEntry$VariantInfo: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgt: int zzb
com.google.android.gms.internal.measurement.zzgv: java.lang.String zze
com.google.android.gms.internal.measurement.zzfh: com.google.android.gms.internal.measurement.zzfr zzd
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController2 platformViewsController2
com.google.android.gms.internal.measurement.zzfw: com.google.android.gms.internal.measurement.zzfw zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ONLY_ALERT_ONCE
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction CreateIfNotExists
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval Hourly
io.flutter.embedding.engine.FlutterJNI: java.util.concurrent.locks.ReentrantReadWriteLock shellHolderLock
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
com.google.crypto.tink.proto.KmsAeadKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean silent
com.google.crypto.tink.proto.KeyTypeEntry: int PRIMITIVE_NAME_FIELD_NUMBER
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LED_COLOR_BLUE
com.google.android.gms.internal.play_billing.zzhk: com.google.android.gms.internal.play_billing.zzjk zzc
com.google.crypto.tink.proto.AesGcmSivKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.google.firebase.sessions.FirebaseSessionsRegistrar: java.lang.String LIBRARY_NAME
com.google.crypto.tink.proto.HmacKeyFormat: int version_
com.google.android.gms.internal.play_billing.zzq: java.lang.Object zzc
androidx.datastore.preferences.PreferencesProto$Value: int DOUBLE_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String PERMISSION_REQUEST_IN_PROGRESS_ERROR_CODE
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String ACTION_ID
com.google.android.gms.internal.measurement.zzid: java.lang.String zzU
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Integer importance
io.flutter.plugin.platform.SingleViewPresentation: boolean startFocused
com.google.crypto.tink.proto.AesSivKeyFormat: int keySize_
androidx.media3.extractor.metadata.id3.CommentFrame: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String GROUP_KEY
com.google.android.gms.internal.play_billing.zzkd: int zzd
com.google.android.gms.internal.measurement.zziu: int zzb
io.flutter.view.AccessibilityViewEmbedder: io.flutter.view.AccessibilityViewEmbedder$ReflectionAccessors reflectionAccessors
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String LED_COLOR_ALPHA
com.google.android.gms.internal.measurement.zzfh: com.google.android.gms.internal.measurement.zzfh zzh
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef
com.google.android.gms.cloudmessaging.zzd: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: com.google.gson.Gson gson
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer audioAttributesUsage
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex
io.flutter.embedding.engine.FlutterJNI: android.os.Looper mainLooper
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd
com.google.android.gms.common.server.response.FastJsonResponse$Field: com.google.android.gms.common.server.response.zaj CREATOR
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.PreferencesProto$StringSet DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzfl: java.lang.String zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TIMESTAMP
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String LED_COLOR_RED
com.google.android.gms.common.Feature: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean playSound
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.RepeatInterval repeatInterval
com.google.android.gms.internal.measurement.zzhc: int zzb
org.chromium.support_lib_boundary.PrefetchStatusCodeBoundaryInterface: int SUCCESS
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer importance
com.dexterous.flutterlocalnotifications.models.PersonDetails: com.dexterous.flutterlocalnotifications.models.IconSource iconBitmapSource
com.google.android.gms.internal.measurement.zzid: java.lang.String zzn
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastDequeueTime
androidx.media3.extractor.metadata.scte35.SpliceInsertCommand: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String COLORIZED
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CALLBACK_HANDLE
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified blockingDispatcher
com.google.android.gms.internal.measurement.zzhu: int zzb
androidx.media3.extractor.metadata.icy.IcyHeaders: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgl: java.lang.String zzj
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.Object mValue
com.google.crypto.tink.proto.Keyset: int KEY_FIELD_NUMBER
androidx.media3.extractor.metadata.id3.ChapterFrame: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfu: int zzf
com.google.android.gms.internal.measurement.zzid: com.google.android.gms.internal.measurement.zzho zzas
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LINES
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String SELECT_FOREGROUND_NOTIFICATION_ACTION
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mOrigin
com.google.android.gms.internal.measurement.zzgp: com.google.android.gms.internal.measurement.zzgp zzg
com.google.android.gms.internal.measurement.zzgj: int zzg
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int APP_SOURCE_AND_WEB_TRIGGER
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode alarmClock
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
com.google.android.gms.internal.play_billing.zzln: java.lang.String zzg
com.google.crypto.tink.proto.AesCmacKey: int KEY_VALUE_FIELD_NUMBER
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting
com.google.android.gms.internal.measurement.zzkr: int zza
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.Object lock
com.google.android.gms.dynamite.descriptors.com.google.android.gms.measurement.dynamite.ModuleDescriptor: int MODULE_VERSION
io.flutter.embedding.engine.FlutterJNI: boolean loadLibraryCalled
com.google.crypto.tink.proto.EncryptedKeyset: com.google.crypto.tink.proto.EncryptedKeyset DEFAULT_INSTANCE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer repeatIntervalMilliseconds
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SET_AS_GROUP_SUMMARY
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Long millisecondsSinceEpoch
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String IMPORTANCE
com.google.android.gms.internal.measurement.zzfn: com.google.android.gms.internal.measurement.zzfh zzf
com.google.android.gms.common.moduleinstall.ModuleInstallStatusUpdate: android.os.Parcelable$Creator CREATOR
androidx.media3.extractor.metadata.scte35.SpliceScheduleCommand: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.KeyTemplate: int VALUE_FIELD_NUMBER
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
com.google.android.gms.location.LocationAvailability: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle[] $VALUES
com.google.crypto.tink.proto.AesCtrKey: int VERSION_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String SHOW_BADGE
com.google.android.gms.internal.measurement.zzii: com.google.android.gms.internal.measurement.zzmn zze
com.google.crypto.tink.proto.RegistryConfig: com.google.crypto.tink.proto.RegistryConfig DEFAULT_INSTANCE
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackLibraryPath
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: java.lang.String typeUrl_
com.google.crypto.tink.proto.AesCtrKeyFormat: int PARAMS_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String TITLE_COLOR_ALPHA
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: com.dexterous.flutterlocalnotifications.models.SoundSource soundSource
com.google.android.gms.internal.measurement.zzff: int zzd
com.google.android.gms.internal.measurement.zzid: java.lang.String zzao
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CALLED_AT
com.google.android.gms.internal.measurement.zzfn: int zzb
com.google.android.gms.internal.measurement.zzgc: int zzb
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode exactAllowWhileIdle
com.google.crypto.tink.proto.Keyset: com.google.crypto.tink.proto.Keyset DEFAULT_INSTANCE
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String ARE_NOTIFICATIONS_ENABLED_METHOD
com.google.android.gms.internal.measurement.zzgl: com.google.android.gms.internal.measurement.zzgp zzq
com.google.android.gms.internal.measurement.zzik: com.google.android.gms.internal.measurement.zzmm zze
com.google.android.gms.internal.measurement.zzfh: boolean zzf
com.google.android.gms.internal.measurement.zzid: int zze
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
com.google.crypto.tink.proto.XChaCha20Poly1305Key: int VERSION_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzja: com.google.android.gms.internal.measurement.zzmn zzd
com.google.android.gms.internal.measurement.zzhg: int zzd
com.google.android.gms.location.LocationSettingsStates: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean ignoringFence
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Integer audioAttributesUsage
com.google.android.gms.auth.api.proxy.ProxyResponse: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfw: int zzd
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String DEFAULT_ICON
com.google.android.gms.internal.measurement.zzid: java.lang.String zzag
com.google.android.gms.internal.play_billing.zzlk: com.google.android.gms.internal.play_billing.zzlk zzb
com.google.android.gms.internal.measurement.zzgp: int zzd
com.google.android.gms.internal.play_billing.zzkr: com.google.android.gms.internal.play_billing.zzki zzi
com.google.android.gms.internal.play_billing.zzq: com.google.android.gms.internal.play_billing.zzj zzd
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: com.google.crypto.tink.proto.KmsEnvelopeAeadKey DEFAULT_INSTANCE
androidx.datastore.preferences.PreferencesProto$Value: int LONG_FIELD_NUMBER
com.google.android.gms.internal.play_billing.zzkr: boolean zzj
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer priority
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat params_
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency Daily
com.google.firebase.installations.FirebaseInstallationsRegistrar: java.lang.String LIBRARY_NAME
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CAN_SCHEDULE_EXACT_NOTIFICATIONS_METHOD
com.google.crypto.tink.proto.Keyset$Key: int KEY_DATA_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ADDITIONAL_FLAGS
androidx.work.impl.utils.futures.AbstractFuture$Waiter: java.lang.Thread thread
com.google.android.gms.internal.play_billing.zzlk: int zze
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CONTENT_TITLE
com.google.android.gms.common.internal.zzk: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$AccessibilityDelegatingFrameLayout rootView
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean released
com.google.android.gms.internal.play_billing.zzln: com.google.android.gms.internal.play_billing.zzho zze
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String TITLE_COLOR_BLUE
com.google.crypto.tink.proto.AesEaxKey: int PARAMS_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHRONOMETER_COUNT_DOWN
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String CACHE_DIRECTORY_BASE_PATH
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TAG
com.google.android.gms.internal.measurement.zzid: boolean zzZ
com.google.android.gms.internal.play_billing.zzlq: com.google.android.gms.internal.play_billing.zzki zze
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.dart.PlatformMessageHandler platformMessageHandler
com.google.android.gms.internal.measurement.zzfr: com.google.android.gms.internal.measurement.zzmn zzg
androidx.media3.extractor.metadata.flac.VorbisComment: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ENABLE_VIBRATION
com.google.crypto.tink.proto.KmsAeadKeyFormat: java.lang.String keyUri_
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int deferredInsetTypes
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: java.lang.Boolean htmlFormatSummaryText
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackClassName
com.google.android.gms.internal.measurement.zzis: com.google.android.gms.internal.measurement.zzis zzg
com.google.crypto.tink.proto.KeyTemplate: int OUTPUT_PREFIX_TYPE_FIELD_NUMBER
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType: int TYPE_ARRAY_BUFFER
com.google.android.gms.internal.measurement.zzfd: boolean zzg
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String URI
com.google.crypto.tink.proto.AesCtrHmacAeadKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.android.gms.internal.measurement.zzff: boolean zzi
com.google.android.gms.common.server.converter.zaa: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String PERIODICALLY_SHOW_METHOD
com.google.android.gms.internal.auth.zzhs: com.google.android.gms.internal.auth.zzhs zzb
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: long[] vibrationPattern
com.google.android.gms.internal.play_billing.zzjz: int zzg
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: java.lang.String kekUri_
com.google.android.gms.internal.measurement.zzid: boolean zzx
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ACTIONS
com.google.android.gms.internal.measurement.zzid: int zzA
com.google.android.gms.internal.play_billing.zzjz: int zze
com.google.android.gms.common.internal.ClientIdentity: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle BigText
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String name
com.google.android.gms.internal.measurement.zzib: int zzb
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.Time repeatTime
com.google.android.gms.internal.measurement.zzid: int zzW
com.google.crypto.tink.proto.AesEaxKey: com.google.crypto.tink.proto.AesEaxParams params_
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.SoundSource soundSource
com.google.android.gms.internal.measurement.zzje: java.lang.String zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer color
com.google.android.gms.internal.measurement.zziy: java.lang.String zzd
com.google.android.gms.internal.measurement.zzgh: com.google.android.gms.internal.measurement.zzmn zze
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_DRAWABLE_RESOURCE_ERROR_MESSAGE
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: java.lang.Runnable onFrameConsumed
com.google.android.gms.internal.measurement.zzid: int zzO
com.google.android.gms.internal.play_billing.zzlx: int zzd
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: java.lang.String id
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.Boolean contextual
com.google.crypto.tink.proto.KeyTypeEntry: int NEW_KEY_ALLOWED_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzhu: java.lang.String zzd
com.google.android.gms.internal.play_billing.zzlb: int zzd
com.google.crypto.tink.proto.AesCmacKeyFormat: int keySize_
android.support.v4.media.MediaMetadataCompat: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzid: java.lang.String zzm
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHANNEL_DESCRIPTION
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: java.lang.Boolean htmlFormatSummaryText
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Boolean enableLights
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean usesChronometer
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: com.dexterous.flutterlocalnotifications.models.BitmapSource bigPictureBitmapSource
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction channelAction
com.google.android.gms.internal.measurement.zzgl: long zzd
com.google.android.gms.internal.measurement.zzid: java.lang.String zzab
com.google.android.gms.internal.measurement.zzff: boolean zzj
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String payload
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String ZONED_SCHEDULE_METHOD
com.google.android.gms.common.internal.zat: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: java.lang.String NAME
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle BigPicture
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id
com.google.android.gms.internal.measurement.zzje: boolean zzh
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated
com.google.android.gms.internal.measurement.zzfu: int zzd
com.google.android.gms.internal.measurement.zzid: java.lang.String zzF
com.google.android.gms.internal.measurement.zzha: java.lang.String zzj
com.google.android.gms.common.ConnectionResult: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HTML_FORMAT_CONTENT_TITLE
com.google.android.gms.internal.measurement.zzhe: boolean zzi
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Long timeoutAfter
com.google.android.gms.internal.play_billing.zzfn: com.google.android.gms.internal.play_billing.zzfn zzb
com.google.android.gms.internal.measurement.zzid: int zzG
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SUMMARY_TEXT
com.google.android.gms.internal.measurement.zzid: boolean zzah
com.google.android.gms.internal.play_billing.zzq: com.google.android.gms.internal.play_billing.zzo zze
com.google.crypto.tink.proto.RegistryConfig: com.google.crypto.tink.shaded.protobuf.Parser PARSER
androidx.fragment.app.BackStackState: android.os.Parcelable$Creator CREATOR
androidx.media3.common.DrmInitData: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zziu: long zzg
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_LED_DETAILS_ERROR_CODE
com.dexterous.flutterlocalnotifications.models.MessageDetails: java.lang.String dataUri
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int KEY_ID_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback callback
com.google.android.gms.internal.location.zzl: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzhy: java.lang.String zzd
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle lifecycle
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String SOUND
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String DESCRIPTION
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer iconResourceId
androidx.datastore.preferences.protobuf.GeneratedMessageLite: androidx.datastore.preferences.protobuf.UnknownFieldSetLite unknownFields
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.PreferencesProto$Value DEFAULT_INSTANCE
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Boolean enableVibration
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer ledOffMs
com.google.android.gms.internal.measurement.zzgl: com.google.android.gms.internal.measurement.zzmn zzi
com.google.android.gms.internal.measurement.zziu: long zzd
com.google.android.gms.internal.measurement.zzfl: com.google.android.gms.internal.measurement.zzfl zzi
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: int FULL_SCREEN_INTENT_PERMISSION_REQUEST_CODE
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String GROUP_ID
com.google.android.gms.auth.api.accounttransfer.DeviceMetaData: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzid: boolean zzD
com.google.android.gms.internal.measurement.zzhe: int zzb
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String KEY
com.dexterous.flutterlocalnotifications.models.styles.MessagingStyleInformation: com.dexterous.flutterlocalnotifications.models.PersonDetails person
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.styles.StyleInformation styleInformation
com.google.crypto.tink.proto.AesCmacKey: int version_
com.google.crypto.tink.proto.AesCtrHmacAeadKey: com.google.crypto.tink.proto.HmacKey hmacKey_
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: java.lang.Boolean htmlFormatContentTitle
com.google.android.gms.internal.measurement.zzid: int zzam
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayList lastDequeuedImage
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle Media
com.google.crypto.tink.proto.ChaCha20Poly1305Key: int version_
com.google.android.gms.internal.measurement.zzid: long zzk
androidx.datastore.preferences.PreferencesProto$Value: int STRING_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String WHEN
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String SELECT_NOTIFICATION
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String AUDIO_ATTRIBUTES_USAGE
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CANCEL_TAG
com.google.crypto.tink.proto.Keyset$Key: int STATUS_FIELD_NUMBER
com.google.android.gms.internal.play_billing.zzfl: com.google.android.gms.internal.play_billing.zzfq zzf
com.google.android.gms.internal.play_billing.zzki: java.lang.String zzh
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CANCEL_NOTIFICATION
com.google.android.gms.internal.measurement.zzid: com.google.android.gms.internal.measurement.zzha zzan
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment
android.support.v4.os.ResultReceiver: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: boolean prefetchDefaultFontManagerCalled
com.google.crypto.tink.proto.KmsAeadKey: com.google.crypto.tink.proto.KmsAeadKey DEFAULT_INSTANCE
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval EveryMinute
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CONVERSATION_TITLE
androidx.appcompat.widget.AppCompatSpinner$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.KeyTemplate: com.google.crypto.tink.shaded.protobuf.ByteString value_
androidx.media3.extractor.metadata.id3.UrlLinkFrame: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzhq: long zze
com.google.crypto.tink.proto.Keyset$Key: com.google.crypto.tink.proto.Keyset$Key DEFAULT_INSTANCE
com.google.android.gms.internal.play_billing.zzln: com.google.android.gms.internal.play_billing.zzln zzb
com.google.android.gms.common.server.converter.StringToIntConverter: android.os.Parcelable$Creator CREATOR
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: long mTimeToLive
com.google.android.gms.internal.measurement.zzfu: com.google.android.gms.internal.measurement.zzfu zzg
com.google.crypto.tink.proto.RegistryConfig: int CONFIG_NAME_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String PAYLOAD
com.google.android.gms.location.LastLocationRequest: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.KeyData: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause
io.flutter.embedding.engine.FlutterJNI: java.util.Set engineLifecycleListeners
com.google.android.gms.internal.play_billing.zzlu: int zzd
com.google.android.gms.internal.measurement.zzhy: int zzb
com.google.android.gms.internal.measurement.zzha: long zzg
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.Object largeIcon
com.google.android.gms.internal.play_billing.zzdy: com.google.android.gms.internal.play_billing.zzdy$zzk waiters
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String SCHEDULED_NOTIFICATIONS
com.google.android.gms.internal.measurement.zzhs: java.lang.String zze
kotlinx.coroutines.internal.Segment: int cleanedAndPointers
com.google.android.gms.internal.play_billing.zzlh: com.google.android.gms.internal.play_billing.zzlh zzb
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.String TAG
io.flutter.embedding.engine.FlutterJNI: java.lang.String vmServiceUri
com.google.android.gms.internal.measurement.zzhl: com.google.android.gms.internal.measurement.zzhl zzf
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
com.google.android.gms.internal.measurement.zzgl: boolean zzk
com.google.android.gms.internal.measurement.zzhw: long zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String shortcutId
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzhs: int zzh
com.google.crypto.tink.proto.KeyTemplate: int TYPE_URL_FIELD_NUMBER
com.google.crypto.tink.proto.HmacKey: com.google.crypto.tink.proto.HmacParams params_
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String NOTIFICATION_ID
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String STYLE
com.google.crypto.tink.proto.KeyData: com.google.crypto.tink.shaded.protobuf.ByteString value_
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer id
io.flutter.view.AccessibilityViewEmbedder: java.util.Map embeddedViewToDisplayBounds
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedHeight
com.dexterous.flutterlocalnotifications.models.MessageDetails: java.lang.String dataMimeType
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource BitmapFilePath
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String UNSUPPORTED_OS_VERSION_ERROR_CODE
com.google.crypto.tink.proto.KeyTypeEntry: int CATALOGUE_NAME_FIELD_NUMBER
androidx.media3.extractor.metadata.scte35.PrivateCommand: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzid: long zzu
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$InsetsListener insetsListener
com.google.android.gms.internal.measurement.zzho: com.google.android.gms.internal.measurement.zzho zzd
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgv: int zzg
androidx.media3.container.Mp4LocationData: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.Boolean showsUserInterface
com.google.android.gms.internal.measurement.zzid: java.lang.String zzo
com.google.android.gms.internal.measurement.zzgp: int zzb
com.google.crypto.tink.proto.AesSivKey: com.google.crypto.tink.proto.AesSivKey DEFAULT_INSTANCE
com.google.android.gms.internal.play_billing.zzlx: int zze
androidx.datastore.preferences.PreferencesProto$Value: int valueCase_
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int TYPE_URL_FIELD_NUMBER
com.google.crypto.tink.proto.KeyTypeEntry: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.KeyData: int VALUE_FIELD_NUMBER
com.google.android.gms.measurement.internal.zzao: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.internal.ConnectionTelemetryConfiguration: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzid: long zzz
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String MATCH_DATE_TIME_COMPONENTS
com.google.crypto.tink.proto.AesGcmKeyFormat: int VERSION_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzgc: java.lang.String zzd
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: java.lang.String contentTitle
com.google.android.gms.common.api.Status: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.play_billing.zzfq: com.google.android.gms.internal.play_billing.zzfq zzb
com.google.android.gms.internal.play_billing.zzdy$zzk: com.google.android.gms.internal.play_billing.zzdy$zzk next
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
androidx.transition.ChangeBounds$7: androidx.transition.ChangeBounds$ViewBounds mViewBounds
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean ongoing
com.dexterous.flutterlocalnotifications.models.NotificationDetails: long[] vibrationPattern
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String DISPATCHER_HANDLE
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String ID
com.google.android.gms.measurement.internal.zzbe: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.play_billing.zzku: int zzd
com.google.android.gms.internal.measurement.zzhu: com.google.android.gms.internal.measurement.zzhu zzf
com.google.android.gms.internal.play_billing.zzln: int zzf
com.google.crypto.tink.proto.KeyTypeEntry: com.google.crypto.tink.proto.KeyTypeEntry DEFAULT_INSTANCE
android.support.v4.media.session.MediaSessionCompat$Token: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.play_billing.zzlf: com.google.android.gms.internal.play_billing.zzlf zzb
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastQueueTime
com.google.crypto.tink.proto.AesEaxParams: int ivSize_
com.google.android.gms.internal.measurement.zzha: java.lang.String zzf
com.google.crypto.tink.proto.ChaCha20Poly1305Key: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesSivKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String icon
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String sound
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: java.lang.String contentTitle
com.google.android.gms.internal.play_billing.zzfl: int zzg
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Boolean showBadge
com.google.android.gms.internal.measurement.zziy: int zzb
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: com.google.crypto.tink.shaded.protobuf.UnknownFieldSetLite unknownFields
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
com.google.android.gms.internal.play_billing.zzkd: com.google.android.gms.internal.play_billing.zzkd zzb
com.google.android.gms.internal.play_billing.zzjz: int zzd
com.google.android.gms.auth.api.signin.GoogleSignInAccount: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String groupKey
com.google.crypto.tink.proto.KeyTypeEntry: int keyManagerVersion_
io.flutter.view.AccessibilityViewEmbedder: android.view.View rootAccessibilityView
com.google.crypto.tink.proto.EncryptedKeyset: com.google.crypto.tink.proto.KeysetInfo keysetInfo_
com.google.android.gms.internal.measurement.zzhe: boolean zzg
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: java.lang.String summaryText
com.google.android.gms.internal.measurement.zzje: int zzd
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String channelId
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: java.lang.Boolean htmlFormatContentTitle
com.google.android.gms.internal.measurement.zzgl: com.google.android.gms.internal.measurement.zzgv zzr
androidx.media3.extractor.metadata.id3.MlltFrame: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean indeterminate
com.google.crypto.tink.proto.AesCtrKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode inexact
com.google.android.gms.internal.measurement.zzgp: int zze
com.google.crypto.tink.proto.AesGcmSivKeyFormat: int keySize_
com.google.android.gms.internal.play_billing.zzlh: com.google.android.gms.internal.play_billing.zzho zzd
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix finalMatrix
com.google.android.gms.auth.api.signin.internal.GoogleSignInOptionsExtensionParcelable: android.os.Parcelable$Creator CREATOR
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified transportFactory
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: java.util.Map subtypeToLabel
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean animating
com.google.android.gms.measurement.internal.zzon: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String SHOWS_USER_INTERFACE
com.google.android.gms.internal.measurement.zzhl: int zzb
com.google.android.gms.internal.measurement.zzig: int zzb
com.google.android.gms.internal.measurement.zzfd: com.google.android.gms.internal.measurement.zzmn zze
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayDeque imageReaderQueue
com.google.android.gms.internal.measurement.zzff: boolean zzk
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
androidx.datastore.preferences.protobuf.AbstractMessageLite: int memoizedHashCode
com.google.android.gms.internal.play_billing.zzfn: com.google.android.gms.internal.play_billing.zzho zzd
com.google.android.gms.internal.measurement.zzid: long zzK
androidx.media3.extractor.metadata.id3.InternalFrame: android.os.Parcelable$Creator CREATOR
com.google.android.gms.location.LocationSettingsRequest: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: boolean initCalled
com.google.android.gms.internal.measurement.zzib: com.google.android.gms.internal.measurement.zzmn zzd
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SHOW_WHEN
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String DATA_DIRECTORY_SUFFIX
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String subText
com.google.android.gms.internal.measurement.zzgj: java.lang.String zzd
com.dexterous.flutterlocalnotifications.models.Time: java.lang.String HOUR
com.google.android.gms.internal.play_billing.zzku: com.google.android.gms.internal.play_billing.zzku zzb
androidx.media3.extractor.metadata.id3.PrivFrame: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CANCEL_ID
com.google.android.gms.internal.play_billing.zzfl: com.google.android.gms.internal.play_billing.zzfq zze
io.flutter.view.AccessibilityViewEmbedder: java.lang.String TAG
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String PRIORITY
com.google.android.gms.internal.measurement.zzff: com.google.android.gms.internal.measurement.zzff zzl
com.google.crypto.tink.proto.KmsAeadKey: int version_
com.google.android.gms.internal.measurement.zzfh: com.google.android.gms.internal.measurement.zzfl zze
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String ID
com.google.android.gms.internal.play_billing.zzfv: int zza
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat: com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat DEFAULT_INSTANCE
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next
com.google.android.gms.internal.measurement.zzfu: int zze
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource[] $VALUES
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String NAME
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int ENABLED
com.google.android.gms.internal.measurement.zzfd: boolean zzh
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LED_COLOR_GREEN
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String SHARED_PREFERENCES_KEY
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean released
com.google.android.gms.internal.measurement.zzgt: java.lang.String zze
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
android.support.v4.media.MediaDescriptionCompat: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfl: int zzd
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController platformViewsController
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String id
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
com.google.crypto.tink.proto.KeysetInfo: int KEY_INFO_FIELD_NUMBER
androidx.media3.extractor.metadata.mp4.SmtaMetadataEntry: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.AesSivKey: int version_
com.google.android.gms.internal.measurement.zzid: long zzi
com.google.android.gms.cloudmessaging.CloudMessage: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfl: java.lang.String zzg
com.google.android.gms.internal.measurement.zzhy: java.lang.String zze
com.google.android.gms.internal.measurement.zzid: com.google.android.gms.internal.measurement.zzmn zzac
com.google.crypto.tink.proto.EncryptedKeyset: int ENCRYPTED_KEYSET_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzhs: long zzg
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CANCEL_METHOD
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int OUTPUT_PREFIX_TYPE_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzhw: com.google.android.gms.internal.measurement.zzmn zzi
com.google.crypto.tink.proto.AesGcmKey: int VERSION_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean enableVibration
com.google.android.gms.internal.measurement.zzib: java.lang.String zzf
com.google.android.gms.internal.measurement.zzgf: com.google.android.gms.internal.measurement.zzmn zze
androidx.work.impl.utils.futures.AbstractFuture: java.lang.Object value
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: int EXACT_ALARM_PERMISSION_REQUEST_CODE
com.google.crypto.tink.proto.AesCmacKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.android.gms.internal.location.zzee: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_SOUND_ERROR_CODE
com.google.crypto.tink.proto.XChaCha20Poly1305Key: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction channelAction
com.google.crypto.tink.proto.AesCtrKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String body
com.google.android.gms.internal.play_billing.zzkd: int zze
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.util.List actionInputs
com.google.crypto.tink.proto.AesCtrParams: int ivSize_
com.google.android.gms.internal.measurement.zzid: java.lang.String zzai
com.google.android.gms.internal.play_billing.zzlx: boolean zzf
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesEaxKey: int KEY_VALUE_FIELD_NUMBER
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mTriggeredEventName
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: com.dexterous.flutterlocalnotifications.models.BitmapSource largeIconBitmapSource
androidx.media3.extractor.metadata.id3.TextInformationFrame: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String STOP_FOREGROUND_SERVICE
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified backgroundDispatcher
com.google.android.gms.measurement.internal.zzop: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
com.google.crypto.tink.proto.Keyset$Key: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.android.gms.internal.auth.zzhs: com.google.android.gms.internal.auth.zzez zzd
com.google.android.gms.measurement.internal.zzaf: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction Update
com.google.android.gms.internal.measurement.zzgt: com.google.android.gms.internal.measurement.zzgt zzf
androidx.work.impl.utils.futures.AbstractFuture: androidx.work.impl.utils.futures.AbstractFuture$Listener listeners
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus
com.google.crypto.tink.proto.AesSivKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int STATUS_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.android.gms.internal.measurement.zzgf: com.google.android.gms.internal.measurement.zzmn zzd
com.google.crypto.tink.proto.AesEaxParams: int IV_SIZE_FIELD_NUMBER
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
androidx.activity.result.ActivityResult: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String MAX_PROGRESS
com.google.crypto.tink.proto.Keyset$Key: int keyId_
com.google.android.gms.internal.measurement.zzid: long zzj
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource FlutterBitmapAsset
com.google.crypto.tink.proto.XChaCha20Poly1305Key: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesGcmSivKeyFormat: com.google.crypto.tink.proto.AesGcmSivKeyFormat DEFAULT_INSTANCE
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String CONFIGURE_PARTITIONED_COOKIES
com.google.crypto.tink.proto.AesCtrParams: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: com.google.crypto.tink.proto.KeysetInfo$KeyInfo DEFAULT_INSTANCE
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int MEDIA_QUERY_ONLY
com.dexterous.flutterlocalnotifications.models.SoundSource: com.dexterous.flutterlocalnotifications.models.SoundSource Uri
com.google.android.gms.internal.measurement.zzgl: java.lang.String zze
com.google.crypto.tink.proto.AesCmacKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.Boolean cancelNotification
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mExpiredEventName
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.BitmapSource largeIconBitmapSource
com.dexterous.flutterlocalnotifications.models.NotificationDetails: int[] additionalFlags
com.google.android.gms.internal.play_billing.zzfl: com.google.android.gms.internal.play_billing.zzfl zzb
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int WEB_SOURCE_AND_WEB_TRIGGER
com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation: java.lang.Boolean htmlFormatTitle
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Boolean playSound
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String NOTIFICATION_RESPONSE_TYPE
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: boolean mActive
com.google.crypto.tink.proto.AesGcmKey: int version_
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: android.os.Bundle mTimedOutEventParams
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: long mTriggerTimeout
com.google.android.gms.internal.measurement.zzid: java.lang.String zzC
com.google.crypto.tink.proto.RegistryConfig: com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList entry_
com.google.android.gms.internal.measurement.zzgp: int zzf
com.google.android.gms.internal.measurement.zzid: com.google.android.gms.internal.measurement.zzig zzQ
com.google.crypto.tink.proto.KeysetInfo: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean chronometerCountDown
com.google.crypto.tink.proto.AesCtrKey: int PARAMS_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode[] $VALUES
com.google.crypto.tink.proto.HmacKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
androidx.media3.common.DrmInitData$SchemeData: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzhl: int zze
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Object largeIcon
androidx.media3.extractor.metadata.scte35.TimeSignalCommand: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.Integer ledColor
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.android.gms.internal.play_billing.zzdy: java.lang.Object value
com.google.crypto.tink.proto.XChaCha20Poly1305Key: com.google.crypto.tink.proto.XChaCha20Poly1305Key DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzii: com.google.android.gms.internal.measurement.zzmn zzf
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String DELETE_NOTIFICATION_CHANNEL_METHOD
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode inexactAllowWhileIdle
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int APP_SOURCE_AND_APP_TRIGGER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String COLOR_ALPHA
com.google.android.gms.internal.measurement.zzfu: int zzb
com.google.android.gms.internal.measurement.zzhg: com.google.android.gms.internal.measurement.zzii zze
com.google.android.gms.internal.measurement.zzgj: int zzb
com.google.crypto.tink.proto.HmacKey: int KEY_VALUE_FIELD_NUMBER
com.google.android.gms.common.zzo: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfw: int zze
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String SHOW_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LARGE_ICON
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.PreferencesProto$PreferenceMap DEFAULT_INSTANCE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String NAME
com.google.android.gms.internal.measurement.zzgj: boolean zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean onlyAlertOnce
com.google.android.gms.internal.measurement.zzid: int zzq
com.google.crypto.tink.proto.HmacParams: int tagSize_
com.google.android.gms.internal.measurement.zzgl: java.lang.String zzo
com.google.android.gms.internal.measurement.zzhe: boolean zze
com.google.android.gms.internal.play_billing.zzkx: boolean zzf
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment
com.google.android.gms.internal.measurement.zzgl: int zzb
com.google.crypto.tink.proto.XChaCha20Poly1305Key: int version_
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image image
com.google.android.gms.internal.measurement.zzid: long zzT
com.google.crypto.tink.proto.AesCtrKey: com.google.crypto.tink.proto.AesCtrKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeyTypeEntry: boolean newKeyAllowed_
com.google.crypto.tink.proto.AesCtrKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String REQUEST_NOTIFICATIONS_PERMISSION_METHOD
com.google.crypto.tink.proto.KeyData: int KEY_MATERIAL_TYPE_FIELD_NUMBER
com.google.crypto.tink.proto.KeyData: com.google.crypto.tink.proto.KeyData DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzme: com.google.android.gms.internal.measurement.zzoi zzc
kotlinx.coroutines.sync.SemaphoreImpl: long enqIdx
com.google.android.gms.internal.measurement.zziw: com.google.android.gms.internal.measurement.zzmn zzb
com.google.android.gms.internal.measurement.zzik: com.google.android.gms.internal.measurement.zzik zzf
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String VIBRATION_PATTERN
com.google.android.gms.measurement.internal.zzr: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfd: int zzb
com.dexterous.flutterlocalnotifications.models.PersonDetails: java.lang.String name
com.google.android.gms.internal.measurement.zzhq: int zzd
com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.ChaCha20Poly1305Key: com.google.crypto.tink.proto.ChaCha20Poly1305Key DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzid: long zzh
com.google.android.gms.internal.measurement.zzgl: com.google.android.gms.internal.measurement.zzmn zzm
com.google.android.gms.internal.measurement.zzgv: java.lang.String zzd
androidx.work.impl.utils.futures.AbstractFuture$Waiter: androidx.work.impl.utils.futures.AbstractFuture$Waiter next
com.google.android.gms.internal.measurement.zzhc: java.lang.String zzh
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String NOTIFICATION_TAG
androidx.fragment.app.BackStackRecordState: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String ICON
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String COLOR_RED
com.dexterous.flutterlocalnotifications.models.MessageDetails: java.lang.String text
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String title
com.dexterous.flutterlocalnotifications.models.MessageDetails: com.dexterous.flutterlocalnotifications.models.PersonDetails person
com.google.android.gms.internal.measurement.zzhc: java.lang.String zzi
androidx.media3.container.Mp4TimestampData: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzhc: java.lang.String zze
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String TITLE
com.google.crypto.tink.proto.AesGcmSivKeyFormat: int version_
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SCHEDULED_DATE_TIME
com.google.android.gms.internal.measurement.zzid: com.google.android.gms.internal.measurement.zzmn zzg
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LED_OFF_MS
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.NotificationStyle style
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_NOTIFICATION_APP_LAUNCH_DETAILS_METHOD
io.flutter.view.AccessibilityViewEmbedder: android.util.SparseArray flutterIdToOrigin
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHANNEL_ID
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String PLAY_SOUND
com.google.android.gms.auth.api.signin.GoogleSignInOptions: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin$PermissionRequestProgress permissionRequestProgress
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int PREFER_MEDIA_QUERY_OVER_FORCE_DARK
com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation: java.lang.Boolean htmlFormatBody
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String AUTO_CANCEL
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_DEQUEUED_IMAGES
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object head
com.google.crypto.tink.proto.HmacParams: int TAG_SIZE_FIELD_NUMBER
com.google.android.gms.internal.play_billing.zzjz: com.google.android.gms.internal.play_billing.zzki zzh
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean fullScreenIntent
com.dexterous.flutterlocalnotifications.models.PersonDetails: java.lang.Boolean bot
com.google.android.gms.internal.play_billing.zzhk: int zzd
com.google.android.gms.internal.measurement.zziu: double zzi
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String CONTEXTUAL
com.google.android.gms.common.zzq: android.os.Parcelable$Creator CREATOR
com.google.android.gms.auth.api.accounttransfer.zzs: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzhg: boolean zzg
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack
com.google.android.gms.common.server.response.zan: android.os.Parcelable$Creator CREATOR
com.google.android.gms.auth.AccountChangeEventsResponse: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String TAG
com.google.android.gms.auth.api.accounttransfer.zzo: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler
com.google.crypto.tink.proto.Keyset$Key: com.google.crypto.tink.proto.KeyData keyData_
com.google.android.gms.location.LocationSettingsResult: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfd: int zzd
com.google.android.gms.internal.measurement.zzid: java.lang.String zzB
com.google.crypto.tink.proto.KmsAeadKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.android.gms.signin.internal.zak: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.KeyTypeEntry: int KEY_MANAGER_VERSION_FIELD_NUMBER
com.google.android.gms.internal.play_billing.zzlu: com.google.android.gms.internal.play_billing.zzlu zzb
com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar: com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar$Companion Companion
com.google.android.gms.internal.auth.zzev: java.util.Map zzb
com.google.android.gms.internal.measurement.zzfr: java.lang.String zze
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String description
com.google.android.gms.internal.measurement.zzhg: com.google.android.gms.internal.measurement.zzhg zzh
com.google.android.gms.internal.measurement.zzgl: com.google.android.gms.internal.measurement.zzgl zzu
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: java.lang.String bigText
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer visibility
com.google.android.gms.internal.measurement.zzhs: com.google.android.gms.internal.measurement.zzmn zzd
com.google.android.gms.internal.measurement.zzhc: java.lang.String zzg
com.google.android.gms.internal.auth.zzbw: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.KeyData: int keyMaterialType_
com.google.android.gms.internal.play_billing.zzlf: int zze
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.HashMap perImageReaders
com.dexterous.flutterlocalnotifications.models.styles.MessagingStyleInformation: java.lang.Boolean groupConversation
kotlinx.coroutines.InvokeOnCancelling: int _invoked
com.google.android.gms.internal.measurement.zzfn: boolean zzg
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: java.lang.Boolean htmlFormatBigText
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean VERBOSE_LOGS
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHANNEL_ACTION
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String id
com.google.android.gms.internal.measurement.zziu: java.lang.String zzf
com.google.crypto.tink.proto.AesGcmKey: com.google.crypto.tink.proto.AesGcmKey DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzhw: float zzg
com.google.android.gms.internal.measurement.zziu: com.google.android.gms.internal.measurement.zziu zzj
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mTriggerEventName
com.google.android.gms.internal.measurement.zzhs: com.google.android.gms.internal.measurement.zzhs zzi
android.support.v4.media.session.PlaybackStateCompat: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.play_billing.zzkr: java.lang.String zze
com.google.android.gms.internal.play_billing.zzki: java.lang.String zzf
androidx.fragment.app.FragmentManager$LaunchedFragmentInfo: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String PLATFORM_SPECIFICS
com.google.crypto.tink.proto.AesCmacParams: int tagSize_
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer ledColor
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: java.lang.Boolean htmlFormatLines
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder
com.google.android.gms.internal.measurement.zzhc: java.lang.String zzj
com.google.android.gms.internal.play_billing.zzjz: java.lang.Object zzf
com.google.android.gms.internal.measurement.zzid: java.lang.String zzw
com.google.android.gms.location.LocationResult: android.os.Parcelable$Creator CREATOR
com.google.android.gms.auth.api.signin.SignInAccount: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Long when
kotlinx.coroutines.sync.SemaphoreImpl: long deqIdx
com.google.android.gms.internal.play_billing.zzlf: int zzd
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.protobuf.Parser PARSER
com.google.crypto.tink.proto.KeysetInfo: int primaryKeyId_
com.google.crypto.tink.proto.AesCtrKeyFormat: com.google.crypto.tink.proto.AesCtrKeyFormat DEFAULT_INSTANCE
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource FilePath
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String sound
com.google.android.gms.common.internal.TelemetryData: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.auth.zzev: int zzd
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int NONE
com.google.crypto.tink.proto.KmsAeadKey: com.google.crypto.tink.proto.KmsAeadKeyFormat params_
io.flutter.embedding.engine.FlutterJNI: float refreshRateFPS
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean released
com.google.android.gms.internal.measurement.zzme: int zzb
com.google.crypto.tink.proto.AesEaxKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrParams: com.google.crypto.tink.proto.AesCtrParams DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeysetInfo: com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList keyInfo_
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.content.Context applicationContext
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: java.util.Map labelToSubtype
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SUB_TEXT
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval[] $VALUES
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String MESSAGES
com.google.android.gms.internal.play_billing.zzkr: com.google.android.gms.internal.play_billing.zzhn zzg
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle Default
android.support.v4.media.MediaBrowserCompat$MediaItem: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_CALLBACK_HANDLE_METHOD
com.google.android.gms.internal.measurement.zzgl: java.lang.String zzn
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency[] $VALUES
com.google.crypto.tink.proto.AesCtrKey: int version_
com.google.android.gms.internal.measurement.zzid: java.lang.String zzY
com.google.crypto.tink.proto.EncryptedKeyset: int KEYSET_INFO_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents DateAndTime
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String BOT
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INITIALIZE_METHOD
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int memoizedSerializedSize
com.google.android.gms.internal.play_billing.zzku: java.lang.String zzf
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_ACTIVE_NOTIFICATIONS_METHOD
com.google.android.gms.internal.measurement.zzhw: double zzh
com.dexterous.flutterlocalnotifications.models.styles.MessagingStyleInformation: java.util.ArrayList messages
com.google.android.gms.common.internal.zav: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgv: java.lang.String zzf
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mTimedOutEventName
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev
com.google.crypto.tink.proto.KmsAeadKeyFormat: com.google.crypto.tink.proto.KmsAeadKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.HmacParams: int HASH_FIELD_NUMBER
android.support.v4.media.session.ParcelableVolumeInfo: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.play_billing.zzlf: java.lang.String zzf
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers
com.google.crypto.tink.proto.AesCtrKey: int KEY_VALUE_FIELD_NUMBER
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
com.google.android.gms.measurement.internal.zzpk: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgr: java.lang.String zzd
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified firebaseApp
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
com.google.android.gms.internal.measurement.zzid: java.lang.String zzM
com.dexterous.flutterlocalnotifications.models.Time: java.lang.Integer second
com.google.android.gms.internal.measurement.zzhs: int zzb
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.android.gms.internal.play_billing.zzfq: java.lang.String zze
com.google.android.gms.common.moduleinstall.ModuleAvailabilityResponse: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean setAsGroupSummary
com.google.android.gms.internal.measurement.zzje: double zzi
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String tag
com.google.crypto.tink.proto.HmacKeyFormat: int PARAMS_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HTML_FORMAT_LINES
com.google.android.gms.internal.play_billing.zzkr: int zzd
androidx.media3.container.MdtaMetadataEntry: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.HmacKeyFormat: com.google.crypto.tink.proto.HmacParams params_
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String START_FOREGROUND_SERVICE
com.google.android.gms.internal.measurement.zzje: java.lang.String zzg
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: long mTriggeredTimestamp
com.google.android.gms.internal.measurement.zzfl: java.lang.String zzh
com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar: com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar$Companion Companion
io.flutter.plugin.platform.SingleViewPresentation: android.view.View$OnFocusChangeListener focusChangeListener
com.google.android.gms.internal.play_billing.zzkr: com.google.android.gms.internal.play_billing.zzho zzh
com.google.android.gms.common.server.response.zam: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String ENABLE_LIGHTS
com.google.android.gms.internal.play_billing.zzki: int zze
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle Messaging
com.google.firebase.messaging.RemoteMessage: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.DateTimeComponents matchDateTimeComponents
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String NUMBER
com.google.android.gms.auth.api.accounttransfer.zzw: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzha: java.lang.String zzd
com.google.android.gms.internal.measurement.zzgl: int zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TIME_ZONE_NAME
kotlinx.coroutines.EventLoopImplBase: int _isCompleted
com.google.crypto.tink.proto.KeyTemplate: java.lang.String typeUrl_
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: com.google.crypto.tink.proto.HmacKeyFormat hmacKeyFormat_
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SHOW_PROGRESS
com.google.android.gms.internal.measurement.zzje: com.google.android.gms.internal.measurement.zzje zzj
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.Boolean hideExpandedLargeIcon
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String INPUTS
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String ENABLE_VIBRATION
androidx.datastore.preferences.PreferencesProto$Value: int FLOAT_FIELD_NUMBER
com.google.crypto.tink.proto.AesEaxKeyFormat: int keySize_
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface surface
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int BROWSER
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String NOTIFICATION_LAUNCHED_APP
org.chromium.support_lib_boundary.PrefetchStatusCodeBoundaryInterface: int FAILURE
com.google.android.gms.internal.measurement.zzfh: java.lang.String zzg
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: long mCreationTimestamp
com.google.android.gms.internal.play_billing.zzlu: int zze
com.google.android.gms.internal.measurement.zzib: com.google.android.gms.internal.measurement.zzib zzh
com.google.crypto.tink.proto.KmsAeadKey: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.Keyset$Key: int status_
androidx.recyclerview.widget.StaggeredGridLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle Inbox
com.google.android.gms.internal.measurement.zzfn: int zzd
com.google.android.gms.internal.measurement.zzhe: boolean zzd
com.dexterous.flutterlocalnotifications.models.Time: java.lang.String SECOND
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.android.gms.internal.measurement.zzid: com.google.android.gms.internal.measurement.zzml zzR
com.google.crypto.tink.proto.AesGcmSivKeyFormat: int VERSION_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.String contentTitle
com.google.android.gms.internal.measurement.zzhl: int zzd
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String REPEAT_INTERVAL_MILLISECONDS
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HTML_FORMAT_CONTENT
com.google.android.gms.internal.measurement.zzib: java.lang.String zze
com.google.crypto.tink.proto.AesCtrHmacAeadKey: int VERSION_FIELD_NUMBER
com.google.android.gms.internal.measurement.zziy: com.google.android.gms.internal.measurement.zziy zzf
com.dexterous.flutterlocalnotifications.models.SoundSource: com.dexterous.flutterlocalnotifications.models.SoundSource RawResource
com.google.crypto.tink.proto.Keyset$Key: int outputPrefixType_
com.google.android.gms.internal.play_billing.zzlk: int zzd
com.google.android.gms.internal.measurement.zzhy: com.google.android.gms.internal.measurement.zzhc zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean showProgress
com.google.crypto.tink.proto.AesGcmKeyFormat: com.google.crypto.tink.proto.AesGcmKeyFormat DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzgj: com.google.android.gms.internal.measurement.zzgj zzh
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List finalClippingPaths
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String BODY
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.Boolean htmlFormatSummaryText
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: int AES_CTR_KEY_FORMAT_FIELD_NUMBER
com.google.android.gms.internal.play_billing.zzfq: int zzd
com.google.android.gms.internal.measurement.zzdf: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgf: com.google.android.gms.internal.measurement.zzgf zzi
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.Parser PARSER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HTML_FORMAT_TITLE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean trimOnMemoryPressure
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int memoizedSerializedSize
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate asyncWaitForVsyncDelegate
com.google.android.gms.internal.measurement.zzgv: java.lang.String zzh
com.google.android.gms.signin.internal.zaa: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzja: com.google.android.gms.internal.measurement.zzja zzf
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean needsSave
com.google.android.gms.internal.measurement.zzid: java.lang.String zzs
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mName
com.google.crypto.tink.proto.KeyData: java.lang.String typeUrl_
com.google.crypto.tink.proto.HmacKey: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.AesEaxParams: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_ACTIVE_NOTIFICATION_MESSAGING_STYLE_METHOD
com.google.crypto.tink.proto.Keyset$Key: int KEY_ID_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrHmacAeadKey: int HMAC_KEY_FIELD_NUMBER
com.google.crypto.tink.proto.HmacKey: int version_
com.google.android.gms.internal.measurement.zzid: java.lang.String zzV
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackName
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.util.List actions
com.google.crypto.tink.proto.HmacKeyFormat: int keySize_
com.google.android.gms.internal.measurement.zzgl: com.google.android.gms.internal.measurement.zzgr zzs
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: java.lang.String typeFieldName
com.google.crypto.tink.proto.AesCmacKey: com.google.crypto.tink.proto.AesCmacParams params_
io.flutter.plugins.firebase.core.FlutterFirebasePlugin: java.util.concurrent.ExecutorService cachedThreadPool
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastScheduleTime
com.google.android.gms.internal.measurement.zzid: long zzl
com.google.crypto.tink.proto.AesCtrHmacAeadKey: com.google.crypto.tink.proto.AesCtrKey aesCtrKey_
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate accessibilityDelegate
com.google.android.gms.internal.measurement.zzff: boolean zzg
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: com.dexterous.flutterlocalnotifications.PermissionRequestListener callback
io.flutter.view.AccessibilityViewEmbedder: int nextFlutterId
androidx.datastore.preferences.PreferencesProto$Value: java.lang.Object value_
androidx.media3.extractor.metadata.id3.GeobFrame: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.KmsAeadKeyFormat: int KEY_URI_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: int NOTIFICATION_PERMISSION_REQUEST_CODE
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.AesGcmSivKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.android.gms.internal.measurement.zzgl: com.google.android.gms.internal.measurement.zzmn zzg
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String TIMEOUT_AFTER
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode exact
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String PERIODICALLY_SHOW_WITH_DURATION
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String REPEAT_INTERVAL
com.google.android.gms.internal.measurement.zzgh: boolean zzf
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String DATA_DIRECTORY_BASE_PATH
com.google.android.gms.internal.measurement.zzgf: com.google.android.gms.internal.measurement.zzmn zzf
com.google.android.gms.internal.play_billing.zzku: int zzg
com.google.android.gms.internal.measurement.zzhg: int zzb
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String CREATE_NOTIFICATION_CHANNEL_METHOD
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String category
com.google.firebase.sessions.FirebaseSessionsRegistrar: java.lang.String TAG
com.google.android.gms.internal.measurement.zzid: com.google.android.gms.internal.measurement.zzhe zzal
com.google.android.gms.internal.measurement.zzhw: int zzb
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String PERSON
com.google.android.gms.internal.play_billing.zzo: com.google.android.gms.internal.play_billing.zzo zzc
com.google.android.gms.internal.measurement.zzid: com.google.android.gms.internal.measurement.zzhy zzaa
com.google.android.gms.internal.measurement.zzha: java.lang.String zze
com.google.crypto.tink.proto.AesGcmSivKey: int version_
com.google.android.gms.internal.play_billing.zzkd: java.lang.Object zzf
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String HTML_FORMAT_SUMMARY_TEXT
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: int KEK_URI_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String COLOR_BLUE
androidx.media3.extractor.metadata.id3.ApicFrame: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int outputPrefixType_
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer number
com.google.android.gms.internal.measurement.zzik: int zzb
com.google.crypto.tink.proto.AesGcmSivKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String CHANNEL_SHOW_BADGE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean colorized
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: int DEK_TEMPLATE_FIELD_NUMBER
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int keyId_
com.google.crypto.tink.proto.ChaCha20Poly1305Key: int VERSION_FIELD_NUMBER
io.flutter.view.AccessibilityViewEmbedder: java.util.Map originToFlutterId
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String ID
com.google.crypto.tink.proto.AesGcmKey: int KEY_VALUE_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String DAY
com.google.crypto.tink.proto.AesGcmSivKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: int PARAMS_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource ByteArray
com.google.crypto.tink.proto.HmacKeyFormat: int VERSION_FIELD_NUMBER
androidx.appcompat.widget.SearchView$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzhw: java.lang.String zze
com.google.android.gms.internal.measurement.zzfd: com.google.android.gms.internal.measurement.zzfd zzi
com.dexterous.flutterlocalnotifications.models.styles.MessagingStyleInformation: java.lang.String conversationTitle
androidx.media3.extractor.metadata.dvbsi.AppInfoTable: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String title
com.google.android.gms.internal.measurement.zzig: int zzd
com.google.android.gms.internal.measurement.zzgn: com.google.android.gms.internal.measurement.zzgn zzd
com.google.android.gms.internal.measurement.zzha: java.lang.String zzi
com.google.crypto.tink.proto.KeyTypeEntry: int TYPE_URL_FIELD_NUMBER
com.google.android.gms.common.server.response.zal: android.os.Parcelable$Creator CREATOR
androidx.fragment.app.FragmentState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader lastReaderDequeuedFrom
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_ACTIVE_NOTIFICATION_MESSAGING_STYLE_ERROR_CODE
com.google.android.gms.internal.measurement.zzme: java.util.Map zzd
com.google.crypto.tink.proto.AesGcmKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.android.gms.internal.measurement.zzhe: boolean zzf
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: com.google.crypto.tink.proto.KeyTemplate dekTemplate_
android.support.v4.media.RatingCompat: android.os.Parcelable$Creator CREATOR
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int FORCE_DARK_ONLY
com.google.crypto.tink.proto.Keyset: int primaryKeyId_
com.google.android.gms.internal.play_billing.zzki: int zzg
com.google.android.gms.internal.measurement.zzff: com.google.android.gms.internal.measurement.zzmn zzf
com.google.android.gms.internal.measurement.zzha: com.google.android.gms.internal.measurement.zznf zzl
com.google.crypto.tink.proto.AesEaxKeyFormat: com.google.crypto.tink.proto.AesEaxKeyFormat DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzis: int zzf
com.google.crypto.tink.proto.AesGcmSivKey: int KEY_VALUE_FIELD_NUMBER
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SCHEDULED_NOTIFICATION_REPEAT_FREQUENCY
com.google.crypto.tink.proto.AesCmacParams: com.google.crypto.tink.proto.AesCmacParams DEFAULT_INSTANCE
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents DayOfWeekAndTime
com.google.android.gms.internal.play_billing.zzkr: com.google.android.gms.internal.play_billing.zzkr zzb
com.google.android.gms.internal.measurement.zzgl: com.google.android.gms.internal.measurement.zzgf zzp
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String REPEAT_TIME
com.google.android.gms.internal.measurement.zzje: com.google.android.gms.internal.measurement.zzmn zze
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat: int version_
com.google.crypto.tink.proto.AesCmacParams: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.Object bigPicture
kotlinx.coroutines.channels.BufferedChannel: long receivers
com.google.android.gms.internal.auth.zzev: com.google.android.gms.internal.auth.zzha zzc
com.google.android.gms.internal.measurement.zzgl: com.google.android.gms.internal.measurement.zzgn zzt
com.google.crypto.tink.proto.Keyset: int PRIMARY_KEY_ID_FIELD_NUMBER
com.google.crypto.tink.proto.AesSivKeyFormat: int VERSION_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean createNewReader
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SOUND_SOURCE
androidx.media3.extractor.metadata.id3.BinaryFrame: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.PersonDetails: java.lang.Boolean important
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.AccessibilityEventsDelegate accessibilityEventsDelegate
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: android.os.Bundle mTriggeredEventParams
androidx.media3.extractor.metadata.id3.ChapterTocFrame: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String COLOR_GREEN
com.google.android.gms.internal.measurement.zzid: long zzaq
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: java.lang.String DESCRIPTION
com.google.android.gms.internal.measurement.zzhw: com.google.android.gms.internal.measurement.zzhw zzj
androidx.media3.extractor.metadata.mp4.SlowMotionData$Segment: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.AesCmacKeyFormat: com.google.crypto.tink.proto.AesCmacParams params_
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LED_COLOR_ALPHA
com.google.crypto.tink.proto.HmacKey: int VERSION_FIELD_NUMBER
com.google.android.gms.signin.internal.zag: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: io.flutter.plugin.common.MethodChannel channel
com.google.android.gms.internal.play_billing.zzlb: com.google.android.gms.internal.play_billing.zzlb zzb
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Boolean channelShowBadge
com.google.crypto.tink.shaded.protobuf.AbstractMessageLite: int memoizedHashCode
com.google.android.gms.internal.measurement.zzis: int zze
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String DATA_URI
androidx.recyclerview.widget.RecyclerView$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.ThreadSafeHeap: int _size
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: java.lang.String TAG
androidx.datastore.preferences.PreferencesProto$Value: int INTEGER_FIELD_NUMBER
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean $assertionsDisabled
com.google.android.gms.common.internal.GetServiceRequest: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgl: com.google.android.gms.internal.measurement.zzmn zzh
com.google.android.gms.internal.measurement.zzff: int zzb
androidx.media3.extractor.metadata.mp4.MotionPhotoMetadata: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzhg: com.google.android.gms.internal.measurement.zzii zzf
com.google.crypto.tink.proto.KmsAeadKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.KeyTypeEntry: java.lang.String typeUrl_
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String STYLE_INFORMATION
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency Weekly
androidx.media3.exoplayer.hls.HlsTrackMetadataEntry: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String MILLISECONDS_SINCE_EPOCH
com.google.android.gms.internal.measurement.zzid: java.lang.String zzak
com.google.android.gms.internal.measurement.zzhe: com.google.android.gms.internal.measurement.zzhe zzk
com.google.crypto.tink.proto.AesEaxKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean CLEANUP_ON_MEMORY_PRESSURE
com.google.android.gms.internal.measurement.zzid: int zzaj
com.google.android.gms.internal.measurement.zzid: boolean zzX
androidx.activity.result.IntentSenderRequest: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: java.lang.String description
com.google.android.gms.internal.measurement.zzid: com.google.android.gms.internal.measurement.zzis zzap
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.Integer titleColor
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INPUT_RESULT
com.google.android.gms.internal.measurement.zzid: int zzH
kotlinx.coroutines.DefaultExecutor: int debugStatus
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: int version_
com.google.android.gms.internal.play_billing.zzlk: java.lang.Object zzf
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_ICON_ERROR_CODE
com.google.android.gms.auth.api.signin.internal.SignInConfiguration: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String timeZoneName
com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver: java.lang.String TAG
com.google.android.gms.internal.play_billing.zzlq: int zzd
com.google.android.gms.internal.measurement.zzid: com.google.android.gms.internal.measurement.zzmn zzf
io.flutter.plugin.platform.SingleViewPresentation: android.content.Context outerContext
com.google.android.gms.internal.play_billing.zzfl: int zzd
com.google.crypto.tink.proto.AesSivKey: int VERSION_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzfr: int zzb
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean ignoringFence
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.MapFieldLite preferences_
io.flutter.embedding.engine.FlutterJNI: float displayDensity
com.google.crypto.tink.proto.AesEaxKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int ENABLED_WITHOUT_APP_IDENTITY
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource ContentUri
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String FULL_SCREEN_INTENT
com.google.android.gms.internal.measurement.zzha: long zzk
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String USES_CHRONOMETER
com.google.android.gms.internal.measurement.zzfl: boolean zze
com.google.android.gms.internal.measurement.zzha: com.google.android.gms.internal.measurement.zzha zzn
com.google.android.gms.auth.api.identity.BeginSignInResult: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.play_billing.zzlq: com.google.android.gms.internal.play_billing.zzlq zzb
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String channelName
com.google.crypto.tink.proto.ChaCha20Poly1305Key: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: java.lang.Boolean htmlFormatContentTitle
com.google.android.gms.internal.measurement.zzid: java.lang.String zzP
com.google.android.gms.internal.play_billing.zzku: java.lang.String zze
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String INDETERMINATE
androidx.media3.extractor.metadata.icy.IcyInfo: android.os.Parcelable$Creator CREATOR
com.google.android.gms.measurement.internal.zzbg: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgl: com.google.android.gms.internal.measurement.zzmn zzl
com.google.android.gms.internal.measurement.zzii: com.google.android.gms.internal.measurement.zzmm zzd
com.dexterous.flutterlocalnotifications.models.PersonDetails: java.lang.String uri
com.google.crypto.tink.proto.AesSivKeyFormat: int version_
com.google.android.gms.internal.measurement.zzgc: com.google.android.gms.internal.measurement.zzgc zzf
com.google.crypto.tink.proto.ChaCha20Poly1305Key: int KEY_VALUE_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_BIG_PICTURE_ERROR_CODE
io.flutter.embedding.engine.FlutterJNI: java.lang.Long nativeShellHolderId
com.google.crypto.tink.proto.RegistryConfig: java.lang.String configName_
com.google.android.gms.common.server.response.SafeParcelResponse: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzid: long zzS
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_LARGE_ICON_ERROR_CODE
com.google.android.gms.internal.play_billing.zzlk: com.google.android.gms.internal.play_billing.zzkx zzh
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer ledOnMs
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback this$0
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String VIBRATION_PATTERN
com.google.crypto.tink.proto.AesGcmSivKey: com.google.crypto.tink.proto.AesGcmSivKey DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzfd: com.google.android.gms.internal.measurement.zzmn zzf
com.google.android.gms.internal.play_billing.zzhk: java.util.Map zzb
com.google.crypto.tink.proto.AesCmacKeyFormat: com.google.crypto.tink.proto.AesCmacKeyFormat DEFAULT_INSTANCE
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SHORTCUT_ID
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int DISABLED
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String BIG_PICTURE
com.google.crypto.tink.proto.AesEaxKeyFormat: int PARAMS_FIELD_NUMBER
kotlinx.coroutines.flow.StateFlowSlot: java.lang.Object _state
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Integer progress
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents[] $VALUES
com.google.firebase.messaging.FirebaseMessagingRegistrar: java.lang.String LIBRARY_NAME
androidx.media3.extractor.metadata.flac.PictureFrame: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzid: java.lang.String zzy
androidx.recyclerview.widget.LinearLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
androidx.media3.common.StreamKey: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.AesEaxParams: com.google.crypto.tink.proto.AesEaxParams DEFAULT_INSTANCE
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View view
com.google.android.gms.internal.measurement.zzid: long zzL
com.google.android.gms.internal.measurement.zzgf: int zzb
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String ICON_SOURCE
kotlinx.coroutines.sync.SemaphoreImpl: int _availablePermits
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String LED_COLOR_BLUE
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String NOTIFICATION_DETAILS
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int APP
com.google.crypto.tink.proto.HmacKeyFormat: com.google.crypto.tink.proto.HmacKeyFormat DEFAULT_INSTANCE
com.google.android.gms.internal.play_billing.zzku: long zzh
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue
androidx.datastore.preferences.PreferencesProto$Value: int STRING_SET_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzfn: com.google.android.gms.internal.measurement.zzfn zzj
com.google.crypto.tink.proto.AesCmacKey: int VERSION_FIELD_NUMBER
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String DATA_MIME_TYPE
com.google.android.gms.location.DeviceOrientation: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zziy: com.google.android.gms.internal.measurement.zzmn zze
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback animationCallback
androidx.datastore.preferences.PreferencesProto$PreferenceMap: int PREFERENCES_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzhy: com.google.android.gms.internal.measurement.zzhy zzg
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: java.lang.String CHANNEL_ACTION
com.google.android.gms.internal.measurement.zzgf: com.google.android.gms.internal.measurement.zzmn zzh
com.google.crypto.tink.proto.HmacKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.android.gms.internal.measurement.zziu: float zzh
com.google.android.gms.auth.api.identity.SavePasswordResult: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgv: int zzb
com.google.android.gms.internal.measurement.zzid: java.lang.String zzar
com.google.android.gms.internal.measurement.zzid: java.lang.String zzJ
com.google.crypto.tink.proto.HmacKeyFormat: int KEY_SIZE_FIELD_NUMBER
kotlinx.coroutines.CancelledContinuation: int _resumed
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.sessions.FirebaseSessionsRegistrar$Companion Companion
com.google.android.gms.internal.measurement.zzje: int zzb
com.google.android.gms.common.internal.MethodInvocation: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.AesEaxKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String GET_ACTIVE_NOTIFICATIONS_ERROR_MESSAGE
com.google.android.gms.internal.measurement.zziw: com.google.android.gms.internal.measurement.zziw zzd
com.google.android.gms.internal.measurement.zzhc: java.lang.String zzd
com.google.crypto.tink.proto.AesGcmKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.android.gms.internal.play_billing.zzo: java.lang.Thread zzb
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
com.google.crypto.tink.proto.AesCmacKeyFormat: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.AesCmacKey: com.google.crypto.tink.proto.AesCmacKey DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzid: java.lang.String zzad
com.google.android.gms.internal.measurement.zzha: java.lang.String zzh
com.dexterous.flutterlocalnotifications.models.PersonDetails: java.lang.String key
com.google.android.gms.internal.play_billing.zzlk: com.google.android.gms.internal.play_billing.zzku zzg
com.google.android.gms.internal.measurement.zzid: long zzae
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource[] $VALUES
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String PERMISSION_REQUEST_IN_PROGRESS_ERROR_MESSAGE
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction[] $VALUES
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified firebaseInstallationsApi
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String REQUEST_EXACT_ALARMS_PERMISSION_METHOD
com.dexterous.flutterlocalnotifications.models.PersonDetails: java.lang.Object icon
com.google.crypto.tink.proto.AesCmacKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.android.gms.internal.measurement.zzid: java.lang.String zzr
com.google.android.gms.internal.measurement.zziu: java.lang.String zze
com.google.android.gms.measurement.internal.zzol: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LED_COLOR_RED
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.Long calledAt
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String LED_ON_MS
android.support.v4.media.session.MediaSessionCompat$QueueItem: android.os.Parcelable$Creator CREATOR
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.lang.String SOUND
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String INVALID_LED_DETAILS_ERROR_MESSAGE
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.lang.String TITLE_COLOR_GREEN
android.support.v4.media.session.PlaybackStateCompat$CustomAction: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState state
com.dexterous.flutterlocalnotifications.models.SoundSource: com.dexterous.flutterlocalnotifications.models.SoundSource[] $values()
androidx.appcompat.widget.AppCompatEditText: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.camera.camera2.internal.compat.quirk.ImageCaptureFlashNotFireQuirk: ImageCaptureFlashNotFireQuirk()
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: HiddenLifecycleReference(androidx.lifecycle.Lifecycle)
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportButtonTintMode(android.graphics.PorterDuff$Mode)
com.google.android.gms.internal.play_billing.zzbi: zzbi()
com.google.android.datatransport.runtime.scheduling.jobscheduling.SchedulerConfig$Flag: com.google.android.datatransport.runtime.scheduling.jobscheduling.SchedulerConfig$Flag[] values()
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails fromNotificationDetails(com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void scheduleNextRepeatingNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
androidx.camera.core.impl.Config$OptionPriority: androidx.camera.core.impl.Config$OptionPriority[] values()
io.flutter.embedding.engine.FlutterJNI: void onDisplayOverlaySurface(int,int,int,int,int)
androidx.camera.video.internal.encoder.EncoderImpl$InternalState: androidx.camera.video.internal.encoder.EncoderImpl$InternalState valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory registerSubtype(java.lang.Class,java.lang.String)
androidx.appcompat.widget.SearchView: void setOnSearchClickListener(android.view.View$OnClickListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
androidx.core.app.NotificationCompat$MessagingStyle$Api24Impl: android.app.Notification$MessagingStyle addMessage(android.app.Notification$MessagingStyle,android.app.Notification$MessagingStyle$Message)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
com.google.android.gms.location.zzv: com.google.android.gms.location.zzw zzb(android.os.IBinder)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin$PermissionRequestProgress: com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin$PermissionRequestProgress valueOf(java.lang.String)
androidx.camera.camera2.internal.compat.quirk.ImageCaptureWithFlashUnderexposureQuirk: ImageCaptureWithFlashUnderexposureQuirk()
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection[] values()
io.flutter.embedding.engine.FlutterJNI: void detachFromNativeAndReleaseResources()
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader: DynamiteModule$DynamiteLoaderClassLoader()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmoji(int)
androidx.security.crypto.MasterKey$KeyScheme: androidx.security.crypto.MasterKey$KeyScheme[] values()
androidx.core.app.ActivityCompat$Api23Impl: void requestPermissions(android.app.Activity,java.lang.String[],int)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getDropDataProvider()
io.flutter.plugins.webviewflutter.JavaScriptChannel: void postMessage(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageResponseCallback(long,int,java.nio.ByteBuffer,int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getEnterpriseAuthenticationAppLinkPolicyEnabled()
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerTriggerAsync(android.net.Uri)
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
io.flutter.view.TextureRegistry$ImageTextureEntry: void pushImage(android.media.Image)
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: NotificationChannelDetails()
android.support.customtabs.ICustomTabsService$Stub: android.support.customtabs.ICustomTabsService asInterface(android.os.IBinder)
com.dexterous.flutterlocalnotifications.models.ScheduleMode: ScheduleMode(java.lang.String,int)
androidx.tracing.TraceApi29Impl: boolean isEnabled()
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQuery()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityPausedByScionActivityInfo(com.google.android.gms.internal.measurement.zzdf,long)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getUserProperties(java.lang.String,java.lang.String,boolean,com.google.android.gms.internal.measurement.zzcu)
androidx.media.AudioAttributesImplApi26Parcelizer: AudioAttributesImplApi26Parcelizer()
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay[] values()
com.google.android.gms.measurement.AppMeasurement: void endAdUnitExposure(java.lang.String)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWebauthnSupport(int)
io.flutter.plugins.camerax.LensFacing: io.flutter.plugins.camerax.LensFacing[] values()
androidx.core.location.LocationManagerCompat$Api31Impl: boolean registerGnssMeasurementsCallback(android.location.LocationManager,java.util.concurrent.Executor,android.location.GnssMeasurementsEvent$Callback)
androidx.room.IMultiInstanceInvalidationCallback$Stub: androidx.room.IMultiInstanceInvalidationCallback asInterface(android.os.IBinder)
io.flutter.plugins.imagepicker.Messages$CacheRetrievalType: io.flutter.plugins.imagepicker.Messages$CacheRetrievalType valueOf(java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void beginAdUnitExposure(java.lang.String,long)
androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportCallback$Stub: androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportCallback asInterface(android.os.IBinder)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getSafeBrowsingEnabled()
androidx.recyclerview.widget.RecyclerView: void setLayoutManager(androidx.recyclerview.widget.RecyclerView$LayoutManager)
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
androidx.core.view.ViewCompat$Api26Impl: int getImportantForAutofill(android.view.View)
com.google.firebase.concurrent.SequentialExecutor$WorkerRunningState: com.google.firebase.concurrent.SequentialExecutor$WorkerRunningState valueOf(java.lang.String)
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
com.google.firebase.installations.remote.InstallationResponse$ResponseCode: com.google.firebase.installations.remote.InstallationResponse$ResponseCode[] values()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getSpeculativeLoadingStatus()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getTestFlag(com.google.android.gms.internal.measurement.zzcu,int)
com.google.crypto.tink.shaded.protobuf.FieldType: com.google.crypto.tink.shaded.protobuf.FieldType valueOf(java.lang.String)
androidx.camera.lifecycle.LifecycleCameraRepository$LifecycleCameraRepositoryObserver: void onStart(androidx.lifecycle.LifecycleOwner)
androidx.camera.core.ImageProcessingUtil: java.nio.ByteBuffer nativeNewDirectByteBuffer(java.nio.ByteBuffer,int,int)
com.google.firebase.crashlytics.internal.common.CommonUtils$Architecture: com.google.firebase.crashlytics.internal.common.CommonUtils$Architecture valueOf(java.lang.String)
android.support.v4.media.AudioAttributesImplApi26Parcelizer: androidx.media.AudioAttributesImplApi26 read(androidx.versionedparcelable.VersionedParcel)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspotBounds(android.graphics.drawable.Drawable,int,int,int,int)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder setGroup(android.app.Notification$Builder,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: boolean getIsSoftwareRenderingEnabled()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
com.google.firebase.components.ComponentDiscoveryService: ComponentDiscoveryService()
androidx.appcompat.widget.ButtonBarLayout: int getMinimumHeight()
io.flutter.embedding.engine.FlutterJNI: void onBeginFrame()
io.flutter.plugins.inapppurchase.Messages$PlatformPurchaseState: io.flutter.plugins.inapppurchase.Messages$PlatformPurchaseState valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getCachedAppInstanceId(com.google.android.gms.internal.measurement.zzcu)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setupNotificationChannel(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void requestNotificationsPermission(com.dexterous.flutterlocalnotifications.PermissionRequestListener)
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
androidx.appcompat.widget.AppCompatEditText: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.camera.camera2.internal.compat.quirk.PreviewDelayWhenVideoCaptureIsBoundQuirk: PreviewDelayWhenVideoCaptureIsBoundQuirk()
androidx.preference.PreferenceScreen: PreferenceScreen(android.content.Context,android.util.AttributeSet)
com.google.android.gms.internal.play_billing.zzhc: com.google.android.gms.internal.play_billing.zzhc[] values()
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.renderer.FlutterRenderer$ViewportMetrics getViewportMetrics()
com.google.crypto.tink.proto.KeyData$KeyMaterialType: com.google.crypto.tink.proto.KeyData$KeyMaterialType[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void getCallbackHandle(io.flutter.plugin.common.MethodChannel$Result)
androidx.core.app.NotificationCompat$Builder$Api21Impl: android.media.AudioAttributes$Builder setLegacyStreamType(android.media.AudioAttributes$Builder,int)
com.google.gson.ToNumberPolicy: com.google.gson.ToNumberPolicy valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: AppCompatTextViewAutoSizeHelper$Impl23()
android.support.v4.media.session.IMediaSession$Stub: android.support.v4.media.session.IMediaSession asInterface(android.os.IBinder)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void setProxyOverride(java.lang.String[][],java.lang.String[],java.lang.Runnable,java.util.concurrent.Executor)
android.support.v4.media.AudioAttributesImplBaseParcelizer: androidx.media.AudioAttributesImplBase read(androidx.versionedparcelable.VersionedParcel)
android.support.customtabs.IPostMessageService$Stub: android.support.customtabs.IPostMessageService asInterface(android.os.IBinder)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintMode(android.graphics.drawable.Drawable,android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.SearchView: void setSearchableInfo(android.app.SearchableInfo)
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.String[] getDigitStrings(android.icu.text.DecimalFormatSymbols)
com.google.android.datatransport.cct.internal.NetworkConnectionInfo$NetworkType: com.google.android.datatransport.cct.internal.NetworkConnectionInfo$NetworkType[] values()
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
io.flutter.view.TextureRegistry$SurfaceProducer: void scheduleFrame()
androidx.appcompat.widget.AppCompatSpinner: void setDropDownHorizontalOffset(int)
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency valueOf(java.lang.String)
com.google.android.gms.internal.measurement.zzin: com.google.android.gms.internal.measurement.zzin[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeMarkTextureFrameAvailable(long,long)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityResumedByScionActivityInfo(com.google.android.gms.internal.measurement.zzdf,long)
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: java.io.FileDescriptor dup(java.io.FileDescriptor)
io.flutter.embedding.engine.FlutterJNI: void updateCustomAccessibilityActions(java.nio.ByteBuffer,java.lang.String[])
androidx.camera.camera2.internal.compat.quirk.ZslDisablerQuirk: ZslDisablerQuirk()
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.String getData()
com.google.android.gms.measurement.AppMeasurement: java.lang.String getGmpAppId()
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: void authenticate(java.lang.Object,java.lang.Object,android.os.CancellationSignal,int,java.lang.Object,android.os.Handler)
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified access$getAppContext$cp()
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
androidx.camera.core.impl.SurfaceConfig$ConfigSize: androidx.camera.core.impl.SurfaceConfig$ConfigSize valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebResourceError(java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void nativeCleanupMessageData(long)
com.google.firebase.heartbeatinfo.HeartBeatInfo$HeartBeat: com.google.firebase.heartbeatinfo.HeartBeatInfo$HeartBeat[] values()
androidx.core.location.LocationCompat$Api26Impl: void setSpeedAccuracyMetersPerSecond(android.location.Location,float)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: float getFinalOpacity()
androidx.camera.camera2.internal.compat.quirk.ExtraSupportedOutputSizeQuirk: ExtraSupportedOutputSizeQuirk()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.recyclerview.widget.RecyclerView: RecyclerView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat$CollectionItemInfoCompat buildCollectionItemInfoCompat(boolean,int,int,int,int,boolean,java.lang.String,java.lang.String)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature[] values()
androidx.media3.exoplayer.audio.AudioCapabilities$Api33: androidx.media3.exoplayer.audio.AudioDeviceInfoApi23 getDefaultRoutedDeviceForAttributes(android.media.AudioManager,androidx.media3.common.AudioAttributes)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: int getCacheMode()
org.chromium.support_lib_boundary.StaticsBoundaryInterface: boolean isMultiProcessEnabled()
androidx.core.app.NotificationCompat$MessagingStyle$Api28Impl: android.app.Notification$MessagingStyle setGroupConversation(android.app.Notification$MessagingStyle,boolean)
androidx.appcompat.widget.AppCompatButton: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.SearchView: void setAppSearchData(android.os.Bundle)
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle valueOf(java.lang.String)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemColumnTitle(java.lang.Object)
androidx.core.location.LocationManagerCompat$Api31Impl: void requestLocationUpdates(android.location.LocationManager,java.lang.String,android.location.LocationRequest,java.util.concurrent.Executor,android.location.LocationListener)
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval[] $values()
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
androidx.appcompat.widget.SwitchCompat: void setSwitchMinWidth(int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean isValidDrawableResource(android.content.Context,java.lang.String,io.flutter.plugin.common.MethodChannel$Result,java.lang.String)
androidx.media3.exoplayer.audio.DefaultAudioSink$Api31: void setLogSessionIdOnAudioTrack(android.media.AudioTrack,androidx.media3.exoplayer.analytics.PlayerId)
android.support.v4.media.AudioAttributesImplApi21Parcelizer: void write(androidx.media.AudioAttributesImplApi21,androidx.versionedparcelable.VersionedParcel)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getBlockNetworkLoads()
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider: FlutterFirebaseMessagingInitProvider()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.camera.camera2.internal.compat.quirk.ImageCaptureFailedForVideoSnapshotQuirk: ImageCaptureFailedForVideoSnapshotQuirk()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getConditionalUserProperties(java.lang.String,java.lang.String,com.google.android.gms.internal.measurement.zzcu)
androidx.core.location.LocationRequestCompat$Api31Impl: android.location.LocationRequest toLocationRequest(androidx.core.location.LocationRequestCompat)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$500(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImages()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,int,java.nio.ByteBuffer,int)
androidx.appcompat.widget.SearchView: int getImeOptions()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
com.google.firebase.encoders.proto.Protobuf$IntEncoding: com.google.firebase.encoders.proto.Protobuf$IntEncoding[] values()
androidx.appcompat.widget.AppCompatSpinner: void setPopupBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SearchView: void setSuggestionsAdapter(androidx.cursoradapter.widget.CursorAdapter)
androidx.biometric.AuthenticationCallbackProvider$Api28Impl: android.hardware.biometrics.BiometricPrompt$AuthenticationCallback createCallback(androidx.biometric.AuthenticationCallbackProvider$Listener)
androidx.appcompat.widget.AppCompatSpinner: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportButtonTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
com.google.gson.LongSerializationPolicy: com.google.gson.LongSerializationPolicy[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportService$Stub: androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportService asInterface(android.os.IBinder)
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface getSurface()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.os.Bundle call(java.lang.String,java.lang.String,android.os.Bundle)
io.flutter.view.AccessibilityViewEmbedder: void copyAccessibilityFields(android.view.accessibility.AccessibilityNodeInfo,android.view.accessibility.AccessibilityNodeInfo)
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture getMeasurementApiStatusAsync()
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
com.google.android.gms.measurement.AppMeasurement: void beginAdUnitExposure(java.lang.String)
io.flutter.plugins.camerax.CameraStateErrorCode: io.flutter.plugins.camerax.CameraStateErrorCode valueOf(java.lang.String)
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle setDeclineButtonColorHint(android.app.Notification$CallStyle,int)
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle setIsVideo(android.app.Notification$CallStyle,boolean)
androidx.appcompat.widget.SwitchCompat: boolean getSplitTrack()
androidx.appcompat.widget.AppCompatRadioButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatButton: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.core.view.ViewCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
androidx.appcompat.widget.SwitchCompat: void setSplitTrack(boolean)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setCurrentScreenByScionActivityInfo(com.google.android.gms.internal.measurement.zzdf,java.lang.String,java.lang.String,long)
com.google.android.gms.internal.measurement.zzco: java.lang.Object zzf(android.os.Bundle,java.lang.Class)
androidx.core.widget.TextViewCompat$Api28Impl: android.text.PrecomputedText$Params getTextMetricsParams(android.widget.TextView)
com.google.crypto.tink.proto.KeyStatusType: com.google.crypto.tink.proto.KeyStatusType valueOf(java.lang.String)
androidx.camera.core.impl.CameraCaptureMetaData$AfState: androidx.camera.core.impl.CameraCaptureMetaData$AfState[] values()
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified access$getBlockingDispatcher$cp()
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageResponseCallback(int,java.nio.ByteBuffer,int)
androidx.appcompat.widget.SearchView: androidx.cursoradapter.widget.CursorAdapter getSuggestionsAdapter()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void initialize(com.google.android.gms.dynamic.IObjectWrapper,com.google.android.gms.internal.measurement.zzdd,long)
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
androidx.appcompat.widget.AppCompatSpinner: int getDropDownWidth()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: DateTimeComponents(java.lang.String,int)
androidx.camera.core.UseCase$State: androidx.camera.core.UseCase$State[] values()
com.google.firebase.sessions.FirebaseSessionsRegistrar: java.util.List getComponents()
androidx.media.AudioAttributesImplApi26Parcelizer: void write(androidx.media.AudioAttributesImplApi26,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: void setViewCacheExtension(androidx.recyclerview.widget.RecyclerView$ViewCacheExtension)
androidx.media3.exoplayer.ExoPlayerImpl$Api23: void registerAudioDeviceCallback(android.media.AudioManager,android.media.AudioDeviceCallback,android.os.Handler)
androidx.camera.camera2.internal.ProcessingCaptureSession$ProcessorState: androidx.camera.camera2.internal.ProcessingCaptureSession$ProcessorState valueOf(java.lang.String)
androidx.activity.ComponentActivity$Api33Impl: android.window.OnBackInvokedDispatcher getOnBackInvokedDispatcher(android.app.Activity)
com.baseflow.geolocator.GeolocatorLocationService: GeolocatorLocationService()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization[] values()
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
io.flutter.plugins.firebase.crashlytics.FlutterFirebaseCrashlyticsPlugin: FlutterFirebaseCrashlyticsPlugin()
androidx.core.location.LocationCompat$Api26Impl: boolean hasVerticalAccuracy(android.location.Location)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewRenderer()
androidx.appcompat.widget.SwitchCompat: void setChecked(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar: java.util.List getComponents()
androidx.core.view.ViewCompat$Api26Impl: void setFocusedByDefault(android.view.View,boolean)
io.flutter.plugins.camerax.CameraStateErrorCode: io.flutter.plugins.camerax.CameraStateErrorCode[] values()
androidx.camera.video.internal.BufferProvider$State: androidx.camera.video.internal.BufferProvider$State valueOf(java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getMaxUserProperties(java.lang.String,com.google.android.gms.internal.measurement.zzcu)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader33()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: java.lang.String[] getStreamTypes(android.net.Uri,java.lang.String)
androidx.appcompat.widget.SearchView: void setIconified(boolean)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebResourceRequest(android.webkit.WebResourceRequest)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: java.lang.String getVariationsHeader()
androidx.appcompat.widget.SearchView: void setInputType(int)
io.flutter.view.AccessibilityViewEmbedder: void addChildrenToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getWebauthnSupport()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
com.google.crypto.tink.proto.OutputPrefixType: com.google.crypto.tink.proto.OutputPrefixType[] values()
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap decodeImage(java.nio.ByteBuffer,long)
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
kotlinx.coroutines.android.AndroidExceptionPreHandler: void handleException(kotlin.coroutines.CoroutineContext,java.lang.Throwable)
com.it_nomads.fluttersecurestorage.ciphers.StorageCipherAlgorithm: com.it_nomads.fluttersecurestorage.ciphers.StorageCipherAlgorithm valueOf(java.lang.String)
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: int getWindowAreaStatus()
androidx.core.app.ActivityCompat$Api31Impl: boolean isLaunchedFromBubble(android.app.Activity)
androidx.camera.video.internal.compat.quirk.DeactivateEncoderSurfaceBeforeStopEncoderQuirk: DeactivateEncoderSurfaceBeforeStopEncoderQuirk()
dev.fluttercommunity.plus.battery.BatteryPlusPlugin: BatteryPlusPlugin()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void generateEventId(com.google.android.gms.internal.measurement.zzcu)
androidx.core.content.res.ResourcesCompat$Api23Impl: int getColor(android.content.res.Resources,int,android.content.res.Resources$Theme)
com.google.firebase.messaging.reporting.MessagingClientEvent$Event: com.google.firebase.messaging.reporting.MessagingClientEvent$Event[] values()
com.google.android.gms.internal.play_billing.zzju: com.google.android.gms.internal.play_billing.zzju[] values()
com.google.firebase.crashlytics.CrashlyticsRegistrar: CrashlyticsRegistrar()
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onDetachedFromActivityForConfigChanges()
com.google.common.collect.BaseImmutableMultimap: BaseImmutableMultimap()
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified access$getFirebaseSessionsComponent$cp()
androidx.camera.video.StreamInfo$StreamState: androidx.camera.video.StreamInfo$StreamState valueOf(java.lang.String)
androidx.core.widget.PopupWindowCompat$Api23Impl: void setWindowLayoutType(android.widget.PopupWindow,int)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI nativeSpawn(long,java.lang.String,java.lang.String,java.lang.String,java.util.List,long)
android.support.v4.media.AudioAttributesImplBaseParcelizer: AudioAttributesImplBaseParcelizer()
androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy: ConstraintProxy$StorageNotLowProxy()
io.flutter.view.TextureRegistry$ImageTextureEntry: long id()
io.flutter.plugins.firebase.core.FlutterFirebasePlugin: com.google.android.gms.tasks.Task getPluginConstantsForFirebaseApp(com.google.firebase.FirebaseApp)
flutter.overlay.window.flutter_overlay_window.FlutterOverlayWindowPlugin: FlutterOverlayWindowPlugin()
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void registerOnMeasurementEventListener(com.google.android.gms.internal.measurement.zzda)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader access$700(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar: FirebaseCrashlyticsKtxRegistrar()
androidx.biometric.BiometricFragment$Api28Impl: void setNegativeButton(android.hardware.biometrics.BiometricPrompt$Builder,java.lang.CharSequence,java.util.concurrent.Executor,android.content.DialogInterface$OnClickListener)
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurfaces()
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void updateJavaAssetManager(android.content.res.AssetManager,java.lang.String)
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType[] values()
androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy: ConstraintProxy$BatteryNotLowProxy()
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedFling(android.view.View,float,float,boolean)
androidx.camera.video.internal.audio.AudioSource$InternalState: androidx.camera.video.internal.audio.AudioSource$InternalState valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void release()
androidx.biometric.KeyguardUtils$Api23Impl: boolean isDeviceSecure(android.app.KeyguardManager)
androidx.core.view.ViewCompat$Api21Impl: void setTranslationZ(android.view.View,float)
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void postMessage(java.lang.reflect.InvocationHandler)
androidx.camera.core.internal.compat.quirk.LargeJpegImageQuirk: LargeJpegImageQuirk()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
io.flutter.embedding.engine.FlutterJNI: void addIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean handlesCropAndRotation()
com.google.gson.ReflectionAccessFilter$FilterResult: com.google.gson.ReflectionAccessFilter$FilterResult[] values()
androidx.core.app.NotificationManagerCompat$Api26Impl: java.lang.String getId(android.app.NotificationChannel)
com.google.android.gms.internal.measurement.zzcq: com.google.android.gms.internal.measurement.zzcr asInterface(android.os.IBinder)
androidx.camera.video.internal.compat.quirk.AudioEncoderIgnoresInputTimestampQuirk: AudioEncoderIgnoresInputTimestampQuirk()
io.flutter.embedding.engine.FlutterJNI: void lambda$decodeImage$0(long,android.graphics.ImageDecoder,android.graphics.ImageDecoder$ImageInfo,android.graphics.ImageDecoder$Source)
androidx.core.view.ViewCompat$Api21Impl: void setTransitionName(android.view.View,java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: androidx.core.app.NotificationCompat$MessagingStyle$Message createMessage(android.content.Context,com.dexterous.flutterlocalnotifications.models.MessageDetails)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readInboxStyleInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map,com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation)
androidx.media3.exoplayer.audio.DefaultAudioOffloadSupportProvider$Api31: androidx.media3.exoplayer.audio.AudioOffloadSupport getOffloadedPlaybackSupport(android.media.AudioFormat,android.media.AudioAttributes,boolean)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void removeRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
com.google.firebase.messaging.threads.ThreadPriority: com.google.firebase.messaging.threads.ThreadPriority valueOf(java.lang.String)
androidx.media.AudioAttributesImplBaseParcelizer: AudioAttributesImplBaseParcelizer()
com.baseflow.geolocator.location.ServiceStatus: com.baseflow.geolocator.location.ServiceStatus[] values()
androidx.appcompat.widget.AppCompatButton: int getAutoSizeTextType()
androidx.appcompat.widget.AppCompatButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.camera.core.internal.compat.quirk.ImageCaptureRotationOptionQuirk: ImageCaptureRotationOptionQuirk()
androidx.media3.exoplayer.mediacodec.MediaCodecPerformancePointCoverageProvider$Api29: int areResolutionAndFrameRateCovered(android.media.MediaCodecInfo$VideoCapabilities,int,int,double)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.work.impl.background.systemjob.SystemJobService$Api28Impl: android.net.Network getNetwork(android.app.job.JobParameters)
androidx.core.app.NotificationManagerCompat$Api26Impl: void createNotificationChannel(android.app.NotificationManager,android.app.NotificationChannel)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.core.os.BundleCompat$Api33Impl: java.util.ArrayList getParcelableArrayList(android.os.Bundle,java.lang.String,java.lang.Class)
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void onRenderingStopped()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
androidx.preference.SwitchPreferenceCompat: SwitchPreferenceCompat(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
com.google.android.gms.internal.play_billing.zzhs: com.google.android.gms.internal.play_billing.zzhs[] values()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object registerWebTrigger(androidx.privacysandbox.ads.adservices.measurement.WebTriggerRegistrationRequest,kotlin.coroutines.Continuation)
androidx.biometric.CryptoObjectUtils$Api23Impl: void initKeyGenerator(javax.crypto.KeyGenerator,android.security.keystore.KeyGenParameterSpec)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean onRequestPermissionsResult(int,java.lang.String[],int[])
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
com.google.firebase.provider.FirebaseInitProvider: FirebaseInitProvider()
androidx.appcompat.widget.SearchView: void setIconifiedByDefault(boolean)
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin: SharedPreferencesPlugin()
com.google.common.util.concurrent.DirectExecutor: com.google.common.util.concurrent.DirectExecutor valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.lang.reflect.InvocationHandler getProfile(java.lang.String)
com.it_nomads.fluttersecurestorage.ciphers.StorageCipherAlgorithm: com.it_nomads.fluttersecurestorage.ciphers.StorageCipherAlgorithm[] values()
androidx.core.app.NotificationCompatBuilder$Api29Impl: android.app.Notification$Builder setLocusId(android.app.Notification$Builder,java.lang.Object)
androidx.core.app.AppOpsManagerCompat$Api29Impl: int checkOpNoThrow(android.app.AppOpsManager,java.lang.String,int,java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintMode(android.view.MenuItem,android.graphics.PorterDuff$Mode)
com.baseflow.geolocator.permission.LocationPermission: com.baseflow.geolocator.permission.LocationPermission valueOf(java.lang.String)
androidx.camera.core.ImageProcessingUtil: int nativeConvertAndroid420ToABGR(java.nio.ByteBuffer,int,java.nio.ByteBuffer,int,java.nio.ByteBuffer,int,int,int,android.view.Surface,java.nio.ByteBuffer,int,int,int,int,int,int)
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
androidx.biometric.BiometricFragment$Api30Impl: void setAllowedAuthenticators(android.hardware.biometrics.BiometricPrompt$Builder,int)
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
com.google.android.gms.measurement.AppMeasurementReceiver: AppMeasurementReceiver()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void initForTests(java.util.Map)
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
androidx.work.impl.foreground.SystemForegroundService$Api31Impl: void startForeground(android.app.Service,int,android.app.Notification,int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
org.chromium.support_lib_boundary.ScriptHandlerBoundaryInterface: void remove()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onReattachedToActivityForConfigChanges(io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: android.view.accessibility.AccessibilityNodeInfo$ExtraRenderingInfo getExtraRenderingInfo(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.core.app.NotificationCompat$MessagingStyle$Api26Impl: android.app.Notification$MessagingStyle addHistoricMessage(android.app.Notification$MessagingStyle,android.app.Notification$MessagingStyle$Message)
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: android.hardware.fingerprint.FingerprintManager getFingerprintManagerOrNull(android.content.Context)
androidx.camera.camera2.internal.compat.quirk.FlashAvailabilityBufferUnderflowQuirk: FlashAvailabilityBufferUnderflowQuirk()
androidx.appcompat.widget.SearchView: void setImeOptions(int)
com.google.android.datatransport.runtime.backends.BackendResponse$Status: com.google.android.datatransport.runtime.backends.BackendResponse$Status[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: AppMeasurementDynamiteService()
io.flutter.view.TextureRegistry$SurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice: io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice[] values()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointRegionalIndicator(int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setLights(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.core.app.AlarmManagerCompat$Api23Impl: void setAndAllowWhileIdle(android.app.AlarmManager,int,long,android.app.PendingIntent)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setCacheMode(int)
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType[] values()
com.google.firebase.sessions.EventType: com.google.firebase.sessions.EventType[] values()
io.flutter.plugin.platform.PlatformViewWrapper: void setTouchProcessor(io.flutter.embedding.android.AndroidTouchProcessor)
androidx.core.view.ViewCompat$Api30Impl: void setImportantForContentCapture(android.view.View,int)
androidx.core.app.AlarmManagerCompat$Api21Impl: android.app.AlarmManager$AlarmClockInfo createAlarmClockInfo(long,android.app.PendingIntent)
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setPublicVersion(android.app.Notification$Builder,android.app.Notification)
androidx.loader.content.ModernAsyncTask$Status: androidx.loader.content.ModernAsyncTask$Status[] values()
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodesTranslateBounds(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect,android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.ViewCompat$Api26Impl: boolean isFocusedByDefault(android.view.View)
io.flutter.plugins.camerax.ResolutionStrategyFallbackRule: io.flutter.plugins.camerax.ResolutionStrategyFallbackRule valueOf(java.lang.String)
androidx.camera.video.internal.compat.quirk.EncoderNotUsePersistentInputSurfaceQuirk: EncoderNotUsePersistentInputSurfaceQuirk()
com.google.crypto.tink.proto.HashType: com.google.crypto.tink.proto.HashType[] values()
androidx.camera.core.impl.CameraCaptureMetaData$AeState: androidx.camera.core.impl.CameraCaptureMetaData$AeState valueOf(java.lang.String)
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: com.google.android.gms.tasks.Task getPluginConstantsForFirebaseApp(com.google.firebase.FirebaseApp)
com.google.android.gms.internal.play_billing.zzam: com.google.android.gms.internal.play_billing.zzan zzu(android.os.IBinder)
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle setVerificationIcon(android.app.Notification$CallStyle,android.graphics.drawable.Icon)
androidx.core.os.BundleApi21ImplKt: void putSizeF(android.os.Bundle,java.lang.String,android.util.SizeF)
androidx.work.impl.utils.futures.DirectExecutor: androidx.work.impl.utils.futures.DirectExecutor valueOf(java.lang.String)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledVerticalScrollFactor(android.view.ViewConfiguration)
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
dev.fluttercommunity.workmanager.Extractor$PossibleWorkManagerCall: dev.fluttercommunity.workmanager.Extractor$PossibleWorkManagerCall[] values()
androidx.camera.core.impl.CameraInternal$State: androidx.camera.core.impl.CameraInternal$State valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.lang.reflect.InvocationHandler getOrCreateProfile(java.lang.String)
androidx.appcompat.widget.AppCompatRadioButton: android.graphics.PorterDuff$Mode getSupportButtonTintMode()
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType valueOf(java.lang.String)
androidx.camera.core.ImageProcessingUtil$Result: androidx.camera.core.ImageProcessingUtil$Result[] values()
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.camera.camera2.internal.compat.quirk.CrashWhenTakingPhotoWithAutoFlashAEModeQuirk: CrashWhenTakingPhotoWithAutoFlashAEModeQuirk()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler createWebView(android.webkit.WebView)
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
io.flutter.plugins.camerax.CameraStateType: io.flutter.plugins.camerax.CameraStateType[] values()
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
androidx.appcompat.widget.AppCompatSpinner: void setBackgroundResource(int)
org.chromium.support_lib_boundary.WebMessageCallbackBoundaryInterface: void onMessage(java.lang.reflect.InvocationHandler,java.lang.reflect.InvocationHandler)
androidx.media3.exoplayer.audio.AudioCapabilitiesReceiver$Api23: void registerAudioDeviceCallback(android.content.Context,android.media.AudioDeviceCallback,android.os.Handler)
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
io.flutter.plugins.camerax.AspectRatio: io.flutter.plugins.camerax.AspectRatio[] values()
io.flutter.plugins.webviewflutter.FileChooserMode: io.flutter.plugins.webviewflutter.FileChooserMode valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api21Impl: void stopNestedScroll(android.view.View)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getOffscreenPreRaster()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind[] values()
androidx.camera.core.UseCase$State: androidx.camera.core.UseCase$State valueOf(java.lang.String)
io.flutter.plugins.googlesignin.GoogleSignInPlugin: GoogleSignInPlugin()
androidx.core.app.NotificationCompat$CallStyle$Api23Impl: android.app.Notification$Action$Builder createActionBuilder(android.graphics.drawable.Icon,java.lang.CharSequence,android.app.PendingIntent)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeStepGranularity()
com.google.firebase.ktx.FirebaseCommonLegacyRegistrar: java.util.List getComponents()
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.AccessibilityEventsDelegate,io.flutter.plugin.platform.SingleViewPresentation$PresentationState,android.view.View$OnFocusChangeListener,boolean)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOpNoThrow(android.app.AppOpsManager,java.lang.String,java.lang.String)
io.flutter.plugins.firebase.crashlytics.FirebaseCrashlyticsTestCrash: FirebaseCrashlyticsTestCrash()
androidx.startup.InitializationProvider: InitializationProvider()
androidx.core.view.ViewCompat$Api26Impl: android.view.autofill.AutofillId getAutofillId(android.view.View)
com.google.android.gms.common.GooglePlayServicesMissingManifestValueException: GooglePlayServicesMissingManifestValueException()
androidx.work.impl.workers.ConstraintTrackingWorker: ConstraintTrackingWorker(android.content.Context,androidx.work.WorkerParameters)
androidx.core.content.res.FontResourcesParserCompat$Api21Impl: int getType(android.content.res.TypedArray,int)
androidx.biometric.CryptoObjectUtils$Api28Impl: android.hardware.biometrics.BiometricPrompt$CryptoObject create(java.security.Signature)
androidx.media3.common.AudioAttributes$Api29: void setAllowedCapturePolicy(android.media.AudioAttributes$Builder,int)
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: RuntimeTypeAdapterFactory(java.lang.Class,java.lang.String)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface: void postMessageWithPayload(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
androidx.media3.exoplayer.audio.DefaultAudioSink$OnRoutingChangedListenerApi24: void onRoutingChanged(android.media.AudioRouting)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void startRearDisplaySession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo getRootNode(android.view.View,int,android.graphics.Rect)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintList(android.widget.TextView,android.content.res.ColorStateList)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.appcompat.widget.AppCompatRadioButton: void setSupportButtonTintList(android.content.res.ColorStateList)
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: android.graphics.Matrix getPlatformViewMatrix()
com.google.android.gms.measurement.AppMeasurement: long generateEventId()
com.google.android.gms.measurement.AppMeasurement: int getMaxUserProperties(java.lang.String)
androidx.media.AudioAttributesCompatParcelizer: void write(androidx.media.AudioAttributesCompat,androidx.versionedparcelable.VersionedParcel)
androidx.recyclerview.widget.RecyclerView: void setEdgeEffectFactory(androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRRect(int,int,int,int,float[])
androidx.work.OverwritingInputMerger: OverwritingInputMerger()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
androidx.media3.exoplayer.mediacodec.MediaCodecRenderer$Api31: void setLogSessionIdToMediaCodecFormat(androidx.media3.exoplayer.mediacodec.MediaCodecAdapter$Configuration,androidx.media3.exoplayer.analytics.PlayerId)
androidx.camera.video.VideoOutput$SourceState: androidx.camera.video.VideoOutput$SourceState[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setConsentThirdParty(android.os.Bundle,long)
com.google.android.gms.dynamic.FragmentWrapper: com.google.android.gms.dynamic.FragmentWrapper wrap(android.app.Fragment)
android.support.customtabs.trusted.ITrustedWebActivityCallback$Stub: android.support.customtabs.trusted.ITrustedWebActivityCallback asInterface(android.os.IBinder)
io.flutter.embedding.engine.FlutterJNI: float getScaledFontSize(float,int)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
com.google.android.datatransport.Priority: com.google.android.datatransport.Priority valueOf(java.lang.String)
com.google.crypto.tink.shaded.protobuf.JavaType: com.google.crypto.tink.shaded.protobuf.JavaType valueOf(java.lang.String)
androidx.media3.exoplayer.drm.DrmUtil$Api23: boolean isMediaDrmResetException(java.lang.Throwable)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void createNotificationChannel(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
androidx.camera.core.impl.utils.SurfaceUtil: int[] nativeGetSurfaceInfo(android.view.Surface)
androidx.core.view.ViewCompat$Api21Impl: void setElevation(android.view.View,float)
io.flutter.embedding.engine.FlutterJNI: void markTextureFrameAvailable(long)
androidx.window.extensions.core.util.function.Consumer: void accept(java.lang.Object)
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: NotificationChannelGroupDetails()
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
androidx.core.app.NotificationCompatBuilder$Api23Impl: android.app.Notification$Action$Builder createBuilder(android.graphics.drawable.Icon,java.lang.CharSequence,android.app.PendingIntent)
androidx.core.view.ViewCompat$Api28Impl: void setScreenReaderFocusable(android.view.View,boolean)
androidx.media3.exoplayer.audio.AudioCapabilities$Api23: com.google.common.collect.ImmutableSet getAllBluetoothDeviceTypes()
androidx.core.graphics.drawable.IconCompat$Api30Impl: android.graphics.drawable.Icon createWithAdaptiveBitmapContentUri(android.net.Uri)
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
com.google.crypto.tink.shaded.protobuf.ProtoSyntax: com.google.crypto.tink.shaded.protobuf.ProtoSyntax[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void zonedSchedule(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar: FlutterFirebaseCoreRegistrar()
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.NotificationDetails from(java.util.Map)
io.flutter.plugins.webviewflutter.SslErrorType: io.flutter.plugins.webviewflutter.SslErrorType[] values()
android.support.v4.os.IResultReceiver2$Stub: android.support.v4.os.IResultReceiver2 asInterface(android.os.IBinder)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
androidx.browser.customtabs.CustomTabsIntent$Api23Impl: android.app.ActivityOptions makeBasicActivityOptions()
androidx.media3.exoplayer.video.VideoFrameReleaseHelper$Api30: void setSurfaceFrameRate(android.view.Surface,float)
androidx.core.widget.CompoundButtonCompat$Api21Impl: void setButtonTintList(android.widget.CompoundButton,android.content.res.ColorStateList)
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService: FlutterFirebaseMessagingBackgroundService()
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
androidx.work.ExistingPeriodicWorkPolicy: androidx.work.ExistingPeriodicWorkPolicy[] values()
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.database.Cursor query(android.net.Uri,java.lang.String[],java.lang.String,java.lang.String[],java.lang.String)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
androidx.camera.lifecycle.LifecycleCamera: void onPause(androidx.lifecycle.LifecycleOwner)
androidx.window.extensions.core.util.function.Predicate: boolean test(java.lang.Object)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void clearPrefetch(java.lang.String,android.webkit.ValueCallback)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.Image acquireLatestImage()
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onReceivedHttpError(android.webkit.WebView,android.webkit.WebResourceRequest,android.webkit.WebResourceResponse)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface: void postMessage(java.lang.String)
com.google.android.gms.common.SupportErrorDialogFragment: SupportErrorDialogFragment()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.view.Surface getSurface()
dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin: DeviceInfoPlusPlugin()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void areNotificationsEnabled(io.flutter.plugin.common.MethodChannel$Result)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setMediaStyle(androidx.core.app.NotificationCompat$Builder)
androidx.camera.camera2.internal.compat.quirk.YuvImageOnePixelShiftQuirk: YuvImageOnePixelShiftQuirk()
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
xyz.luan.audioplayers.PlayerMode: xyz.luan.audioplayers.PlayerMode valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintMode(android.view.View,android.graphics.PorterDuff$Mode)
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Builder setCustomContentView(android.app.Notification$Builder,android.widget.RemoteViews)
androidx.camera.video.Recorder$State: androidx.camera.video.Recorder$State[] values()
androidx.appcompat.widget.AppCompatEditText: void setBackgroundResource(int)
io.flutter.embedding.engine.FlutterJNI: void notifyLowMemoryWarning()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource[] values()
io.flutter.view.FlutterCallbackInformation: io.flutter.view.FlutterCallbackInformation lookupCallbackInformation(long)
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode[] values()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWebViewMediaIntegrityApiStatus(int,java.util.Map)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void pushImage(android.media.Image)
io.flutter.plugins.pathprovider.PathProviderPlugin: PathProviderPlugin()
io.flutter.plugins.inapppurchase.Messages$PlatformBillingClientFeature: io.flutter.plugins.inapppurchase.Messages$PlatformBillingClientFeature[] values()
androidx.biometric.AuthenticationCallbackProvider$Api28Impl$1: void onAuthenticationSucceeded(android.hardware.biometrics.BiometricPrompt$AuthenticationResult)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory valueOf(java.lang.String)
com.google.android.gms.location.zzs: com.google.android.gms.location.zzt zzb(android.os.IBinder)
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
androidx.privacysandbox.ads.adservices.internal.AdServicesInfo$Extensions30Impl: int getAdServicesVersion()
androidx.camera.camera2.internal.compat.quirk.Preview3AThreadCrashQuirk: Preview3AThreadCrashQuirk()
androidx.core.app.RemoteInput$Api20Impl: android.os.Bundle getResultsFromIntent(android.content.Intent)
androidx.camera.camera2.internal.compat.quirk.SmallDisplaySizeQuirk: SmallDisplaySizeQuirk()
androidx.appcompat.widget.SwitchCompat: void setThumbResource(int)
xyz.luan.audioplayers.ReleaseMode: xyz.luan.audioplayers.ReleaseMode[] values()
androidx.work.WorkManager$UpdateResult: androidx.work.WorkManager$UpdateResult[] values()
androidx.appcompat.widget.AppCompatButton: void setAutoSizeTextTypeWithDefaults(int)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.GeolocationPermissions getGeoLocationPermissions()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getTracingController()
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
com.google.firebase.sessions.LogEnvironment: com.google.firebase.sessions.LogEnvironment[] values()
androidx.work.impl.background.systemalarm.RescheduleReceiver: RescheduleReceiver()
androidx.core.widget.EdgeEffectCompat$Api31Impl: android.widget.EdgeEffect create(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
androidx.security.crypto.EncryptedSharedPreferences$PrefValueEncryptionScheme: androidx.security.crypto.EncryptedSharedPreferences$PrefValueEncryptionScheme valueOf(java.lang.String)
androidx.core.app.NotificationCompat$Builder$Api21Impl: android.media.AudioAttributes$Builder setUsage(android.media.AudioAttributes$Builder,int)
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurface2()
androidx.core.app.NotificationManagerCompat$Api26Impl: void createNotificationChannelGroups(android.app.NotificationManager,java.util.List)
androidx.work.impl.background.systemalarm.SystemAlarmService: SystemAlarmService()
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: android.hardware.fingerprint.FingerprintManager$CryptoObject getCryptoObject(java.lang.Object)
com.google.firebase.concurrent.SequentialExecutor$WorkerRunningState: com.google.firebase.concurrent.SequentialExecutor$WorkerRunningState[] values()
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintList(android.widget.ImageView,android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void addWebMessageListener(java.lang.String,java.lang.String[],java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertSettings(android.webkit.WebSettings)
androidx.appcompat.widget.AppCompatSpinner: java.lang.CharSequence getPrompt()
androidx.appcompat.widget.AppCompatRadioButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setVibrationPattern(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean onActivityResult(int,int,android.content.Intent)
androidx.camera.video.internal.compat.quirk.ExtraSupportedResolutionQuirk: ExtraSupportedResolutionQuirk()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readBigPictureStyleInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map,com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation)
com.google.android.gms.internal.measurement.zzbm: android.os.IBinder asBinder()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object registerWebSource$suspendImpl(androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon,androidx.privacysandbox.ads.adservices.measurement.WebSourceRegistrationRequest,kotlin.coroutines.Continuation)
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
com.google.android.datatransport.cct.internal.QosTier: com.google.android.datatransport.cct.internal.QosTier valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void asyncWaitForVsync(long)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getTrackDrawable()
com.google.android.gms.internal.play_billing.zzd: com.google.android.gms.internal.play_billing.zzd[] values()
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerWebSourceAsync(androidx.privacysandbox.ads.adservices.measurement.WebSourceRegistrationRequest)
io.flutter.plugins.webviewflutter.ConsoleMessageLevel: io.flutter.plugins.webviewflutter.ConsoleMessageLevel valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void getNotificationChannels(io.flutter.plugin.common.MethodChannel$Result)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler addDocumentStartJavaScript(java.lang.String,java.lang.String[])
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: long calculateRepeatIntervalMilliseconds(com.dexterous.flutterlocalnotifications.models.NotificationDetails)
androidx.appcompat.widget.AppCompatSpinner: android.content.Context getPopupContext()
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float onPullDistance(android.widget.EdgeEffect,float,float)
androidx.camera.video.internal.compat.quirk.SizeCannotEncodeVideoQuirk: SizeCannotEncodeVideoQuirk()
androidx.core.view.ViewCompat$Api21Impl: boolean hasNestedScrollingParent(android.view.View)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType[] values()
androidx.media3.exoplayer.audio.AudioCapabilities$Api29: int getMaxSupportedChannelCountForPassthrough(int,int,androidx.media3.common.AudioAttributes)
androidx.camera.core.impl.Timebase: androidx.camera.core.impl.Timebase valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void init(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long,int)
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture deleteRegistrationsAsync(androidx.privacysandbox.ads.adservices.measurement.DeletionRequest)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage dequeueImage()
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportButtonTintList()
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase[] values()
io.flutter.plugins.camerax.CameraStateType: io.flutter.plugins.camerax.CameraStateType valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setVisibility(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
androidx.core.view.ViewCompat$Api26Impl: boolean hasExplicitFocusable(android.view.View)
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact[] values()
com.google.android.gms.internal.measurement.zzlw: com.google.android.gms.internal.measurement.zzlw[] values()
androidx.appcompat.widget.AppCompatButton: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
com.google.common.collect.AbstractMultimap: AbstractMultimap()
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.reflect.InvocationHandler[] getPorts()
androidx.appcompat.widget.SwitchCompat: int getSwitchPadding()
androidx.recyclerview.widget.RecyclerView: int getMinFlingVelocity()
androidx.core.app.NotificationCompatBuilder$Api28Impl: android.app.Notification$Builder addPerson(android.app.Notification$Builder,android.app.Person)
com.jrai.flutter_keyboard_visibility.FlutterKeyboardVisibilityPlugin: FlutterKeyboardVisibilityPlugin()
androidx.recyclerview.widget.RecyclerView: void setAdapter(androidx.recyclerview.widget.RecyclerView$Adapter)
com.google.firebase.sessions.DataCollectionState: com.google.firebase.sessions.DataCollectionState valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void disableFenceForTest()
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Icon createWithAdaptiveBitmap(android.graphics.Bitmap)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.WebStorage getWebStorage()
com.google.android.gms.measurement.AppMeasurement: void logEventInternal(java.lang.String,java.lang.String,android.os.Bundle)
androidx.profileinstaller.ProfileVerifier$Api33Impl: android.content.pm.PackageInfo getPackageInfo(android.content.pm.PackageManager,android.content.Context)
androidx.core.content.ContextCompat$Api24Impl: android.content.Context createDeviceProtectedStorageContext(android.content.Context)
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetHeight()
androidx.camera.camera2.internal.compat.quirk.TemporalNoiseQuirk: TemporalNoiseQuirk()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getSessionId(com.google.android.gms.internal.measurement.zzcu)
androidx.core.app.NotificationCompat$Builder$Api21Impl: android.media.AudioAttributes$Builder setContentType(android.media.AudioAttributes$Builder,int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$400(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
androidx.work.WorkManagerInitializer: WorkManagerInitializer()
androidx.appcompat.widget.SwitchCompat: void setSwitchPadding(int)
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void showInterstitial(boolean)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityDelegate(io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate)
androidx.camera.core.impl.CameraCaptureMetaData$AwbMode: androidx.camera.core.impl.CameraCaptureMetaData$AwbMode[] values()
io.flutter.embedding.engine.FlutterJNI: void onEndFrame()
io.flutter.plugins.webviewflutter.OverScrollMode: io.flutter.plugins.webviewflutter.OverScrollMode[] values()
androidx.core.os.LocaleListCompat$Api24Impl: android.os.LocaleList createLocaleList(java.util.Locale[])
com.dexterous.flutterlocalnotifications.models.ScheduleMode: boolean useExactAlarm()
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
androidx.core.app.NotificationManagerCompat$Api26Impl: void deleteNotificationChannelGroup(android.app.NotificationManager,java.lang.String)
io.flutter.embedding.engine.FlutterOverlaySurface: FlutterOverlaySurface(int,android.view.Surface)
kotlin.time.DurationUnit: kotlin.time.DurationUnit valueOf(java.lang.String)
com.spencerccf.app_settings.AppSettingsPlugin: AppSettingsPlugin()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object registerSource(android.net.Uri,android.view.InputEvent,kotlin.coroutines.Continuation)
androidx.media3.exoplayer.drm.FrameworkMediaDrm$Api31: boolean requiresSecureDecoder(android.media.MediaDrm,java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setShortcut(android.view.MenuItem,char,char,int,int)
androidx.media.AudioAttributesImplApi21Parcelizer: AudioAttributesImplApi21Parcelizer()
androidx.appcompat.widget.AppCompatCheckBox: void setButtonDrawable(int)
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void backToSafety(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
androidx.recyclerview.widget.RecyclerView: long getNanoTime()
androidx.biometric.CancellationSignalProvider$Api16Impl: void cancel(android.os.CancellationSignal)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object registerWebTrigger$suspendImpl(androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon,androidx.privacysandbox.ads.adservices.measurement.WebTriggerRegistrationRequest,kotlin.coroutines.Continuation)
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: android.hardware.fingerprint.FingerprintManager$CryptoObject wrapCryptoObject(androidx.core.hardware.fingerprint.FingerprintManagerCompat$CryptoObject)
androidx.appcompat.widget.AppCompatCheckedTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void performAction(android.os.Bundle,com.google.android.gms.internal.measurement.zzcu,long)
com.google.android.gms.measurement.AppMeasurement: void setConditionalUserProperty(com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService: FlutterFirebaseMessagingService()
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action[] values()
androidx.core.app.NotificationManagerCompat$Api26Impl: android.app.NotificationChannel getNotificationChannel(android.app.NotificationManager,java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void checkCanScheduleExactAlarms(android.app.AlarmManager)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness valueOf(java.lang.String)
androidx.camera.core.impl.SurfaceConfig$ConfigType: androidx.camera.core.impl.SurfaceConfig$ConfigType valueOf(java.lang.String)
androidx.media3.exoplayer.audio.DefaultAudioOffloadSupportProvider$Api29: androidx.media3.exoplayer.audio.AudioOffloadSupport getOffloadedPlaybackSupport(android.media.AudioFormat,android.media.AudioAttributes,boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.content.Intent getLaunchIntent(android.content.Context)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getHeight()
androidx.camera.camera2.Camera2Config$DefaultProvider: androidx.camera.core.CameraXConfig getCameraXConfig()
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle getLifecycle()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: android.webkit.WebViewClient getWebViewClient()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceChanged(long,int,int)
com.google.firebase.analytics.FirebaseAnalytics$ConsentStatus: com.google.firebase.analytics.FirebaseAnalytics$ConsentStatus valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsVariationSelector(int)
com.google.crypto.tink.proto.OutputPrefixType: com.google.crypto.tink.proto.OutputPrefixType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: boolean canApplyTheme(android.graphics.drawable.Drawable)
androidx.media.AudioAttributesImplApi21Parcelizer: void write(androidx.media.AudioAttributesImplApi21,androidx.versionedparcelable.VersionedParcel)
androidx.camera.camera2.internal.CaptureSession$State: androidx.camera.camera2.internal.CaptureSession$State[] values()
androidx.work.impl.diagnostics.DiagnosticsReceiver: DiagnosticsReceiver()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View$OnApplyWindowInsetsListener getInsetsListener()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.biometric.BiometricFragment$Api28Impl: void authenticate(android.hardware.biometrics.BiometricPrompt,android.os.CancellationSignal,java.util.concurrent.Executor,android.hardware.biometrics.BiometricPrompt$AuthenticationCallback)
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness[] values()
androidx.recyclerview.widget.RecyclerView: int getItemDecorationCount()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setBigPictureStyle(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode valueOf(java.lang.String)
androidx.core.app.RemoteInput$Api26Impl: java.util.Set getAllowedDataTypes(java.lang.Object)
android.support.customtabs.IEngagementSignalsCallback$Stub: android.support.customtabs.IEngagementSignalsCallback asInterface(android.os.IBinder)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: long getMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.location.LocationCompat$Api26Impl: float getBearingAccuracyDegrees(android.location.Location)
androidx.appcompat.widget.AppCompatButton: int[] getAutoSizeTextAvailableSizes()
androidx.core.widget.CompoundButtonCompat$Api21Impl: void setButtonTintMode(android.widget.CompoundButton,android.graphics.PorterDuff$Mode)
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setCategory(android.app.Notification$Builder,java.lang.String)
com.google.firebase.ktx.FirebaseCommonKtxRegistrar: FirebaseCommonKtxRegistrar()
androidx.camera.core.impl.Timebase: androidx.camera.core.impl.Timebase[] values()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Map getWebViewMediaIntegrityApiOverrideRules()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void logEvent(java.lang.String,java.lang.String,android.os.Bundle,boolean,boolean,long)
io.flutter.plugins.videoplayer.ExoPlayerEventListener$RotationDegrees: io.flutter.plugins.videoplayer.ExoPlayerEventListener$RotationDegrees valueOf(java.lang.String)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode[] values()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getProfileStore()
io.flutter.embedding.android.FlutterView: android.view.accessibility.AccessibilityNodeProvider getAccessibilityNodeProvider()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Map getUserAgentMetadataMap()
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.os.ParcelFileDescriptor openFile(android.content.ContentProvider,android.net.Uri)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: void onDragEnd(boolean)
androidx.preference.TwoStatePreference: TwoStatePreference(android.content.Context,android.util.AttributeSet)
com.dexterous.flutterlocalnotifications.utils.BooleanUtils: BooleanUtils()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object registerSource$suspendImpl(androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon,android.net.Uri,android.view.InputEvent,kotlin.coroutines.Continuation)
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertWebMessagePort(java.lang.reflect.InvocationHandler)
androidx.biometric.KeyguardUtils$Api23Impl: android.app.KeyguardManager getKeyguardManager(android.content.Context)
io.flutter.plugins.localauth.Messages$AuthClassification: io.flutter.plugins.localauth.Messages$AuthClassification valueOf(java.lang.String)
androidx.camera.camera2.internal.compat.quirk.FlashTooSlowQuirk: FlashTooSlowQuirk()
org.chromium.support_lib_boundary.WebViewRendererBoundaryInterface: boolean terminate()
com.google.android.datatransport.cct.internal.NetworkConnectionInfo$MobileSubtype: com.google.android.datatransport.cct.internal.NetworkConnectionInfo$MobileSubtype valueOf(java.lang.String)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature valueOf(java.lang.String)
androidx.camera.camera2.internal.compat.quirk.ImageCaptureWashedOutImageQuirk: ImageCaptureWashedOutImageQuirk()
androidx.appcompat.widget.AppCompatButton: void setBackgroundResource(int)
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: java.lang.Class access$100(com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory)
androidx.preference.internal.PreferenceImageView: int getMaxHeight()
androidx.media3.exoplayer.audio.MediaCodecAudioRenderer$Api23: void setAudioSinkPreferredDevice(androidx.media3.exoplayer.audio.AudioSink,java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPointerDataPacket(long,java.nio.ByteBuffer,int)
androidx.appcompat.widget.AppCompatSpinner: android.graphics.drawable.Drawable getPopupBackground()
androidx.camera.core.impl.UseCaseConfigFactory$CaptureType: androidx.camera.core.impl.UseCaseConfigFactory$CaptureType[] values()
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
com.google.firebase.encoders.proto.Protobuf$IntEncoding: com.google.firebase.encoders.proto.Protobuf$IntEncoding valueOf(java.lang.String)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Drawable loadDrawable(android.graphics.drawable.Icon,android.content.Context)
androidx.camera.lifecycle.LifecycleCameraRepository$LifecycleCameraRepositoryObserver: void onDestroy(androidx.lifecycle.LifecycleOwner)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setProgress(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void setWebMessageCallback(java.lang.reflect.InvocationHandler,android.os.Handler)
com.google.firebase.ktx.FirebaseCommonKtxRegistrar: java.util.List getComponents()
com.google.firebase.sessions.SessionLifecycleService: SessionLifecycleService()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void postMessageToMainFrame(java.lang.reflect.InvocationHandler,android.net.Uri)
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setAttributionBehavior(int)
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.loader.app.LoaderManagerImpl$LoaderViewModel: LoaderManagerImpl$LoaderViewModel()
com.google.crypto.tink.shaded.protobuf.FieldType$Collection: com.google.crypto.tink.shaded.protobuf.FieldType$Collection[] values()
io.flutter.plugins.camerax.AspectRatioStrategyFallbackRule: io.flutter.plugins.camerax.AspectRatioStrategyFallbackRule[] values()
androidx.sqlite.db.framework.FrameworkSQLiteOpenHelper$OpenHelper$CallbackName: androidx.sqlite.db.framework.FrameworkSQLiteOpenHelper$OpenHelper$CallbackName[] values()
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: android.webkit.WebChromeClient getWebChromeClient()
io.flutter.embedding.engine.FlutterJNI: void setRefreshRateFPS(float)
androidx.work.ArrayCreatingInputMerger: ArrayCreatingInputMerger()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: NotificationDetails()
com.tekartik.sqflite.SqflitePlugin: SqflitePlugin()
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Builder setCustomHeadsUpContentView(android.app.Notification$Builder,android.widget.RemoteViews)
androidx.biometric.PackageUtils$Api23Impl: boolean hasSystemFeatureFingerprint(android.content.pm.PackageManager)
androidx.biometric.BiometricFragment$Api28Impl: android.hardware.biometrics.BiometricPrompt$Builder createPromptBuilder(android.content.Context)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityDestroyedByScionActivityInfo(com.google.android.gms.internal.measurement.zzdf,long)
androidx.recyclerview.widget.RecyclerView: int getScrollState()
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: com.google.gson.Gson buildGson()
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.CharSequence castToCharSequence(android.text.PrecomputedText)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.String[] getSupportedFeatures()
androidx.core.view.ViewCompat$Api28Impl: void removeOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides[] values()
androidx.camera.camera2.internal.compat.quirk.CaptureNoResponseQuirk: CaptureNoResponseQuirk()
io.flutter.embedding.engine.FlutterJNI: void setPlatformMessageHandler(io.flutter.embedding.engine.dart.PlatformMessageHandler)
com.dexterous.flutterlocalnotifications.models.BitmapSource: BitmapSource(java.lang.String,int)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType[] values()
com.google.android.gms.common.internal.IAccountAccessor$Stub: com.google.android.gms.common.internal.IAccountAccessor asInterface(android.os.IBinder)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readStyleInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource valueOf(java.lang.String)
androidx.fragment.app.DefaultSpecialEffectsController$Api26Impl: void setCurrentPlayTime(android.animation.AnimatorSet,long)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler[] createWebMessageChannel()
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeatures(int)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder createBuilder(android.content.Context,java.lang.String)
androidx.camera.lifecycle.LifecycleCamera: void onStart(androidx.lifecycle.LifecycleOwner)
com.google.android.gms.common.api.GoogleApiActivity: GoogleApiActivity()
android.support.v4.os.IResultReceiver$Stub: android.support.v4.os.IResultReceiver asInterface(android.os.IBinder)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void repeat(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
androidx.media3.exoplayer.video.MediaCodecVideoRenderer$Api26: boolean doesDisplaySupportDolbyVision(android.content.Context)
androidx.core.app.NotificationManagerCompat$Api26Impl: void createNotificationChannelGroup(android.app.NotificationManager,android.app.NotificationChannelGroup)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingLeft()
androidx.appcompat.widget.SearchView: int getSuggestionRowLayout()
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void resetAnalyticsData(long)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertSafeBrowsingResponse(java.lang.reflect.InvocationHandler)
androidx.core.view.ViewParentCompat$Api21Impl: void onStopNestedScroll(android.view.ViewParent,android.view.View)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsetsAnimation$Callback getAnimationCallback()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.work.impl.utils.NetworkApi21: android.net.NetworkCapabilities getNetworkCapabilitiesCompat(android.net.ConnectivityManager,android.net.Network)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
com.google.common.collect.Ordering: Ordering()
androidx.camera.core.CameraState$Type: androidx.camera.core.CameraState$Type valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatSpinner: void setPrompt(java.lang.CharSequence)
com.google.common.collect.Iterators$EmptyModifiableIterator: com.google.common.collect.Iterators$EmptyModifiableIterator[] values()
io.flutter.plugins.GeneratedPluginRegistrant: GeneratedPluginRegistrant()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object registerWebSource(androidx.privacysandbox.ads.adservices.measurement.WebSourceRegistrationRequest,kotlin.coroutines.Continuation)
com.google.firebase.datatransport.TransportRegistrar: com.google.android.datatransport.TransportFactory lambda$getComponents$0(com.google.firebase.components.ComponentContainer)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix getFinalMatrix()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void deleteNotificationChannelGroup(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection valueOf(java.lang.String)
com.google.crypto.tink.proto.KeyStatusType: com.google.crypto.tink.proto.KeyStatusType[] values()
androidx.appcompat.widget.AppCompatEditText: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.activity.OnBackPressedDispatcher$Api33Impl: android.window.OnBackInvokedCallback createOnBackInvokedCallback(kotlin.jvm.functions.Function0)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setSettingsText(android.app.Notification$Builder,java.lang.CharSequence)
androidx.biometric.AuthenticationCallbackProvider$Api30Impl: int getAuthenticationType(android.hardware.biometrics.BiometricPrompt$AuthenticationResult)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Icon toIcon(androidx.core.graphics.drawable.IconCompat,android.content.Context)
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
androidx.camera.core.impl.MetadataHolderService: MetadataHolderService()
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: int getType()
androidx.appcompat.widget.AppCompatSpinner: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.preference.Preference: Preference(android.content.Context,android.util.AttributeSet)
com.dexterous.flutterlocalnotifications.models.Time: com.dexterous.flutterlocalnotifications.models.Time from(java.util.Map)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void scheduleNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.lang.Boolean)
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getType(java.lang.Object)
io.flutter.plugins.inapppurchase.Messages$PlatformBillingClientFeature: io.flutter.plugins.inapppurchase.Messages$PlatformBillingClientFeature valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readLargeIconInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
androidx.core.app.NotificationManagerCompat$Api24Impl: boolean areNotificationsEnabled(android.app.NotificationManager)
androidx.appcompat.widget.AppCompatRadioButton: void setButtonDrawable(int)
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType[] values()
androidx.appcompat.widget.AppCompatButton: int getAutoSizeMinTextSize()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setDisabledActionModeMenuItems(int)
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified access$getTransportFactory$cp()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.util.Map describeIcon(androidx.core.graphics.drawable.IconCompat)
androidx.media3.datasource.FileDataSource$Api21: boolean isPermissionError(java.lang.Throwable)
androidx.core.view.ViewCompat$Api26Impl: void addKeyboardNavigationClusters(android.view.View,java.util.Collection,int)
io.flutter.plugins.googlesignin.Messages$SignInType: io.flutter.plugins.googlesignin.Messages$SignInType valueOf(java.lang.String)
com.google.firebase.messaging.FirebaseMessagingKtxRegistrar: FirebaseMessagingKtxRegistrar()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void getActiveNotificationMessagingStyle(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
com.android.billingclient.api.ProxyBillingActivityV2: ProxyBillingActivityV2()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$102(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
androidx.core.app.NotificationCompat$Builder$Api21Impl: android.media.AudioAttributes$Builder createBuilder()
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setSound(android.app.Notification$Builder,android.net.Uri,java.lang.Object)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
io.flutter.embedding.engine.FlutterJNI: void updateRefreshRate()
com.google.firebase.FirebaseCommonRegistrar: FirebaseCommonRegistrar()
androidx.appcompat.widget.AppCompatRadioButton: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: void registerPlugin(java.lang.String,io.flutter.plugins.firebase.core.FlutterFirebasePlugin)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
androidx.core.graphics.drawable.IconCompat: IconCompat()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo,long)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void release()
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat[] values()
androidx.biometric.CryptoObjectUtils$Api28Impl: android.hardware.biometrics.BiometricPrompt$CryptoObject create(javax.crypto.Mac)
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
com.google.android.gms.measurement.internal.zzjh: com.google.android.gms.measurement.internal.zzjh[] values()
androidx.biometric.BiometricFragment: BiometricFragment()
io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat: io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityPaused(com.google.android.gms.dynamic.IObjectWrapper,long)
androidx.core.app.NotificationCompat$Builder$Api21Impl: android.media.AudioAttributes build(android.media.AudioAttributes$Builder)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsRegionalIndicator(int)
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: boolean deleteProfile(java.lang.String)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readMessagingStyleInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map,com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation)
io.flutter.plugins.camerax.LiveDataSupportedType: io.flutter.plugins.camerax.LiveDataSupportedType valueOf(java.lang.String)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
androidx.fragment.app.FragmentContainerView: void setDrawDisappearingViewsLast(boolean)
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode valueOf(java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: android.content.res.ColorStateList getIconTintList(android.view.MenuItem)
androidx.biometric.BiometricManager$Api30Impl: int canAuthenticate(android.hardware.biometrics.BiometricManager,int)
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
androidx.camera.camera2.internal.compat.quirk.ImageCaptureFailedWhenVideoCaptureIsBoundQuirk: ImageCaptureFailedWhenVideoCaptureIsBoundQuirk()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: double deltaMillis(long)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object getMeasurementApiStatus(kotlin.coroutines.Continuation)
io.flutter.view.TextureRegistry$SurfaceProducer: long id()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setSound(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.camera.video.VideoEncoderSession$VideoEncoderState: androidx.camera.video.VideoEncoderSession$VideoEncoderState valueOf(java.lang.String)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: android.graphics.SurfaceTexture surfaceTexture()
androidx.media3.exoplayer.drm.DrmUtil$Api21: int mediaDrmStateExceptionToErrorCode(java.lang.Throwable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims()
io.flutter.plugins.localauth.LocalAuthPlugin: LocalAuthPlugin()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
com.google.crypto.tink.shaded.protobuf.WireFormat$FieldType: com.google.crypto.tink.shaded.protobuf.WireFormat$FieldType valueOf(java.lang.String)
androidx.biometric.CryptoObjectUtils$Api23Impl: android.security.keystore.KeyGenParameterSpec buildKeyGenParameterSpec(android.security.keystore.KeyGenParameterSpec$Builder)
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar: AnalyticsConnectorRegistrar()
androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver: ConstraintProxyUpdateReceiver()
io.flutter.view.AccessibilityViewEmbedder: boolean requestSendAccessibilityEvent(android.view.View,android.view.View,android.view.accessibility.AccessibilityEvent)
com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver: ScheduledNotificationReceiver()
androidx.core.view.ViewCompat$Api26Impl: android.view.View keyboardNavigationClusterSearch(android.view.View,android.view.View,int)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.String permissionToOp(java.lang.String)
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.core.app.Person$Api28Impl: android.app.Person toAndroidPerson(androidx.core.app.Person)
androidx.fragment.app.DialogFragment: DialogFragment()
androidx.camera.core.ImageProcessingUtil: int nativeCopyBetweenByteBufferAndBitmap(android.graphics.Bitmap,java.nio.ByteBuffer,int,int,int,int,boolean)
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
androidx.recyclerview.widget.RecyclerView: void setScrollState(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onEnd(android.view.WindowInsetsAnimation)
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void endAdUnitExposure(java.lang.String,long)
androidx.core.location.LocationCompat$Api26Impl: void removeSpeedAccuracy(android.location.Location)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: void lambda$didReinitializeFirebaseCore$1(com.google.android.gms.tasks.TaskCompletionSource)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Builder setCustomBigContentView(android.app.Notification$Builder,android.widget.RemoteViews)
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessageResponse(int,java.nio.ByteBuffer)
androidx.camera.core.internal.utils.ImageUtil$CodecFailedException$FailureType: androidx.camera.core.internal.utils.ImageUtil$CodecFailedException$FailureType valueOf(java.lang.String)
androidx.work.WorkManager$UpdateResult: androidx.work.WorkManager$UpdateResult valueOf(java.lang.String)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
com.google.firebase.analytics.FirebaseAnalytics$ConsentType: com.google.firebase.analytics.FirebaseAnalytics$ConsentType[] values()
androidx.core.content.ContextCompat$Api33Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$RecycledViewPool getRecycledViewPool()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.core.widget.TextViewCompat$Api24Impl: android.icu.text.DecimalFormatSymbols getInstance(java.util.Locale)
androidx.core.os.BundleCompat$Api33Impl: android.util.SparseArray getSparseParcelableArray(android.os.Bundle,java.lang.String,java.lang.Class)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface()
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setAlphabeticShortcut(android.view.MenuItem,char,int)
io.flutter.embedding.engine.FlutterJNI: void ensureNotAttachedToNative()
com.dexterous.flutterlocalnotifications.models.NotificationAction: java.util.List castList(java.lang.Class,java.util.Collection)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String getNextFireDateMatchingDateTimeComponents(com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void cancelNotification(java.lang.Integer,java.lang.String)
androidx.appcompat.widget.SwitchCompat: int getThumbScrollRange()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader()
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setColor(android.app.Notification$Builder,int)
androidx.appcompat.widget.AppCompatSpinner: void setAdapter(android.widget.Adapter)
io.flutter.plugins.localauth.Messages$AuthResult: io.flutter.plugins.localauth.Messages$AuthResult[] values()
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI spawn(java.lang.String,java.lang.String,java.lang.String,java.util.List,long)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$100(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.recyclerview.widget.RecyclerView: boolean getPreserveFocusAfterLayout()
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar: java.util.List getComponents()
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
com.google.android.gms.measurement.AppMeasurement: java.lang.String getAppInstanceId()
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
io.flutter.embedding.android.FlutterImageView: android.view.Surface getSurface()
com.google.android.gms.common.ErrorDialogFragment: ErrorDialogFragment()
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.MainCoroutineDispatcher createDispatcher(java.util.List)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: void setClearCachedDataIntervalMs(int)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateJavaAssetManager(long,android.content.res.AssetManager,java.lang.String)
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface: int getErrorCode()
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
com.google.android.gms.common.internal.AccountAccessor: android.accounts.Account getAccountBinderSafe(com.google.android.gms.common.internal.IAccountAccessor)
io.flutter.plugins.inapppurchase.Messages$PlatformReplacementMode: io.flutter.plugins.inapppurchase.Messages$PlatformReplacementMode valueOf(java.lang.String)
androidx.camera.core.processing.util.GLUtils$InputFormat: androidx.camera.core.processing.util.GLUtils$InputFormat[] values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.google.android.gms.common.api.internal.zza: zza()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: boolean isHorizontallyScrollable(android.widget.TextView)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void markDirty()
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setSpeculativeLoadingStatus(int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean shouldUpdate()
com.islamicapps.athkar.athkar_app.MainActivity: MainActivity()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
androidx.security.crypto.EncryptedSharedPreferences$PrefKeyEncryptionScheme: androidx.security.crypto.EncryptedSharedPreferences$PrefKeyEncryptionScheme[] values()
androidx.biometric.BiometricFragment$Api29Impl: void setConfirmationRequired(android.hardware.biometrics.BiometricPrompt$Builder,boolean)
io.flutter.embedding.engine.FlutterJNI: void prefetchDefaultFontManager()
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar: com.google.firebase.analytics.connector.AnalyticsConnector lambda$getComponents$0(com.google.firebase.components.ComponentContainer)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
com.google.firebase.messaging.FirebaseMessagingRegistrar: com.google.firebase.messaging.FirebaseMessaging lambda$getComponents$0(com.google.firebase.components.Qualified,com.google.firebase.components.ComponentContainer)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
com.google.android.datatransport.cct.internal.NetworkConnectionInfo$NetworkType: com.google.android.datatransport.cct.internal.NetworkConnectionInfo$NetworkType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void swapTransactions()
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory of(java.lang.Class)
com.google.firebase.analytics.FirebaseAnalytics: java.lang.String getFirebaseInstanceId()
androidx.appcompat.widget.SwitchCompat: int getSwitchMinWidth()
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.camera.core.internal.compat.quirk.LowMemoryQuirk: LowMemoryQuirk()
androidx.camera.camera2.internal.compat.quirk.PreviewStretchWhenVideoCaptureIsBoundQuirk: PreviewStretchWhenVideoCaptureIsBoundQuirk()
com.google.android.gms.auth.account.zzd: com.google.android.gms.auth.account.zze zzb(android.os.IBinder)
androidx.appcompat.widget.AppCompatButton: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.PlatformView getView()
com.google.common.collect.AbstractIterator$State: com.google.common.collect.AbstractIterator$State valueOf(java.lang.String)
com.google.android.gms.dynamite.descriptors.com.google.android.gms.measurement.dynamite.ModuleDescriptor: ModuleDescriptor()
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.utils.StringUtils: StringUtils()
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setRequestedWithHeaderOriginAllowList(java.util.Set)
io.flutter.plugins.inapppurchase.InAppPurchasePlugin: InAppPurchasePlugin()
com.google.firebase.installations.FirebaseInstallationsException$Status: com.google.firebase.installations.FirebaseInstallationsException$Status valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
androidx.camera.core.internal.utils.ImageUtil$CodecFailedException$FailureType: androidx.camera.core.internal.utils.ImageUtil$CodecFailedException$FailureType[] values()
androidx.core.app.ActivityCompat$Api23Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
io.flutter.plugins.localauth.Messages$AuthResult: io.flutter.plugins.localauth.Messages$AuthResult valueOf(java.lang.String)
androidx.recyclerview.widget.LinearLayoutManager: LinearLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
com.google.android.gms.internal.measurement.zzot: com.google.android.gms.internal.measurement.zzot[] values()
io.flutter.plugins.firebase.analytics.FlutterFirebaseAnalyticsPlugin: FlutterFirebaseAnalyticsPlugin()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: int getDrawableResourceId(android.content.Context,java.lang.String)
androidx.recyclerview.widget.RecyclerView: void setAccessibilityDelegateCompat(androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate)
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerSourceAsync(androidx.privacysandbox.ads.adservices.measurement.SourceRegistrationRequest)
io.flutter.plugin.platform.PlatformViewWrapper: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.camera.camera2.internal.compat.quirk.AfRegionFlipHorizontallyQuirk: AfRegionFlipHorizontallyQuirk()
androidx.appcompat.widget.SearchView: void setOnCloseListener(androidx.appcompat.widget.SearchView$OnCloseListener)
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
androidx.preference.internal.PreferenceImageView: PreferenceImageView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRect(int,int,int,int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: long calculateNextNotificationTrigger(long,long)
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
androidx.biometric.CryptoObjectUtils$Api30Impl: android.hardware.biometrics.BiometricPrompt$CryptoObject create(android.security.identity.IdentityCredential)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceWindowChanged(long,android.view.Surface)
androidx.core.app.NotificationCompatBuilder$Api23Impl: android.app.Notification$Builder setLargeIcon(android.app.Notification$Builder,android.graphics.drawable.Icon)
androidx.work.NetworkType: androidx.work.NetworkType[] values()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$MethodToInvoke: com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
io.flutter.plugins.inapppurchase.Messages$PlatformPurchaseState: io.flutter.plugins.inapppurchase.Messages$PlatformPurchaseState[] values()
com.google.firebase.crashlytics.internal.common.CommonUtils$Architecture: com.google.firebase.crashlytics.internal.common.CommonUtils$Architecture[] values()
dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin: PackageInfoPlugin()
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportBackgroundTintList()
org.chromium.support_lib_boundary.IsomorphicObjectBoundaryInterface: java.lang.Object getOrCreatePeer(java.util.concurrent.Callable)
androidx.camera.core.impl.CameraCaptureFailure$Reason: androidx.camera.core.impl.CameraCaptureFailure$Reason valueOf(java.lang.String)
androidx.media.AudioAttributesCompatParcelizer: androidx.media.AudioAttributesCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.AppCompatEditText: android.text.Editable getText()
androidx.core.view.ViewConfigurationCompat$Api28Impl: boolean shouldShowMenuShortcutsWhenKeyboardPresent(android.view.ViewConfiguration)
io.flutter.embedding.engine.FlutterJNI: void nativePrefetchDefaultFontManager()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setBackForwardCacheEnabled(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void removeWebMessageListener(java.lang.String)
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact valueOf(java.lang.String)
androidx.camera.camera2.internal.compat.quirk.ImageCapturePixelHDRPlusQuirk: ImageCapturePixelHDRPlusQuirk()
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.core.app.NotificationCompat$BigPictureStyle$Api31Impl: void setBigPicture(android.app.Notification$BigPictureStyle,android.graphics.drawable.Icon)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder setSortKey(android.app.Notification$Builder,java.lang.String)
com.google.firebase.datatransport.TransportRegistrar: com.google.android.datatransport.TransportFactory lambda$getComponents$2(com.google.firebase.components.ComponentContainer)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebMessagePort(java.lang.Object)
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
androidx.core.app.NotificationCompat$MessagingStyle$Message$Api28Impl: android.app.Notification$MessagingStyle$Message createMessage(java.lang.CharSequence,long,android.app.Person)
androidx.security.crypto.MasterKey$Builder$Api23Impl$Api30Impl: void setUserAuthenticationParameters(android.security.keystore.KeyGenParameterSpec$Builder,int,int)
com.google.crypto.tink.shaded.protobuf.WireFormat$JavaType: com.google.crypto.tink.shaded.protobuf.WireFormat$JavaType valueOf(java.lang.String)
androidx.core.app.NotificationManagerCompat$Api26Impl: void deleteNotificationChannel(android.app.NotificationManager,java.lang.String)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatSpinner: void setAdapter(android.widget.SpinnerAdapter)
androidx.core.content.FileProvider$Api21Impl: java.io.File[] getExternalMediaDirs(android.content.Context)
androidx.security.crypto.MasterKey$Builder$Api23Impl: java.lang.String getKeystoreAlias(android.security.keystore.KeyGenParameterSpec)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setBlockNetworkLoads(boolean)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
com.google.android.datatransport.cct.internal.NetworkConnectionInfo$MobileSubtype: com.google.android.datatransport.cct.internal.NetworkConnectionInfo$MobileSubtype[] values()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.embedding.engine.FlutterJNI: void setDeferredComponentManager(io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager)
com.google.android.datatransport.cct.CctBackendFactory: com.google.android.datatransport.runtime.backends.TransportBackend create(com.google.android.datatransport.runtime.backends.CreationContext)
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void isDataCollectionEnabled(com.google.android.gms.internal.measurement.zzcu)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.android.FlutterImageView getCurrentImageSurface()
androidx.core.location.LocationCompat$Api26Impl: boolean hasSpeedAccuracy(android.location.Location)
android.support.v4.media.AudioAttributesImplApi21Parcelizer: AudioAttributesImplApi21Parcelizer()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnFrameConsumedListener(io.flutter.view.TextureRegistry$OnFrameConsumedListener)
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection valueOf(java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityDestroyed(com.google.android.gms.dynamic.IObjectWrapper,long)
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory of(java.lang.Class,java.lang.String)
androidx.biometric.FingerprintDialogFragment: FingerprintDialogFragment()
androidx.core.view.DisplayCutoutCompat$Api28Impl: java.util.List getBoundingRects(android.view.DisplayCutout)
androidx.camera.video.internal.compat.quirk.PrematureEndOfStreamVideoQuirk: PrematureEndOfStreamVideoQuirk()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getChild(android.view.accessibility.AccessibilityNodeInfo,int,int)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object getMeasurementApiStatus$suspendImpl(androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon,kotlin.coroutines.Continuation)
com.google.crypto.tink.shaded.protobuf.ProtoSyntax: com.google.crypto.tink.shaded.protobuf.ProtoSyntax valueOf(java.lang.String)
com.google.android.gms.internal.play_billing.zzde: zzde()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: AppCompatTextViewAutoSizeHelper$Impl()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWillSuppressErrorPage(boolean)
io.flutter.plugins.camerax.CameraXFlashMode: io.flutter.plugins.camerax.CameraXFlashMode[] values()
com.google.android.gms.internal.play_billing.zzkn: com.google.android.gms.internal.play_billing.zzkn[] values()
androidx.core.os.BundleCompat$Api33Impl: java.lang.Object[] getParcelableArray(android.os.Bundle,java.lang.String,java.lang.Class)
androidx.work.CoroutineWorker: CoroutineWorker(android.content.Context,androidx.work.WorkerParameters)
io.flutter.view.TextureRegistry$ImageTextureEntry: void release()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getResId(java.lang.Object)
io.flutter.plugins.GeneratedPluginRegistrant: void registerWith(io.flutter.embedding.engine.FlutterEngine)
androidx.appcompat.widget.AppCompatEditText: java.lang.CharSequence getText()
androidx.recyclerview.widget.RecyclerView: void setScrollingTouchSlop(int)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setConditionalUserProperty(android.os.Bundle,long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$300(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintMode(android.widget.TextView,android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatEditText: android.view.textclassifier.TextClassifier getTextClassifier()
com.google.common.base.AbstractIterator$State: com.google.common.base.AbstractIterator$State[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageEmptyResponseCallback(long,int)
dev.fluttercommunity.workmanager.WorkmanagerPlugin: WorkmanagerPlugin()
com.google.firebase.messaging.reporting.MessagingClientEvent$SDKPlatform: com.google.firebase.messaging.reporting.MessagingClientEvent$SDKPlatform valueOf(java.lang.String)
androidx.fragment.app.DefaultSpecialEffectsController$Api26Impl: void reverse(android.animation.AnimatorSet)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readBigTextStyleInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map,com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation)
androidx.core.view.ViewCompat$Api26Impl: boolean restoreDefaultFocus(android.view.View)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspot(android.graphics.drawable.Drawable,float,float)
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: void close(java.io.FileDescriptor)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getWillSuppressErrorPage()
androidx.room.RoomDatabase$JournalMode: androidx.room.RoomDatabase$JournalMode valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setSafeBrowsingAllowlist(java.util.Set,android.webkit.ValueCallback)
androidx.core.app.NotificationCompat$CallStyle$Api21Impl: android.app.Notification$Builder addPerson(android.app.Notification$Builder,java.lang.String)
com.baseflow.geolocator.errors.ErrorCodes: com.baseflow.geolocator.errors.ErrorCodes[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setUserProperty(java.lang.String,java.lang.String,com.google.android.gms.dynamic.IObjectWrapper,boolean,long)
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
androidx.work.impl.utils.NetworkApi24: void registerDefaultNetworkCallbackCompat(android.net.ConnectivityManager,android.net.ConnectivityManager$NetworkCallback)
com.dexterous.flutterlocalnotifications.models.NotificationStyle: NotificationStyle(java.lang.String,int)
androidx.fragment.app.FragmentContainerView: void setOnApplyWindowInsetsListener(android.view.View$OnApplyWindowInsetsListener)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
io.flutter.plugins.inapppurchase.Messages$PlatformProductType: io.flutter.plugins.inapppurchase.Messages$PlatformProductType valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource[] values()
androidx.camera.camera2.internal.Camera2CameraImpl$InternalState: androidx.camera.camera2.internal.Camera2CameraImpl$InternalState valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setSmallIcon(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.camera.video.internal.audio.AudioSource$InternalState: androidx.camera.video.internal.audio.AudioSource$InternalState[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateDisplayMetrics(long)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setMeasurementEnabled(boolean,long)
androidx.camera.video.internal.BufferProvider$State: androidx.camera.video.internal.BufferProvider$State[] values()
androidx.biometric.BiometricViewModel: BiometricViewModel()
androidx.core.view.ViewCompat$Api21Impl: boolean isNestedScrollingEnabled(android.view.View)
androidx.biometric.CryptoObjectUtils$Api30Impl: android.security.identity.IdentityCredential getIdentityCredential(android.hardware.biometrics.BiometricPrompt$CryptoObject)
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
com.baseflow.geolocator.location.ServiceStatus: com.baseflow.geolocator.location.ServiceStatus valueOf(java.lang.String)
androidx.media3.exoplayer.drm.DrmUtil$Api21: boolean isMediaDrmStateException(java.lang.Throwable)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: java.lang.String getName()
androidx.core.view.ViewCompat$Api26Impl: void setKeyboardNavigationCluster(android.view.View,boolean)
android.support.v4.media.AudioAttributesCompatParcelizer: AudioAttributesCompatParcelizer()
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
com.google.android.gms.measurement.internal.zzlq: com.google.android.gms.measurement.internal.zzlq[] values()
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readProgressInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void clearProxyOverride(java.lang.Runnable,java.util.concurrent.Executor)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType valueOf(java.lang.String)
androidx.biometric.FingerprintDialogFragment$Api21Impl: void startAnimation(android.graphics.drawable.Drawable)
androidx.security.crypto.MasterKey$Builder$Api23Impl$Api28Impl: void setIsStrongBoxBacked(android.security.keystore.KeyGenParameterSpec$Builder)
androidx.work.impl.utils.futures.DirectExecutor: androidx.work.impl.utils.futures.DirectExecutor[] values()
com.google.firebase.crashlytics.internal.settings.SettingsCacheBehavior: com.google.firebase.crashlytics.internal.settings.SettingsCacheBehavior[] values()
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.app.NotificationCompat$BubbleMetadata$Api29Impl: android.app.Notification$BubbleMetadata toPlatform(androidx.core.app.NotificationCompat$BubbleMetadata)
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.work.BackoffPolicy: androidx.work.BackoffPolicy[] values()
androidx.core.app.NotificationCompat$CallStyle$Api28Impl: android.os.Parcelable castToParcelable(android.app.Person)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getForceDarkBehavior()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTint(android.graphics.drawable.Drawable,int)
com.google.firebase.messaging.ktx.FirebaseMessagingKtxRegistrar: FirebaseMessagingKtxRegistrar()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: FlutterLocalNotificationsPlugin()
androidx.browser.customtabs.CustomTabsIntent$Api34Impl: void setShareIdentityEnabled(android.app.ActivityOptions,boolean)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory getEdgeEffectFactory()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
org.chromium.support_lib_boundary.ServiceWorkerClientBoundaryInterface: android.webkit.WebResourceResponse shouldInterceptRequest(android.webkit.WebResourceRequest)
androidx.core.widget.TextViewCompat$Api23Impl: void setBreakStrategy(android.widget.TextView,int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.accessibility.AccessibilityNodeInfo)
com.google.android.datatransport.cct.internal.QosTier: com.google.android.datatransport.cct.internal.QosTier[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: FlutterRenderer$ImageTextureRegistryEntry(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface2()
androidx.core.app.AlarmManagerCompat$Api21Impl: void setAlarmClock(android.app.AlarmManager,java.lang.Object,android.app.PendingIntent)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedFling(android.view.ViewParent,android.view.View,float,float,boolean)
com.dexterous.flutterlocalnotifications.utils.StringUtils: java.lang.Boolean isNullOrEmpty(java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getCurrentScreenClass(com.google.android.gms.internal.measurement.zzcu)
androidx.work.WorkInfo$State: androidx.work.WorkInfo$State[] values()
androidx.work.impl.WorkManagerImpl$Api24Impl: boolean isDeviceProtectedStorage(android.content.Context)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getTooltipText(android.view.MenuItem)
kotlinx.coroutines.android.AndroidDispatcherFactory: java.lang.String hintOnError()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation: LegacySavedStateHandleController$OnRecreation()
com.google.android.gms.internal.play_billing.zzau: com.google.android.gms.internal.play_billing.zzav zzc(android.os.IBinder)
androidx.recyclerview.widget.RecyclerView: void setOnFlingListener(androidx.recyclerview.widget.RecyclerView$OnFlingListener)
androidx.media3.exoplayer.audio.DefaultAudioSink$Api23: void setPreferredDeviceOnAudioTrack(android.media.AudioTrack,androidx.media3.exoplayer.audio.AudioDeviceInfoApi23)
androidx.camera.camera2.internal.RequestForwardingCaptureCallback: void onReadoutStarted(android.hardware.camera2.CameraCaptureSession,android.hardware.camera2.CaptureRequest,long,long)
io.flutter.view.TextureRegistry$GLTextureConsumer: android.graphics.SurfaceTexture getSurfaceTexture()
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
androidx.core.app.ActivityCompat$Api23Impl: void onSharedElementsReady(java.lang.Object)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.app.PendingIntent getBroadcastPendingIntent(android.content.Context,int,android.content.Intent)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifierBase(int)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void clearConditionalUserProperty(java.lang.String,java.lang.String,android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: void onPreEngineRestart()
io.flutter.embedding.engine.FlutterJNI: void nativeOnVsync(long,long,long)
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
androidx.camera.video.internal.compat.quirk.CameraUseInconsistentTimebaseQuirk: CameraUseInconsistentTimebaseQuirk()
androidx.preference.EditTextPreference: EditTextPreference(android.content.Context,android.util.AttributeSet)
androidx.media.AudioAttributesImplApi21Parcelizer: androidx.media.AudioAttributesImplApi21 read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.SearchView: void setMaxWidth(int)
com.google.firebase.sessions.EventType: com.google.firebase.sessions.EventType valueOf(java.lang.String)
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$800(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmoji(int)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabledInNative(boolean)
androidx.room.MultiInstanceInvalidationService: MultiInstanceInvalidationService()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setAlgorithmicDarkeningAllowed(boolean)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void prefetchUrl(java.lang.String,android.webkit.ValueCallback)
androidx.biometric.BiometricFragment$Api28Impl: void authenticate(android.hardware.biometrics.BiometricPrompt,android.hardware.biometrics.BiometricPrompt$CryptoObject,android.os.CancellationSignal,java.util.concurrent.Executor,android.hardware.biometrics.BiometricPrompt$AuthenticationCallback)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setQueryFromAppProcessEnabled(android.view.accessibility.AccessibilityNodeInfo,android.view.View,boolean)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setProfile(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeUnregisterTexture(long,long)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getAttributionBehavior()
androidx.core.view.ViewCompat$Api21Impl: float getElevation(android.view.View)
androidx.core.location.LocationCompat$Api26Impl: void setVerticalAccuracyMeters(android.location.Location,float)
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
androidx.camera.video.internal.compat.quirk.StopCodecAfterSurfaceRemovalCrashMediaServerQuirk: StopCodecAfterSurfaceRemovalCrashMediaServerQuirk()
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType[] values()
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: ScheduledNotificationRepeatFrequency(java.lang.String,int)
androidx.camera.camera2.internal.compat.quirk.ExtraSupportedSurfaceCombinationsQuirk: ExtraSupportedSurfaceCombinationsQuirk()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setAudioMuted(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageEmptyResponseCallback(int)
io.flutter.plugins.webviewflutter.ConsoleMessageLevel: io.flutter.plugins.webviewflutter.ConsoleMessageLevel[] values()
androidx.appcompat.widget.AppCompatSpinner: void setPopupBackgroundResource(int)
androidx.recyclerview.widget.RecyclerView: void setClipToPadding(boolean)
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
androidx.core.location.LocationCompat$Api26Impl: void setBearingAccuracyDegrees(android.location.Location,float)
io.flutter.embedding.engine.FlutterJNI: void nativeSetAccessibilityFeatures(long,int)
io.flutter.embedding.engine.FlutterJNI: void requestDartDeferredLibrary(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
androidx.camera.video.internal.compat.quirk.NegativeLatLongSavesIncorrectlyQuirk: NegativeLatLongSavesIncorrectlyQuirk()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getAllowFileAccess()
io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar: java.util.List getComponents()
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
android.support.v4.media.AudioAttributesImplApi26Parcelizer: AudioAttributesImplApi26Parcelizer()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreScroll(android.view.View,int,int,int[],int[])
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setTooltipText(android.view.MenuItem,java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifier(int)
androidx.preference.internal.PreferenceImageView: void setMaxHeight(int)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreFling(android.view.View,float,float)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader29()
com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar: java.util.List getComponents()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: android.view.WindowInsets onProgress(android.view.WindowInsets,java.util.List)
androidx.preference.SeekBarPreference: SeekBarPreference(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativeLoadDartDeferredLibrary(long,int,java.lang.String[])
io.flutter.embedding.android.FlutterView: void setWindowInfoListenerDisplayFeatures(androidx.window.layout.WindowLayoutInfo)
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintMode(android.widget.ImageView,android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatCheckBox: void setBackgroundResource(int)
kotlin.collections.AbstractMutableSet: AbstractMutableSet()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
io.flutter.plugins.urllauncher.UrlLauncherPlugin: UrlLauncherPlugin()
com.google.android.datatransport.cct.internal.ClientInfo$ClientType: com.google.android.datatransport.cct.internal.ClientInfo$ClientType valueOf(java.lang.String)
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
com.baseflow.geolocator.location.LocationAccuracy: com.baseflow.geolocator.location.LocationAccuracy[] values()
androidx.core.content.ContextCompat$Api23Impl: int getColor(android.content.Context,int)
com.google.android.gms.internal.measurement.zzbp: com.google.android.gms.internal.measurement.zzbq zzb(android.os.IBinder)
androidx.activity.Api34Impl: float touchY(android.window.BackEvent)
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.biometric.BiometricFragment$Api28Impl: void setSubtitle(android.hardware.biometrics.BiometricPrompt$Builder,java.lang.CharSequence)
androidx.camera.camera2.internal.compat.quirk.StillCaptureFlashStopRepeatingQuirk: StillCaptureFlashStopRepeatingQuirk()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onImage(android.media.ImageReader,android.media.Image)
io.flutter.embedding.engine.FlutterJNI: void removeEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.core.view.MenuItemCompat$Api26Impl: android.graphics.PorterDuff$Mode getIconTintMode(android.view.MenuItem)
io.flutter.plugins.camerax.InfoSupportedHardwareLevel: io.flutter.plugins.camerax.InfoSupportedHardwareLevel valueOf(java.lang.String)
io.flutter.embedding.android.FlutterView: void setVisibility(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
androidx.recyclerview.widget.RecyclerView: void setItemViewCacheSize(int)
androidx.camera.core.CameraX$InternalInitState: androidx.camera.core.CameraX$InternalInitState[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void scheduleFrame()
com.android.billingclient.api.ProxyBillingActivity: ProxyBillingActivity()
io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar: FlutterFirebaseAppRegistrar()
com.google.firebase.installations.ktx.FirebaseInstallationsKtxRegistrar: FirebaseInstallationsKtxRegistrar()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
com.google.android.gms.internal.measurement.zzmr: com.google.android.gms.internal.measurement.zzmr[] values()
androidx.core.view.ViewCompat$Api29Impl: java.util.List getSystemGestureExclusionRects(android.view.View)
io.flutter.plugins.imagepicker.ImagePickerCache$CacheType: io.flutter.plugins.imagepicker.ImagePickerCache$CacheType valueOf(java.lang.String)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type[] values()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void applyTheme(android.graphics.drawable.Drawable,android.content.res.Resources$Theme)
io.flutter.plugins.camerax.CameraAndroidCameraxPlugin: CameraAndroidCameraxPlugin()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setSafeBrowsingWhitelist(java.util.List,android.webkit.ValueCallback)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemRowTitle(java.lang.Object)
androidx.biometric.AuthenticationCallbackProvider$Api28Impl$1: void onAuthenticationFailed()
androidx.camera.core.internal.compat.quirk.CaptureFailedRetryQuirk: CaptureFailedRetryQuirk()
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: boolean setLayoutDirection(android.graphics.drawable.Drawable,int)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.biometric.CryptoObjectUtils$Api28Impl: java.security.Signature getSignature(android.hardware.biometrics.BiometricPrompt$CryptoObject)
androidx.core.app.NotificationCompatBuilder$Api23Impl: android.app.Notification$Builder setSmallIcon(android.app.Notification$Builder,java.lang.Object)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void getNotificationAppLaunchDetails(io.flutter.plugin.common.MethodChannel$Result)
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents valueOf(java.lang.String)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetBottom(android.view.DisplayCutout)
androidx.core.location.LocationManagerCompat$Api19Impl: boolean tryRequestLocationUpdates(android.location.LocationManager,java.lang.String,androidx.core.location.LocationRequestCompat,androidx.core.location.LocationListenerCompat,android.os.Looper)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void setWebMessageCallback(java.lang.reflect.InvocationHandler)
io.flutter.plugins.googlesignin.Messages$SignInType: io.flutter.plugins.googlesignin.Messages$SignInType[] values()
com.google.android.gms.common.api.internal.IStatusCallback$Stub: com.google.android.gms.common.api.internal.IStatusCallback asInterface(android.os.IBinder)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int)
androidx.sqlite.db.framework.FrameworkSQLiteOpenHelper$OpenHelper$CallbackName: androidx.sqlite.db.framework.FrameworkSQLiteOpenHelper$OpenHelper$CallbackName valueOf(java.lang.String)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Action$Builder createBuilder(int,java.lang.CharSequence,android.app.PendingIntent)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
com.google.crypto.tink.config.internal.TinkFipsUtil$AlgorithmFipsCompatibility: com.google.crypto.tink.config.internal.TinkFipsUtil$AlgorithmFipsCompatibility[] values()
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
androidx.appcompat.widget.Toolbar: int getPopupTheme()
androidx.core.view.ViewConfigurationCompat$Api28Impl: int getScaledHoverSlop(android.view.ViewConfiguration)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointVariantSelector(int)
io.flutter.view.AccessibilityViewEmbedder: boolean onAccessibilityHoverEvent(int,android.view.MotionEvent)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertWebResourceError(java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController2(io.flutter.plugin.platform.PlatformViewsController2)
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.graphics.Bitmap getBitmapFromSource(android.content.Context,java.lang.Object,com.dexterous.flutterlocalnotifications.models.BitmapSource)
com.dexterous.flutterlocalnotifications.models.RepeatInterval: RepeatInterval(java.lang.String,int)
io.flutter.embedding.engine.FlutterJNI: boolean isAttached()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: android.view.accessibility.AccessibilityNodeInfo$AccessibilityAction getActionScrollInDirection()
androidx.work.WorkInfo$State: androidx.work.WorkInfo$State valueOf(java.lang.String)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getStatics()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Set getRequestedWithHeaderOriginAllowList()
com.google.firebase.sessions.api.SessionSubscriber$Name: com.google.firebase.sessions.api.SessionSubscriber$Name[] values()
androidx.work.impl.utils.NetworkApi21: void unregisterNetworkCallbackCompat(android.net.ConnectivityManager,android.net.ConnectivityManager$NetworkCallback)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawableForDensity(android.content.res.Resources,int,int,android.content.res.Resources$Theme)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: int getLayoutDirection(android.graphics.drawable.Drawable)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: androidx.core.app.NotificationManagerCompat getNotificationManager(android.content.Context)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
dev.fluttercommunity.workmanager.TaskType: dev.fluttercommunity.workmanager.TaskType valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: void setZ(android.view.View,float)
com.google.firebase.crashlytics.internal.common.DeliveryMechanism: com.google.firebase.crashlytics.internal.common.DeliveryMechanism valueOf(java.lang.String)
io.flutter.plugins.imagepicker.Messages$SourceType: io.flutter.plugins.imagepicker.Messages$SourceType valueOf(java.lang.String)
io.flutter.plugins.camerax.VideoQuality: io.flutter.plugins.camerax.VideoQuality valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void rescheduleNotifications(android.content.Context)
androidx.core.os.BundleCompat$Api33Impl: java.io.Serializable getSerializable(android.os.Bundle,java.lang.String,java.lang.Class)
dev.fluttercommunity.workmanager.Extractor$PossibleWorkManagerCall: dev.fluttercommunity.workmanager.Extractor$PossibleWorkManagerCall valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.fragment.app.strictmode.FragmentStrictMode$Flag: androidx.fragment.app.strictmode.FragmentStrictMode$Flag[] values()
androidx.appcompat.widget.AppCompatEditText: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: boolean isHorizontallyScrollable(android.widget.TextView)
io.flutter.plugins.inapppurchase.Messages$PlatformBillingChoiceMode: io.flutter.plugins.inapppurchase.Messages$PlatformBillingChoiceMode valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatSpinner: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getTrackTintMode()
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.SearchView: void setOnSuggestionListener(androidx.appcompat.widget.SearchView$OnSuggestionListener)
androidx.camera.video.internal.compat.quirk.AudioTimestampFramePositionIncorrectQuirk: AudioTimestampFramePositionIncorrectQuirk()
androidx.biometric.BiometricManager$Api29Impl: android.hardware.biometrics.BiometricManager create(android.content.Context)
androidx.core.app.NotificationCompat$MessagingStyle$Message$Api28Impl: android.os.Parcelable castToParcelable(android.app.Person)
com.google.common.collect.AbstractIterator$State: com.google.common.collect.AbstractIterator$State[] values()
androidx.media3.exoplayer.audio.AudioCapabilities$Api33: androidx.media3.exoplayer.audio.AudioCapabilities getCapabilitiesInternalForDirectPlayback(android.media.AudioManager,androidx.media3.common.AudioAttributes)
androidx.camera.camera2.Camera2Config$DefaultProvider: Camera2Config$DefaultProvider()
io.flutter.embedding.engine.FlutterJNI: void hideOverlaySurface2()
io.flutter.view.FlutterCallbackInformation: FlutterCallbackInformation(java.lang.String,java.lang.String,java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$302(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer,boolean)
com.google.android.gms.common.internal.zzaf: com.google.android.gms.common.internal.zzag zzb(android.os.IBinder)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingRight()
io.flutter.embedding.engine.FlutterJNI: void onSurfaceDestroyed()
com.google.gson.FieldNamingPolicy: com.google.gson.FieldNamingPolicy valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: android.content.Context getPresentationContext()
com.google.crypto.tink.shaded.protobuf.JavaType: com.google.crypto.tink.shaded.protobuf.JavaType[] values()
androidx.appcompat.widget.AppCompatRadioButton: android.content.res.ColorStateList getSupportButtonTintList()
androidx.camera.core.impl.SurfaceConfig$ConfigType: androidx.camera.core.impl.SurfaceConfig$ConfigType[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setInboxStyle(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
com.dexterous.flutterlocalnotifications.models.SoundSource: com.dexterous.flutterlocalnotifications.models.SoundSource[] values()
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setGroupAlertBehavior(android.app.Notification$Builder,int)
androidx.lifecycle.SavedStateHandlesVM: SavedStateHandlesVM()
com.google.firebase.messaging.reporting.MessagingClientEvent$Event: com.google.firebase.messaging.reporting.MessagingClientEvent$Event valueOf(java.lang.String)
com.google.firebase.concurrent.ExecutorsRegistrar: ExecutorsRegistrar()
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
com.google.firebase.analytics.FirebaseAnalytics: com.google.android.gms.measurement.internal.zzlj getScionFrontendApiImplementation(android.content.Context,android.os.Bundle)
com.google.common.util.concurrent.DirectExecutor: com.google.common.util.concurrent.DirectExecutor[] values()
androidx.camera.camera2.internal.compat.quirk.LegacyCameraOutputConfigNullPointerQuirk: LegacyCameraOutputConfigNullPointerQuirk()
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float getDistance(android.widget.EdgeEffect)
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingPlugin: FlutterFirebaseMessagingPlugin()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceCreated(long,android.view.Surface)
androidx.recyclerview.widget.RecyclerView: void setPreserveFocusAfterLayout(boolean)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean onNewIntent(android.content.Intent)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture,java.lang.Runnable)
androidx.work.impl.WorkDatabase: WorkDatabase()
io.flutter.embedding.android.FlutterTextureView: void setRenderSurface(android.view.Surface)
androidx.biometric.CryptoObjectUtils$Api23Impl: void setBlockModeCBC(android.security.keystore.KeyGenParameterSpec$Builder)
androidx.media.AudioAttributesImplBaseParcelizer: androidx.media.AudioAttributesImplBase read(androidx.versionedparcelable.VersionedParcel)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onReceivedError(android.webkit.WebView,android.webkit.WebResourceRequest,java.lang.reflect.InvocationHandler)
androidx.recyclerview.widget.RecyclerView: androidx.core.view.NestedScrollingChildHelper getScrollingChildHelper()
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: void setPresentationView(android.view.View)
androidx.appcompat.widget.SwitchCompat: void setSwitchTypeface(android.graphics.Typeface)
androidx.media3.exoplayer.audio.DefaultAudioSink$StreamEventCallbackV29: void unregister(android.media.AudioTrack)
com.google.firebase.messaging.FirebaseMessaging: com.google.firebase.messaging.FirebaseMessaging getInstance(com.google.firebase.FirebaseApp)
androidx.recyclerview.widget.RecyclerView: void setRecyclerListener(androidx.recyclerview.widget.RecyclerView$RecyclerListener)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintList(android.view.View,android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: void nativeRunBundleAndSnapshotFromLibrary(long,java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List,long)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
androidx.camera.core.impl.Config$OptionPriority: androidx.camera.core.impl.Config$OptionPriority valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onMethodCall(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setForceDark(int)
androidx.biometric.CryptoObjectUtils$Api23Impl: android.security.keystore.KeyGenParameterSpec$Builder createKeyGenParameterSpecBuilder(java.lang.String,int)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getTrackTintList()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode valueOf(java.lang.String)
androidx.media.AudioAttributesCompat: AudioAttributesCompat()
androidx.browser.customtabs.CustomTabsIntent$Api24Impl: java.lang.String getDefaultLocale()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
androidx.recyclerview.widget.RecyclerView: void setItemAnimator(androidx.recyclerview.widget.RecyclerView$ItemAnimator)
androidx.activity.OnBackPressedDispatcher$Api33Impl: void registerOnBackInvokedCallback(java.lang.Object,int,java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivitySaveInstanceState(com.google.android.gms.dynamic.IObjectWrapper,com.google.android.gms.internal.measurement.zzcu,long)
dev.fluttercommunity.plus.share.ShareFileProvider: ShareFileProvider()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: boolean isAudioMuted()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScroll(android.view.ViewParent,android.view.View,int,int,int,int)
androidx.media.AudioAttributesImplApi26: AudioAttributesImplApi26()
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityPaneTitle(android.view.View,java.lang.CharSequence)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void release()
androidx.appcompat.widget.SwitchCompat: void setTrackDrawable(android.graphics.drawable.Drawable)
androidx.work.impl.background.systemjob.SystemJobService$Api31Impl: int getStopReason(android.app.job.JobParameters)
io.flutter.embedding.engine.FlutterJNI: void onVsync(long,long,long)
androidx.appcompat.widget.AppCompatButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.app.NotificationCompat$CallStyle$Api28Impl: android.app.Notification$Builder addPerson(android.app.Notification$Builder,android.app.Person)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: boolean onCreate()
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Action$Builder addRemoteInput(android.app.Notification$Action$Builder,android.app.RemoteInput)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onStartNestedScroll(android.view.ViewParent,android.view.View,android.view.View,int)
io.flutter.view.TextureRegistry$SurfaceProducer: int getWidth()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setContentDescription(android.view.MenuItem,java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void nativeDeferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.biometric.AuthenticationCallbackProvider$Api28Impl$1: void onAuthenticationHelp(int,java.lang.CharSequence)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean hasInvalidBigPictureResources(io.flutter.plugin.common.MethodChannel$Result,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
androidx.camera.core.impl.SurfaceConfig$ConfigSize: androidx.camera.core.impl.SurfaceConfig$ConfigSize[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.Boolean sendNotificationPayloadMessage(android.content.Intent)
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
androidx.preference.ListPreference: ListPreference(android.content.Context,android.util.AttributeSet)
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: java.lang.String access$000(com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory)
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
androidx.core.app.NotificationCompat$MessagingStyle$Api24Impl: android.app.Notification$MessagingStyle setConversationTitle(android.app.Notification$MessagingStyle,java.lang.CharSequence)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getAllowContentAccess()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getOrCreatePerImageReader(android.media.ImageReader)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledHorizontalScrollFactor(android.view.ViewConfiguration)
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: android.graphics.ColorFilter getColorFilter(android.graphics.drawable.Drawable)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation getDefaultStyleInformation(java.util.Map)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
io.flutter.plugins.flutter_plugin_android_lifecycle.FlutterAndroidLifecyclePlugin: FlutterAndroidLifecyclePlugin()
androidx.camera.core.impl.utils.executor.SequentialExecutor$WorkerRunningState: androidx.camera.core.impl.utils.executor.SequentialExecutor$WorkerRunningState valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory: RtspMediaSource$Factory()
androidx.media3.exoplayer.audio.DefaultAudioSink$StreamEventCallbackV29: void register(android.media.AudioTrack)
androidx.camera.video.internal.encoder.EncoderImpl$InternalState: androidx.camera.video.internal.encoder.EncoderImpl$InternalState[] values()
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Action$Builder setAllowGeneratedReplies(android.app.Notification$Action$Builder,boolean)
androidx.media3.common.BundleListRetriever: com.google.common.collect.ImmutableList getList(android.os.IBinder)
androidx.biometric.BiometricFragment$Api28Impl: void setTitle(android.hardware.biometrics.BiometricPrompt$Builder,java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30(androidx.core.view.WindowInsetsCompat)
androidx.biometric.BiometricFragment$Api28Impl: void setDescription(android.hardware.biometrics.BiometricPrompt$Builder,java.lang.CharSequence)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getProxyController()
androidx.camera.core.impl.CameraCaptureMetaData$AfMode: androidx.camera.core.impl.CameraCaptureMetaData$AfMode[] values()
androidx.core.view.ViewCompat$Api26Impl: int getNextClusterForwardId(android.view.View)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$300(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void removeNotificationFromCache(android.content.Context,java.lang.Integer)
io.flutter.view.AccessibilityViewEmbedder: void cacheVirtualIdMappings(android.view.View,int,int)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void logHealthData(int,java.lang.String,com.google.android.gms.dynamic.IObjectWrapper,com.google.android.gms.dynamic.IObjectWrapper,com.google.android.gms.dynamic.IObjectWrapper)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onTrimMemory(int)
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode[] values()
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onSafeBrowsingHit(android.webkit.WebView,android.webkit.WebResourceRequest,int,java.lang.reflect.InvocationHandler)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
com.google.android.datatransport.runtime.scheduling.jobscheduling.SchedulerConfig$Flag: com.google.android.datatransport.runtime.scheduling.jobscheduling.SchedulerConfig$Flag valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.camera.camera2.internal.compat.quirk.ConfigureSurfaceToSecondarySessionFailQuirk: ConfigureSurfaceToSecondarySessionFailQuirk()
androidx.recyclerview.widget.RecyclerView: void setHasFixedSize(boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void getActiveNotifications(io.flutter.plugin.common.MethodChannel$Result)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityStoppedByScionActivityInfo(com.google.android.gms.internal.measurement.zzdf,long)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.AppCompatSpinner: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy: ConstraintProxy$NetworkStateProxy()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
androidx.security.crypto.EncryptedSharedPreferences$EncryptedType: androidx.security.crypto.EncryptedSharedPreferences$EncryptedType[] values()
androidx.appcompat.widget.SwitchCompat: void setShowText(boolean)
androidx.camera.video.internal.compat.quirk.ExtraSupportedQualityQuirk: ExtraSupportedQualityQuirk()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.util.HashMap getMappedNotificationChannel(android.app.NotificationChannel)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void cancelAllNotifications(io.flutter.plugin.common.MethodChannel$Result)
org.chromium.support_lib_boundary.WebMessageListenerBoundaryInterface: void onPostMessage(android.webkit.WebView,java.lang.reflect.InvocationHandler,android.net.Uri,boolean,java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setWebViewRendererClient(java.lang.reflect.InvocationHandler)
androidx.core.app.NotificationCompat$MessagingStyle$Message$Api24Impl: android.app.Notification$MessagingStyle$Message createMessage(java.lang.CharSequence,long,java.lang.CharSequence)
androidx.camera.camera2.internal.compat.quirk.ExcludedSupportedSizesQuirk: ExcludedSupportedSizesQuirk()
io.flutter.embedding.engine.FlutterJNI: void loadDartDeferredLibrary(int,java.lang.String[])
io.flutter.view.TextureRegistry$SurfaceProducer: void setSize(int,int)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type valueOf(java.lang.String)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: long id()
androidx.core.app.NotificationManagerCompat$Api26Impl: java.lang.String getId(android.app.NotificationChannelGroup)
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
androidx.room.Index$Order: androidx.room.Index$Order valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
org.chromium.support_lib_boundary.PrefetchParamsBoundaryInterface: java.lang.String getNoVarySearchHint()
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.engine.FlutterJNI: void ensureRunningOnMainThread()
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getWebViewMediaIntegrityApiDefaultStatus()
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.models.IconSource: com.dexterous.flutterlocalnotifications.models.IconSource[] $values()
com.google.gson.ToNumberPolicy: com.google.gson.ToNumberPolicy[] values()
androidx.appcompat.widget.SwitchCompat: boolean getTargetCheckedState()
com.dexterous.flutterlocalnotifications.ActionBroadcastReceiver: ActionBroadcastReceiver()
androidx.core.app.NotificationCompat$BigPictureStyle$Api31Impl: void setContentDescription(android.app.Notification$BigPictureStyle,java.lang.CharSequence)
androidx.core.content.ContextCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
androidx.appcompat.widget.AppCompatSpinner: androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup getInternalPopup()
androidx.recyclerview.widget.RecyclerView: int getBaseline()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readColor(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
io.flutter.embedding.engine.FlutterJNI: void runBundleAndSnapshotFromLibrary(java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List,long)
io.flutter.plugins.imagepicker.Messages$SourceCamera: io.flutter.plugins.imagepicker.Messages$SourceCamera[] values()
com.baseflow.geolocator.location.LocationAccuracyStatus: com.baseflow.geolocator.location.LocationAccuracyStatus valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void detachFromGLContext()
kotlin.random.Random: Random()
androidx.camera.core.impl.utils.ExifData$WhiteBalanceMode: androidx.camera.core.impl.utils.ExifData$WhiteBalanceMode[] values()
androidx.appcompat.widget.AppCompatRadioButton: int getCompoundPaddingLeft()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getGmpAppId(com.google.android.gms.internal.measurement.zzcu)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder addAction(android.app.Notification$Builder,android.app.Notification$Action)
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType valueOf(java.lang.String)
com.google.firebase.installations.FirebaseInstallationsKtxRegistrar: FirebaseInstallationsKtxRegistrar()
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified access$getBackgroundDispatcher$cp()
com.dexterous.flutterlocalnotifications.models.styles.BigPictureStyleInformation: BigPictureStyleInformation(java.lang.Boolean,java.lang.Boolean,java.lang.String,java.lang.Boolean,java.lang.String,java.lang.Boolean,java.lang.Object,com.dexterous.flutterlocalnotifications.models.BitmapSource,java.lang.Object,com.dexterous.flutterlocalnotifications.models.BitmapSource,java.lang.Boolean)
io.flutter.plugins.inapppurchase.Messages$PlatformBillingResponse: io.flutter.plugins.inapppurchase.Messages$PlatformBillingResponse valueOf(java.lang.String)
androidx.profileinstaller.ProfileInstallerInitializer$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
androidx.core.widget.NestedScrollView$Api21Impl: boolean getClipToPadding(android.view.ViewGroup)
androidx.work.impl.background.systemjob.SystemJobService$Api24Impl: android.net.Uri[] getTriggeredContentUris(android.app.job.JobParameters)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean isAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertSafeBrowsingResponse(java.lang.Object)
com.google.android.datatransport.runtime.firebase.transport.LogEventDropped$Reason: com.google.android.datatransport.runtime.firebase.transport.LogEventDropped$Reason valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodeParent(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQueryHint()
com.google.firebase.sessions.DataCollectionState: com.google.firebase.sessions.DataCollectionState[] values()
com.dexterous.flutterlocalnotifications.models.styles.DefaultStyleInformation: DefaultStyleInformation(java.lang.Boolean,java.lang.Boolean)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
io.flutter.plugins.imagepicker.Messages$CacheRetrievalType: io.flutter.plugins.imagepicker.Messages$CacheRetrievalType[] values()
androidx.camera.video.StreamInfo$StreamState: androidx.camera.video.StreamInfo$StreamState[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setInstanceIdProvider(com.google.android.gms.internal.measurement.zzdc)
io.flutter.plugins.videoplayer.Messages$PlatformVideoViewType: io.flutter.plugins.videoplayer.Messages$PlatformVideoViewType[] values()
io.flutter.embedding.engine.FlutterJNI: FlutterJNI()
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setupAlarm(com.dexterous.flutterlocalnotifications.models.NotificationDetails,android.app.AlarmManager,long,android.app.PendingIntent)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void retrieveAndUploadBatches(com.google.android.gms.internal.measurement.zzcx)
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle[] $values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getParent(android.view.accessibility.AccessibilityNodeInfo,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMinimumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.media3.exoplayer.audio.AudioCapabilities$Api29: com.google.common.collect.ImmutableList getDirectPlaybackSupportedEncodings(androidx.media3.common.AudioAttributes)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void logEventAndBundle(java.lang.String,java.lang.String,android.os.Bundle,com.google.android.gms.internal.measurement.zzcu,long)
androidx.camera.core.ImageProcessingUtil: int nativeConvertAndroid420ToBitmap(java.nio.ByteBuffer,int,java.nio.ByteBuffer,int,java.nio.ByteBuffer,int,int,int,android.graphics.Bitmap,int,int,int)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Drawable createAdaptiveIconDrawable(android.graphics.drawable.Drawable,android.graphics.drawable.Drawable)
com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver: void onReceive(android.content.Context,android.content.Intent)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void clearMeasurementEnabled(long)
androidx.window.layout.util.ContextCompatHelperApi30: androidx.core.view.WindowInsetsCompat currentWindowInsets(android.content.Context)
androidx.biometric.CryptoObjectUtils$Api28Impl: javax.crypto.Mac getMac(android.hardware.biometrics.BiometricPrompt$CryptoObject)
androidx.media3.exoplayer.ExoPlayerImpl$Api31: androidx.media3.exoplayer.analytics.PlayerId registerMediaMetricsListener(android.content.Context,androidx.media3.exoplayer.ExoPlayerImpl,boolean,java.lang.String)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setForceDarkBehavior(int)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewRendererClient()
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap getBitmap()
com.google.android.gms.measurement.AppMeasurement: java.util.Map getUserProperties(java.lang.String,java.lang.String,boolean)
io.flutter.embedding.engine.FlutterJNI: void endFrame2()
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportBackgroundTintList(android.content.res.ColorStateList)
android.support.v4.media.AudioAttributesImplApi26Parcelizer: void write(androidx.media.AudioAttributesImplApi26,androidx.versionedparcelable.VersionedParcel)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void processForegroundNotificationAction(android.content.Intent,java.util.Map)
androidx.recyclerview.widget.RecyclerView: void setNestedScrollingEnabled(boolean)
io.flutter.embedding.engine.FlutterJNI: void attachToNative()
com.google.gson.stream.JsonToken: com.google.gson.stream.JsonToken valueOf(java.lang.String)
androidx.camera.core.impl.CameraCaptureMetaData$AeMode: androidx.camera.core.impl.CameraCaptureMetaData$AeMode valueOf(java.lang.String)
com.google.android.gms.measurement.internal.zzlr: com.google.android.gms.measurement.internal.zzlr[] values()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setBackgroundResource(int)
androidx.media3.datasource.RawResourceDataSource: android.net.Uri buildRawResourceUri(int)
androidx.work.OutOfQuotaPolicy: androidx.work.OutOfQuotaPolicy valueOf(java.lang.String)
kotlin.collections.AbstractMutableList: AbstractMutableList()
io.flutter.view.TextureRegistry$SurfaceLifecycle: io.flutter.view.TextureRegistry$SurfaceLifecycle[] values()
io.flutter.embedding.engine.FlutterJNI: long nativeAttach(io.flutter.embedding.engine.FlutterJNI)
androidx.camera.core.ImageProcessingUtil: int nativeWriteJpegToSurface(byte[],android.view.Surface)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getUniqueId(android.view.accessibility.AccessibilityNodeInfo)
com.google.android.datatransport.Priority: com.google.android.datatransport.Priority[] values()
androidx.camera.core.ImageProcessingUtil: int nativeShiftPixel(java.nio.ByteBuffer,int,java.nio.ByteBuffer,int,java.nio.ByteBuffer,int,int,int,int,int,int,int,int)
com.google.android.gms.auth.zzn: com.google.android.gms.auth.zzn[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void requestExactAlarmsPermission(com.dexterous.flutterlocalnotifications.PermissionRequestListener)
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: android.view.View getView()
com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver: void onReceive(android.content.Context,android.content.Intent)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
com.google.firebase.messaging.reporting.MessagingClientEvent$MessageType: com.google.firebase.messaging.reporting.MessagingClientEvent$MessageType valueOf(java.lang.String)
androidx.security.crypto.EncryptedSharedPreferences$EncryptedType: androidx.security.crypto.EncryptedSharedPreferences$EncryptedType valueOf(java.lang.String)
io.flutter.plugins.videoplayer.Messages$PlatformVideoViewType: io.flutter.plugins.videoplayer.Messages$PlatformVideoViewType valueOf(java.lang.String)
androidx.biometric.BiometricFragment$Api29Impl: void setDeviceCredentialAllowed(android.hardware.biometrics.BiometricPrompt$Builder,boolean)
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
com.google.android.gms.internal.auth.zze: com.google.android.gms.internal.auth.zzf zzb(android.os.IBinder)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onPageCommitVisible(android.webkit.WebView,java.lang.String)
androidx.fragment.app.FragmentContainerView: void setLayoutTransition(android.animation.LayoutTransition)
androidx.lifecycle.ProcessLifecycleOwner$Api29Impl: void registerActivityLifecycleCallbacks(android.app.Activity,android.app.Application$ActivityLifecycleCallbacks)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchSemanticsAction(long,int,int,java.nio.ByteBuffer,int)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportButtonTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
androidx.activity.Api34Impl: float progress(android.window.BackEvent)
com.google.android.gms.internal.measurement.zzos: com.google.android.gms.internal.measurement.zzos[] values()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
com.google.firebase.sessions.api.SessionSubscriber$Name: com.google.firebase.sessions.api.SessionSubscriber$Name valueOf(java.lang.String)
com.google.android.gms.dynamic.ObjectWrapper: java.lang.Object unwrap(com.google.android.gms.dynamic.IObjectWrapper)
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object registerSource$suspendImpl(androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon,androidx.privacysandbox.ads.adservices.measurement.SourceRegistrationRequest,kotlin.coroutines.Continuation)
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.sessions.FirebaseSessionsComponent getComponents$lambda$1(com.google.firebase.components.ComponentContainer)
io.flutter.embedding.engine.FlutterJNI: void updateDisplayMetrics(int,float,float,float)
android.support.v4.media.AudioAttributesCompatParcelizer: androidx.media.AudioAttributesCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
io.flutter.view.TextureRegistry$ImageConsumer: android.media.Image acquireLatestImage()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setMessagingStyle(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
dev.fluttercommunity.plus.share.SharePlusPendingIntent: SharePlusPendingIntent()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void deleteNotificationChannel(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy: ConstraintProxy$BatteryChargingProxy()
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getContentDescription(android.view.MenuItem)
io.flutter.embedding.engine.FlutterJNI: void scheduleFrame()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29(androidx.core.view.WindowInsetsCompat)
com.aboutyou.dart_packages.sign_in_with_apple.SignInWithApplePlugin: SignInWithApplePlugin()
io.flutter.embedding.engine.FlutterJNI: void addEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int,int)
androidx.camera.core.impl.UseCaseConfigFactory$CaptureType: androidx.camera.core.impl.UseCaseConfigFactory$CaptureType valueOf(java.lang.String)
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.util.List getAllProfileNames()
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
androidx.core.content.ContextCompat$Api24Impl: java.io.File getDataDir(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void dispatchPlatformMessage(java.lang.String,java.nio.ByteBuffer,int,int)
io.flutter.view.AccessibilityViewEmbedder: java.lang.Integer getRecordFlutterId(android.view.View,android.view.accessibility.AccessibilityRecord)
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
androidx.work.ExistingWorkPolicy: androidx.work.ExistingWorkPolicy[] values()
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
androidx.core.app.NotificationCompatBuilder$Api29Impl: android.app.Notification$Action$Builder setContextual(android.app.Notification$Action$Builder,boolean)
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: byte[] getAsArrayBuffer()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState[] values()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object registerTrigger(android.net.Uri,kotlin.coroutines.Continuation)
kotlin.collections.AbstractList: AbstractList()
androidx.camera.video.internal.compat.quirk.PreviewFreezeAfterHighSpeedRecordingQuirk: PreviewFreezeAfterHighSpeedRecordingQuirk()
androidx.biometric.KeyguardUtils$Api16Impl: boolean isKeyguardSecure(android.app.KeyguardManager)
androidx.camera.core.ImageProcessingUtil: int nativeRotateYUV(java.nio.ByteBuffer,int,java.nio.ByteBuffer,int,java.nio.ByteBuffer,int,int,java.nio.ByteBuffer,int,int,java.nio.ByteBuffer,int,int,java.nio.ByteBuffer,int,int,java.nio.ByteBuffer,java.nio.ByteBuffer,java.nio.ByteBuffer,int,int,int)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedScroll(android.view.View,int,int,int,int,int[])
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setDefaultEventParameters(android.os.Bundle)
com.google.android.gms.internal.measurement.zzbk: com.google.android.gms.internal.measurement.zzbk[] values()
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: boolean isTracing()
io.flutter.embedding.engine.FlutterJNI: void unregisterTexture(long)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object registerTrigger$suspendImpl(androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon,android.net.Uri,kotlin.coroutines.Continuation)
androidx.recyclerview.widget.RecyclerView: boolean getClipToPadding()
androidx.media3.exoplayer.audio.AudioCapabilitiesReceiver$Api23: void unregisterAudioDeviceCallback(android.content.Context,android.media.AudioDeviceCallback)
androidx.appcompat.widget.SwitchCompat: void setTrackResource(int)
androidx.core.view.ViewCompat$Api30Impl: boolean isImportantForContentCapture(android.view.View)
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified access$getFirebaseInstallationsApi$cp()
com.dexterous.flutterlocalnotifications.models.NotificationAction: NotificationAction(java.util.Map)
com.dexterous.flutterlocalnotifications.models.ScheduleMode: com.dexterous.flutterlocalnotifications.models.ScheduleMode[] $values()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushTransform(float[])
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap nativeGetBitmap(long)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOff()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.view.TextureRegistry$SurfaceProducer: void release()
io.flutter.plugins.camerax.LiveDataSupportedType: io.flutter.plugins.camerax.LiveDataSupportedType[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int pendingDequeuedImages()
io.flutter.view.TextureRegistry$SurfaceLifecycle: io.flutter.view.TextureRegistry$SurfaceLifecycle valueOf(java.lang.String)
androidx.core.app.NotificationCompat$Style$Api24Impl: void setChronometerCountDown(android.widget.RemoteViews,int,boolean)
com.google.firebase.installations.remote.TokenResult$ResponseCode: com.google.firebase.installations.remote.TokenResult$ResponseCode[] values()
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.CookieManager getCookieManager()
com.google.crypto.tink.shaded.protobuf.Writer$FieldOrder: com.google.crypto.tink.shaded.protobuf.Writer$FieldOrder valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterImageTexture(long,long,java.lang.ref.WeakReference,boolean)
io.flutter.plugins.webviewflutter.FileChooserMode: io.flutter.plugins.webviewflutter.FileChooserMode[] values()
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedPreFling(android.view.ViewParent,android.view.View,float,float)
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: void setWebViewClient(android.webkit.WebViewClient)
androidx.appcompat.widget.AppCompatCheckBox: int getCompoundPaddingLeft()
androidx.core.view.ViewCompat$Api29Impl: android.view.contentcapture.ContentCaptureSession getContentCaptureSession(android.view.View)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void initialize(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
android.support.v4.media.session.IMediaControllerCallback$Stub: android.support.v4.media.session.IMediaControllerCallback asInterface(android.os.IBinder)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void insertVisualStateCallback(long,java.lang.reflect.InvocationHandler)
androidx.work.ExistingWorkPolicy: androidx.work.ExistingWorkPolicy valueOf(java.lang.String)
androidx.camera.camera2.internal.compat.quirk.TorchIsClosedAfterImageCapturingQuirk: TorchIsClosedAfterImageCapturingQuirk()
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: boolean hasEnrolledFingerprints(java.lang.Object)
com.google.android.gms.measurement.AppMeasurementJobService: AppMeasurementJobService()
com.google.firebase.crashlytics.internal.settings.SettingsCacheBehavior: com.google.firebase.crashlytics.internal.settings.SettingsCacheBehavior valueOf(java.lang.String)
androidx.camera.core.impl.SessionConfig$SessionError: androidx.camera.core.impl.SessionConfig$SessionError[] values()
androidx.core.widget.PopupWindowCompat$Api23Impl: void setOverlapAnchor(android.widget.PopupWindow,boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean hasInvalidLedDetails(io.flutter.plugin.common.MethodChannel$Result,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
io.flutter.embedding.engine.FlutterJNI: void cleanupMessageData(long)
androidx.camera.core.impl.CameraInternal$State: androidx.camera.core.impl.CameraInternal$State[] values()
androidx.camera.core.impl.CameraCaptureMetaData$FlashState: androidx.camera.core.impl.CameraCaptureMetaData$FlashState valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax valueOf(java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin$PermissionRequestProgress: com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin$PermissionRequestProgress[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void waitOnFence(android.media.Image)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertServiceWorkerSettings(java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.FlutterJNI: void setAsyncWaitForVsyncDelegate(io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setBigTextStyle(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.core.app.NotificationCompatBuilder$Api29Impl: android.app.Notification$Builder setBubbleMetadata(android.app.Notification$Builder,android.app.Notification$BubbleMetadata)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction[] values()
io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar: FlutterFirebaseAppRegistrar()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: java.util.ArrayList readMessages(java.util.ArrayList)
com.dexterous.flutterlocalnotifications.models.RepeatInterval: com.dexterous.flutterlocalnotifications.models.RepeatInterval valueOf(java.lang.String)
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerWebTriggerAsync(androidx.privacysandbox.ads.adservices.measurement.WebTriggerRegistrationRequest)
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder addPerson(android.app.Notification$Builder,java.lang.String)
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: void setWebChromeClient(android.webkit.WebChromeClient)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: androidx.window.extensions.area.ExtensionWindowAreaPresentation getRearDisplayPresentation()
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
androidx.core.app.RemoteInput$Api26Impl: android.app.RemoteInput$Builder setAllowDataType(android.app.RemoteInput$Builder,java.lang.String,boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void pendingNotificationRequests(io.flutter.plugin.common.MethodChannel$Result)
androidx.camera.camera2.internal.compat.quirk.InvalidVideoProfilesQuirk: InvalidVideoProfilesQuirk()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$ItemAnimator getItemAnimator()
androidx.core.app.NotificationCompat$BigPictureStyle$Api23Impl: void setBigLargeIcon(android.app.Notification$BigPictureStyle,android.graphics.drawable.Icon)
androidx.core.view.ViewCompat$Api26Impl: boolean isKeyboardNavigationCluster(android.view.View)
dev.fluttercommunity.workmanager.TaskType: dev.fluttercommunity.workmanager.TaskType[] values()
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents[] $values()
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Action$Builder addExtras(android.app.Notification$Action$Builder,android.os.Bundle)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getThumbTintList()
androidx.core.app.NotificationCompat$CallStyle$Api21Impl: android.app.Notification$Builder setCategory(android.app.Notification$Builder,java.lang.String)
androidx.camera.video.internal.compat.quirk.MediaCodecDefaultDataSpaceQuirk: MediaCodecDefaultDataSpaceQuirk()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setAllowFileAccess(boolean)
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
androidx.work.OutOfQuotaPolicy: androidx.work.OutOfQuotaPolicy[] values()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean isAlgorithmicDarkeningAllowed()
androidx.core.widget.EdgeEffectCompat$Api21Impl: void onPull(android.widget.EdgeEffect,float,float)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setUserId(java.lang.String,long)
io.flutter.embedding.engine.FlutterJNI: void updateSemantics(java.nio.ByteBuffer,java.lang.String[],java.nio.ByteBuffer[])
androidx.fragment.app.FragmentContainerView: androidx.fragment.app.Fragment getFragment()
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.reflect.InvocationHandler getMessagePayload()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
androidx.appcompat.widget.SearchView: int getInputType()
com.google.firebase.installations.FirebaseInstallationsRegistrar: java.util.List getComponents()
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness valueOf(java.lang.String)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder setLocalOnly(android.app.Notification$Builder,boolean)
androidx.core.widget.TextViewCompat$Api23Impl: android.graphics.PorterDuff$Mode getCompoundDrawableTintMode(android.widget.TextView)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getCodeCacheDir(android.content.Context)
com.google.firebase.messaging.FirebaseMessagingRegistrar: java.util.List getComponents()
androidx.appcompat.widget.AppCompatButton: int getAutoSizeMaxTextSize()
androidx.core.view.ViewCompat$Api28Impl: void addOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
io.flutter.embedding.android.FlutterSurfaceView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.camera.camera2.internal.compat.quirk.AbnormalStreamWhenImageAnalysisBindWithTemplateRecordQuirk: AbnormalStreamWhenImageAnalysisBindWithTemplateRecordQuirk()
androidx.activity.Api34Impl: android.window.BackEvent createOnBackEvent(float,float,float,int)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate getCompatAccessibilityDelegate()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setupAllowWhileIdleAlarm(com.dexterous.flutterlocalnotifications.models.NotificationDetails,android.app.AlarmManager,long,android.app.PendingIntent)
androidx.appcompat.widget.AppCompatCheckBox: void setButtonDrawable(android.graphics.drawable.Drawable)
androidx.biometric.AuthenticationCallbackProvider$Api28Impl$1: void onAuthenticationError(int,java.lang.CharSequence)
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
androidx.core.widget.ImageViewCompat$Api21Impl: android.content.res.ColorStateList getImageTintList(android.widget.ImageView)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getMutators()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityCreated(com.google.android.gms.dynamic.IObjectWrapper,android.os.Bundle,long)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
androidx.media.AudioAttributesImplApi26Parcelizer: androidx.media.AudioAttributesImplApi26 read(androidx.versionedparcelable.VersionedParcel)
com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails: com.dexterous.flutterlocalnotifications.models.NotificationChannelGroupDetails from(java.util.Map)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnTrimMemoryListener(io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
androidx.camera.video.internal.compat.quirk.VideoEncoderCrashQuirk: VideoEncoderCrashQuirk()
androidx.appcompat.widget.AppCompatCheckedTextView: void setCheckMarkDrawable(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void maybeWaitOnFence(android.media.Image)
com.dexterous.flutterlocalnotifications.models.DateTimeComponents: com.dexterous.flutterlocalnotifications.models.DateTimeComponents[] values()
com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar: FirebaseCrashlyticsKtxRegistrar()
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
android.support.v4.app.INotificationSideChannel$Stub: android.support.v4.app.INotificationSideChannel asInterface(android.os.IBinder)
org.chromium.support_lib_boundary.PrefetchOperationResultBoundaryInterface: int getStatusCode()
com.google.gson.stream.JsonToken: com.google.gson.stream.JsonToken[] values()
androidx.core.location.LocationCompat$Api26Impl: float getSpeedAccuracyMetersPerSecond(android.location.Location)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation valueOf(java.lang.String)
androidx.exifinterface.media.ExifInterfaceUtils$Api23Impl: void setDataSource(android.media.MediaMetadataRetriever,android.media.MediaDataSource)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void startForegroundService(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: com.dexterous.flutterlocalnotifications.models.PersonDetails readPersonDetails(java.util.Map)
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource valueOf(java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityStopped(com.google.android.gms.dynamic.IObjectWrapper,long)
androidx.appcompat.widget.SwitchCompat: void setTextOn(java.lang.CharSequence)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipPath(android.graphics.Path)
androidx.appcompat.widget.SwitchCompat: boolean getShowText()
io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar: java.util.List getComponents()
androidx.biometric.CryptoObjectUtils$Api23Impl: void setEncryptionPaddingPKCS7(android.security.keystore.KeyGenParameterSpec$Builder)
androidx.media3.exoplayer.ExoPlayerImpl$Api23: boolean isSuitableAudioOutputPresentInAudioDeviceInfoList(android.content.Context,android.media.AudioDeviceInfo[])
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabled(boolean)
com.google.firebase.analytics.FirebaseAnalytics: com.google.firebase.analytics.FirebaseAnalytics getInstance(android.content.Context)
androidx.appcompat.widget.SearchView: int getPreferredWidth()
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State[] values()
androidx.core.app.NotificationManagerCompat$Api26Impl: java.util.List getNotificationChannels(android.app.NotificationManager)
io.flutter.embedding.engine.FlutterJNI: io.flutter.view.FlutterCallbackInformation nativeLookupCallbackInformation(long)
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.sessions.FirebaseSessions getComponents$lambda$0(com.google.firebase.components.ComponentContainer)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action)
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$Action$Builder setAuthenticationRequired(android.app.Notification$Action$Builder,boolean)
androidx.work.impl.utils.NetworkApi23: android.net.Network getActiveNetworkCompat(android.net.ConnectivityManager)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void install()
com.google.android.gms.internal.measurement.zzkp: com.google.android.gms.internal.measurement.zzkp[] values()
androidx.room.Index$Order: androidx.room.Index$Order[] values()
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo createAccessibilityNodeInfo(int)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.core.app.NotificationManagerCompat$Api26Impl: java.util.List getNotificationChannelGroups(android.app.NotificationManager)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
androidx.core.os.BundleApi21ImplKt: void putSize(android.os.Bundle,java.lang.String,android.util.Size)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$302(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityCreatedByScionActivityInfo(com.google.android.gms.internal.measurement.zzdf,android.os.Bundle,long)
com.google.common.base.AbstractIterator$State: com.google.common.base.AbstractIterator$State valueOf(java.lang.String)
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.app.NotificationManagerCompat$Api24Impl: int getImportance(android.app.NotificationManager)
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified access$getFirebaseApp$cp()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void startRearDisplayPresentationSession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface: java.lang.CharSequence getDescription()
androidx.core.app.RemoteInput$Api29Impl: int getEditChoicesBeforeSending(java.lang.Object)
dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin: WakelockPlusPlugin()
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType[] values()
androidx.biometric.CryptoObjectUtils$Api28Impl: android.hardware.biometrics.BiometricPrompt$CryptoObject create(javax.crypto.Cipher)
androidx.appcompat.widget.AppCompatButton: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.gms.measurement.AppMeasurement: java.lang.String getCurrentScreenName()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: androidx.core.app.Person buildPerson(android.content.Context,com.dexterous.flutterlocalnotifications.models.PersonDetails)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: java.lang.CharSequence getContainerTitle(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode[] values()
androidx.core.view.ViewCompat$Api26Impl: boolean isImportantForAutofill(android.view.View)
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerSourceAsync(android.net.Uri,android.view.InputEvent)
androidx.core.view.ViewCompat$Api29Impl: void setSystemGestureExclusionRects(android.view.View,java.util.List)
io.flutter.embedding.engine.FlutterJNI: void showOverlaySurface2()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintList(android.graphics.drawable.Drawable,android.content.res.ColorStateList)
xyz.luan.audioplayers.ReleaseMode: xyz.luan.audioplayers.ReleaseMode valueOf(java.lang.String)
androidx.activity.ComponentActivity: void setContentView(android.view.View)
com.google.crypto.tink.proto.HashType: com.google.crypto.tink.proto.HashType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.models.SoundSource: SoundSource(java.lang.String,int)
androidx.transition.FragmentTransitionSupport: FragmentTransitionSupport()
io.flutter.plugins.inapppurchase.Messages$PlatformBillingChoiceMode: io.flutter.plugins.inapppurchase.Messages$PlatformBillingChoiceMode[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
io.flutter.view.AccessibilityViewEmbedder: boolean performAction(int,int,android.os.Bundle)
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOp(android.app.AppOpsManager,java.lang.String,java.lang.String)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityHeading(android.view.View,boolean)
androidx.camera.camera2.internal.compat.quirk.LegacyCameraSurfaceCleanupQuirk: LegacyCameraSurfaceCleanupQuirk()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.camera.video.internal.compat.quirk.MediaCodecInfoReportIncorrectInfoQuirk: MediaCodecInfoReportIncorrectInfoQuirk()
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.PlatformView,io.flutter.plugin.platform.AccessibilityEventsDelegate,int,android.view.View$OnFocusChangeListener)
io.flutter.plugins.inapppurchase.Messages$PlatformBillingResponse: io.flutter.plugins.inapppurchase.Messages$PlatformBillingResponse[] values()
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
androidx.recyclerview.widget.RecyclerView: void setRecycledViewPool(androidx.recyclerview.widget.RecyclerView$RecycledViewPool)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void remove()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$502(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.WindowInsets)
io.flutter.plugins.firebase.crashlytics.FlutterError: FlutterError(java.lang.String)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode[] values()
androidx.media3.exoplayer.audio.DefaultAudioSink$OnRoutingChangedListenerApi24: void release()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.preference.CheckBoxPreference: CheckBoxPreference(android.content.Context,android.util.AttributeSet)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetRight(android.view.DisplayCutout)
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: void start(int,java.util.Collection,int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void saveScheduledNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityStartedByScionActivityInfo(com.google.android.gms.internal.measurement.zzdf,long)
androidx.camera.video.internal.compat.quirk.ExcludeStretchedVideoQualityQuirk: ExcludeStretchedVideoQualityQuirk()
com.google.crypto.tink.shaded.protobuf.FieldType$Collection: com.google.crypto.tink.shaded.protobuf.FieldType$Collection valueOf(java.lang.String)
androidx.camera.core.CameraState$Type: androidx.camera.core.CameraState$Type[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: androidx.core.graphics.drawable.IconCompat getIconFromSource(android.content.Context,java.lang.Object,com.dexterous.flutterlocalnotifications.models.IconSource)
androidx.core.view.ViewCompat$Api21Impl: void setNestedScrollingEnabled(android.view.View,boolean)
androidx.camera.lifecycle.LifecycleCamera: void onDestroy(androidx.lifecycle.LifecycleOwner)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType[] values()
io.flutter.plugins.camerax.InfoSupportedHardwareLevel: io.flutter.plugins.camerax.InfoSupportedHardwareLevel[] values()
androidx.work.impl.background.systemalarm.Alarms$Api19Impl: void setExact(android.app.AlarmManager,int,long,android.app.PendingIntent)
androidx.core.os.BundleCompat$Api33Impl: java.lang.Object getParcelable(android.os.Bundle,java.lang.String,java.lang.Class)
androidx.biometric.CryptoObjectUtils$Api28Impl: javax.crypto.Cipher getCipher(android.hardware.biometrics.BiometricPrompt$CryptoObject)
androidx.appcompat.widget.SearchView: void setSubmitButtonEnabled(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService: JobInfoSchedulerService()
com.dexterous.flutterlocalnotifications.models.ScheduleMode: boolean useAlarmClock()
androidx.camera.core.featurecombination.impl.feature.FeatureTypeInternal: androidx.camera.core.featurecombination.impl.feature.FeatureTypeInternal[] values()
androidx.appcompat.widget.SwitchCompat: void setThumbTextPadding(int)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setStyle(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object deleteRegistrations(androidx.privacysandbox.ads.adservices.measurement.DeletionRequest,kotlin.coroutines.Continuation)
com.google.android.gms.auth.api.signin.internal.SignInHubActivity: SignInHubActivity()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setConsent(android.os.Bundle,long)
io.flutter.embedding.engine.FlutterJNI: void nativeSetViewportMetrics(long,float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
com.google.firebase.installations.FirebaseInstallationsRegistrar: FirebaseInstallationsRegistrar()
androidx.core.view.ViewCompat$Api21Impl: float getTranslationZ(android.view.View)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.net.Uri retrieveSoundResourceUri(android.content.Context,java.lang.String,com.dexterous.flutterlocalnotifications.models.SoundSource)
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
com.google.crypto.tink.KeyTemplate$OutputPrefixType: com.google.crypto.tink.KeyTemplate$OutputPrefixType valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: void setThumbPosition(float)
io.flutter.plugin.platform.SingleViewPresentation: void onCreate(android.os.Bundle)
androidx.camera.lifecycle.LifecycleCamera: void onResume(androidx.lifecycle.LifecycleOwner)
androidx.camera.camera2.internal.Camera2CameraImpl$InternalState: androidx.camera.camera2.internal.Camera2CameraImpl$InternalState[] values()
com.google.android.gms.internal.ads_identifier.zze: com.google.android.gms.internal.ads_identifier.zzf zza(android.os.IBinder)
androidx.recyclerview.widget.RecyclerView: int getMaxFlingVelocity()
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
io.flutter.view.TextureRegistry$SurfaceProducer: boolean handlesCropAndRotation()
androidx.core.content.ContextCompat$Api26Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20(androidx.core.view.WindowInsetsCompat)
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Builder setRemoteInputHistory(android.app.Notification$Builder,java.lang.CharSequence[])
androidx.core.app.RemoteInput$Api29Impl: android.app.RemoteInput$Builder setEditChoicesBeforeSending(android.app.RemoteInput$Builder,int)
androidx.work.NetworkType: androidx.work.NetworkType valueOf(java.lang.String)
androidx.camera.video.internal.compat.quirk.VideoEncoderSuspendDoesNotIncludeSuspendTimeQuirk: VideoEncoderSuspendDoesNotIncludeSuspendTimeQuirk()
androidx.work.impl.utils.Api28Impl: java.lang.String getProcessName()
com.google.crypto.tink.proto.KeyData$KeyMaterialType: com.google.crypto.tink.proto.KeyData$KeyMaterialType valueOf(java.lang.String)
io.flutter.plugins.imagepicker.Messages$SourceCamera: io.flutter.plugins.imagepicker.Messages$SourceCamera valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
androidx.camera.camera2.internal.compat.quirk.CaptureSessionShouldUseMrirQuirk: CaptureSessionShouldUseMrirQuirk()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.text.Spanned fromHtml(java.lang.String)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean hasInvalidRawSoundResource(io.flutter.plugin.common.MethodChannel$Result,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readSoundInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void addRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
androidx.core.view.ViewGroupCompat$Api21Impl: void setTransitionGroup(android.view.ViewGroup,boolean)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setSessionTimeoutDuration(long)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
androidx.media.AudioAttributesCompatParcelizer: AudioAttributesCompatParcelizer()
com.google.crypto.tink.KeyTemplate$OutputPrefixType: com.google.crypto.tink.KeyTemplate$OutputPrefixType[] values()
androidx.camera.camera2.internal.compat.quirk.JpegCaptureDownsizingQuirk: JpegCaptureDownsizingQuirk()
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readLedInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getAppInstanceId(com.google.android.gms.internal.measurement.zzcu)
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
androidx.core.widget.ImageViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getImageTintMode(android.widget.ImageView)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet)
com.google.android.datatransport.cct.CctBackendFactory: CctBackendFactory()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeSetSemanticsEnabled(long,boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: com.dexterous.flutterlocalnotifications.models.NotificationDetails extractNotificationDetails(io.flutter.plugin.common.MethodChannel$Result,java.util.Map)
io.flutter.embedding.engine.FlutterJNI: long performNativeAttach(io.flutter.embedding.engine.FlutterJNI)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setSize(int,int)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setShortcutId(android.app.Notification$Builder,java.lang.String)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: void setStateDescription(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
com.google.firebase.installations.local.PersistedInstallation$RegistrationStatus: com.google.firebase.installations.local.PersistedInstallation$RegistrationStatus[] values()
io.flutter.embedding.engine.FlutterJNI: void setLocalizationPlugin(io.flutter.plugin.localization.LocalizationPlugin)
androidx.window.extensions.core.util.function.Function: java.lang.Object apply(java.lang.Object)
com.google.common.base.Function: boolean equals(java.lang.Object)
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: NotificationChannelAction(java.lang.String,int)
androidx.fragment.app.DefaultSpecialEffectsController$Api24Impl: long totalDuration(android.animation.AnimatorSet)
com.google.firebase.messaging.reporting.MessagingClientEvent$SDKPlatform: com.google.firebase.messaging.reporting.MessagingClientEvent$SDKPlatform[] values()
androidx.camera.camera2.internal.compat.quirk.JpegHalCorruptImageQuirk: JpegHalCorruptImageQuirk()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$LayoutManager getLayoutManager()
androidx.work.impl.WorkDatabase_Impl: WorkDatabase_Impl()
androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver: ForceStopRunnable$BroadcastReceiver()
androidx.core.view.ViewCompat$Api30Impl: int getImportantForContentCapture(android.view.View)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getCurrentScreenName(com.google.android.gms.internal.measurement.zzcu)
com.google.firebase.ktx.FirebaseCommonLegacyRegistrar: FirebaseCommonLegacyRegistrar()
com.dexterous.flutterlocalnotifications.models.styles.InboxStyleInformation: InboxStyleInformation(java.lang.Boolean,java.lang.Boolean,java.lang.String,java.lang.Boolean,java.lang.String,java.lang.Boolean,java.util.ArrayList,java.lang.Boolean)
com.google.firebase.analytics.FirebaseAnalytics$ConsentType: com.google.firebase.analytics.FirebaseAnalytics$ConsentType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void registerImageTexture(long,io.flutter.view.TextureRegistry$ImageConsumer,boolean)
androidx.camera.camera2.internal.compat.quirk.Nexus4AndroidLTargetAspectRatioQuirk: Nexus4AndroidLTargetAspectRatioQuirk()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setContainerTitle(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo convertToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,int,android.view.View)
com.google.gson.FieldNamingPolicy: com.google.gson.FieldNamingPolicy[] values()
androidx.media3.exoplayer.audio.AudioCapabilities$Api23: boolean isBluetoothConnected(android.media.AudioManager,androidx.media3.exoplayer.audio.AudioDeviceInfoApi23)
androidx.camera.core.impl.utils.ExifData$WhiteBalanceMode: androidx.camera.core.impl.utils.ExifData$WhiteBalanceMode valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: void setThumbDrawable(android.graphics.drawable.Drawable)
dev.fluttercommunity.plus.share.SharePlusPlugin: SharePlusPlugin()
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: android.util.DisplayMetrics getWindowAreaDisplayMetrics()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onAttachedToActivity(io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding)
io.flutter.embedding.engine.FlutterJNI: void nativeNotifyLowMemoryWarning(long)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setSgtmDebugInfo(android.content.Intent)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.util.Map describePerson(androidx.core.app.Person)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.core.app.NotificationCompat$MessagingStyle$Message$Api24Impl: android.app.Notification$MessagingStyle$Message setData(android.app.Notification$MessagingStyle$Message,java.lang.String,android.net.Uri)
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
com.baseflow.geolocator.GeolocatorPlugin: GeolocatorPlugin()
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
androidx.biometric.CancellationSignalProvider$Api16Impl: android.os.CancellationSignal create()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void close()
androidx.core.os.LocaleListCompat$Api24Impl: android.os.LocaleList getDefault()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setMinimumSessionDuration(long)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
androidx.core.location.LocationManagerCompat$Api31Impl: boolean hasProvider(android.location.LocationManager,java.lang.String)
androidx.preference.UnPressableLinearLayout: UnPressableLinearLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: android.webkit.WebChromeClient getWebChromeClient()
io.flutter.embedding.engine.FlutterJNI: void loadLibrary(android.content.Context)
com.google.android.gms.common.internal.zzz: com.google.android.gms.common.internal.zzaa zzg(android.os.IBinder)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void show(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
androidx.media.AudioAttributesImplApi21: AudioAttributesImplApi21()
io.flutter.plugins.firebase.core.FlutterFirebasePlugin: com.google.android.gms.tasks.Task didReinitializeFirebaseCore()
androidx.core.view.ViewCompat$Api26Impl: void setNextClusterForwardId(android.view.View,int)
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
io.flutter.plugins.webviewflutter.OverScrollMode: io.flutter.plugins.webviewflutter.OverScrollMode valueOf(java.lang.String)
com.google.firebase.heartbeatinfo.HeartBeatInfo$HeartBeat: com.google.firebase.heartbeatinfo.HeartBeatInfo$HeartBeat valueOf(java.lang.String)
androidx.biometric.BiometricManager$Api29Impl: java.lang.reflect.Method getCanAuthenticateWithCryptoMethod()
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void proceed(boolean)
androidx.appcompat.widget.AppCompatCheckBox: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.app.NotificationCompat$BigPictureStyle$Api31Impl: void showBigPictureWhenCollapsed(android.app.Notification$BigPictureStyle,boolean)
io.flutter.embedding.engine.FlutterJNI: android.view.SurfaceControl$Transaction createTransaction()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void endRearDisplayPresentationSession()
androidx.media3.exoplayer.hls.HlsMediaSource$Factory: HlsMediaSource$Factory(androidx.media3.datasource.DataSource$Factory)
com.dexterous.flutterlocalnotifications.models.IconSource: IconSource(java.lang.String,int)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintList(android.view.MenuItem,android.content.res.ColorStateList)
io.flutter.embedding.android.FlutterView: io.flutter.plugin.common.BinaryMessenger getBinaryMessenger()
androidx.loader.content.ModernAsyncTask$Status: androidx.loader.content.ModernAsyncTask$Status valueOf(java.lang.String)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getFinalClippingPaths()
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.ServiceWorkerController getServiceWorkerController()
androidx.core.view.DisplayCutoutCompat$Api28Impl: android.view.DisplayCutout createDisplayCutout(android.graphics.Rect,java.util.List)
androidx.camera.core.impl.CameraCaptureMetaData$AfState: androidx.camera.core.impl.CameraCaptureMetaData$AfState valueOf(java.lang.String)
io.flutter.plugins.imagepicker.ImagePickerPlugin: ImagePickerPlugin()
androidx.media.AudioAttributesImplBase: AudioAttributesImplBase()
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
com.dexterous.flutterlocalnotifications.models.NotificationStyle: com.dexterous.flutterlocalnotifications.models.NotificationStyle[] values()
com.google.android.gms.measurement.internal.zzji: com.google.android.gms.measurement.internal.zzji[] values()
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.net.Uri getUri(java.lang.Object)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.camera.video.internal.compat.quirk.HdrRepeatingRequestFailureQuirk: HdrRepeatingRequestFailureQuirk()
androidx.appcompat.widget.SwitchCompat: void setTrackTintList(android.content.res.ColorStateList)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void cancelPrefetch(java.lang.String)
androidx.core.widget.PopupWindowCompat$Api23Impl: int getWindowLayoutType(android.widget.PopupWindow)
com.google.android.datatransport.runtime.firebase.transport.LogEventDropped$Reason: com.google.android.datatransport.runtime.firebase.transport.LogEventDropped$Reason[] values()
com.google.android.gms.auth.api.signin.RevocationBoundService: RevocationBoundService()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action,java.lang.Object)
androidx.camera.camera2.internal.compat.quirk.AspectRatioLegacyApi21Quirk: AspectRatioLegacyApi21Quirk()
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
androidx.camera.camera2.internal.compat.quirk.ImageCaptureFailWithAutoFlashQuirk: ImageCaptureFailWithAutoFlashQuirk()
androidx.camera.camera2.internal.CaptureSession$State: androidx.camera.camera2.internal.CaptureSession$State valueOf(java.lang.String)
androidx.work.impl.foreground.SystemForegroundService$Api29Impl: void startForeground(android.app.Service,int,android.app.Notification,int)
org.chromium.support_lib_boundary.WebResourceRequestBoundaryInterface: boolean isRedirect()
androidx.camera.core.impl.CameraCaptureMetaData$AeMode: androidx.camera.core.impl.CameraCaptureMetaData$AeMode[] values()
com.google.android.gms.dynamic.SupportFragmentWrapper: com.google.android.gms.dynamic.SupportFragmentWrapper wrap(androidx.fragment.app.Fragment)
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
androidx.core.widget.PopupWindowCompat$Api23Impl: boolean getOverlapAnchor(android.widget.PopupWindow)
com.google.firebase.crashlytics.internal.common.DeliveryMechanism: com.google.firebase.crashlytics.internal.common.DeliveryMechanism[] values()
androidx.core.location.LocationCompat$Api26Impl: void removeVerticalAccuracy(android.location.Location)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getServiceWorkerController()
com.it_nomads.fluttersecurestorage.ciphers.KeyCipherAlgorithm: com.it_nomads.fluttersecurestorage.ciphers.KeyCipherAlgorithm valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPlatformMessage(long,java.lang.String,java.nio.ByteBuffer,int,int)
io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice: io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice valueOf(java.lang.String)
com.dexterous.flutterlocalnotifications.models.PersonDetails: PersonDetails(java.lang.Boolean,java.lang.Object,com.dexterous.flutterlocalnotifications.models.IconSource,java.lang.Boolean,java.lang.String,java.lang.String,java.lang.String)
androidx.core.app.NotificationCompat$CallStyle$Api23Impl: android.os.Parcelable castToParcelable(android.graphics.drawable.Icon)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchEmptyPlatformMessage(long,java.lang.String,int)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver: ScheduledNotificationBootReceiver()
com.google.firebase.FirebaseCommonKtxRegistrar: FirebaseCommonKtxRegistrar()
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides valueOf(java.lang.String)
io.flutter.embedding.android.FlutterImageView: android.media.ImageReader getImageReader()
androidx.core.graphics.drawable.IconCompat$Api28Impl: android.net.Uri getUri(java.lang.Object)
com.google.android.datatransport.cct.internal.ComplianceData$ProductIdOrigin: com.google.android.datatransport.cct.internal.ComplianceData$ProductIdOrigin valueOf(java.lang.String)
com.google.firebase.datatransport.TransportRegistrar: java.util.List getComponents()
dev.fluttercommunity.plus.network_info.NetworkInfoPlusPlugin: NetworkInfoPlusPlugin()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void updateTexImage()
com.google.android.gms.internal.play_billing.zzed: com.google.android.gms.internal.play_billing.zzed[] values()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertServiceWorkerSettings(java.lang.Object)
androidx.camera.core.impl.SessionConfig$SessionError: androidx.camera.core.impl.SessionConfig$SessionError valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getThumbDrawable()
androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory: SsMediaSource$Factory(androidx.media3.datasource.DataSource$Factory)
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
androidx.core.content.ContextCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.Context,int)
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle forOngoingCall(android.app.Person,android.app.PendingIntent)
androidx.appcompat.widget.SwitchCompat: void setTrackTintMode(android.graphics.PorterDuff$Mode)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
com.dexterous.flutterlocalnotifications.models.NotificationChannelAction: com.dexterous.flutterlocalnotifications.models.NotificationChannelAction[] $values()
androidx.preference.SwitchPreference: SwitchPreference(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface: void setServiceWorkerClient(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
com.google.android.datatransport.runtime.backends.TransportBackendDiscovery: TransportBackendDiscovery()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.camera.video.internal.compat.quirk.MediaFormatMustNotUseFrameRateToFindEncoderQuirk: MediaFormatMustNotUseFrameRateToFindEncoderQuirk()
com.it_nomads.fluttersecurestorage.FlutterSecureStoragePlugin: FlutterSecureStoragePlugin()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType[] values()
androidx.camera.video.Recorder$AudioState: androidx.camera.video.Recorder$AudioState[] values()
io.flutter.embedding.engine.FlutterJNI: void setViewportMetrics(float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
androidx.camera.core.featurecombination.impl.UseCaseType: androidx.camera.core.featurecombination.impl.UseCaseType valueOf(java.lang.String)
android.support.v4.media.AudioAttributesCompatParcelizer: void write(androidx.media.AudioAttributesCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation[] values()
com.it_nomads.fluttersecurestorage.ciphers.KeyCipherAlgorithm: com.it_nomads.fluttersecurestorage.ciphers.KeyCipherAlgorithm[] values()
io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar: java.util.List getComponents()
androidx.camera.core.processing.util.GLUtils$InputFormat: androidx.camera.core.processing.util.GLUtils$InputFormat valueOf(java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
com.google.firebase.installations.remote.InstallationResponse$ResponseCode: com.google.firebase.installations.remote.InstallationResponse$ResponseCode valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
androidx.camera.core.impl.CameraCaptureMetaData$AwbState: androidx.camera.core.impl.CameraCaptureMetaData$AwbState[] values()
androidx.core.view.ViewCompat$Api21Impl: java.lang.String getTransitionName(android.view.View)
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getVMServiceUri()
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory registerSubtype(java.lang.Class)
androidx.appcompat.widget.Toolbar: void setLogo(int)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOn()
androidx.activity.Api34Impl: float touchX(android.window.BackEvent)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency: com.dexterous.flutterlocalnotifications.models.ScheduledNotificationRepeatFrequency[] $values()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getObservatoryUri()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
androidx.camera.lifecycle.LifecycleCamera: void onStop(androidx.lifecycle.LifecycleOwner)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder[] values()
androidx.core.content.ContextCompat$Api26Impl: android.content.ComponentName startForegroundService(android.content.Context,android.content.Intent)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onDetachedFromActivity()
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.core.view.ViewCompat$Api26Impl: void setAutofillHints(android.view.View,java.lang.String[])
net.jonhanson.flutter_native_splash.FlutterNativeSplashPlugin: FlutterNativeSplashPlugin()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$Adapter getAdapter()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.core.app.NotificationCompat$DecoratedCustomViewStyle$Api24Impl: android.app.Notification$Style createDecoratedCustomViewStyle()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean hasInvalidLargeIcon(io.flutter.plugin.common.MethodChannel$Result,java.lang.Object,com.dexterous.flutterlocalnotifications.models.BitmapSource)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setDataCollectionEnabled(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setUniqueId(android.view.accessibility.AccessibilityNodeInfo,java.lang.String)
com.google.android.gms.dynamic.IFragmentWrapper$Stub: com.google.android.gms.dynamic.IFragmentWrapper asInterface(android.os.IBinder)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setColorized(android.app.Notification$Builder,boolean)
androidx.core.widget.TextViewCompat$Api23Impl: int getHyphenationFrequency(android.widget.TextView)
io.flutter.embedding.engine.FlutterJNI: void nativeDestroy(long)
com.google.android.datatransport.runtime.backends.BackendResponse$Status: com.google.android.datatransport.runtime.backends.BackendResponse$Status valueOf(java.lang.String)
com.google.firebase.sessions.FirebaseSessionsRegistrar: FirebaseSessionsRegistrar()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void waitOnFence(android.media.Image)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setCanScheduleExactNotifications(io.flutter.plugin.common.MethodChannel$Result)
androidx.appcompat.widget.AppCompatRadioButton: void setButtonDrawable(android.graphics.drawable.Drawable)
androidx.core.view.ViewGroupCompat$Api21Impl: int getNestedScrollAxes(android.view.ViewGroup)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onAttachedToEngine(io.flutter.embedding.engine.plugins.FlutterPlugin$FlutterPluginBinding)
io.flutter.plugins.webviewflutter.SslErrorType: io.flutter.plugins.webviewflutter.SslErrorType valueOf(java.lang.String)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: boolean isTextSelectable(android.view.accessibility.AccessibilityNodeInfo)
com.baseflow.geolocator.location.LocationAccuracyStatus: com.baseflow.geolocator.location.LocationAccuracyStatus[] values()
com.dexterous.flutterlocalnotifications.utils.BooleanUtils: boolean getValue(java.lang.Boolean)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void prefetchUrl(java.lang.String,java.lang.reflect.InvocationHandler,android.webkit.ValueCallback)
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
androidx.appcompat.widget.SwitchCompat: void setThumbTintMode(android.graphics.PorterDuff$Mode)
androidx.room.IMultiInstanceInvalidationService$Stub: androidx.room.IMultiInstanceInvalidationService asInterface(android.os.IBinder)
androidx.core.widget.TextViewCompat$Api23Impl: android.content.res.ColorStateList getCompoundDrawableTintList(android.widget.TextView)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag valueOf(java.lang.String)
androidx.work.BackoffPolicy: androidx.work.BackoffPolicy valueOf(java.lang.String)
io.flutter.plugins.camerax.ResolutionStrategyFallbackRule: io.flutter.plugins.camerax.ResolutionStrategyFallbackRule[] values()
androidx.core.view.MenuItemCompat$Api26Impl: int getNumericModifiers(android.view.MenuItem)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: boolean shouldOverrideUrlLoading(android.webkit.WebView,android.webkit.WebResourceRequest)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
androidx.core.app.Person$Api28Impl: androidx.core.app.Person fromAndroidPerson(android.app.Person)
androidx.biometric.FingerprintDialogFragment$Api26Impl: int getColorErrorAttr()
com.google.android.gms.measurement.AppMeasurementService: AppMeasurementService()
com.dexterous.flutterlocalnotifications.models.styles.MessagingStyleInformation: MessagingStyleInformation(com.dexterous.flutterlocalnotifications.models.PersonDetails,java.lang.String,java.lang.Boolean,java.util.ArrayList,java.lang.Boolean,java.lang.Boolean)
io.flutter.plugins.firebase.core.FlutterFirebaseCorePlugin: FlutterFirebaseCorePlugin()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: void removeIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityStarted(com.google.android.gms.dynamic.IObjectWrapper,long)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getWebkitToCompatConverter()
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void cancel(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
androidx.core.os.ConfigurationCompat$Api24Impl: void setLocales(android.content.res.Configuration,androidx.core.os.LocaleListCompat)
com.google.android.gms.measurement.internal.zzod: com.google.android.gms.measurement.internal.zzod[] values()
com.google.crypto.tink.shaded.protobuf.FieldType: com.google.crypto.tink.shaded.protobuf.FieldType[] values()
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
androidx.camera.core.impl.CameraCaptureFailure$Reason: androidx.camera.core.impl.CameraCaptureFailure$Reason[] values()
androidx.work.impl.workers.CombineContinuationsWorker: CombineContinuationsWorker(android.content.Context,androidx.work.WorkerParameters)
com.google.firebase.sessions.LogEnvironment: com.google.firebase.sessions.LogEnvironment valueOf(java.lang.String)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setAllowContentAccess(boolean)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness[] values()
androidx.core.graphics.drawable.IconCompat$Api28Impl: java.lang.String getResPackage(java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: java.lang.String[] computePlatformResolvedLocale(java.lang.String[])
com.google.android.gms.measurement.internal.zzam: com.google.android.gms.measurement.internal.zzam[] values()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetLeft(android.view.DisplayCutout)
androidx.appcompat.widget.AppCompatSpinner: void setDropDownVerticalOffset(int)
com.google.common.base.Function: java.lang.Object apply(java.lang.Object)
io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar: FlutterFirebaseAppRegistrar()
androidx.appcompat.widget.SwitchCompat: int getThumbOffset()
io.flutter.embedding.engine.FlutterJNI: void dispatchEmptyPlatformMessage(java.lang.String,int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: ImeSyncDeferringInsetsCallback$AnimationCallback(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView2(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
androidx.profileinstaller.ProfileInstallerInitializer$Choreographer16Impl: void postFrameCallback(java.lang.Runnable)
io.flutter.plugins.camerax.AspectRatio: io.flutter.plugins.camerax.AspectRatio valueOf(java.lang.String)
io.flutter.plugins.imagepicker.ImagePickerCache$CacheType: io.flutter.plugins.imagepicker.ImagePickerCache$CacheType[] values()
com.dexterous.flutterlocalnotifications.models.styles.BigTextStyleInformation: BigTextStyleInformation(java.lang.Boolean,java.lang.Boolean,java.lang.String,java.lang.Boolean,java.lang.String,java.lang.Boolean,java.lang.String,java.lang.Boolean)
androidx.recyclerview.widget.RecyclerView: void setOnScrollListener(androidx.recyclerview.widget.RecyclerView$OnScrollListener)
io.flutter.plugins.urllauncher.WebViewActivity: WebViewActivity()
androidx.preference.DialogPreference: DialogPreference(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
androidx.core.app.RemoteInput$Api26Impl: void addDataResultToIntent(androidx.core.app.RemoteInput,android.content.Intent,java.util.Map)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void zonedScheduleNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.lang.Boolean)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
io.flutter.embedding.engine.FlutterJNI: void nativeImageHeaderCallback(long,int,int)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object deleteRegistrations$suspendImpl(androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon,androidx.privacysandbox.ads.adservices.measurement.DeletionRequest,kotlin.coroutines.Continuation)
com.google.firebase.analytics.FirebaseAnalytics$ConsentStatus: com.google.firebase.analytics.FirebaseAnalytics$ConsentStatus[] values()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: FlutterMutatorsStack()
io.flutter.plugins.webviewflutter.WebViewFlutterPlugin: WebViewFlutterPlugin()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
androidx.camera.video.Recorder$State: androidx.camera.video.Recorder$State valueOf(java.lang.String)
com.google.common.collect.Maps$EntryFunction: com.google.common.collect.Maps$EntryFunction valueOf(java.lang.String)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int access$200(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController(io.flutter.plugin.platform.PlatformViewsController)
androidx.recyclerview.widget.StaggeredGridLayoutManager: StaggeredGridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.preference.MultiSelectListPreference: MultiSelectListPreference(android.content.Context,android.util.AttributeSet)
androidx.core.widget.TextViewCompat$Api23Impl: int getBreakStrategy(android.widget.TextView)
androidx.core.view.ViewCompat$Api23Impl: int getScrollIndicators(android.view.View)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType valueOf(java.lang.String)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void addRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
io.flutter.plugins.camerax.MeteringMode: io.flutter.plugins.camerax.MeteringMode[] values()
io.flutter.embedding.android.FlutterImageView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void release()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifier(int)
io.flutter.embedding.engine.FlutterJNI: void dispatchPointerDataPacket(java.nio.ByteBuffer,int)
androidx.core.app.NotificationCompatBuilder$Api28Impl: android.app.Notification$Action$Builder setSemanticAction(android.app.Notification$Action$Builder,int)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
androidx.core.widget.CompoundButtonCompat$Api21Impl: android.content.res.ColorStateList getButtonTintList(android.widget.CompoundButton)
io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver: FlutterFirebaseMessagingReceiver()
androidx.appcompat.widget.AppCompatButton: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.appcompat.widget.AppCompatButton: void setSupportAllCaps(boolean)
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType valueOf(java.lang.String)
io.flutter.plugins.camerax.MeteringMode: io.flutter.plugins.camerax.MeteringMode valueOf(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void finalize()
io.flutter.plugins.camerax.LensFacing: io.flutter.plugins.camerax.LensFacing valueOf(java.lang.String)
com.baseflow.geolocator.permission.LocationPermission: com.baseflow.geolocator.permission.LocationPermission[] values()
androidx.preference.internal.PreferenceImageView: void setMaxWidth(int)
androidx.core.app.NotificationCompat$CallStyle$Api23Impl: void setLargeIcon(android.app.Notification$Builder,android.graphics.drawable.Icon)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void attachToGLContext(int)
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin: LegacySharedPreferencesPlugin()
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: boolean isHardwareDetected(java.lang.Object)
androidx.activity.Api34Impl: int swipeEdge(android.window.BackEvent)
androidx.fragment.app.strictmode.FragmentStrictMode$Flag: androidx.fragment.app.strictmode.FragmentStrictMode$Flag valueOf(java.lang.String)
androidx.camera.core.ImageProcessingUtil: int nativeGetYUVImageVUOff(java.nio.ByteBuffer,java.nio.ByteBuffer)
androidx.core.content.ContextCompat$Api24Impl: boolean isDeviceProtectedStorage(android.content.Context)
androidx.camera.camera2.internal.compat.quirk.CameraNoResponseWhenEnablingFlashQuirk: CameraNoResponseWhenEnablingFlashQuirk()
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface: void onRendererUnresponsive(android.webkit.WebView,java.lang.reflect.InvocationHandler)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
io.flutter.plugins.videoplayer.ExoPlayerEventListener$RotationDegrees: io.flutter.plugins.videoplayer.ExoPlayerEventListener$RotationDegrees[] values()
com.google.android.gms.measurement.AppMeasurement: void clearConditionalUserProperty(java.lang.String,java.lang.String,android.os.Bundle)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void maybeWaitOnFence(android.media.Image)
androidx.core.app.NotificationManagerCompat$Api26Impl: void createNotificationChannels(android.app.NotificationManager,java.util.List)
androidx.camera.camera2.internal.ProcessingCaptureSession$ProcessorState: androidx.camera.camera2.internal.ProcessingCaptureSession$ProcessorState[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: byte[] castObjectToByteArray(java.lang.Object)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.appcompat.widget.SwitchCompat: void setTextOff(java.lang.CharSequence)
com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver: AlarmManagerSchedulerBroadcastReceiver()
androidx.camera.core.impl.CameraCaptureMetaData$AwbState: androidx.camera.core.impl.CameraCaptureMetaData$AwbState valueOf(java.lang.String)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onPrepare(android.view.WindowInsetsAnimation)
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
io.flutter.embedding.engine.FlutterJNI: void deferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.core.view.MenuItemCompat$Api26Impl: int getAlphabeticModifiers(android.view.MenuItem)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.Boolean canCreateNotificationChannel(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails)
androidx.core.location.LocationManagerCompat$Api19Impl: boolean tryRequestLocationUpdates(android.location.LocationManager,java.lang.String,androidx.core.location.LocationRequestCompat,androidx.core.location.LocationManagerCompat$LocationListenerTransport)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceWindowChanged(android.view.Surface)
androidx.core.app.RemoteInput$Api26Impl: java.util.Map getDataResultsFromIntent(android.content.Intent,java.lang.String)
androidx.media3.common.AudioAttributes$Api32: void setSpatializationBehavior(android.media.AudioAttributes$Builder,int)
androidx.appcompat.widget.AppCompatSpinner: void setDropDownWidth(int)
androidx.core.app.NotificationCompat$BubbleMetadata$Api30Impl: android.app.Notification$BubbleMetadata toPlatform(androidx.core.app.NotificationCompat$BubbleMetadata)
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar: com.google.firebase.analytics.connector.AnalyticsConnector zza(com.google.firebase.components.ComponentContainer)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setRequestedWithHeaderOriginAllowList(java.util.Set)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.android.FlutterView: void setDelegate(io.flutter.embedding.android.FlutterViewDelegate)
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: long lseek(java.io.FileDescriptor,long,int)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setCurrentScreen(com.google.android.gms.dynamic.IObjectWrapper,java.lang.String,java.lang.String,long)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean launchedActivityFromHistory(android.content.Intent)
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void zonedScheduleNextNotificationMatchingDateComponents(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
androidx.camera.camera2.internal.compat.quirk.ExtraCroppingQuirk: ExtraCroppingQuirk()
androidx.activity.OnBackPressedDispatcher$Api33Impl: void unregisterOnBackInvokedCallback(java.lang.Object,java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: boolean nativeGetIsSoftwareRenderingEnabled()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.app.AppOpsManagerCompat$Api29Impl: android.app.AppOpsManager getSystemService(android.content.Context)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
androidx.security.crypto.MasterKey$KeyScheme: androidx.security.crypto.MasterKey$KeyScheme valueOf(java.lang.String)
androidx.camera.core.internal.compat.quirk.PreviewGreenTintQuirk: PreviewGreenTintQuirk()
dev.fluttercommunity.workmanager.BackgroundWorker: BackgroundWorker(android.content.Context,androidx.work.WorkerParameters)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void finalize()
com.google.android.gms.dynamic.IObjectWrapper$Stub: com.google.android.gms.dynamic.IObjectWrapper asInterface(android.os.IBinder)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType valueOf(java.lang.String)
com.google.crypto.tink.shaded.protobuf.Writer$FieldOrder: com.google.crypto.tink.shaded.protobuf.Writer$FieldOrder[] values()
androidx.biometric.AuthenticationCallbackProvider$Api28Impl$1: AuthenticationCallbackProvider$Api28Impl$1(androidx.biometric.AuthenticationCallbackProvider$Listener)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: AppCompatTextViewAutoSizeHelper$Impl29()
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder setGroupSummary(android.app.Notification$Builder,boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.Object createRangeInfo(int,float,float,float)
com.baseflow.geolocator.errors.ErrorCodes: com.baseflow.geolocator.errors.ErrorCodes valueOf(java.lang.String)
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetWidth()
androidx.camera.core.impl.CameraCaptureMetaData$AfMode: androidx.camera.core.impl.CameraCaptureMetaData$AfMode valueOf(java.lang.String)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean hasRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.AppCompatSpinner: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int)
org.chromium.support_lib_boundary.PrefetchParamsBoundaryInterface: java.util.Map getAdditionalHeaders()
com.google.android.gms.measurement.internal.zzjj: com.google.android.gms.measurement.internal.zzjj[] values()
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int,int)
com.google.android.gms.measurement.AppMeasurement: com.google.android.gms.measurement.AppMeasurement getInstance(android.content.Context)
io.flutter.view.TextureRegistry$SurfaceProducer: int getHeight()
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
androidx.core.view.ViewCompat$Api21Impl: boolean isImportantForAccessibility(android.view.View)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void lambda$dequeueImage$0()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
com.google.firebase.analytics.FirebaseAnalytics: void setCurrentScreen(android.app.Activity,java.lang.String,java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar: java.util.List getComponents()
androidx.appcompat.widget.SearchView: void setQuery(java.lang.CharSequence)
com.google.common.collect.Maps$EntryFunction: com.google.common.collect.Maps$EntryFunction[] values()
androidx.camera.camera2.internal.compat.quirk.PreviewUnderExposureQuirk: PreviewUnderExposureQuirk()
com.dexterous.flutterlocalnotifications.models.MessageDetails: MessageDetails(java.lang.String,java.lang.Long,com.dexterous.flutterlocalnotifications.models.PersonDetails,java.lang.String,java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: android.content.res.ColorStateList getBackgroundTintList(android.view.View)
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
androidx.appcompat.widget.SearchView: SearchView(android.content.Context)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void cleanup()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setTimeoutAfter(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat: io.flutter.plugins.videoplayer.VideoAsset$StreamingFormat[] values()
android.support.customtabs.trusted.ITrustedWebActivityService$Stub: android.support.customtabs.trusted.ITrustedWebActivityService asInterface(android.os.IBinder)
androidx.camera.video.internal.compat.quirk.PreviewBlackScreenQuirk: PreviewBlackScreenQuirk()
com.google.android.gms.location.zzy: com.google.android.gms.location.zzz zzb(android.os.IBinder)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
androidx.appcompat.widget.SwitchCompat: SwitchCompat(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getProfile()
androidx.recyclerview.widget.RecyclerView: void setLayoutFrozen(boolean)
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void setProxyOverride(java.lang.String[][],java.lang.String[],java.lang.Runnable,java.util.concurrent.Executor,boolean)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void scheduleNextNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
androidx.core.view.ViewCompat$Api26Impl: void setImportantForAutofill(android.view.View,int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void requestFullScreenIntentPermission(com.dexterous.flutterlocalnotifications.PermissionRequestListener)
org.chromium.support_lib_boundary.FeatureFlagHolderBoundaryInterface: java.lang.String[] getSupportedFeatures()
com.google.crypto.tink.shaded.protobuf.WireFormat$JavaType: com.google.crypto.tink.shaded.protobuf.WireFormat$JavaType[] values()
androidx.core.app.ActivityCompat$Api32Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
com.google.android.gms.internal.auth.zzby: com.google.android.gms.internal.auth.zzby[] values()
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$MethodToInvoke: com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessage(java.lang.String,java.nio.ByteBuffer,int,long)
io.flutter.plugins.inapppurchase.Messages$PlatformProductType: io.flutter.plugins.inapppurchase.Messages$PlatformProductType[] values()
com.google.firebase.datatransport.TransportRegistrar: TransportRegistrar()
androidx.appcompat.widget.AppCompatSpinner: int getDropDownVerticalOffset()
androidx.core.widget.CompoundButtonCompat$Api21Impl: android.graphics.PorterDuff$Mode getButtonTintMode(android.widget.CompoundButton)
com.google.common.collect.AbstractMapEntry: AbstractMapEntry()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushOpacity(float)
androidx.camera.core.impl.CameraCaptureMetaData$AeState: androidx.camera.core.impl.CameraCaptureMetaData$AeState[] values()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void endRearDisplaySession()
androidx.core.view.ViewCompat$Api28Impl: void setAutofillId(android.view.View,androidx.core.view.autofill.AutofillIdCompat)
androidx.work.impl.workers.DiagnosticsWorker: DiagnosticsWorker(android.content.Context,androidx.work.WorkerParameters)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
androidx.core.view.ViewCompat$Api29Impl: void setContentCaptureSession(android.view.View,androidx.core.view.contentcapture.ContentCaptureSessionCompat)
io.flutter.plugin.platform.PlatformViewWrapper: void setLayoutParams(android.widget.FrameLayout$LayoutParams)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.work.impl.Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: boolean IsSurfaceControlEnabled()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setEventInterceptor(com.google.android.gms.internal.measurement.zzda)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.util.Map extractNotificationResponseMap(android.content.Intent)
androidx.camera.camera2.internal.compat.quirk.IncorrectCaptureStateQuirk: IncorrectCaptureStateQuirk()
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle setAnswerButtonColorHint(android.app.Notification$CallStyle,int)
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
com.google.android.gms.common.internal.ICancelToken$Stub: com.google.android.gms.common.internal.ICancelToken asInterface(android.os.IBinder)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceCreated(android.view.Surface)
com.google.crypto.tink.config.internal.TinkFipsUtil$AlgorithmFipsCompatibility: com.google.crypto.tink.config.internal.TinkFipsUtil$AlgorithmFipsCompatibility valueOf(java.lang.String)
kotlinx.coroutines.android.AndroidDispatcherFactory: int getLoadPriority()
io.flutter.embedding.engine.FlutterJNI: void ensureAttachedToNative()
io.flutter.plugins.imagepicker.Messages$SourceType: io.flutter.plugins.imagepicker.Messages$SourceType[] values()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setNumericShortcut(android.view.MenuItem,char,int)
androidx.appcompat.widget.AppCompatButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.camera.camera2.internal.compat.quirk.CaptureSessionStuckWhenCreatingBeforeClosingCameraQuirk: CaptureSessionStuckWhenCreatingBeforeClosingCameraQuirk()
androidx.core.os.UserManagerCompat$Api24Impl: boolean isUserUnlocked(android.content.Context)
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.camera.core.impl.utils.executor.SequentialExecutor$WorkerRunningState: androidx.camera.core.impl.utils.executor.SequentialExecutor$WorkerRunningState[] values()
androidx.appcompat.widget.SearchView: void setQueryRefinementEnabled(boolean)
com.google.firebase.concurrent.UiExecutor: com.google.firebase.concurrent.UiExecutor valueOf(java.lang.String)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void initSafeBrowsing(android.content.Context,android.webkit.ValueCallback)
androidx.preference.PreferenceGroup: PreferenceGroup(android.content.Context,android.util.AttributeSet)
androidx.preference.DropDownPreference: DropDownPreference(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: java.util.Set getRequestedWithHeaderOriginAllowList()
io.flutter.embedding.android.FlutterTextureView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
io.flutter.view.AccessibilityViewEmbedder: android.view.View platformViewOfNode(int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void getTransformMatrix(float[])
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setTimeoutAfter(android.app.Notification$Builder,long)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind valueOf(java.lang.String)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType valueOf(java.lang.String)
com.google.gson.LongSerializationPolicy: com.google.gson.LongSerializationPolicy valueOf(java.lang.String)
androidx.core.os.ConfigurationCompat$Api24Impl: android.os.LocaleList getLocales(android.content.res.Configuration)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.app.AlarmManager getAlarmManager(android.content.Context)
com.baseflow.geolocator.location.LocationAccuracy: com.baseflow.geolocator.location.LocationAccuracy valueOf(java.lang.String)
com.google.android.gms.common.api.internal.zzd: zzd()
androidx.camera.video.Recorder$AudioState: androidx.camera.video.Recorder$AudioState valueOf(java.lang.String)
androidx.core.widget.CompoundButtonCompat$Api23Impl: android.graphics.drawable.Drawable getButtonDrawable(android.widget.CompoundButton)
androidx.camera.camera2.internal.compat.quirk.TextureViewIsClosedQuirk: TextureViewIsClosedQuirk()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScrollAccepted(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
androidx.appcompat.widget.SearchView: int getMaxWidth()
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
androidx.core.content.res.ResourcesCompat$Api23Impl: android.content.res.ColorStateList getColorStateList(android.content.res.Resources,int,android.content.res.Resources$Theme)
io.flutter.plugins.inapppurchase.Messages$PlatformReplacementMode: io.flutter.plugins.inapppurchase.Messages$PlatformReplacementMode[] values()
androidx.media3.exoplayer.dash.DashMediaSource$Factory: DashMediaSource$Factory(androidx.media3.datasource.DataSource$Factory)
androidx.camera.core.impl.CameraCaptureMetaData$FlashState: androidx.camera.core.impl.CameraCaptureMetaData$FlashState[] values()
com.google.firebase.installations.remote.TokenResult$ResponseCode: com.google.firebase.installations.remote.TokenResult$ResponseCode valueOf(java.lang.String)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: android.net.Uri getSafeBrowsingPrivacyPolicyUrl()
androidx.core.view.ViewCompat$Api21Impl: float getZ(android.view.View)
androidx.core.view.ViewCompat$Api21Impl: boolean startNestedScroll(android.view.View,int)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifierBase(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image acquireLatestImage()
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
androidx.camera.video.internal.compat.quirk.StretchedVideoResolutionQuirk: StretchedVideoResolutionQuirk()
androidx.core.widget.NestedScrollView: int getScrollRange()
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection[] values()
androidx.camera.video.VideoEncoderSession$VideoEncoderState: androidx.camera.video.VideoEncoderSession$VideoEncoderState[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void pruneImageReaderQueue()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.firebase.concurrent.UiExecutor: com.google.firebase.concurrent.UiExecutor[] values()
io.flutter.plugins.imagepicker.ImagePickerFileProvider: ImagePickerFileProvider()
com.google.firebase.datatransport.TransportRegistrar: com.google.android.datatransport.TransportFactory lambda$getComponents$1(com.google.firebase.components.ComponentContainer)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$OnFlingListener getOnFlingListener()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.recyclerview.widget.GridLayoutManager: GridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.camera.video.internal.compat.quirk.MediaStoreVideoCannotWrite: MediaStoreVideoCannotWrite()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType[] values()
org.chromium.support_lib_boundary.WebViewCookieManagerBoundaryInterface: java.util.List getCookieInfo(java.lang.String)
androidx.appcompat.widget.AppCompatButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.camera.video.internal.compat.quirk.SignalEosOutputBufferNotComeQuirk: SignalEosOutputBufferNotComeQuirk()
androidx.core.view.VelocityTrackerCompat$Api34Impl: boolean isAxisSupported(android.view.VelocityTracker,int)
com.google.firebase.messaging.reporting.MessagingClientEvent$MessageType: com.google.firebase.messaging.reporting.MessagingClientEvent$MessageType[] values()
androidx.core.app.AlarmManagerCompat$Api23Impl: void setExactAndAllowWhileIdle(android.app.AlarmManager,int,long,android.app.PendingIntent)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$402(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.View)
androidx.appcompat.widget.AppCompatRadioButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.camera.camera2.internal.compat.quirk.RepeatingStreamConstraintForVideoRecordingQuirk: RepeatingStreamConstraintForVideoRecordingQuirk()
androidx.core.app.NotificationCompat$MessagingStyle$Api28Impl: android.app.Notification$MessagingStyle createMessagingStyle(android.app.Person)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
io.flutter.embedding.engine.FlutterJNI: void nativeScheduleFrame(long)
androidx.core.location.LocationCompat$Api26Impl: float getVerticalAccuracyMeters(android.location.Location)
androidx.appcompat.widget.SearchView: void setQueryHint(java.lang.CharSequence)
androidx.camera.core.featurecombination.impl.UseCaseType: androidx.camera.core.featurecombination.impl.UseCaseType[] values()
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportButtonTintMode()
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle setVerificationText(android.app.Notification$CallStyle,java.lang.CharSequence)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getActiveReader()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImageReaders()
androidx.core.app.NotificationCompatBuilder$Api20Impl: java.lang.String getGroup(android.app.Notification)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateRefreshRate(float)
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
com.google.android.gms.measurement.AppMeasurement: java.lang.String getCurrentScreenClass()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void stopForegroundService(io.flutter.plugin.common.MethodChannel$Result)
androidx.camera.core.internal.compat.quirk.IncorrectJpegMetadataQuirk: IncorrectJpegMetadataQuirk()
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
androidx.work.impl.foreground.SystemForegroundService: SystemForegroundService()
androidx.preference.internal.PreferenceImageView: int getMaxWidth()
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle forIncomingCall(android.app.Person,android.app.PendingIntent,android.app.PendingIntent)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Action build(android.app.Notification$Action$Builder)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMaximumFlingVelocity(android.view.ViewConfiguration,int,int,int)
com.google.android.gms.measurement.AppMeasurement: java.util.List getConditionalUserProperties(java.lang.String,java.lang.String)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
com.dexterous.flutterlocalnotifications.models.SoundSource: com.dexterous.flutterlocalnotifications.models.SoundSource valueOf(java.lang.String)
com.google.android.gms.internal.play_billing.zzjv: com.google.android.gms.internal.play_billing.zzjv[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void applyGrouping(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceChanged(int,int)
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: ImeSyncDeferringInsetsCallback(android.view.View)
com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails: com.dexterous.flutterlocalnotifications.models.NotificationChannelDetails from(java.util.Map)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setOffscreenPreRaster(boolean)
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getThumbTintMode()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnFrameConsumedListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
com.dexterous.flutterlocalnotifications.models.styles.StyleInformation: StyleInformation()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: boolean hasInvalidIcon(io.flutter.plugin.common.MethodChannel$Result,java.lang.String)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertCookieManager(java.lang.Object)
com.google.android.datatransport.cct.internal.ComplianceData$ProductIdOrigin: com.google.android.datatransport.cct.internal.ComplianceData$ProductIdOrigin[] values()
androidx.core.app.NotificationCompatBuilder$Api29Impl: android.app.Notification$Builder setAllowSystemGeneratedContextualActions(android.app.Notification$Builder,boolean)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedPreScroll(android.view.ViewParent,android.view.View,int,int,int[])
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode valueOf(java.lang.String)
com.google.crypto.tink.shaded.protobuf.WireFormat$FieldType: com.google.crypto.tink.shaded.protobuf.WireFormat$FieldType[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setUserAgentMetadataFromMap(java.util.Map)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id()
com.google.firebase.installations.FirebaseInstallationsRegistrar: com.google.firebase.installations.FirebaseInstallationsApi lambda$getComponents$0(com.google.firebase.components.ComponentContainer)
com.google.firebase.messaging.FirebaseMessagingRegistrar: FirebaseMessagingRegistrar()
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
androidx.appcompat.widget.AppCompatRadioButton: void setBackgroundResource(int)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType valueOf(java.lang.String)
androidx.camera.core.internal.compat.quirk.SurfaceOrderQuirk: SurfaceOrderQuirk()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.AppCompatCheckBox: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
android.support.v4.media.AudioAttributesImplApi21Parcelizer: androidx.media.AudioAttributesImplApi21 read(androidx.versionedparcelable.VersionedParcel)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getDisabledActionModeMenuItems()
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: FlutterRenderer$ImageReaderSurfaceProducer(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void getBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
androidx.camera.camera2.internal.compat.quirk.PreviewOrientationIncorrectQuirk: PreviewOrientationIncorrectQuirk()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void releaseInternal()
com.google.common.collect.Iterators$EmptyModifiableIterator: com.google.common.collect.Iterators$EmptyModifiableIterator valueOf(java.lang.String)
androidx.work.impl.utils.NetworkApi21: boolean hasCapabilityCompat(android.net.NetworkCapabilities,int)
androidx.camera.core.featurecombination.impl.feature.FeatureTypeInternal: androidx.camera.core.featurecombination.impl.feature.FeatureTypeInternal valueOf(java.lang.String)
androidx.core.app.NotificationCompatBuilder$Api31Impl: android.app.Notification$Builder setForegroundServiceBehavior(android.app.Notification$Builder,int)
androidx.privacysandbox.ads.adservices.internal.AdServicesInfo$Extensions30ExtImpl: int getAdExtServicesVersionS()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.String getNextFireDate(com.dexterous.flutterlocalnotifications.models.NotificationDetails)
androidx.camera.video.internal.compat.quirk.ReportedVideoQualityNotSupportedQuirk: ReportedVideoQualityNotSupportedQuirk()
org.chromium.support_lib_boundary.VisualStateCallbackBoundaryInterface: void onComplete(long)
com.google.android.gms.dynamic.ObjectWrapper: com.google.android.gms.dynamic.IObjectWrapper wrap(java.lang.Object)
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
androidx.core.app.AppOpsManagerCompat$Api29Impl: java.lang.String getOpPackageName(android.content.Context)
androidx.work.multiprocess.IListenableWorkerImpl$Stub: androidx.work.multiprocess.IListenableWorkerImpl asInterface(android.os.IBinder)
androidx.work.ExistingPeriodicWorkPolicy: androidx.work.ExistingPeriodicWorkPolicy valueOf(java.lang.String)
androidx.work.multiprocess.IWorkManagerImpl$Stub: androidx.work.multiprocess.IWorkManagerImpl asInterface(android.os.IBinder)
androidx.activity.OnBackPressedDispatcher$Api34Impl: android.window.OnBackInvokedCallback createOnBackAnimationCallback(kotlin.jvm.functions.Function1,kotlin.jvm.functions.Function1,kotlin.jvm.functions.Function0,kotlin.jvm.functions.Function0)
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: androidx.core.hardware.fingerprint.FingerprintManagerCompat$CryptoObject unwrapCryptoObject(java.lang.Object)
androidx.core.view.ViewCompat$Api30Impl: void setStateDescription(android.view.View,java.lang.CharSequence)
com.dexterous.flutterlocalnotifications.models.ScheduleMode: boolean useAllowWhileIdle()
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.FlutterEngine getAttachedFlutterEngine()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback access$200(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnTrimMemoryListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.core.os.LocaleListCompat$Api24Impl: android.os.LocaleList getAdjustedDefault()
androidx.core.view.ViewCompat$Api28Impl: java.lang.Object requireViewById(android.view.View,int)
dev.fluttercommunity.plus.sensors.SensorsPlugin: SensorsPlugin()
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.appcompat.widget.SearchView: int getSuggestionCommitIconResId()
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setVisibility(android.app.Notification$Builder,int)
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
com.google.android.gms.internal.measurement.zzko: com.google.android.gms.internal.measurement.zzko[] values()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode[] values()
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
androidx.appcompat.widget.SwitchCompat: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
io.flutter.plugins.camerax.AspectRatioStrategyFallbackRule: io.flutter.plugins.camerax.AspectRatioStrategyFallbackRule valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceDestroyed(long)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getBackForwardCacheEnabled()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
com.google.android.gms.internal.measurement.zzbm: boolean onTransact(int,android.os.Parcel,android.os.Parcel,int)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void removeRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetTop(android.view.DisplayCutout)
androidx.camera.video.internal.compat.quirk.CodecStuckOnFlushQuirk: CodecStuckOnFlushQuirk()
androidx.appcompat.widget.Toolbar: void setTitle(int)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readPlatformSpecifics(java.util.Map,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
androidx.core.content.ContextCompat$Api28Impl: java.util.concurrent.Executor getMainExecutor(android.content.Context)
androidx.appcompat.widget.AppCompatEditText: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: com.google.android.gms.tasks.Task didReinitializeFirebaseCore()
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface: void onRendererResponsive(android.webkit.WebView,java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getWidth()
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState valueOf(java.lang.String)
androidx.work.multiprocess.IWorkManagerImplCallback$Stub: androidx.work.multiprocess.IWorkManagerImplCallback asInterface(android.os.IBinder)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivitySaveInstanceStateByScionActivityInfo(com.google.android.gms.internal.measurement.zzdf,com.google.android.gms.internal.measurement.zzcu,long)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityResumed(com.google.android.gms.dynamic.IObjectWrapper,long)
io.flutter.embedding.engine.FlutterJNI: void applyTransactions()
androidx.camera.video.VideoOutput$SourceState: androidx.camera.video.VideoOutput$SourceState valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: FlutterFirebasePluginRegistry()
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode valueOf(java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setSafeBrowsingEnabled(boolean)
androidx.work.impl.background.systemjob.SystemJobService$Api24Impl: java.lang.String[] getTriggeredContentAuthorities(android.app.job.JobParameters)
androidx.core.app.RemoteInput$Api20Impl: void addResultsToIntent(java.lang.Object,android.content.Intent,android.os.Bundle)
androidx.camera.camera2.internal.compat.quirk.PreviewPixelHDRnetQuirk: PreviewPixelHDRnetQuirk()
android.support.v4.media.AudioAttributesImplBaseParcelizer: void write(androidx.media.AudioAttributesImplBase,androidx.versionedparcelable.VersionedParcel)
androidx.camera.camera2.internal.compat.quirk.CaptureSessionOnClosedNotCalledQuirk: CaptureSessionOnClosedNotCalledQuirk()
io.flutter.plugins.camerax.VideoQuality: io.flutter.plugins.camerax.VideoQuality[] values()
com.mr.flutter.plugin.filepicker.FilePickerPlugin: FilePickerPlugin()
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: java.lang.String getAsString()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void saveScheduledNotifications(android.content.Context,java.util.ArrayList)
io.flutter.embedding.engine.FlutterOverlaySurface: int getId()
androidx.appcompat.widget.SearchView: void setOnQueryTextFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.core.view.ViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getBackgroundTintMode(android.view.View)
dev.fluttercommunity.plus.connectivity.ConnectivityPlugin: ConnectivityPlugin()
android.support.customtabs.ICustomTabsCallback$Stub: android.support.customtabs.ICustomTabsCallback asInterface(android.os.IBinder)
com.benjaminabel.vibration.VibrationPlugin: VibrationPlugin()
xyz.luan.audioplayers.PlayerMode: xyz.luan.audioplayers.PlayerMode[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void repeatNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.lang.Boolean)
androidx.camera.core.featurecombination.impl.feature.VideoStabilizationFeature$StabilizationMode: androidx.camera.core.featurecombination.impl.feature.VideoStabilizationFeature$StabilizationMode[] values()
androidx.media3.exoplayer.drm.FrameworkMediaDrm$Api31: void setLogSessionIdOnMediaDrmSession(android.media.MediaDrm,byte[],androidx.media3.exoplayer.analytics.PlayerId)
io.flutter.plugins.inapppurchase.Messages$PlatformRecurrenceMode: io.flutter.plugins.inapppurchase.Messages$PlatformRecurrenceMode[] values()
io.flutter.embedding.engine.FlutterJNI: void registerTexture(long,io.flutter.embedding.engine.renderer.SurfaceTextureWrapper)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: android.util.DisplayMetrics getRearDisplayMetrics()
com.dexterous.flutterlocalnotifications.models.Time: Time()
com.google.firebase.FirebaseCommonKtxRegistrar: java.util.List getComponents()
androidx.appcompat.widget.SearchView: void setOnQueryTextListener(androidx.appcompat.widget.SearchView$OnQueryTextListener)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readChannelInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
com.dexterous.flutterlocalnotifications.models.BitmapSource: com.dexterous.flutterlocalnotifications.models.BitmapSource[] $values()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.recyclerview.widget.RecyclerView: void setChildDrawingOrderCallback(androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
io.flutter.embedding.engine.FlutterJNI: void nativeInit(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long,int)
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void showNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: java.util.Map access$200(com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory)
androidx.work.impl.background.systemjob.SystemJobService: SystemJobService()
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
io.flutter.plugins.inapppurchase.Messages$PlatformRecurrenceMode: io.flutter.plugins.inapppurchase.Messages$PlatformRecurrenceMode valueOf(java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void unregisterOnMeasurementEventListener(com.google.android.gms.internal.measurement.zzda)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
androidx.appcompat.widget.AppCompatSpinner: int getDropDownHorizontalOffset()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: android.app.Notification createNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
com.google.firebase.installations.local.PersistedInstallation$RegistrationStatus: com.google.firebase.installations.local.PersistedInstallation$RegistrationStatus valueOf(java.lang.String)
com.google.firebase.messaging.threads.ThreadPriority: com.google.firebase.messaging.threads.ThreadPriority[] values()
io.flutter.embedding.engine.FlutterJNI: void onFirstFrame()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState detachState()
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
androidx.core.content.ContextCompat$Api23Impl: java.lang.String getSystemServiceName(android.content.Context,java.lang.Class)
com.baseflow.permissionhandler.PermissionHandlerPlugin: PermissionHandlerPlugin()
androidx.room.RoomDatabase$JournalMode: androidx.room.RoomDatabase$JournalMode[] values()
androidx.core.app.ActivityCompat$Api31Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
androidx.camera.core.ImageProcessingUtil$Result: androidx.camera.core.ImageProcessingUtil$Result valueOf(java.lang.String)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManagerImplCommon: java.lang.Object registerSource(androidx.privacysandbox.ads.adservices.measurement.SourceRegistrationRequest,kotlin.coroutines.Continuation)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterTexture(long,long,java.lang.ref.WeakReference)
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
com.llfbandit.app_links.AppLinksPlugin: AppLinksPlugin()
androidx.core.app.NotificationCompat$MessagingStyle$Api24Impl: android.app.Notification$MessagingStyle createMessagingStyle(java.lang.CharSequence)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType valueOf(java.lang.String)
androidx.camera.core.featurecombination.impl.feature.VideoStabilizationFeature$StabilizationMode: androidx.camera.core.featurecombination.impl.feature.VideoStabilizationFeature$StabilizationMode valueOf(java.lang.String)
androidx.camera.core.internal.compat.quirk.ImageCaptureFailedForSpecificCombinationQuirk: ImageCaptureFailedForSpecificCombinationQuirk()
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: java.lang.String getType(android.net.Uri)
com.dexterous.flutterlocalnotifications.models.NotificationDetails: void readGroupingInformation(com.dexterous.flutterlocalnotifications.models.NotificationDetails,java.util.Map)
xyz.luan.audioplayers.AudioplayersPlugin: AudioplayersPlugin()
androidx.core.location.LocationCompat$Api26Impl: void removeBearingAccuracy(android.location.Location)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setTextSelectable(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.security.crypto.EncryptedSharedPreferences$PrefValueEncryptionScheme: androidx.security.crypto.EncryptedSharedPreferences$PrefValueEncryptionScheme[] values()
androidx.core.widget.TextViewCompat$Api28Impl: void setFirstBaselineToTopHeight(android.widget.TextView,int)
com.google.android.datatransport.cct.internal.ClientInfo$ClientType: com.google.android.datatransport.cct.internal.ClientInfo$ClientType[] values()
androidx.camera.camera2.internal.compat.quirk.CaptureSessionStuckQuirk: CaptureSessionStuckQuirk()
io.flutter.plugins.videoplayer.VideoPlayerPlugin: VideoPlayerPlugin()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void onDetachedFromEngine(io.flutter.embedding.engine.plugins.FlutterPlugin$FlutterPluginBinding)
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface: java.lang.reflect.InvocationHandler getServiceWorkerWebSettings()
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.biometric.BiometricFragment$Api28Impl: android.hardware.biometrics.BiometricPrompt buildPrompt(android.hardware.biometrics.BiometricPrompt$Builder)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet,int)
androidx.camera.core.CameraX$InternalInitState: androidx.camera.core.CameraX$InternalInitState valueOf(java.lang.String)
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.media.AudioAttributesImplBaseParcelizer: void write(androidx.media.AudioAttributesImplBase,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
androidx.appcompat.widget.SearchView: int getPreferredHeight()
androidx.appcompat.widget.SwitchCompat: void setThumbTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
com.google.firebase.installations.FirebaseInstallationsException$Status: com.google.firebase.installations.FirebaseInstallationsException$Status[] values()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void zonedScheduleNextNotification(android.content.Context,com.dexterous.flutterlocalnotifications.models.NotificationDetails)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
io.flutter.plugins.localauth.Messages$AuthClassification: io.flutter.plugins.localauth.Messages$AuthClassification[] values()
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: boolean stop(java.io.OutputStream,java.util.concurrent.Executor)
io.flutter.plugins.camerax.CameraXFlashMode: io.flutter.plugins.camerax.CameraXFlashMode valueOf(java.lang.String)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.net.Uri cache(byte[],java.lang.String,java.lang.String)
com.google.gson.ReflectionAccessFilter$FilterResult: com.google.gson.ReflectionAccessFilter$FilterResult valueOf(java.lang.String)
androidx.lifecycle.ReportFragment: ReportFragment()
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setBadgeIconType(android.app.Notification$Builder,int)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
androidx.appcompat.widget.AppCompatEditText: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setEnterpriseAuthenticationAppLinkPolicyEnabled(boolean)
io.flutter.embedding.engine.FlutterJNI: boolean nativeIsSurfaceControlEnabled(long)
androidx.appcompat.widget.SwitchCompat: int getThumbTextPadding()
com.dexterous.flutterlocalnotifications.RuntimeTypeAdapterFactory: com.google.gson.TypeAdapter create(com.google.gson.Gson,com.google.gson.reflect.TypeToken)
com.google.firebase.messaging.FirebaseMessagingService: FirebaseMessagingService()
androidx.webkit.WebViewClientCompat: WebViewClientCompat()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void createNotificationChannelGroup(io.flutter.plugin.common.MethodCall,io.flutter.plugin.common.MethodChannel$Result)
androidx.biometric.BiometricFragment$Api21Impl: android.content.Intent createConfirmDeviceCredentialIntent(android.app.KeyguardManager,java.lang.CharSequence,java.lang.CharSequence)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setCategory(com.dexterous.flutterlocalnotifications.models.NotificationDetails,androidx.core.app.NotificationCompat$Builder)
androidx.core.app.NotificationCompatBuilder$Api31Impl: android.app.Notification$Action$Builder setAuthenticationRequired(android.app.Notification$Action$Builder,boolean)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getForceDark()
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: void lambda$getPluginConstantsForFirebaseApp$0(com.google.firebase.FirebaseApp,com.google.android.gms.tasks.TaskCompletionSource)
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.security.crypto.EncryptedSharedPreferences$PrefKeyEncryptionScheme: androidx.security.crypto.EncryptedSharedPreferences$PrefKeyEncryptionScheme valueOf(java.lang.String)
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
androidx.core.widget.TextViewCompat$Api23Impl: void setHyphenationFrequency(android.widget.TextView,int)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.util.ArrayList loadScheduledNotifications(android.content.Context)
androidx.camera.core.impl.CameraCaptureMetaData$AwbMode: androidx.camera.core.impl.CameraCaptureMetaData$AwbMode valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatEditText: void setTextClassifier(android.view.textclassifier.TextClassifier)
androidx.core.view.ViewGroupCompat$Api21Impl: boolean isTransitionGroup(android.view.ViewGroup)
androidx.core.location.LocationCompat$Api26Impl: boolean hasBearingAccuracy(android.location.Location)
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: void setActivity(android.app.Activity)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder valueOf(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: AccessibilityViewEmbedder(android.view.View,int)
com.google.firebase.iid.FirebaseInstanceIdReceiver: FirebaseInstanceIdReceiver()
androidx.core.graphics.Insets$Api29Impl: android.graphics.Insets of(int,int,int,int)
androidx.biometric.BiometricManager$Api29Impl: int canAuthenticate(android.hardware.biometrics.BiometricManager)
androidx.core.app.NotificationCompat$CallStyle$Api31Impl: android.app.Notification$CallStyle forScreeningCall(android.app.Person,android.app.PendingIntent,android.app.PendingIntent)
androidx.work.Worker: Worker(android.content.Context,androidx.work.WorkerParameters)
kotlin.time.DurationUnit: kotlin.time.DurationUnit[] values()
com.google.android.gms.common.util.zzb: android.os.StrictMode$VmPolicy$Builder zza(android.os.StrictMode$VmPolicy$Builder)
androidx.preference.PreferenceCategory: PreferenceCategory(android.content.Context,android.util.AttributeSet)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase valueOf(java.lang.String)
androidx.media3.exoplayer.mediacodec.MediaCodecRenderer$Api21: boolean registerOnBufferAvailableListener(androidx.media3.exoplayer.mediacodec.MediaCodecAdapter,androidx.media3.exoplayer.mediacodec.MediaCodecRenderer$MediaCodecRendererCodecAdapterListener)
io.flutter.view.TextureRegistry$SurfaceProducer: android.view.Surface getSurface()
com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin: java.lang.Integer tryParseInt(java.lang.String)
androidx.camera.lifecycle.LifecycleCameraRepository$LifecycleCameraRepositoryObserver: void onStop(androidx.lifecycle.LifecycleOwner)
androidx.browser.browseractions.BrowserActionsFallbackMenuView: BrowserActionsFallbackMenuView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeaturesInNative(int)
io.flutter.plugin.platform.PlatformViewWrapper: android.view.ViewTreeObserver$OnGlobalFocusChangeListener getActiveFocusListener()
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type valueOf(java.lang.String)
