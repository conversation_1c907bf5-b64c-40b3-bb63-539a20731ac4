import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../services/database_service.dart';
import '../../theme/app_theme.dart';
import '../../screens/comprehensive_testing_screen.dart';

class DatabaseTestingWidget extends StatefulWidget {
  final Function(TestingStatus) onStatusChanged;

  const DatabaseTestingWidget({
    super.key,
    required this.onStatusChanged,
  });

  @override
  State<DatabaseTestingWidget> createState() => _DatabaseTestingWidgetState();
}

class _DatabaseTestingWidgetState extends State<DatabaseTestingWidget> {
  final DatabaseService _databaseService = DatabaseService();
  
  final Map<String, TestResult> _testResults = {};
  bool _isRunningTests = false;
  int _currentTestRound = 0;
  final int _totalRounds = 5;

  final List<DatabaseTest> _databaseTests = [
    DatabaseTest(
      id: 'database_initialization',
      nameAr: 'تهيئة قاعدة البيانات',
      nameEn: 'Database Initialization',
      description: 'Test database initialization and setup',
    ),
    DatabaseTest(
      id: 'offline_first_operations',
      nameAr: 'العمليات دون اتصال',
      nameEn: 'Offline-first Operations',
      description: 'Test offline-first database operations',
    ),
    DatabaseTest(
      id: 'crud_operations',
      nameAr: 'العمليات الأساسية',
      nameEn: 'CRUD Operations',
      description: 'Test Create, Read, Update, Delete operations',
    ),
    DatabaseTest(
      id: 'data_integrity',
      nameAr: 'سلامة البيانات',
      nameEn: 'Data Integrity',
      description: 'Test data integrity and validation',
    ),
    DatabaseTest(
      id: 'backup_restore',
      nameAr: 'النسخ الاحتياطي والاستعادة',
      nameEn: 'Backup & Restore',
      description: 'Test backup and restore functionality',
    ),
    DatabaseTest(
      id: 'performance_testing',
      nameAr: 'اختبار الأداء',
      nameEn: 'Performance Testing',
      description: 'Test database performance with large datasets',
    ),
    DatabaseTest(
      id: 'migration_testing',
      nameAr: 'اختبار الترحيل',
      nameEn: 'Migration Testing',
      description: 'Test database schema migrations',
    ),
    DatabaseTest(
      id: 'concurrent_access',
      nameAr: 'الوصول المتزامن',
      nameEn: 'Concurrent Access',
      description: 'Test concurrent database access',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeTestResults();
  }

  void _initializeTestResults() {
    for (final test in _databaseTests) {
      _testResults[test.id] = TestResult.notStarted;
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(languageService),
          const SizedBox(height: 24),
          _buildTestControls(languageService),
          const SizedBox(height: 24),
          _buildDatabaseStatus(languageService),
          const SizedBox(height: 24),
          _buildTestResults(languageService),
          const SizedBox(height: 24),
          if (_isRunningTests) _buildRoundProgress(languageService),
        ],
      ),
    );
  }

  Widget _buildHeader(LanguageService languageService) {
    final passedTests = _testResults.values.where((result) => result == TestResult.passed).length;
    final totalTests = _testResults.length;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.brown,
            Colors.brown.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.storage, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  languageService.isArabic ? 'اختبار قاعدة البيانات' : 'Database Testing',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            languageService.isArabic 
                ? 'اختبار شامل لقاعدة البيانات والعمليات دون اتصال'
                : 'Comprehensive testing of database and offline operations',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageService.isArabic ? 'التقدم' : 'Progress',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '$passedTests / $totalTests',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: passedTests / totalTests,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildTestControls(LanguageService languageService) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isRunningTests ? null : _runAllTests,
            icon: _isRunningTests 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(
              _isRunningTests
                  ? (languageService.isArabic ? 'جاري التشغيل...' : 'Running...')
                  : (languageService.isArabic ? 'تشغيل جميع الاختبارات' : 'Run All Tests'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.brown,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: _isRunningTests ? null : _resetTests,
          icon: const Icon(Icons.refresh),
          label: Text(languageService.isArabic ? 'إعادة تعيين' : 'Reset'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildDatabaseStatus(LanguageService languageService) {
    return FutureBuilder<Map<String, dynamic>>(
      future: _getDatabaseStatus(),
      builder: (context, snapshot) {
        final status = snapshot.data ?? {};
        final isHealthy = status['healthy'] ?? false;
        final recordCount = status['recordCount'] ?? 0;
        final dbSize = status['size'] ?? '0 KB';
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isHealthy ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isHealthy ? Colors.green : Colors.red,
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    isHealthy ? Icons.check_circle : Icons.error,
                    color: isHealthy ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      languageService.isArabic ? 'حالة قاعدة البيانات' : 'Database Status',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isHealthy ? Colors.green : Colors.red,
                      ),
                    ),
                  ),
                  Text(
                    isHealthy
                        ? (languageService.isArabic ? 'سليمة' : 'Healthy')
                        : (languageService.isArabic ? 'خطأ' : 'Error'),
                    style: TextStyle(
                      color: isHealthy ? Colors.green : Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      Text(
                        languageService.isArabic ? 'السجلات' : 'Records',
                        style: const TextStyle(fontSize: 12),
                      ),
                      Text(
                        '$recordCount',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Text(
                        languageService.isArabic ? 'الحجم' : 'Size',
                        style: const TextStyle(fontSize: 12),
                      ),
                      Text(
                        dbSize,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Text(
                        languageService.isArabic ? 'الإصدار' : 'Version',
                        style: const TextStyle(fontSize: 12),
                      ),
                      Text(
                        '${status['version'] ?? '1.0'}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTestResults(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'نتائج الاختبارات' : 'Test Results',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.brown,
          ),
        ),
        const SizedBox(height: 12),
        ...(_databaseTests.map((test) {
          final result = _testResults[test.id] ?? TestResult.notStarted;
          return _buildTestResultCard(test, result, languageService);
        }).toList()),
      ],
    );
  }

  Widget _buildTestResultCard(DatabaseTest test, TestResult result, LanguageService languageService) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (result) {
      case TestResult.passed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = languageService.isArabic ? 'نجح' : 'Passed';
        break;
      case TestResult.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = languageService.isArabic ? 'فشل' : 'Failed';
        break;
      case TestResult.inProgress:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = languageService.isArabic ? 'قيد التشغيل' : 'Running';
        break;
      case TestResult.notStarted:
        statusColor = Colors.grey;
        statusIcon = Icons.radio_button_unchecked;
        statusText = languageService.isArabic ? 'لم يبدأ' : 'Not Started';
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          languageService.isArabic ? test.nameAr : test.nameEn,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(test.description),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            if (result == TestResult.inProgress)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        onTap: () => _runSingleTest(test.id),
      ),
    );
  }

  Widget _buildRoundProgress(LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.brown.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.brown.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            languageService.isArabic 
                ? 'جولة الاختبار ${_currentTestRound + 1} من $_totalRounds'
                : 'Test Round ${_currentTestRound + 1} of $_totalRounds',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.brown,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentTestRound + 1) / _totalRounds,
            backgroundColor: Colors.brown.withValues(alpha: 0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.brown),
          ),
        ],
      ),
    );
  }

  Future<Map<String, dynamic>> _getDatabaseStatus() async {
    try {
      return await _databaseService.getStatus();
    } catch (e) {
      return {
        'healthy': false,
        'recordCount': 0,
        'size': '0 KB',
        'version': '1.0',
      };
    }
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTests = true;
      _currentTestRound = 0;
    });

    widget.onStatusChanged(TestingStatus.inProgress);

    // Run 5 rounds of testing as per requirements
    for (int round = 0; round < _totalRounds; round++) {
      setState(() {
        _currentTestRound = round;
      });

      for (final test in _databaseTests) {
        await _runSingleTestInternal(test.id);
        await Future.delayed(const Duration(milliseconds: 600));
      }

      await Future.delayed(const Duration(seconds: 1));
    }

    setState(() {
      _isRunningTests = false;
    });

    final allPassed = _testResults.values.every((result) => result == TestResult.passed);
    widget.onStatusChanged(allPassed ? TestingStatus.passed : TestingStatus.failed);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            allPassed
                ? (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'جميع اختبارات قاعدة البيانات نجحت!'
                    : 'All database tests passed!')
                : (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'بعض اختبارات قاعدة البيانات فشلت'
                    : 'Some database tests failed'),
          ),
          backgroundColor: allPassed ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _runSingleTest(String testId) async {
    await _runSingleTestInternal(testId);
  }

  Future<void> _runSingleTestInternal(String testId) async {
    setState(() {
      _testResults[testId] = TestResult.inProgress;
    });

    try {
      bool testPassed = false;

      switch (testId) {
        case 'database_initialization':
          testPassed = await _testDatabaseInitialization();
          break;
        case 'offline_first_operations':
          testPassed = await _testOfflineFirstOperations();
          break;
        case 'crud_operations':
          testPassed = await _testCrudOperations();
          break;
        case 'data_integrity':
          testPassed = await _testDataIntegrity();
          break;
        case 'backup_restore':
          testPassed = await _testBackupRestore();
          break;
        case 'performance_testing':
          testPassed = await _testPerformance();
          break;
        case 'migration_testing':
          testPassed = await _testMigration();
          break;
        case 'concurrent_access':
          testPassed = await _testConcurrentAccess();
          break;
      }

      setState(() {
        _testResults[testId] = testPassed ? TestResult.passed : TestResult.failed;
      });
    } catch (e) {
      setState(() {
        _testResults[testId] = TestResult.failed;
      });
    }
  }

  Future<bool> _testDatabaseInitialization() async {
    try {
      await _databaseService.initialize();
      await Future.delayed(const Duration(seconds: 1));
      return await _databaseService.isInitialized();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testOfflineFirstOperations() async {
    try {
      await _databaseService.testOfflineOperations();
      await Future.delayed(const Duration(seconds: 1));
      return await _databaseService.verifyOfflineOperations();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testCrudOperations() async {
    try {
      // Test Create
      await _databaseService.create('test_table', {'name': 'test', 'value': 123});
      
      // Test Read
      final records = await _databaseService.read('test_table');
      if (records.isEmpty) return false;
      
      // Test Update
      await _databaseService.update('test_table', records.first['id'], {'value': 456});
      
      // Test Delete
      await _databaseService.delete('test_table', records.first['id']);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testDataIntegrity() async {
    try {
      await _databaseService.testDataIntegrity();
      await Future.delayed(const Duration(seconds: 1));
      return await _databaseService.verifyDataIntegrity();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testBackupRestore() async {
    try {
      await _databaseService.createBackup();
      await _databaseService.restoreFromBackup();
      await Future.delayed(const Duration(seconds: 1));
      return await _databaseService.verifyBackupRestore();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testPerformance() async {
    try {
      final startTime = DateTime.now();
      await _databaseService.performLargeDataOperation();
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      return duration.inSeconds < 5; // Performance threshold
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testMigration() async {
    try {
      await _databaseService.testMigration();
      await Future.delayed(const Duration(seconds: 1));
      return await _databaseService.verifyMigration();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testConcurrentAccess() async {
    try {
      await _databaseService.testConcurrentAccess();
      await Future.delayed(const Duration(seconds: 2));
      return await _databaseService.verifyConcurrentAccess();
    } catch (e) {
      return false;
    }
  }

  void _resetTests() {
    setState(() {
      _initializeTestResults();
      _isRunningTests = false;
      _currentTestRound = 0;
    });
    widget.onStatusChanged(TestingStatus.notStarted);
  }
}

class DatabaseTest {
  final String id;
  final String nameAr;
  final String nameEn;
  final String description;

  DatabaseTest({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.description,
  });
}

enum TestResult {
  notStarted,
  inProgress,
  passed,
  failed,
}
