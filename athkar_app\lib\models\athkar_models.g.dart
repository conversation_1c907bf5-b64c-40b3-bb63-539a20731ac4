// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'athkar_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AthkarCategory _$AthkarCategoryFromJson(Map<String, dynamic> json) =>
    AthkarCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      color: json['color'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$AthkarCategoryToJson(AthkarCategory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'icon': instance.icon,
      'color': instance.color,
      'isDefault': instance.isDefault,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

AthkarRoutine _$AthkarRoutineFromJson(Map<String, dynamic> json) =>
    AthkarRoutine(
      id: json['id'] as String,
      userId: json['userId'] as String?,
      categoryId: json['categoryId'] as String?,
      title: json['title'] as String,
      description: json['description'] as String?,
      isPublic: json['isPublic'] as bool? ?? false,
      isFavorite: json['isFavorite'] as bool? ?? false,
      totalSteps: (json['totalSteps'] as num?)?.toInt() ?? 0,
      estimatedDuration: (json['estimatedDuration'] as num?)?.toInt(),
      colorHex: json['colorHex'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      steps: (json['steps'] as List<dynamic>?)
          ?.map((e) => AthkarStep.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AthkarRoutineToJson(AthkarRoutine instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'categoryId': instance.categoryId,
      'title': instance.title,
      'description': instance.description,
      'isPublic': instance.isPublic,
      'isFavorite': instance.isFavorite,
      'totalSteps': instance.totalSteps,
      'estimatedDuration': instance.estimatedDuration,
      'colorHex': instance.colorHex,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'steps': instance.steps,
    };

AthkarStep _$AthkarStepFromJson(Map<String, dynamic> json) => AthkarStep(
  id: json['id'] as String,
  routineId: json['routineId'] as String,
  stepOrder: (json['stepOrder'] as num).toInt(),
  arabicText: json['arabicText'] as String,
  transliteration: json['transliteration'] as String?,
  translation: json['translation'] as String?,
  targetCount: (json['targetCount'] as num?)?.toInt() ?? 1,
  audioUrl: json['audioUrl'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$AthkarStepToJson(AthkarStep instance) =>
    <String, dynamic>{
      'id': instance.id,
      'routineId': instance.routineId,
      'stepOrder': instance.stepOrder,
      'arabicText': instance.arabicText,
      'transliteration': instance.transliteration,
      'translation': instance.translation,
      'targetCount': instance.targetCount,
      'audioUrl': instance.audioUrl,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

UserProgress _$UserProgressFromJson(Map<String, dynamic> json) => UserProgress(
  id: json['id'] as String,
  userId: json['userId'] as String,
  routineId: json['routineId'] as String,
  stepId: json['stepId'] as String,
  currentCount: (json['currentCount'] as num?)?.toInt() ?? 0,
  completedAt: json['completedAt'] == null
      ? null
      : DateTime.parse(json['completedAt'] as String),
  sessionDate: DateTime.parse(json['sessionDate'] as String),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$UserProgressToJson(UserProgress instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'routineId': instance.routineId,
      'stepId': instance.stepId,
      'currentCount': instance.currentCount,
      'completedAt': instance.completedAt?.toIso8601String(),
      'sessionDate': instance.sessionDate.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

ReminderSetting _$ReminderSettingFromJson(Map<String, dynamic> json) =>
    ReminderSetting(
      id: json['id'] as String,
      userId: json['userId'] as String,
      routineId: json['routineId'] as String,
      isEnabled: json['isEnabled'] as bool? ?? true,
      reminderTimes:
          (json['reminderTimes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      daysOfWeek:
          (json['daysOfWeek'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      notificationTitle: json['notificationTitle'] as String?,
      notificationBody: json['notificationBody'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$ReminderSettingToJson(ReminderSetting instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'routineId': instance.routineId,
      'isEnabled': instance.isEnabled,
      'reminderTimes': instance.reminderTimes,
      'daysOfWeek': instance.daysOfWeek,
      'notificationTitle': instance.notificationTitle,
      'notificationBody': instance.notificationBody,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
  id: json['id'] as String,
  username: json['username'] as String?,
  fullName: json['fullName'] as String?,
  avatarUrl: json['avatarUrl'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'fullName': instance.fullName,
      'avatarUrl': instance.avatarUrl,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

TasbeehItem _$TasbeehItemFromJson(Map<String, dynamic> json) => TasbeehItem(
  id: json['id'] as String,
  arabicText: json['arabicText'] as String,
  transliteration: json['transliteration'] as String?,
  translation: json['translation'] as String?,
  category: json['category'] as String?,
  colorHex: json['colorHex'] as String?,
  isDefault: json['isDefault'] as bool? ?? false,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$TasbeehItemToJson(TasbeehItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'arabicText': instance.arabicText,
      'transliteration': instance.transliteration,
      'translation': instance.translation,
      'category': instance.category,
      'colorHex': instance.colorHex,
      'isDefault': instance.isDefault,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

TasbeehSession _$TasbeehSessionFromJson(Map<String, dynamic> json) =>
    TasbeehSession(
      id: json['id'] as String,
      userId: json['userId'] as String,
      tasbeehId: json['tasbeehId'] as String,
      targetCount: (json['targetCount'] as num).toInt(),
      currentCount: (json['currentCount'] as num?)?.toInt() ?? 0,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      isCompleted: json['isCompleted'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$TasbeehSessionToJson(TasbeehSession instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'tasbeehId': instance.tasbeehId,
      'targetCount': instance.targetCount,
      'currentCount': instance.currentCount,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'isCompleted': instance.isCompleted,
      'createdAt': instance.createdAt.toIso8601String(),
    };

DuaItem _$DuaItemFromJson(Map<String, dynamic> json) => DuaItem(
  id: json['id'] as String,
  title: json['title'] as String,
  arabicText: json['arabicText'] as String,
  transliteration: json['transliteration'] as String?,
  translation: json['translation'] as String?,
  category: json['category'] as String?,
  source: json['source'] as String?,
  reference: json['reference'] as String?,
  colorHex: json['colorHex'] as String?,
  isDefault: json['isDefault'] as bool? ?? false,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$DuaItemToJson(DuaItem instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'arabicText': instance.arabicText,
  'transliteration': instance.transliteration,
  'translation': instance.translation,
  'category': instance.category,
  'source': instance.source,
  'reference': instance.reference,
  'colorHex': instance.colorHex,
  'isDefault': instance.isDefault,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};
