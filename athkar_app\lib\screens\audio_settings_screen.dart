import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../services/audio_service.dart';
import '../widgets/loading_overlay.dart';

class AudioSettingsScreen extends StatefulWidget {
  const AudioSettingsScreen({super.key});

  @override
  State<AudioSettingsScreen> createState() => _AudioSettingsScreenState();
}

class _AudioSettingsScreenState extends State<AudioSettingsScreen> {
  bool _soundEnabled = true;
  String _selectedSoundTheme = 'default';
  List<String> _customSounds = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);
    
    try {
      _soundEnabled = AudioService.isSoundEnabled;
      _selectedSoundTheme = AudioService.currentSoundTheme;
      _customSounds = await AudioService.getCustomSounds();
    } catch (e) {
      debugPrint('Error loading audio settings: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _uploadCustomSound() async {
    setState(() => _isLoading = true);
    
    try {
      final soundPath = await AudioService.uploadCustomSound();
      if (soundPath != null) {
        await _loadSettings(); // Refresh the list
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم رفع الصوت المخصص بنجاح'),
              backgroundColor: AppTheme.primaryGreen,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في رفع الصوت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deleteCustomSound(String soundPath) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الصوت المخصص'),
        content: const Text('هل أنت متأكد من حذف هذا الصوت؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);
      
      try {
        final success = await AudioService.deleteCustomSound(soundPath);
        if (success) {
          await _loadSettings(); // Refresh the list
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف الصوت بنجاح'),
                backgroundColor: AppTheme.primaryGreen,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الصوت: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _testSound(String theme) async {
    try {
      await AudioService.testSoundTheme(theme);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تشغيل الصوت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _playCustomSound(String soundPath) async {
    try {
      await AudioService.playCustomSound(soundPath);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تشغيل الصوت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getSoundThemeDisplayName(String theme) {
    switch (theme) {
      case 'default':
        return 'افتراضي';
      case 'soft':
        return 'ناعم';
      case 'wood':
        return 'خشبي';
      case 'bell':
        return 'جرس';
      default:
        return theme;
    }
  }

  String _getCustomSoundDisplayName(String path) {
    return path.split('/').last;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الصوت'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Sound enabled toggle
            Card(
              child: SwitchListTile(
                title: const Text('تفعيل الأصوات'),
                subtitle: const Text('تفعيل أو إلغاء جميع أصوات التطبيق'),
                value: _soundEnabled,
                activeColor: AppTheme.primaryGreen,
                onChanged: (value) async {
                  setState(() => _soundEnabled = value);
                  await AudioService.setSoundEnabled(value);
                },
              ),
            ),

            const SizedBox(height: 16),

            // Sound themes section
            Card(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'أنماط الصوت المبنية مسبقاً',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  ...AudioService.getAvailableSoundThemes().map((theme) => 
                    RadioListTile<String>(
                      title: Text(_getSoundThemeDisplayName(theme)),
                      value: theme,
                      groupValue: _selectedSoundTheme,
                      activeColor: AppTheme.primaryGreen,
                      onChanged: _soundEnabled ? (value) async {
                        setState(() => _selectedSoundTheme = value!);
                        await AudioService.setSoundTheme(value!);
                      } : null,
                      secondary: IconButton(
                        icon: const Icon(Icons.play_arrow),
                        onPressed: _soundEnabled ? () => _testSound(theme) : null,
                        tooltip: 'تجربة الصوت',
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Custom sounds section
            Card(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'الأصوات المخصصة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _soundEnabled ? _uploadCustomSound : null,
                          icon: const Icon(Icons.upload_file),
                          label: const Text('رفع صوت'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryGreen,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (_customSounds.isEmpty)
                    const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text(
                        'لا توجد أصوات مخصصة. يمكنك رفع أصوات مخصصة من خلال الزر أعلاه.',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  else
                    ..._customSounds.map((soundPath) => ListTile(
                      title: Text(_getCustomSoundDisplayName(soundPath)),
                      subtitle: Text(soundPath),
                      leading: const Icon(Icons.audiotrack),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.play_arrow),
                            onPressed: _soundEnabled ? () => _playCustomSound(soundPath) : null,
                            tooltip: 'تشغيل',
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => _deleteCustomSound(soundPath),
                            tooltip: 'حذف',
                          ),
                        ],
                      ),
                    )),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Sound types section
            Card(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'أنواع الأصوات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  ListTile(
                    title: const Text('صوت النقر'),
                    subtitle: const Text('يُشغل عند الضغط على عداد الذكر'),
                    leading: const Icon(Icons.touch_app),
                    trailing: IconButton(
                      icon: const Icon(Icons.play_arrow),
                      onPressed: _soundEnabled ? AudioService.playClickSound : null,
                    ),
                  ),
                  ListTile(
                    title: const Text('صوت الإكمال'),
                    subtitle: const Text('يُشغل عند إكمال الذكر أو التسبيح'),
                    leading: const Icon(Icons.check_circle),
                    trailing: IconButton(
                      icon: const Icon(Icons.play_arrow),
                      onPressed: _soundEnabled ? AudioService.playCompletionSound : null,
                    ),
                  ),
                  ListTile(
                    title: const Text('صوت تذكير الصلاة'),
                    subtitle: const Text('يُشغل عند حان وقت الصلاة'),
                    leading: const Icon(Icons.access_time),
                    trailing: IconButton(
                      icon: const Icon(Icons.play_arrow),
                      onPressed: _soundEnabled ? () => AudioService.playPrayerReminderSound() : null,
                    ),
                  ),
                  ListTile(
                    title: const Text('صوت إكمال التسبيح'),
                    subtitle: const Text('يُشغل عند إكمال جلسة التسبيح'),
                    leading: const Icon(Icons.beenhere),
                    trailing: IconButton(
                      icon: const Icon(Icons.play_arrow),
                      onPressed: _soundEnabled ? AudioService.playTasbeehCompletionSound : null,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Instructions card
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تعليمات',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '• يمكنك رفع ملفات صوتية بصيغة MP3, WAV, أو M4A\n'
                      '• الأصوات المخصصة تُحفظ في ذاكرة الجهاز\n'
                      '• يمكنك تجربة الأصوات قبل اختيارها\n'
                      '• الأصوات تعمل مع العدادات العائمة أيضاً',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
