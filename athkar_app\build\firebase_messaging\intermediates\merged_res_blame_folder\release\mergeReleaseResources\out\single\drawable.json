[{"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_text_light_normal.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_text_light_normal.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/notification_bg_low.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-core-1.13.1-12:/drawable/notification_bg_low.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_text_dark_focused.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_text_dark_focused.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/notification_tile_bg.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-core-1.13.1-12:/drawable/notification_tile_bg.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_text_dark_normal.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_text_dark_normal.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_icon_dark_normal.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_icon_dark_normal.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/notification_icon_background.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-core-1.13.1-12:/drawable/notification_icon_background.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_text_light.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_text_light.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_icon_light.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_icon_light.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_icon_disabled.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_icon_disabled.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_icon_dark.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_icon_dark.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_icon_light_normal.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_icon_light_normal.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_text_disabled.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_text_disabled.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_icon_dark_focused.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_icon_dark_focused.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_icon_light_focused.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_icon_light_focused.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_text_dark.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_text_dark.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/notification_bg.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-core-1.13.1-12:/drawable/notification_bg.xml"}, {"merged": "io.flutter.plugins.firebase.messaging.firebase_messaging-release-31:/drawable/common_google_signin_btn_text_light_focused.xml", "source": "io.flutter.plugins.firebase.messaging.firebase_messaging-jetified-play-services-base-18.1.0-14:/drawable/common_google_signin_btn_text_light_focused.xml"}]