["D:\\projects\\12july\\athkar\\athkar_app\\lib\\generated\\l10n\\app_localizations_ar.dart", "D:\\projects\\12july\\athkar\\athkar_app\\lib\\generated\\l10n\\app_localizations_en.dart", "D:\\projects\\12july\\athkar\\athkar_app\\lib\\generated\\l10n\\app_localizations.dart", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\icons\\app_icon.svg", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\data\\jordan_prayer_times_2024.json", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\data\\prebuilt_content.json", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\data\\quran-simple.txt", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\data\\quran-uthmani.txt", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\data\\quran.txt", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\data\\sample_athkar.json", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\data\\tafseer.txt", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\sounds\\sounds_info.md", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\sql\\supabase_schema.sql", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages\\material_design_icons_flutter\\lib\\fonts\\materialdesignicons-webfont.ttf", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages\\wakelock_plus\\assets\\no_sleep.js", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\shaders\\ink_sparkle.frag", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.json", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.bin", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\FontManifest.json", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NOTICES.Z", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NativeAssetsManifest.json", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\armeabi-v7a\\app.so", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\arm64-v8a\\app.so", "D:\\projects\\12july\\athkar\\athkar_app\\build\\app\\intermediates\\flutter\\release\\x86_64\\app.so"]