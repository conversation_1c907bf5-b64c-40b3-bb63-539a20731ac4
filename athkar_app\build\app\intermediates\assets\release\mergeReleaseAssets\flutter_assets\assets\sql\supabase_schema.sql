-- Supabase Database Schema for Islamic Athkar App
-- This script creates all necessary tables and RLS policies

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables if they exist (for fresh setup)
DROP TABLE IF EXISTS user_progress CASCADE;
DROP TABLE IF EXISTS bookmarks CASCADE;
DROP TABLE IF EXISTS athkar_steps CASCADE;
DROP TABLE IF EXISTS athkar_routines CASCADE;
DROP TABLE IF EXISTS user_settings CASCADE;
DROP TABLE IF EXISTS user_preferences CASCADE;
DROP TABLE IF EXISTS prayer_times_jordan CASCADE;
DROP TABLE IF EXISTS prebuilt_content CASCADE;
DROP TABLE IF EXISTS user_profiles CASCADE;

-- Create user_profiles table
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    phone_number TEXT,
    avatar_url TEXT,
    location TEXT DEFAULT 'Amman, Jordan',
    language TEXT DEFAULT 'ar',
    theme TEXT DEFAULT 'light',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create athkar_routines table
CREATE TABLE athkar_routines (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    category TEXT DEFAULT 'custom',
    estimated_duration INTEGER,
    color_hex TEXT DEFAULT '2E7D32',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create athkar_steps table
CREATE TABLE athkar_steps (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    routine_id UUID REFERENCES athkar_routines(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    step_order INTEGER NOT NULL,
    arabic_text TEXT NOT NULL,
    transliteration TEXT,
    translation TEXT,
    target_count INTEGER DEFAULT 1,
    audio_url TEXT,
    color_hex TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_progress table
CREATE TABLE user_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    routine_id UUID REFERENCES athkar_routines(id) ON DELETE CASCADE,
    step_id UUID REFERENCES athkar_steps(id) ON DELETE CASCADE,
    current_count INTEGER DEFAULT 0,
    completed_at TIMESTAMP WITH TIME ZONE,
    session_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create bookmarks table
CREATE TABLE bookmarks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    content_type TEXT NOT NULL, -- 'routine', 'step', 'quran_verse', 'dua'
    content_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_settings table
CREATE TABLE user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    setting_key TEXT NOT NULL,
    setting_value TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, setting_key)
);

-- Create user_preferences table
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    prayer_notifications BOOLEAN DEFAULT true,
    athkar_reminders BOOLEAN DEFAULT true,
    sound_enabled BOOLEAN DEFAULT true,
    vibration_enabled BOOLEAN DEFAULT true,
    auto_sync BOOLEAN DEFAULT true,
    preferred_language TEXT DEFAULT 'ar',
    theme_mode TEXT DEFAULT 'light',
    font_size TEXT DEFAULT 'medium',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create prayer_times_jordan table
CREATE TABLE prayer_times_jordan (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    city TEXT NOT NULL,
    prayer_date DATE NOT NULL,
    fajr TIME NOT NULL,
    sunrise TIME NOT NULL,
    dhuhr TIME NOT NULL,
    asr TIME NOT NULL,
    maghrib TIME NOT NULL,
    isha TIME NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(city, prayer_date)
);

-- Create prebuilt_content table
CREATE TABLE prebuilt_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    arabic_text TEXT NOT NULL,
    transliteration TEXT,
    translation TEXT,
    description TEXT,
    category TEXT NOT NULL, -- 'athkar', 'dua', 'tasbeeh', 'routine'
    subcategory TEXT,
    difficulty TEXT DEFAULT 'beginner', -- 'beginner', 'intermediate', 'advanced'
    target_count INTEGER DEFAULT 1,
    source TEXT, -- 'Quran', 'Hadith', etc.
    reference TEXT, -- Specific verse or hadith reference
    tags TEXT[], -- Array of tags
    audio_url TEXT,
    color_hex TEXT DEFAULT '2E7D32',
    is_popular BOOLEAN DEFAULT false,
    usage_count INTEGER DEFAULT 0,
    steps JSONB, -- For routines with multiple steps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_email ON user_profiles(email);
CREATE INDEX idx_athkar_routines_user_id ON athkar_routines(user_id);
CREATE INDEX idx_athkar_routines_category ON athkar_routines(category);
CREATE INDEX idx_athkar_steps_routine_id ON athkar_steps(routine_id);
CREATE INDEX idx_athkar_steps_user_id ON athkar_steps(user_id);
CREATE INDEX idx_user_progress_user_id ON user_progress(user_id);
CREATE INDEX idx_user_progress_routine_id ON user_progress(routine_id);
CREATE INDEX idx_user_progress_session_date ON user_progress(session_date);
CREATE INDEX idx_bookmarks_user_id ON bookmarks(user_id);
CREATE INDEX idx_bookmarks_content_type ON bookmarks(content_type);
CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX idx_prayer_times_city_date ON prayer_times_jordan(city, prayer_date);
CREATE INDEX idx_prebuilt_content_category ON prebuilt_content(category);
CREATE INDEX idx_prebuilt_content_subcategory ON prebuilt_content(subcategory);
CREATE INDEX idx_prebuilt_content_tags ON prebuilt_content USING GIN(tags);

-- Enable Row Level Security on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE athkar_routines ENABLE ROW LEVEL SECURITY;
ALTER TABLE athkar_steps ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookmarks ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE prayer_times_jordan ENABLE ROW LEVEL SECURITY;
ALTER TABLE prebuilt_content ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_profiles
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own profile" ON user_profiles
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for athkar_routines
CREATE POLICY "Users can view own routines" ON athkar_routines
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own routines" ON athkar_routines
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own routines" ON athkar_routines
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own routines" ON athkar_routines
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for athkar_steps
CREATE POLICY "Users can view own steps" ON athkar_steps
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own steps" ON athkar_steps
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own steps" ON athkar_steps
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own steps" ON athkar_steps
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for user_progress
CREATE POLICY "Users can view own progress" ON user_progress
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own progress" ON user_progress
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own progress" ON user_progress
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own progress" ON user_progress
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for bookmarks
CREATE POLICY "Users can view own bookmarks" ON bookmarks
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own bookmarks" ON bookmarks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own bookmarks" ON bookmarks
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own bookmarks" ON bookmarks
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for user_settings
CREATE POLICY "Users can view own settings" ON user_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own settings" ON user_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own settings" ON user_settings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own settings" ON user_settings
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for user_preferences
CREATE POLICY "Users can view own preferences" ON user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own preferences" ON user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences" ON user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own preferences" ON user_preferences
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for prayer_times_jordan (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view prayer times" ON prayer_times_jordan
    FOR SELECT USING (auth.role() = 'authenticated');

-- Create RLS policies for prebuilt_content (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view prebuilt content" ON prebuilt_content
    FOR SELECT USING (auth.role() = 'authenticated');

-- Create functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_athkar_routines_updated_at BEFORE UPDATE ON athkar_routines FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_athkar_steps_updated_at BEFORE UPDATE ON athkar_steps FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_progress_updated_at BEFORE UPDATE ON user_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_settings_updated_at BEFORE UPDATE ON user_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_prebuilt_content_updated_at BEFORE UPDATE ON prebuilt_content FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample prayer times data for Jordan
INSERT INTO prayer_times_jordan (city, prayer_date, fajr, sunrise, dhuhr, asr, maghrib, isha, latitude, longitude) VALUES
('Amman', '2024-01-01', '05:45', '07:10', '12:15', '15:05', '17:20', '18:45', 31.9454, 35.9284),
('Amman', '2024-06-15', '04:15', '05:40', '12:25', '16:15', '19:10', '20:35', 31.9454, 35.9284),
('Amman', '2024-12-31', '05:55', '07:20', '12:15', '14:50', '17:10', '18:35', 31.9454, 35.9284),
('Irbid', '2024-01-01', '05:40', '07:05', '12:10', '15:00', '17:15', '18:40', 32.5556, 35.8500),
('Irbid', '2024-06-15', '04:10', '05:35', '12:20', '16:10', '19:05', '20:30', 32.5556, 35.8500),
('Aqaba', '2024-01-01', '05:50', '07:15', '12:20', '15:10', '17:25', '18:50', 29.5320, 35.0063),
('Aqaba', '2024-06-15', '04:20', '05:45', '12:30', '16:20', '19:15', '20:40', 29.5320, 35.0063);

-- Insert sample prebuilt content
INSERT INTO prebuilt_content (title, arabic_text, transliteration, translation, description, category, subcategory, difficulty, target_count, source, reference, tags, color_hex, is_popular) VALUES
('Morning Protection', 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ', 'A''udhu billahi min ash-shaytani''r-rajim', 'I seek refuge in Allah from Satan, the accursed', 'Seeking protection from Satan at the start of the day', 'athkar', 'morning', 'beginner', 3, 'Quran', '16:98', ARRAY['morning', 'protection', 'daily'], '2E7D32', true),
('Bismillah', 'بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ', 'Bismillahi''r-rahmani''r-rahim', 'In the name of Allah, the Most Gracious, the Most Merciful', 'The opening phrase of the Quran, used to begin any good deed', 'athkar', 'general', 'beginner', 1, 'Quran', '1:1', ARRAY['general', 'blessing', 'daily'], '1976D2', true),
('Subhan Allah', 'سُبْحَانَ اللَّهِ', 'Subhan Allah', 'Glory be to Allah', 'Glorifying Allah, acknowledging His perfection', 'tasbeeh', 'subhan_allah', 'beginner', 33, 'Hadith', 'Sahih Muslim', ARRAY['glorification', 'daily', 'remembrance'], '2E7D32', true),
('Alhamdulillah', 'الْحَمْدُ لِلَّهِ', 'Alhamdulillah', 'All praise is due to Allah', 'Praising Allah for all His blessings', 'tasbeeh', 'alhamdulillah', 'beginner', 33, 'Hadith', 'Sahih Muslim', ARRAY['praise', 'gratitude', 'daily'], '1976D2', true),
('Dua for Forgiveness', 'رَبِّ اغْفِرْ لِي ذَنْبِي وَخَطَئِي وَجَهْلِي', 'Rabbi''ghfir li dhanbi wa khata''i wa jahli', 'My Lord, forgive me my sin, my error, and my ignorance', 'A comprehensive dua for seeking Allah''s forgiveness', 'dua', 'forgiveness', 'beginner', 3, 'Hadith', 'Sahih Bukhari', ARRAY['forgiveness', 'repentance', 'daily'], 'E91E63', true);

-- Create function to get current user ID
CREATE OR REPLACE FUNCTION get_current_user_id()
RETURNS UUID AS $$
BEGIN
    RETURN auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
