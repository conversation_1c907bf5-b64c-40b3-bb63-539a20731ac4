import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/view_models.dart';
import '../../services/language_service.dart';
import '../../theme/app_theme.dart';

class LayoutOptionsWidget extends StatefulWidget {
  final ViewConfiguration configuration;
  final Function(ViewConfiguration) onConfigurationChanged;

  const LayoutOptionsWidget({
    super.key,
    required this.configuration,
    required this.onConfigurationChanged,
  });

  @override
  State<LayoutOptionsWidget> createState() => _LayoutOptionsWidgetState();
}

class _LayoutOptionsWidgetState extends State<LayoutOptionsWidget> {
  late ViewConfiguration _config;

  @override
  void initState() {
    super.initState();
    _config = widget.configuration;
  }

  void _updateConfig(ViewConfiguration newConfig) {
    setState(() {
      _config = newConfig;
    });
    widget.onConfigurationChanged(newConfig);
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Grid Columns (for grid view)
          if (_config.viewMode == ViewMode.grid) ...[
            _buildSectionTitle(
              languageService.isArabic ? 'عدد الأعمدة' : 'Grid Columns',
              Icons.grid_view,
            ),
            const SizedBox(height: 12),
            _buildGridColumnsSelector(languageService),
            const SizedBox(height: 24),
          ],

          // Card Elevation
          _buildSectionTitle(
            languageService.isArabic ? 'ارتفاع البطاقات' : 'Card Elevation',
            Icons.layers,
          ),
          const SizedBox(height: 12),
          _buildElevationSlider(languageService),
          const SizedBox(height: 24),

          // Border Radius
          _buildSectionTitle(
            languageService.isArabic ? 'انحناء الحواف' : 'Border Radius',
            Icons.rounded_corner,
          ),
          const SizedBox(height: 12),
          _buildBorderRadiusSlider(languageService),
          const SizedBox(height: 24),

          // Content Padding
          _buildSectionTitle(
            languageService.isArabic ? 'المسافات الداخلية' : 'Content Padding',
            Icons.space_bar,
          ),
          const SizedBox(height: 12),
          _buildPaddingControls(languageService),
          const SizedBox(height: 24),

          // Layout Preview
          _buildSectionTitle(
            languageService.isArabic ? 'معاينة التخطيط' : 'Layout Preview',
            Icons.preview,
          ),
          const SizedBox(height: 12),
          _buildLayoutPreview(languageService),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: AppTheme.primaryGreen,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
      ],
    );
  }

  Widget _buildGridColumnsSelector(LanguageService languageService) {
    return Row(
      children: [1, 2, 3, 4].map((columns) {
        final isSelected = _config.gridColumns == columns;
        return Expanded(
          child: GestureDetector(
            onTap: () {
              _updateConfig(_config.copyWith(gridColumns: columns));
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.primaryGreen : Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? AppTheme.primaryGreen : Colors.grey[400]!,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.grid_view,
                    color: isSelected ? Colors.white : Colors.grey[700],
                    size: 20,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    columns.toString(),
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.grey[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildElevationSlider(LanguageService languageService) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              languageService.isArabic ? 'مسطح' : 'Flat',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              _config.cardElevation.toStringAsFixed(1),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryGreen,
              ),
            ),
            Text(
              languageService.isArabic ? 'مرتفع' : 'Elevated',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        Slider(
          value: _config.cardElevation,
          min: 0.0,
          max: 8.0,
          divisions: 16,
          onChanged: (value) {
            _updateConfig(_config.copyWith(cardElevation: value));
          },
          activeColor: AppTheme.primaryGreen,
        ),
        // Preview card
        SizedBox(
          width: double.infinity,
          height: 60,
          child: Card(
            elevation: _config.cardElevation,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_config.borderRadius),
            ),
            child: Center(
              child: Text(
                languageService.isArabic ? 'معاينة البطاقة' : 'Card Preview',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBorderRadiusSlider(LanguageService languageService) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              languageService.isArabic ? 'مربع' : 'Square',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              '${_config.borderRadius.toInt()}px',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryGreen,
              ),
            ),
            Text(
              languageService.isArabic ? 'دائري' : 'Rounded',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        Slider(
          value: _config.borderRadius,
          min: 0.0,
          max: 20.0,
          divisions: 20,
          onChanged: (value) {
            _updateConfig(_config.copyWith(borderRadius: value));
          },
          activeColor: AppTheme.primaryGreen,
        ),
        // Preview shapes
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(_config.borderRadius),
                border: Border.all(color: AppTheme.primaryGreen),
              ),
            ),
            Container(
              width: 80,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(_config.borderRadius),
                border: Border.all(color: AppTheme.primaryGreen),
              ),
            ),
            Container(
              width: 60,
              height: 30,
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(_config.borderRadius),
                border: Border.all(color: AppTheme.primaryGreen),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPaddingControls(LanguageService languageService) {
    return Column(
      children: [
        // All sides padding
        Row(
          children: [
            Expanded(
              child: Text(
                languageService.isArabic ? 'المسافة الشاملة' : 'All Sides',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
            Text(
              '${_config.contentPadding.left.toInt()}px',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryGreen,
              ),
            ),
          ],
        ),
        Slider(
          value: _config.contentPadding.left,
          min: 8.0,
          max: 32.0,
          divisions: 24,
          onChanged: (value) {
            _updateConfig(_config.copyWith(
              contentPadding: EdgeInsets.all(value),
            ));
          },
          activeColor: AppTheme.primaryGreen,
        ),
        
        const SizedBox(height: 16),
        
        // Individual padding controls
        Text(
          languageService.isArabic ? 'تحكم فردي' : 'Individual Control',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 8),
        
        // Top padding
        _buildIndividualPaddingControl(
          languageService.isArabic ? 'أعلى' : 'Top',
          _config.contentPadding.top,
          (value) {
            _updateConfig(_config.copyWith(
              contentPadding: EdgeInsets.only(
                top: value,
                right: _config.contentPadding.right,
                bottom: _config.contentPadding.bottom,
                left: _config.contentPadding.left,
              ),
            ));
          },
        ),
        
        // Horizontal paddings
        Row(
          children: [
            Expanded(
              child: _buildIndividualPaddingControl(
                languageService.isArabic ? 'يسار' : 'Left',
                _config.contentPadding.left,
                (value) {
                  _updateConfig(_config.copyWith(
                    contentPadding: EdgeInsets.only(
                      top: _config.contentPadding.top,
                      right: _config.contentPadding.right,
                      bottom: _config.contentPadding.bottom,
                      left: value,
                    ),
                  ));
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildIndividualPaddingControl(
                languageService.isArabic ? 'يمين' : 'Right',
                _config.contentPadding.right,
                (value) {
                  _updateConfig(_config.copyWith(
                    contentPadding: EdgeInsets.only(
                      top: _config.contentPadding.top,
                      right: value,
                      bottom: _config.contentPadding.bottom,
                      left: _config.contentPadding.left,
                    ),
                  ));
                },
              ),
            ),
          ],
        ),
        
        // Bottom padding
        _buildIndividualPaddingControl(
          languageService.isArabic ? 'أسفل' : 'Bottom',
          _config.contentPadding.bottom,
          (value) {
            _updateConfig(_config.copyWith(
              contentPadding: EdgeInsets.only(
                top: _config.contentPadding.top,
                right: _config.contentPadding.right,
                bottom: value,
                left: _config.contentPadding.left,
              ),
            ));
          },
        ),
      ],
    );
  }

  Widget _buildIndividualPaddingControl(
    String label,
    double value,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              '${value.toInt()}px',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryGreen,
              ),
            ),
          ],
        ),
        Slider(
          value: value,
          min: 0.0,
          max: 32.0,
          divisions: 32,
          onChanged: onChanged,
          activeColor: AppTheme.primaryGreen,
        ),
      ],
    );
  }

  Widget _buildLayoutPreview(LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageService.isArabic ? 'معاينة التخطيط الحالي' : 'Current Layout Preview',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 12),
          
          // Preview based on view mode
          if (_config.viewMode == ViewMode.list)
            _buildListPreview()
          else if (_config.viewMode == ViewMode.grid)
            _buildGridPreview()
          else if (_config.viewMode == ViewMode.card)
            _buildCardPreview()
          else
            _buildCompactPreview(),
        ],
      ),
    );
  }

  Widget _buildListPreview() {
    return Column(
      children: List.generate(3, (index) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Card(
            elevation: _config.cardElevation,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_config.borderRadius),
            ),
            child: Container(
              padding: _config.contentPadding,
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(_config.borderRadius / 2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 16,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          height: 12,
                          width: 120,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildGridPreview() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _config.gridColumns,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1.2,
      ),
      itemCount: 6,
      itemBuilder: (context, index) {
        return Card(
          elevation: _config.cardElevation,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_config.borderRadius),
          ),
          child: Container(
            padding: _config.contentPadding / 2,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(_config.borderRadius / 2),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 12,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCardPreview() {
    return Card(
      elevation: _config.cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_config.borderRadius),
      ),
      child: Container(
        padding: _config.contentPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 20,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(height: 12),
            Container(
              height: 60,
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(_config.borderRadius / 2),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 60,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactPreview() {
    return Column(
      children: List.generate(4, (index) {
        return Container(
          margin: const EdgeInsets.only(bottom: 4),
          padding: _config.contentPadding / 2,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(_config.borderRadius),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(_config.borderRadius / 4),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Container(
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}
