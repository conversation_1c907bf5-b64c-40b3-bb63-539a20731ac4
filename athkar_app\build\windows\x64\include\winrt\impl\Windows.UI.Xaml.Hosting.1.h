// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Hosting_1_H
#define WINRT_Windows_UI_Xaml_Hosting_1_H
#include "winrt/impl/Windows.UI.Xaml.Hosting.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Hosting
{
    struct __declspec(empty_bases) IElementCompositionPreview :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementCompositionPreview>
    {
        IElementCompositionPreview(std::nullptr_t = nullptr) noexcept {}
        IElementCompositionPreview(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IElementCompositionPreviewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementCompositionPreviewStatics>
    {
        IElementCompositionPreviewStatics(std::nullptr_t = nullptr) noexcept {}
        IElementCompositionPreviewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IElementCompositionPreviewStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementCompositionPreviewStatics2>
    {
        IElementCompositionPreviewStatics2(std::nullptr_t = nullptr) noexcept {}
        IElementCompositionPreviewStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IElementCompositionPreviewStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElementCompositionPreviewStatics3>
    {
        IElementCompositionPreviewStatics3(std::nullptr_t = nullptr) noexcept {}
        IElementCompositionPreviewStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
