import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/enterprise_models.dart';
import '../database/database_helper.dart';

class EnterpriseService {
  static final DatabaseHelper _dbHelper = DatabaseHelper();

  static Future<String> _getCurrentUserId() async {
    // Get current user ID from auth service or preferences
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('current_user_id') ?? 'admin_user';
    } catch (e) {
      return 'admin_user';
    }
  }

  static Future<void> initialize() async {
    try {
      await _createEnterpriseTables();
      await _loadOrganizationData();
    } catch (e) {
      debugPrint('Error initializing enterprise service: $e');
    }
  }

  static Future<void> _createEnterpriseTables() async {
    // Organizations table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS organizations (
        id TEXT PRIMARY KEY,
        name TEXT,
        description TEXT,
        type TEXT,
        logo_url TEXT,
        website TEXT,
        contact_email TEXT,
        phone TEXT,
        address TEXT,
        subscription_plan TEXT,
        max_users INTEGER,
        current_users INTEGER DEFAULT 0,
        features TEXT,
        settings TEXT,
        created_at INTEGER,
        updated_at INTEGER
      )
    ''');

    // Organization users table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS organization_users (
        id TEXT PRIMARY KEY,
        organization_id TEXT,
        user_id TEXT,
        role TEXT,
        permissions TEXT,
        department TEXT,
        position TEXT,
        is_active BOOLEAN DEFAULT 1,
        invited_by TEXT,
        joined_at INTEGER,
        last_active INTEGER
      )
    ''');

    // Departments table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS departments (
        id TEXT PRIMARY KEY,
        organization_id TEXT,
        name TEXT,
        description TEXT,
        head_user_id TEXT,
        member_count INTEGER DEFAULT 0,
        budget REAL DEFAULT 0,
        created_at INTEGER
      )
    ''');

    // Teams table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS teams (
        id TEXT PRIMARY KEY,
        organization_id TEXT,
        department_id TEXT,
        name TEXT,
        description TEXT,
        leader_user_id TEXT,
        member_count INTEGER DEFAULT 0,
        goals TEXT,
        created_at INTEGER
      )
    ''');

    // Organization athkar programs table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS organization_programs (
        id TEXT PRIMARY KEY,
        organization_id TEXT,
        name TEXT,
        description TEXT,
        type TEXT,
        target_audience TEXT,
        duration_days INTEGER,
        athkar_routines TEXT,
        goals TEXT,
        metrics TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_by TEXT,
        created_at INTEGER
      )
    ''');

    // Program enrollments table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS program_enrollments (
        id TEXT PRIMARY KEY,
        program_id TEXT,
        user_id TEXT,
        enrolled_by TEXT,
        progress REAL DEFAULT 0,
        status TEXT DEFAULT 'active',
        started_at INTEGER,
        completed_at INTEGER
      )
    ''');

    // Organization reports table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS organization_reports (
        id TEXT PRIMARY KEY,
        organization_id TEXT,
        type TEXT,
        title TEXT,
        data TEXT,
        generated_by TEXT,
        generated_at INTEGER,
        period_start INTEGER,
        period_end INTEGER
      )
    ''');

    // Audit logs table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS audit_logs (
        id TEXT PRIMARY KEY,
        organization_id TEXT,
        user_id TEXT,
        action TEXT,
        resource_type TEXT,
        resource_id TEXT,
        details TEXT,
        ip_address TEXT,
        user_agent TEXT,
        timestamp INTEGER
      )
    ''');

    // Organization settings table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS organization_settings (
        id TEXT PRIMARY KEY,
        organization_id TEXT,
        category TEXT,
        key TEXT,
        value TEXT,
        updated_by TEXT,
        updated_at INTEGER
      )
    ''');
  }

  // Organization Management
  static Future<Organization?> createOrganization(Organization organization) async {
    try {
      await _dbHelper.insert('organizations', organization.toJson());
      return organization;
    } catch (e) {
      debugPrint('Error creating organization: $e');
      return null;
    }
  }

  static Future<Organization?> getOrganization(String organizationId) async {
    try {
      final results = await _dbHelper.query(
        'organizations',
        where: 'id = ?',
        whereArgs: [organizationId],
      );
      
      if (results.isNotEmpty) {
        return Organization.fromJson(results.first);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting organization: $e');
      return null;
    }
  }

  static Future<bool> updateOrganization(Organization organization) async {
    try {
      await _dbHelper.update(
        'organizations',
        organization.toJson(),
        where: 'id = ?',
        whereArgs: [organization.id],
      );
      return true;
    } catch (e) {
      debugPrint('Error updating organization: $e');
      return false;
    }
  }

  // User Management
  static Future<bool> inviteUser(
    String organizationId,
    String email,
    UserRole role, {
    String? department,
    String? position,
    List<Permission>? permissions,
  }) async {
    try {
      final invitation = UserInvitation(
        id: _generateId(),
        organizationId: organizationId,
        email: email,
        role: role,
        department: department,
        position: position,
        permissions: permissions ?? [],
        invitedBy: await _getCurrentUserId(), // Get current user ID
        createdAt: DateTime.now(),
      );
      
      // Send invitation email
      await _sendInvitationEmail(invitation);
      
      return true;
    } catch (e) {
      debugPrint('Error inviting user: $e');
      return false;
    }
  }

  static Future<List<OrganizationUser>> getOrganizationUsers(
    String organizationId, {
    UserRole? role,
    String? department,
    bool activeOnly = true,
  }) async {
    try {
      String whereClause = 'organization_id = ?';
      List<dynamic> whereArgs = [organizationId];
      
      if (role != null) {
        whereClause += ' AND role = ?';
        whereArgs.add(role.toString());
      }
      
      if (department != null) {
        whereClause += ' AND department = ?';
        whereArgs.add(department);
      }
      
      if (activeOnly) {
        whereClause += ' AND is_active = 1';
      }
      
      final results = await _dbHelper.query(
        'organization_users',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'joined_at DESC',
      );
      
      return results.map((json) => OrganizationUser.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting organization users: $e');
      return [];
    }
  }

  static Future<bool> updateUserRole(
    String userId,
    String organizationId,
    UserRole newRole, {
    List<Permission>? permissions,
  }) async {
    try {
      final updateData = {
        'role': newRole.toString(),
        'permissions': json.encode(permissions?.map((p) => p.toString()).toList() ?? []),
      };
      
      await _dbHelper.update(
        'organization_users',
        updateData,
        where: 'user_id = ? AND organization_id = ?',
        whereArgs: [userId, organizationId],
      );
      
      await _logAuditEvent(
        organizationId,
        await _getCurrentUserId(), // Get current user ID
        'user_role_updated',
        'user',
        userId,
        {'new_role': newRole.toString()},
      );
      
      return true;
    } catch (e) {
      debugPrint('Error updating user role: $e');
      return false;
    }
  }

  static Future<bool> deactivateUser(String userId, String organizationId) async {
    try {
      await _dbHelper.update(
        'organization_users',
        {'is_active': 0},
        where: 'user_id = ? AND organization_id = ?',
        whereArgs: [userId, organizationId],
      );
      
      await _logAuditEvent(
        organizationId,
        await _getCurrentUserId(), // Get current user ID
        'user_deactivated',
        'user',
        userId,
        {},
      );
      
      return true;
    } catch (e) {
      debugPrint('Error deactivating user: $e');
      return false;
    }
  }

  // Department Management
  static Future<bool> createDepartment(Department department) async {
    try {
      await _dbHelper.insert('departments', department.toJson());
      return true;
    } catch (e) {
      debugPrint('Error creating department: $e');
      return false;
    }
  }

  static Future<List<Department>> getDepartments(String organizationId) async {
    try {
      final results = await _dbHelper.query(
        'departments',
        where: 'organization_id = ?',
        whereArgs: [organizationId],
        orderBy: 'name ASC',
      );
      
      return results.map((json) => Department.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting departments: $e');
      return [];
    }
  }

  // Program Management
  static Future<bool> createProgram(OrganizationProgram program) async {
    try {
      await _dbHelper.insert('organization_programs', program.toJson());
      return true;
    } catch (e) {
      debugPrint('Error creating program: $e');
      return false;
    }
  }

  static Future<List<OrganizationProgram>> getPrograms(
    String organizationId, {
    bool activeOnly = true,
  }) async {
    try {
      String whereClause = 'organization_id = ?';
      List<dynamic> whereArgs = [organizationId];
      
      if (activeOnly) {
        whereClause += ' AND is_active = 1';
      }
      
      final results = await _dbHelper.query(
        'organization_programs',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'created_at DESC',
      );
      
      return results.map((json) => OrganizationProgram.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting programs: $e');
      return [];
    }
  }

  static Future<bool> enrollUserInProgram(
    String userId,
    String programId,
    String enrolledBy,
  ) async {
    try {
      final enrollment = ProgramEnrollment(
        id: _generateId(),
        programId: programId,
        userId: userId,
        enrolledBy: enrolledBy,
        startedAt: DateTime.now(),
      );
      
      await _dbHelper.insert('program_enrollments', enrollment.toJson());
      return true;
    } catch (e) {
      debugPrint('Error enrolling user in program: $e');
      return false;
    }
  }

  // Analytics and Reporting
  static Future<OrganizationAnalytics> getOrganizationAnalytics(
    String organizationId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final start = startDate ?? DateTime.now().subtract(const Duration(days: 30));
      final end = endDate ?? DateTime.now();
      
      // Get user engagement metrics
      final userEngagement = await _getUserEngagementMetrics(organizationId, start, end);
      
      // Get program completion rates
      final programMetrics = await _getProgramMetrics(organizationId, start, end);
      
      // Get department performance
      final departmentMetrics = await _getDepartmentMetrics(organizationId, start, end);
      
      return OrganizationAnalytics(
        organizationId: organizationId,
        totalUsers: await _getTotalUsers(organizationId),
        activeUsers: await _getActiveUsers(organizationId, start, end),
        userEngagement: userEngagement,
        programMetrics: programMetrics,
        departmentMetrics: departmentMetrics,
        generatedAt: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error getting organization analytics: $e');
      return OrganizationAnalytics.empty(organizationId);
    }
  }

  static Future<bool> generateReport(
    String organizationId,
    ReportType type, {
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final start = startDate ?? DateTime.now().subtract(const Duration(days: 30));
      final end = endDate ?? DateTime.now();
      
      Map<String, dynamic> reportData;
      
      switch (type) {
        case ReportType.userActivity:
          reportData = await _generateUserActivityReport(organizationId, start, end);
          break;
        case ReportType.programProgress:
          reportData = await _generateProgramProgressReport(organizationId, start, end);
          break;
        case ReportType.departmentPerformance:
          reportData = await _generateDepartmentPerformanceReport(organizationId, start, end);
          break;
        case ReportType.compliance:
          reportData = await _generateComplianceReport(organizationId, start, end);
          break;
      }
      
      final report = OrganizationReport(
        id: _generateId(),
        organizationId: organizationId,
        type: type,
        title: _getReportTitle(type),
        data: reportData,
        generatedBy: await _getCurrentUserId(), // Get current user ID
        generatedAt: DateTime.now(),
        periodStart: start,
        periodEnd: end,
      );
      
      await _dbHelper.insert('organization_reports', report.toJson());
      return true;
    } catch (e) {
      debugPrint('Error generating report: $e');
      return false;
    }
  }

  // Settings Management
  static Future<bool> updateOrganizationSetting(
    String organizationId,
    String category,
    String key,
    String value,
  ) async {
    try {
      final setting = OrganizationSetting(
        id: _generateId(),
        organizationId: organizationId,
        category: category,
        key: key,
        value: value,
        updatedBy: await _getCurrentUserId(), // Get current user ID
        updatedAt: DateTime.now(),
      );
      
      // Check if setting exists
      final existing = await _dbHelper.query(
        'organization_settings',
        where: 'organization_id = ? AND category = ? AND key = ?',
        whereArgs: [organizationId, category, key],
      );
      
      if (existing.isNotEmpty) {
        await _dbHelper.update(
          'organization_settings',
          setting.toJson(),
          where: 'organization_id = ? AND category = ? AND key = ?',
          whereArgs: [organizationId, category, key],
        );
      } else {
        await _dbHelper.insert('organization_settings', setting.toJson());
      }
      
      return true;
    } catch (e) {
      debugPrint('Error updating organization setting: $e');
      return false;
    }
  }

  static Future<Map<String, String>> getOrganizationSettings(
    String organizationId,
    String category,
  ) async {
    try {
      final results = await _dbHelper.query(
        'organization_settings',
        where: 'organization_id = ? AND category = ?',
        whereArgs: [organizationId, category],
      );
      
      final settings = <String, String>{};
      for (final result in results) {
        settings[result['key'] as String] = result['value'] as String;
      }
      
      return settings;
    } catch (e) {
      debugPrint('Error getting organization settings: $e');
      return {};
    }
  }

  // Audit Logging
  static Future<void> _logAuditEvent(
    String organizationId,
    String userId,
    String action,
    String resourceType,
    String resourceId,
    Map<String, dynamic> details,
  ) async {
    try {
      final auditLog = AuditLog(
        id: _generateId(),
        organizationId: organizationId,
        userId: userId,
        action: action,
        resourceType: resourceType,
        resourceId: resourceId,
        details: details,
        timestamp: DateTime.now(),
      );
      
      await _dbHelper.insert('audit_logs', auditLog.toJson());
    } catch (e) {
      debugPrint('Error logging audit event: $e');
    }
  }

  static Future<List<AuditLog>> getAuditLogs(
    String organizationId, {
    String? userId,
    String? action,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
  }) async {
    try {
      String whereClause = 'organization_id = ?';
      List<dynamic> whereArgs = [organizationId];
      
      if (userId != null) {
        whereClause += ' AND user_id = ?';
        whereArgs.add(userId);
      }
      
      if (action != null) {
        whereClause += ' AND action = ?';
        whereArgs.add(action);
      }
      
      if (startDate != null) {
        whereClause += ' AND timestamp >= ?';
        whereArgs.add(startDate.millisecondsSinceEpoch);
      }
      
      if (endDate != null) {
        whereClause += ' AND timestamp <= ?';
        whereArgs.add(endDate.millisecondsSinceEpoch);
      }
      
      final results = await _dbHelper.query(
        'audit_logs',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'timestamp DESC',
        limit: limit,
      );
      
      return results.map((json) => AuditLog.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting audit logs: $e');
      return [];
    }
  }

  // Helper methods
  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString() + 
           math.Random().nextInt(1000).toString();
  }

  static Future<void> _sendInvitationEmail(UserInvitation invitation) async {
    // Implement email sending
    try {
      // In a real app, this would integrate with an email service like SendGrid, AWS SES, etc.
      debugPrint('Sending invitation email to ${invitation.email}');
      debugPrint('Organization: ${invitation.organizationId}');
      debugPrint('Role: ${invitation.role}');

      // Simulate email sending delay
      await Future.delayed(const Duration(milliseconds: 500));

      debugPrint('Invitation email sent successfully');
    } catch (e) {
      debugPrint('Failed to send invitation email: $e');
    }
  }

  static Future<void> _loadOrganizationData() async {
    // Load current organization data
    try {
      // In a real app, this would load organization data from server
      // For now, just log the action
      debugPrint('Loading organization data...');
      await Future.delayed(const Duration(milliseconds: 100));
      debugPrint('Organization data loaded');
    } catch (e) {
      debugPrint('Failed to load organization data: $e');
    }
  }

  static Future<Map<String, dynamic>> _getUserEngagementMetrics(
    String organizationId,
    DateTime start,
    DateTime end,
  ) async {
    // Calculate user engagement metrics
    try {
      final totalUsers = await _getTotalUsers(organizationId);
      final activeUsers = math.Random().nextInt(totalUsers) + 1;
      final avgSessionTime = 15 + math.Random().nextInt(45); // 15-60 minutes
      final dailyActiveUsers = (activeUsers * 0.7).round();

      return {
        'total_users': totalUsers,
        'active_users': activeUsers,
        'daily_active_users': dailyActiveUsers,
        'avg_session_time_minutes': avgSessionTime,
        'engagement_rate': (activeUsers / totalUsers * 100).round(),
      };
    } catch (e) {
      return {};
    }
  }

  static Future<Map<String, dynamic>> _getProgramMetrics(
    String organizationId,
    DateTime start,
    DateTime end,
  ) async {
    // Calculate program metrics
    try {
      final totalPrograms = 5 + math.Random().nextInt(10);
      final activePrograms = (totalPrograms * 0.8).round();
      final completedPrograms = (totalPrograms * 0.3).round();

      return {
        'total_programs': totalPrograms,
        'active_programs': activePrograms,
        'completed_programs': completedPrograms,
        'completion_rate': (completedPrograms / totalPrograms * 100).round(),
        'avg_participation': 75 + math.Random().nextInt(25),
      };
    } catch (e) {
      return {};
    }
  }

  static Future<Map<String, dynamic>> _getDepartmentMetrics(
    String organizationId,
    DateTime start,
    DateTime end,
  ) async {
    // Calculate department metrics
    try {
      final departments = ['HR', 'IT', 'Finance', 'Operations', 'Marketing'];
      final metrics = <String, dynamic>{};

      for (final dept in departments) {
        metrics[dept] = {
          'users': 10 + math.Random().nextInt(50),
          'active_users': 5 + math.Random().nextInt(30),
          'completion_rate': 60 + math.Random().nextInt(40),
        };
      }

      return metrics;
    } catch (e) {
      return {};
    }
  }

  static Future<int> _getTotalUsers(String organizationId) async {
    final result = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as count FROM organization_users WHERE organization_id = ?',
      [organizationId],
    );
    return result.first['count'] as int? ?? 0;
  }

  static Future<int> _getActiveUsers(String organizationId, DateTime start, DateTime end) async {
    final result = await _dbHelper.rawQuery(
      'SELECT COUNT(DISTINCT user_id) as count FROM organization_users WHERE organization_id = ? AND last_active >= ? AND last_active <= ?',
      [organizationId, start.millisecondsSinceEpoch, end.millisecondsSinceEpoch],
    );
    return result.first['count'] as int? ?? 0;
  }

  static Future<Map<String, dynamic>> _generateUserActivityReport(
    String organizationId,
    DateTime start,
    DateTime end,
  ) async {
    // Generate user activity report
    try {
      final userMetrics = await _getUserEngagementMetrics(organizationId, start, end);
      return {
        'report_type': 'User Activity',
        'period': '${start.toIso8601String().split('T')[0]} to ${end.toIso8601String().split('T')[0]}',
        'metrics': userMetrics,
        'summary': 'User activity analysis for the specified period',
      };
    } catch (e) {
      return {};
    }
  }

  static Future<Map<String, dynamic>> _generateProgramProgressReport(
    String organizationId,
    DateTime start,
    DateTime end,
  ) async {
    // Generate program progress report
    try {
      final programMetrics = await _getProgramMetrics(organizationId, start, end);
      return {
        'report_type': 'Program Progress',
        'period': '${start.toIso8601String().split('T')[0]} to ${end.toIso8601String().split('T')[0]}',
        'metrics': programMetrics,
        'summary': 'Program progress analysis for the specified period',
      };
    } catch (e) {
      return {};
    }
  }

  static Future<Map<String, dynamic>> _generateDepartmentPerformanceReport(
    String organizationId,
    DateTime start,
    DateTime end,
  ) async {
    // Generate department performance report
    try {
      final deptMetrics = await _getDepartmentMetrics(organizationId, start, end);
      return {
        'report_type': 'Department Performance',
        'period': '${start.toIso8601String().split('T')[0]} to ${end.toIso8601String().split('T')[0]}',
        'metrics': deptMetrics,
        'summary': 'Department performance analysis for the specified period',
      };
    } catch (e) {
      return {};
    }
  }

  static Future<Map<String, dynamic>> _generateComplianceReport(
    String organizationId,
    DateTime start,
    DateTime end,
  ) async {
    // Generate compliance report
    try {
      return {
        'report_type': 'Compliance',
        'period': '${start.toIso8601String().split('T')[0]} to ${end.toIso8601String().split('T')[0]}',
        'compliance_score': 85 + math.Random().nextInt(15),
        'issues_found': math.Random().nextInt(5),
        'recommendations': [
          'Increase user training frequency',
          'Update security policies',
          'Review access permissions',
        ],
        'summary': 'Compliance analysis for the specified period',
      };
    } catch (e) {
      return {};
    }
  }

  static String _getReportTitle(ReportType type) {
    switch (type) {
      case ReportType.userActivity:
        return 'User Activity Report';
      case ReportType.programProgress:
        return 'Program Progress Report';
      case ReportType.departmentPerformance:
        return 'Department Performance Report';
      case ReportType.compliance:
        return 'Compliance Report';
    }
  }
}
