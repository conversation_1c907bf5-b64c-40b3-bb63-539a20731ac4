<variant
    name="release"
    package="dev.fluttercommunity.workmanager"
    minSdkVersion="19"
    targetSdkVersion="19"
    mergedManifest="D:\projects\12july\athkar\athkar_app\build\workmanager_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="D:\projects\12july\athkar\athkar_app\build\workmanager_android\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="D:\projects\12july\athkar\athkar_app\build\workmanager_android\intermediates\default_proguard_files\global\proguard-android.txt-8.7.3"
    partialResultsDir="D:\projects\12july\athkar\athkar_app\build\workmanager_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\main\kotlin;src\release\java;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\projects\12july\athkar\athkar_app\build\workmanager_android\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\projects\12july\athkar\athkar_app\build\workmanager_android\tmp\kotlin-classes\release;D:\projects\12july\athkar\athkar_app\build\workmanager_android\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="dev.fluttercommunity.workmanager"
      generatedSourceFolders="D:\projects\12july\athkar\athkar_app\build\workmanager_android\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\projects\12july\athkar\athkar_app\build\workmanager_android\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\a7a9bf31db10813def01c228e81c73cc\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
