import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/permissions_manager.dart';
import '../services/language_service.dart';
import '../theme/app_theme.dart';
import '../widgets/permissions/permission_card_widget.dart';
import '../widgets/permissions/permission_progress_widget.dart';

class PermissionsOnboardingScreen extends StatefulWidget {
  final VoidCallback? onCompleted;

  const PermissionsOnboardingScreen({
    super.key,
    this.onCompleted,
  });

  @override
  State<PermissionsOnboardingScreen> createState() => _PermissionsOnboardingScreenState();
}

class _PermissionsOnboardingScreenState extends State<PermissionsOnboardingScreen>
    with TickerProviderStateMixin {
  final PermissionsManager _permissionsManager = PermissionsManager();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  Map<String, dynamic>? _permissionsSummary;
  bool _isLoading = true;
  int _currentStep = 0;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadPermissionsSummary();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  Future<void> _loadPermissionsSummary() async {
    final languageService = Provider.of<LanguageService>(context, listen: false);
    final summary = await _permissionsManager.getPermissionsSummary(languageService);
    
    if (mounted) {
      setState(() {
        _permissionsSummary = summary;
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    
    return Scaffold(
      backgroundColor: Colors.white,
      body: _isLoading
          ? _buildLoadingScreen(languageService)
          : _buildPermissionsFlow(languageService),
    );
  }

  Widget _buildLoadingScreen(LanguageService languageService) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
          ),
          const SizedBox(height: 24),
          Text(
            languageService.isArabic ? 'جاري التحضير...' : 'Preparing...',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsFlow(LanguageService languageService) {
    return SafeArea(
      child: Column(
        children: [
          // Header
          _buildHeader(languageService),
          
          // Progress indicator
          if (_permissionsSummary != null)
            PermissionProgressWidget(
              current: _currentStep,
              total: _permissionsSummary!['details'].length,
              granted: _permissionsSummary!['granted'],
            ),
          
          // Content
          Expanded(
            child: _currentStep == 0
                ? _buildWelcomeScreen(languageService)
                : _buildPermissionsScreen(languageService),
          ),
          
          // Bottom actions
          _buildBottomActions(languageService),
        ],
      ),
    );
  }

  Widget _buildHeader(LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryGreen,
            AppTheme.primaryGreen.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.security,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageService.isArabic ? 'الصلاحيات المطلوبة' : 'Required Permissions',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  languageService.isArabic 
                      ? 'لتجربة أفضل في استخدام التطبيق'
                      : 'For the best app experience',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeScreen(LanguageService languageService) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Islamic illustration
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.mosque,
                  size: 60,
                  color: AppTheme.primaryGreen,
                ),
              ),
              
              const SizedBox(height: 32),
              
              Text(
                languageService.isArabic ? 'مرحباً بك في تطبيق الأذكار' : 'Welcome to Athkar App',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryGreen,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              Text(
                languageService.isArabic
                    ? 'لنقوم بإعداد بعض الصلاحيات الأساسية لضمان عمل التطبيق بأفضل شكل ممكن'
                    : 'Let\'s set up some essential permissions to ensure the app works at its best',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              // Features preview
              _buildFeaturesList(languageService),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturesList(LanguageService languageService) {
    final features = [
      {
        'icon': Icons.notifications_active,
        'title': languageService.isArabic ? 'تذكيرات الأذكار' : 'Athkar Reminders',
        'description': languageService.isArabic ? 'إشعارات في الوقت المناسب' : 'Timely notifications',
      },
      {
        'icon': Icons.explore,
        'title': languageService.isArabic ? 'اتجاه القبلة' : 'Qibla Direction',
        'description': languageService.isArabic ? 'تحديد دقيق للاتجاه' : 'Accurate direction finding',
      },
      {
        'icon': Icons.picture_in_picture,
        'title': languageService.isArabic ? 'العداد العائم' : 'Floating Counter',
        'description': languageService.isArabic ? 'عد الأذكار في أي مكان' : 'Count anywhere',
      },
      {
        'icon': Icons.cloud_off,
        'title': languageService.isArabic ? 'استخدام بدون إنترنت' : 'Offline Usage',
        'description': languageService.isArabic ? 'جميع المحتويات محفوظة محلياً' : 'All content saved locally',
      },
    ];

    return Column(
      children: features.map((feature) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  feature['icon'] as IconData,
                  color: AppTheme.primaryGreen,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      feature['title'] as String,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      feature['description'] as String,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPermissionsScreen(LanguageService languageService) {
    if (_permissionsSummary == null) return const SizedBox();
    
    final permissions = _permissionsSummary!['details'] as List;
    
    return PageView.builder(
      controller: _pageController,
      onPageChanged: (index) {
        setState(() {
          _currentStep = index + 1;
        });
      },
      itemCount: permissions.length,
      itemBuilder: (context, index) {
        final permission = permissions[index];
        return Padding(
          padding: const EdgeInsets.all(24),
          child: PermissionCardWidget(
            permission: permission,
            languageService: languageService,
            onPermissionGranted: () => _handlePermissionGranted(),
            onSkip: () => _nextPermission(),
          ),
        );
      },
    );
  }

  Widget _buildBottomActions(LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0) ...[
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppTheme.primaryGreen,
                  side: const BorderSide(color: AppTheme.primaryGreen),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  languageService.isArabic ? 'السابق' : 'Previous',
                ),
              ),
            ),
            const SizedBox(width: 16),
          ],
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _currentStep == 0 ? _startPermissionsFlow : _nextPermission,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                _getButtonText(languageService),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getButtonText(LanguageService languageService) {
    if (_currentStep == 0) {
      return languageService.isArabic ? 'ابدأ الإعداد' : 'Start Setup';
    }
    
    if (_permissionsSummary != null) {
      final totalPermissions = _permissionsSummary!['details'].length;
      if (_currentStep >= totalPermissions) {
        return languageService.isArabic ? 'إنهاء' : 'Finish';
      }
    }
    
    return languageService.isArabic ? 'التالي' : 'Next';
  }

  void _startPermissionsFlow() {
    setState(() {
      _currentStep = 1;
    });
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _nextPermission() {
    if (_permissionsSummary != null) {
      final totalPermissions = _permissionsSummary!['details'].length;
      if (_currentStep >= totalPermissions) {
        _completeOnboarding();
        return;
      }
    }
    
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _previousStep() {
    if (_currentStep > 1) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      setState(() {
        _currentStep = 0;
      });
    }
  }

  void _handlePermissionGranted() {
    // Refresh permissions summary
    _loadPermissionsSummary();
    // Move to next permission after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      _nextPermission();
    });
  }

  void _completeOnboarding() {
    if (widget.onCompleted != null) {
      widget.onCompleted!();
    } else {
      Navigator.of(context).pop();
    }
  }
}
