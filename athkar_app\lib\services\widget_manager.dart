import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'language_service.dart';

/// Widget types available in the app
enum WidgetType {
  routineCounter,
  ayahOfDay,
  athkarReminder,
  quranProgress,
  prayerTimes,
  islamicCalendar,
  dhikrCounter,
  statistics,
}

/// Widget sizes supported
enum WidgetSize {
  small1x1,
  medium2x1,
  large2x2,
  extraLarge4x2,
}

/// Widget configuration model
class WidgetConfig {
    final String id;
    final WidgetType type;
    final WidgetSize size;
    final Map<String, dynamic> settings;
    final DateTime createdAt;
    final DateTime updatedAt;

    WidgetConfig({
      required this.id,
      required this.type,
      required this.size,
      required this.settings,
      required this.createdAt,
      required this.updatedAt,
    });

    Map<String, dynamic> toMap() {
      return {
        'id': id,
        'type': type.index,
        'size': size.index,
        'settings': settings,
        'createdAt': createdAt.millisecondsSinceEpoch,
        'updatedAt': updatedAt.millisecondsSinceEpoch,
      };
    }

    factory WidgetConfig.fromMap(Map<String, dynamic> map) {
      return WidgetConfig(
        id: map['id'],
        type: WidgetType.values[map['type']],
        size: WidgetSize.values[map['size']],
        settings: Map<String, dynamic>.from(map['settings']),
        createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
        updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      );
    }
  }

/// Comprehensive widget manager for Android home screen widgets
class WidgetManager {
  static final WidgetManager _instance = WidgetManager._internal();
  factory WidgetManager() => _instance;
  WidgetManager._internal();

  static const MethodChannel _channel = MethodChannel('athkar_widgets');

  /// Initialize widget manager
  Future<void> initialize() async {
    if (Platform.isAndroid) {
      try {
        await _channel.invokeMethod('initialize');
      } catch (e) {
        debugPrint('Error initializing widget manager: $e');
      }
    }
  }

  /// Create a new widget
  Future<bool> createWidget(
    WidgetType type,
    WidgetSize size,
    Map<String, dynamic> settings,
  ) async {
    if (!Platform.isAndroid) return false;

    try {
      final widgetId = DateTime.now().millisecondsSinceEpoch.toString();
      final config = WidgetConfig(
        id: widgetId,
        type: type,
        size: size,
        settings: settings,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save configuration locally
      await _saveWidgetConfig(config);

      // Create widget on Android side
      final result = await _channel.invokeMethod('createWidget', {
        'widgetId': widgetId,
        'type': type.name,
        'size': _getSizeString(size),
        'settings': settings,
      });

      return result == true;
    } catch (e) {
      debugPrint('Error creating widget: $e');
      return false;
    }
  }

  /// Update existing widget
  Future<bool> updateWidget(String widgetId, Map<String, dynamic> settings) async {
    if (!Platform.isAndroid) return false;

    try {
      // Update local configuration
      final config = await _getWidgetConfig(widgetId);
      if (config != null) {
        final updatedConfig = WidgetConfig(
          id: config.id,
          type: config.type,
          size: config.size,
          settings: {...config.settings, ...settings},
          createdAt: config.createdAt,
          updatedAt: DateTime.now(),
        );
        await _saveWidgetConfig(updatedConfig);
      }

      // Update widget on Android side
      final result = await _channel.invokeMethod('updateWidget', {
        'widgetId': widgetId,
        'settings': settings,
      });

      return result == true;
    } catch (e) {
      debugPrint('Error updating widget: $e');
      return false;
    }
  }

  /// Delete widget
  Future<bool> deleteWidget(String widgetId) async {
    if (!Platform.isAndroid) return false;

    try {
      // Remove local configuration
      await _removeWidgetConfig(widgetId);

      // Delete widget on Android side
      final result = await _channel.invokeMethod('deleteWidget', {
        'widgetId': widgetId,
      });

      return result == true;
    } catch (e) {
      debugPrint('Error deleting widget: $e');
      return false;
    }
  }

  /// Get all active widgets
  Future<List<WidgetConfig>> getActiveWidgets() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final widgetKeys = prefs.getKeys().where((key) => key.startsWith('widget_')).toList();
      
      final widgets = <WidgetConfig>[];
      for (final key in widgetKeys) {
        final configJson = prefs.getString(key);
        if (configJson != null) {
          final configMap = json.decode(configJson);
          widgets.add(WidgetConfig.fromMap(configMap));
        }
      }
      
      return widgets;
    } catch (e) {
      debugPrint('Error getting active widgets: $e');
      return [];
    }
  }

  /// Update widget data (called from native side)
  Future<Map<String, dynamic>> getWidgetData(String widgetId, String type) async {
    try {
      switch (type) {
        case 'routineCounter':
          return await _getRoutineCounterData(widgetId);
        case 'ayahOfDay':
          return await _getAyahOfDayData();
        case 'athkarReminder':
          return await _getAthkarReminderData();
        case 'quranProgress':
          return await _getQuranProgressData();
        case 'prayerTimes':
          return await _getPrayerTimesData();
        case 'islamicCalendar':
          return await _getIslamicCalendarData();
        case 'dhikrCounter':
          return await _getDhikrCounterData(widgetId);
        case 'statistics':
          return await _getStatisticsData();
        default:
          return {};
      }
    } catch (e) {
      debugPrint('Error getting widget data: $e');
      return {};
    }
  }

  /// Refresh all widgets
  Future<void> refreshAllWidgets() async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('refreshAllWidgets');
    } catch (e) {
      debugPrint('Error refreshing widgets: $e');
    }
  }

  /// Refresh specific widget
  Future<void> refreshWidget(String widgetId) async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('refreshWidget', {
        'widgetId': widgetId,
      });
    } catch (e) {
      debugPrint('Error refreshing widget: $e');
    }
  }

  /// Get widget configuration screen
  Widget getWidgetConfigScreen(WidgetType type, WidgetSize size) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getWidgetIcon(type),
              size: 48,
              color: const Color(0xFF2E7D32),
            ),
            const SizedBox(height: 16),
            Text(
              'Widget Configuration',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Type: ${type.name}',
              style: const TextStyle(fontSize: 14),
            ),
            Text(
              'Size: ${_getSizeString(size)}',
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getWidgetIcon(WidgetType type) {
    switch (type) {
      case WidgetType.routineCounter:
        return Icons.track_changes;
      case WidgetType.ayahOfDay:
        return Icons.menu_book;
      case WidgetType.athkarReminder:
        return Icons.notifications_active;
      case WidgetType.quranProgress:
        return Icons.auto_stories;
      case WidgetType.prayerTimes:
        return Icons.access_time;
      case WidgetType.islamicCalendar:
        return Icons.calendar_today;
      case WidgetType.dhikrCounter:
        return Icons.add_circle;
      case WidgetType.statistics:
        return Icons.bar_chart;
    }
  }

  // Private methods

  Future<void> _saveWidgetConfig(WidgetConfig config) async {
    final prefs = await SharedPreferences.getInstance();
    final configJson = json.encode(config.toMap());
    await prefs.setString('widget_${config.id}', configJson);
  }

  Future<WidgetConfig?> _getWidgetConfig(String widgetId) async {
    final prefs = await SharedPreferences.getInstance();
    final configJson = prefs.getString('widget_$widgetId');
    if (configJson != null) {
      final configMap = json.decode(configJson);
      return WidgetConfig.fromMap(configMap);
    }
    return null;
  }

  Future<void> _removeWidgetConfig(String widgetId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('widget_$widgetId');
  }

  String _getSizeString(WidgetSize size) {
    switch (size) {
      case WidgetSize.small1x1:
        return '1x1';
      case WidgetSize.medium2x1:
        return '2x1';
      case WidgetSize.large2x2:
        return '2x2';
      case WidgetSize.extraLarge4x2:
        return '4x2';
    }
  }

  // Widget data providers

  Future<Map<String, dynamic>> _getRoutineCounterData(String widgetId) async {
    // Get routine counter data from database
    return {
      'title': 'أذكار الصباح',
      'current': 15,
      'total': 33,
      'percentage': 45,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getAyahOfDayData() async {
    // Get daily Ayah from database
    return {
      'arabicText': 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
      'translation': 'And whoever fears Allah - He will make for him a way out',
      'surahName': 'الطلاق',
      'ayahNumber': 2,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getAthkarReminderData() async {
    // Get next athkar reminder
    return {
      'nextReminder': 'أذكار المساء',
      'timeRemaining': '2:30:00',
      'isActive': true,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getQuranProgressData() async {
    // Get Quran reading progress
    return {
      'currentSurah': 'البقرة',
      'currentAyah': 156,
      'totalProgress': 12.5,
      'dailyGoal': 5,
      'dailyProgress': 3,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getPrayerTimesData() async {
    // Get prayer times for today
    return {
      'nextPrayer': 'العصر',
      'nextPrayerTime': '15:30',
      'timeRemaining': '1:45:00',
      'allPrayerTimes': {
        'fajr': '05:15',
        'sunrise': '06:45',
        'dhuhr': '12:30',
        'asr': '15:30',
        'maghrib': '18:15',
        'isha': '19:45',
      },
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getIslamicCalendarData() async {
    // Get Islamic calendar information
    return {
      'hijriDate': '15 رجب 1445',
      'gregorianDate': '27 يناير 2024',
      'islamicEvent': 'الإسراء والمعراج',
      'daysUntilEvent': 12,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getDhikrCounterData(String widgetId) async {
    // Get dhikr counter data
    return {
      'dhikrText': 'سبحان الله',
      'currentCount': 67,
      'targetCount': 100,
      'percentage': 67,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getStatisticsData() async {
    // Get user statistics
    return {
      'totalAthkar': 1250,
      'streakDays': 15,
      'completedRoutines': 45,
      'totalDhikr': 5670,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }
}

  /// Create a new widget
  Future<bool> createWidget(
    WidgetType type,
    WidgetSize size,
    Map<String, dynamic> settings,
  ) async {
    if (!Platform.isAndroid) return false;

    try {
      final widgetId = DateTime.now().millisecondsSinceEpoch.toString();
      final config = WidgetConfig(
        id: widgetId,
        type: type,
        size: size,
        settings: settings,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save configuration locally
      await _saveWidgetConfig(config);

      // Create widget on Android side
      final result = await _channel.invokeMethod('createWidget', {
        'widgetId': widgetId,
        'type': type.name,
        'size': _getSizeString(size),
        'settings': settings,
      });

      return result == true;
    } catch (e) {
      debugPrint('Error creating widget: $e');
      return false;
    }
  }

  /// Update existing widget
  Future<bool> updateWidget(String widgetId, Map<String, dynamic> settings) async {
    if (!Platform.isAndroid) return false;

    try {
      // Update local configuration
      final config = await _getWidgetConfig(widgetId);
      if (config != null) {
        final updatedConfig = WidgetConfig(
          id: config.id,
          type: config.type,
          size: config.size,
          settings: {...config.settings, ...settings},
          createdAt: config.createdAt,
          updatedAt: DateTime.now(),
        );
        await _saveWidgetConfig(updatedConfig);
      }

      // Update widget on Android side
      final result = await _channel.invokeMethod('updateWidget', {
        'widgetId': widgetId,
        'settings': settings,
      });

      return result == true;
    } catch (e) {
      debugPrint('Error updating widget: $e');
      return false;
    }
  }

  /// Delete widget
  Future<bool> deleteWidget(String widgetId) async {
    if (!Platform.isAndroid) return false;

    try {
      // Remove local configuration
      await _removeWidgetConfig(widgetId);

      // Delete widget on Android side
      final result = await _channel.invokeMethod('deleteWidget', {
        'widgetId': widgetId,
      });

      return result == true;
    } catch (e) {
      debugPrint('Error deleting widget: $e');
      return false;
    }
  }

  /// Get all active widgets
  Future<List<WidgetConfig>> getActiveWidgets() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final widgetKeys = prefs.getKeys().where((key) => key.startsWith('widget_')).toList();

      final widgets = <WidgetConfig>[];
      for (final key in widgetKeys) {
        final configJson = prefs.getString(key);
        if (configJson != null) {
          final configMap = json.decode(configJson);
          widgets.add(WidgetConfig.fromMap(configMap));
        }
      }

      return widgets;
    } catch (e) {
      debugPrint('Error getting active widgets: $e');
      return [];
    }
  }

  /// Update widget data (called from native side)
  Future<Map<String, dynamic>> getWidgetData(String widgetId, String type) async {
    try {
      switch (type) {
        case 'routineCounter':
          return await _getRoutineCounterData(widgetId);
        case 'ayahOfDay':
          return await _getAyahOfDayData();
        case 'athkarReminder':
          return await _getAthkarReminderData();
        case 'quranProgress':
          return await _getQuranProgressData();
        case 'prayerTimes':
          return await _getPrayerTimesData();
        case 'islamicCalendar':
          return await _getIslamicCalendarData();
        case 'dhikrCounter':
          return await _getDhikrCounterData(widgetId);
        case 'statistics':
          return await _getStatisticsData();
        default:
          return {};
      }
    } catch (e) {
      debugPrint('Error getting widget data: $e');
      return {};
    }
  }

  /// Refresh all widgets
  Future<void> refreshAllWidgets() async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('refreshAllWidgets');
    } catch (e) {
      debugPrint('Error refreshing widgets: $e');
    }
  }

  /// Refresh specific widget
  Future<void> refreshWidget(String widgetId) async {
    if (!Platform.isAndroid) return;

    try {
      await _channel.invokeMethod('refreshWidget', {
        'widgetId': widgetId,
      });
    } catch (e) {
      debugPrint('Error refreshing widget: $e');
    }
  }

  /// Get widget configuration screen
  Widget getWidgetConfigScreen(WidgetType type, WidgetSize size) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Text('Widget Configuration for ${type.name}'),
      ),
    );
  }

  // Private methods

  Future<void> _saveWidgetConfig(WidgetConfig config) async {
    final prefs = await SharedPreferences.getInstance();
    final configJson = json.encode(config.toMap());
    await prefs.setString('widget_${config.id}', configJson);
  }

  Future<WidgetConfig?> _getWidgetConfig(String widgetId) async {
    final prefs = await SharedPreferences.getInstance();
    final configJson = prefs.getString('widget_$widgetId');
    if (configJson != null) {
      final configMap = json.decode(configJson);
      return WidgetConfig.fromMap(configMap);
    }
    return null;
  }

  Future<void> _removeWidgetConfig(String widgetId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('widget_$widgetId');
  }

  String _getSizeString(WidgetSize size) {
    switch (size) {
      case WidgetSize.small1x1:
        return '1x1';
      case WidgetSize.medium2x1:
        return '2x1';
      case WidgetSize.large2x2:
        return '2x2';
      case WidgetSize.extraLarge4x2:
        return '4x2';
    }
  }

  // Widget data providers

  Future<Map<String, dynamic>> _getRoutineCounterData(String widgetId) async {
    // Get routine counter data from database
    return {
      'title': 'أذكار الصباح',
      'current': 15,
      'total': 33,
      'percentage': 45,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getAyahOfDayData() async {
    // Get daily Ayah from database
    return {
      'arabicText': 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
      'translation': 'And whoever fears Allah - He will make for him a way out',
      'surahName': 'الطلاق',
      'ayahNumber': 2,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getAthkarReminderData() async {
    // Get next athkar reminder
    return {
      'nextReminder': 'أذكار المساء',
      'timeRemaining': '2:30:00',
      'isActive': true,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getQuranProgressData() async {
    // Get Quran reading progress
    return {
      'currentSurah': 'البقرة',
      'currentAyah': 156,
      'totalProgress': 12.5,
      'dailyGoal': 5,
      'dailyProgress': 3,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getPrayerTimesData() async {
    // Get prayer times for today
    return {
      'nextPrayer': 'العصر',
      'nextPrayerTime': '15:30',
      'timeRemaining': '1:45:00',
      'allPrayerTimes': {
        'fajr': '05:15',
        'sunrise': '06:45',
        'dhuhr': '12:30',
        'asr': '15:30',
        'maghrib': '18:15',
        'isha': '19:45',
      },
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getIslamicCalendarData() async {
    // Get Islamic calendar information
    return {
      'hijriDate': '15 رجب 1445',
      'gregorianDate': '27 يناير 2024',
      'islamicEvent': 'الإسراء والمعراج',
      'daysUntilEvent': 12,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getDhikrCounterData(String widgetId) async {
    // Get dhikr counter data
    return {
      'dhikrText': 'سبحان الله',
      'currentCount': 67,
      'targetCount': 100,
      'percentage': 67,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }

  Future<Map<String, dynamic>> _getStatisticsData() async {
    // Get user statistics
    return {
      'totalAthkar': 1250,
      'streakDays': 15,
      'completedRoutines': 45,
      'totalDhikr': 5670,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    };
  }
}
