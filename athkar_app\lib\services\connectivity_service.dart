import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityService {
  static final Connectivity _connectivity = Connectivity();
  static StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  static bool _isConnected = true;
  static final List<VoidCallback> _listeners = [];

  static bool get isConnected => _isConnected;

  // Initialize connectivity monitoring
  static Future<void> initialize() async {
    try {
      // Check initial connectivity
      final result = await _connectivity.checkConnectivity();
      _isConnected = _isConnectedResult(result);

      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        (List<ConnectivityResult> results) {
          final wasConnected = _isConnected;
          _isConnected = _isConnectedResults(results);

          if (wasConnected != _isConnected) {
            _notifyListeners();
            debugPrint('Connectivity changed: ${_isConnected ? 'Connected' : 'Disconnected'}');
          }
        },
      );
    } catch (e) {
      debugPrint('Error initializing connectivity service: $e');
    }
  }

  // Check if connectivity results indicate connection
  static bool _isConnectedResult(List<ConnectivityResult> results) {
    return results.any((result) => result != ConnectivityResult.none);
  }

  // Check if connectivity results indicate connection (for list)
  static bool _isConnectedResults(List<ConnectivityResult> results) {
    return results.any((result) => result != ConnectivityResult.none);
  }

  // Add connectivity listener
  static void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  // Remove connectivity listener
  static void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  // Notify all listeners
  static void _notifyListeners() {
    for (final listener in _listeners) {
      try {
        listener();
      } catch (e) {
        debugPrint('Error in connectivity listener: $e');
      }
    }
  }

  // Dispose resources
  static void dispose() {
    _connectivitySubscription?.cancel();
    _listeners.clear();
  }

  // Check current connectivity status
  static Future<bool> checkConnectivity() async {
    try {
      final results = await _connectivity.checkConnectivity();
      return _isConnectedResult(results);
    } catch (e) {
      debugPrint('Error checking connectivity: $e');
      return false;
    }
  }
}
