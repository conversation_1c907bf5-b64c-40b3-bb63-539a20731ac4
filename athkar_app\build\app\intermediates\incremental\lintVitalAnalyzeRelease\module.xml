<lint-module
    format="1"
    dir="D:\projects\12july\athkar\athkar_app\android\app"
    name=":app"
    type="APP"
    maven="android:app:unspecified"
    agpVersion="8.7.3"
    buildFolder="D:\projects\12july\athkar\athkar_app\build\app"
    bootClassPath="d:\Sdk\platforms\android-35\android.jar;d:\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
