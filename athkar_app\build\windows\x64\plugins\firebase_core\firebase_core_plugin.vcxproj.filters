﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\firebase_core_plugin_c_api.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\firebase_core_plugin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\messages.g.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\include\firebase_core\firebase_core_plugin_c_api.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\firebase_core_plugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\messages.g.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\projects\12july\athkar\athkar_app\build\windows\x64\generated\firebase_core\plugin_version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\projects\12july\athkar\athkar_app\windows\flutter\ephemeral\.plugin_symlinks\firebase_core\windows\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{4562FD57-446E-38A6-A0FF-791D53118348}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{A7C8CA60-4A25-3507-9F24-88E795F09F2F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
