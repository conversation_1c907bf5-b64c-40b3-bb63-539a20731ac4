import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/permissions_service.dart';
import '../services/language_service.dart';
import '../theme/app_theme.dart';
import 'permissions_request_screen.dart';
import 'main_navigation.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _animationController.forward();
    
    // Check permissions after animation
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _checkPermissionsAndNavigate();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _checkPermissionsAndNavigate() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    try {
      // Check if all critical permissions are granted
      final allCriticalGranted = await PermissionsService.areAllCriticalPermissionsGranted();
      
      if (mounted) {
        if (allCriticalGranted) {
          // Navigate to main app
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => const MainNavigation(),
            ),
          );
        } else {
          // Navigate to permissions request screen
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => const PermissionsRequestScreen(),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error checking permissions: $e');
      // Navigate to permissions screen on error
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const PermissionsRequestScreen(),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    
    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.primaryGreen,
                Color(0xFF1B5E20),
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Animated logo
                AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: _fadeAnimation,
                      child: ScaleTransition(
                        scale: _scaleAnimation,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(60),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.2),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.mosque,
                            color: AppTheme.primaryGreen,
                            size: 60,
                          ),
                        ),
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 32),
                
                // App title
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Text(
                    languageService.isArabic ? 'أذكار المسلم' : 'Muslim Athkar',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // App subtitle
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Text(
                    languageService.isArabic 
                        ? 'تطبيق شامل للأذكار والأدعية'
                        : 'Comprehensive Athkar and Dua App',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                
                const SizedBox(height: 64),
                
                // Loading indicator
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Column(
                    children: [
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 2,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        languageService.isArabic ? 'جاري التحميل...' : 'Loading...',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 64),
                
                // Version info
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Text(
                    languageService.isArabic ? 'الإصدار 1.0.0' : 'Version 1.0.0',
                    style: const TextStyle(
                      color: Colors.white54,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
