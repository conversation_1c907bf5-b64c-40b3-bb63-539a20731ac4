﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{111A9FF4-8B3B-37D8-8033-71CC4F4EA623}"
	ProjectSection(ProjectDependencies) = postProject
		{1C9A04CE-B521-3F10-9853-ECEA216A6078} = {1C9A04CE-B521-3F10-9853-ECEA216A6078}
		{447F2E6A-00CF-3337-B9E8-7870668D4876} = {447F2E6A-00CF-3337-B9E8-7870668D4876}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{588E6C57-45B4-30AC-8483-ACC8A1B7DCFB}"
	ProjectSection(ProjectDependencies) = postProject
		{111A9FF4-8B3B-37D8-8033-71CC4F4EA623} = {111A9FF4-8B3B-37D8-8033-71CC4F4EA623}
		{1C9A04CE-B521-3F10-9853-ECEA216A6078} = {1C9A04CE-B521-3F10-9853-ECEA216A6078}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{1C9A04CE-B521-3F10-9853-ECEA216A6078}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "connectivity_plus_plugin", "connectivity_plus_plugin.vcxproj", "{447F2E6A-00CF-3337-B9E8-7870668D4876}"
	ProjectSection(ProjectDependencies) = postProject
		{1C9A04CE-B521-3F10-9853-ECEA216A6078} = {1C9A04CE-B521-3F10-9853-ECEA216A6078}
		{A1E5DC38-EC07-396C-9451-1BA08EC76A07} = {A1E5DC38-EC07-396C-9451-1BA08EC76A07}
		{67915962-D970-3935-936B-151B711292DB} = {67915962-D970-3935-936B-151B711292DB}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{A1E5DC38-EC07-396C-9451-1BA08EC76A07}"
	ProjectSection(ProjectDependencies) = postProject
		{1C9A04CE-B521-3F10-9853-ECEA216A6078} = {1C9A04CE-B521-3F10-9853-ECEA216A6078}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{67915962-D970-3935-936B-151B711292DB}"
	ProjectSection(ProjectDependencies) = postProject
		{1C9A04CE-B521-3F10-9853-ECEA216A6078} = {1C9A04CE-B521-3F10-9853-ECEA216A6078}
		{A1E5DC38-EC07-396C-9451-1BA08EC76A07} = {A1E5DC38-EC07-396C-9451-1BA08EC76A07}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{111A9FF4-8B3B-37D8-8033-71CC4F4EA623}.Debug|x64.ActiveCfg = Debug|x64
		{111A9FF4-8B3B-37D8-8033-71CC4F4EA623}.Debug|x64.Build.0 = Debug|x64
		{111A9FF4-8B3B-37D8-8033-71CC4F4EA623}.Profile|x64.ActiveCfg = Profile|x64
		{111A9FF4-8B3B-37D8-8033-71CC4F4EA623}.Profile|x64.Build.0 = Profile|x64
		{111A9FF4-8B3B-37D8-8033-71CC4F4EA623}.Release|x64.ActiveCfg = Release|x64
		{111A9FF4-8B3B-37D8-8033-71CC4F4EA623}.Release|x64.Build.0 = Release|x64
		{588E6C57-45B4-30AC-8483-ACC8A1B7DCFB}.Debug|x64.ActiveCfg = Debug|x64
		{588E6C57-45B4-30AC-8483-ACC8A1B7DCFB}.Profile|x64.ActiveCfg = Profile|x64
		{588E6C57-45B4-30AC-8483-ACC8A1B7DCFB}.Release|x64.ActiveCfg = Release|x64
		{1C9A04CE-B521-3F10-9853-ECEA216A6078}.Debug|x64.ActiveCfg = Debug|x64
		{1C9A04CE-B521-3F10-9853-ECEA216A6078}.Debug|x64.Build.0 = Debug|x64
		{1C9A04CE-B521-3F10-9853-ECEA216A6078}.Profile|x64.ActiveCfg = Profile|x64
		{1C9A04CE-B521-3F10-9853-ECEA216A6078}.Profile|x64.Build.0 = Profile|x64
		{1C9A04CE-B521-3F10-9853-ECEA216A6078}.Release|x64.ActiveCfg = Release|x64
		{1C9A04CE-B521-3F10-9853-ECEA216A6078}.Release|x64.Build.0 = Release|x64
		{447F2E6A-00CF-3337-B9E8-7870668D4876}.Debug|x64.ActiveCfg = Debug|x64
		{447F2E6A-00CF-3337-B9E8-7870668D4876}.Debug|x64.Build.0 = Debug|x64
		{447F2E6A-00CF-3337-B9E8-7870668D4876}.Profile|x64.ActiveCfg = Profile|x64
		{447F2E6A-00CF-3337-B9E8-7870668D4876}.Profile|x64.Build.0 = Profile|x64
		{447F2E6A-00CF-3337-B9E8-7870668D4876}.Release|x64.ActiveCfg = Release|x64
		{447F2E6A-00CF-3337-B9E8-7870668D4876}.Release|x64.Build.0 = Release|x64
		{A1E5DC38-EC07-396C-9451-1BA08EC76A07}.Debug|x64.ActiveCfg = Debug|x64
		{A1E5DC38-EC07-396C-9451-1BA08EC76A07}.Debug|x64.Build.0 = Debug|x64
		{A1E5DC38-EC07-396C-9451-1BA08EC76A07}.Profile|x64.ActiveCfg = Profile|x64
		{A1E5DC38-EC07-396C-9451-1BA08EC76A07}.Profile|x64.Build.0 = Profile|x64
		{A1E5DC38-EC07-396C-9451-1BA08EC76A07}.Release|x64.ActiveCfg = Release|x64
		{A1E5DC38-EC07-396C-9451-1BA08EC76A07}.Release|x64.Build.0 = Release|x64
		{67915962-D970-3935-936B-151B711292DB}.Debug|x64.ActiveCfg = Debug|x64
		{67915962-D970-3935-936B-151B711292DB}.Debug|x64.Build.0 = Debug|x64
		{67915962-D970-3935-936B-151B711292DB}.Profile|x64.ActiveCfg = Profile|x64
		{67915962-D970-3935-936B-151B711292DB}.Profile|x64.Build.0 = Profile|x64
		{67915962-D970-3935-936B-151B711292DB}.Release|x64.ActiveCfg = Release|x64
		{67915962-D970-3935-936B-151B711292DB}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {261D28B9-A163-350A-971F-92B62376BB18}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
