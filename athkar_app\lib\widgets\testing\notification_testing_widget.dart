import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../services/notification_service.dart';
import '../../theme/app_theme.dart';
import '../../screens/comprehensive_testing_screen.dart';

class NotificationTestingWidget extends StatefulWidget {
  final Function(TestingStatus) onStatusChanged;

  const NotificationTestingWidget({
    super.key,
    required this.onStatusChanged,
  });

  @override
  State<NotificationTestingWidget> createState() => _NotificationTestingWidgetState();
}

class _NotificationTestingWidgetState extends State<NotificationTestingWidget> {
  final NotificationService _notificationService = NotificationService();
  
  final Map<String, TestResult> _testResults = {};
  bool _isRunningTests = false;
  int _currentTestRound = 0;
  final int _totalRounds = 5;

  final List<NotificationTest> _notificationTests = [
    NotificationTest(
      id: 'basic_notification',
      nameAr: 'الإشعار الأساسي',
      nameEn: 'Basic Notification',
      description: 'Test basic notification display',
    ),
    NotificationTest(
      id: 'scheduled_notification',
      nameAr: 'الإشعار المجدول',
      nameEn: 'Scheduled Notification',
      description: 'Test scheduled notification functionality',
    ),
    NotificationTest(
      id: 'recurring_notification',
      nameAr: 'الإشعار المتكرر',
      nameEn: 'Recurring Notification',
      description: 'Test recurring notification patterns',
    ),
    NotificationTest(
      id: 'athkar_reminder',
      nameAr: 'تذكير الأذكار',
      nameEn: 'Athkar Reminder',
      description: 'Test Athkar-specific notifications',
    ),
    NotificationTest(
      id: 'prayer_time_alert',
      nameAr: 'تنبيه وقت الصلاة',
      nameEn: 'Prayer Time Alert',
      description: 'Test prayer time notifications',
    ),
    NotificationTest(
      id: 'custom_sound',
      nameAr: 'الصوت المخصص',
      nameEn: 'Custom Sound',
      description: 'Test custom notification sounds',
    ),
    NotificationTest(
      id: 'notification_actions',
      nameAr: 'إجراءات الإشعار',
      nameEn: 'Notification Actions',
      description: 'Test notification action buttons',
    ),
    NotificationTest(
      id: 'background_notification',
      nameAr: 'الإشعار الخلفي',
      nameEn: 'Background Notification',
      description: 'Test notifications when app is in background',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeTestResults();
  }

  void _initializeTestResults() {
    for (final test in _notificationTests) {
      _testResults[test.id] = TestResult.notStarted;
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with progress
          _buildHeader(languageService),
          
          const SizedBox(height: 24),
          
          // Test controls
          _buildTestControls(languageService),
          
          const SizedBox(height: 24),
          
          // Test results
          _buildTestResults(languageService),
          
          const SizedBox(height: 24),
          
          // Round progress
          if (_isRunningTests) _buildRoundProgress(languageService),
        ],
      ),
    );
  }

  Widget _buildHeader(LanguageService languageService) {
    final passedTests = _testResults.values.where((result) => result == TestResult.passed).length;
    final totalTests = _testResults.length;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryGreen,
            AppTheme.primaryGreen.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.notifications, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  languageService.isArabic ? 'اختبار نظام الإشعارات' : 'Notification System Testing',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            languageService.isArabic 
                ? 'اختبار شامل لجميع أنواع الإشعارات والتذكيرات الإسلامية'
                : 'Comprehensive testing of all notification types and Islamic reminders',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageService.isArabic ? 'التقدم' : 'Progress',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '$passedTests / $totalTests',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: passedTests / totalTests,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildTestControls(LanguageService languageService) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isRunningTests ? null : _runAllTests,
            icon: _isRunningTests 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(
              _isRunningTests
                  ? (languageService.isArabic ? 'جاري التشغيل...' : 'Running...')
                  : (languageService.isArabic ? 'تشغيل جميع الاختبارات' : 'Run All Tests'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: _isRunningTests ? null : _resetTests,
          icon: const Icon(Icons.refresh),
          label: Text(languageService.isArabic ? 'إعادة تعيين' : 'Reset'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildTestResults(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'نتائج الاختبارات' : 'Test Results',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 12),
        ...(_notificationTests.map((test) {
          final result = _testResults[test.id] ?? TestResult.notStarted;
          return _buildTestResultCard(test, result, languageService);
        }).toList()),
      ],
    );
  }

  Widget _buildTestResultCard(NotificationTest test, TestResult result, LanguageService languageService) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (result) {
      case TestResult.passed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = languageService.isArabic ? 'نجح' : 'Passed';
        break;
      case TestResult.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = languageService.isArabic ? 'فشل' : 'Failed';
        break;
      case TestResult.inProgress:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = languageService.isArabic ? 'قيد التشغيل' : 'Running';
        break;
      case TestResult.notStarted:
        statusColor = Colors.grey;
        statusIcon = Icons.radio_button_unchecked;
        statusText = languageService.isArabic ? 'لم يبدأ' : 'Not Started';
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          languageService.isArabic ? test.nameAr : test.nameEn,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(test.description),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            if (result == TestResult.inProgress)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        onTap: () => _runSingleTest(test.id),
      ),
    );
  }

  Widget _buildRoundProgress(LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            languageService.isArabic 
                ? 'جولة الاختبار ${_currentTestRound + 1} من $_totalRounds'
                : 'Test Round ${_currentTestRound + 1} of $_totalRounds',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentTestRound + 1) / _totalRounds,
            backgroundColor: Colors.blue.withValues(alpha: 0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
        ],
      ),
    );
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTests = true;
      _currentTestRound = 0;
    });

    widget.onStatusChanged(TestingStatus.inProgress);

    // Run 5 rounds of testing as per requirements
    for (int round = 0; round < _totalRounds; round++) {
      setState(() {
        _currentTestRound = round;
      });

      for (final test in _notificationTests) {
        await _runSingleTestInternal(test.id);
        await Future.delayed(const Duration(milliseconds: 500)); // Brief delay between tests
      }

      await Future.delayed(const Duration(seconds: 1)); // Delay between rounds
    }

    setState(() {
      _isRunningTests = false;
    });

    // Check if all tests passed
    final allPassed = _testResults.values.every((result) => result == TestResult.passed);
    widget.onStatusChanged(allPassed ? TestingStatus.passed : TestingStatus.failed);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            allPassed
                ? (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'جميع اختبارات الإشعارات نجحت!'
                    : 'All notification tests passed!')
                : (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'بعض اختبارات الإشعارات فشلت'
                    : 'Some notification tests failed'),
          ),
          backgroundColor: allPassed ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _runSingleTest(String testId) async {
    await _runSingleTestInternal(testId);
  }

  Future<void> _runSingleTestInternal(String testId) async {
    setState(() {
      _testResults[testId] = TestResult.inProgress;
    });

    try {
      bool testPassed = false;

      switch (testId) {
        case 'basic_notification':
          testPassed = await _testBasicNotification();
          break;
        case 'scheduled_notification':
          testPassed = await _testScheduledNotification();
          break;
        case 'recurring_notification':
          testPassed = await _testRecurringNotification();
          break;
        case 'athkar_reminder':
          testPassed = await _testAthkarReminder();
          break;
        case 'prayer_time_alert':
          testPassed = await _testPrayerTimeAlert();
          break;
        case 'custom_sound':
          testPassed = await _testCustomSound();
          break;
        case 'notification_actions':
          testPassed = await _testNotificationActions();
          break;
        case 'background_notification':
          testPassed = await _testBackgroundNotification();
          break;
      }

      setState(() {
        _testResults[testId] = testPassed ? TestResult.passed : TestResult.failed;
      });
    } catch (e) {
      setState(() {
        _testResults[testId] = TestResult.failed;
      });
    }
  }

  Future<bool> _testBasicNotification() async {
    try {
      await _notificationService.showNotification(
        title: 'Test Notification',
        body: 'This is a test notification',
        id: 999,
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testScheduledNotification() async {
    try {
      await _notificationService.scheduleNotification(
        title: 'Scheduled Test',
        body: 'This is a scheduled test notification',
        scheduledTime: DateTime.now().add(const Duration(seconds: 5)),
        id: 998,
      );
      await Future.delayed(const Duration(seconds: 2));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testRecurringNotification() async {
    try {
      await _notificationService.scheduleRecurringNotification(
        title: 'Recurring Test',
        body: 'This is a recurring test notification',
        initialTime: DateTime.now().add(const Duration(seconds: 3)),
        repeatInterval: const Duration(minutes: 1),
        id: 997,
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testAthkarReminder() async {
    try {
      await _notificationService.scheduleAthkarReminder(
        athkarName: 'أذكار الصباح',
        scheduledTime: DateTime.now().add(const Duration(seconds: 2)),
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testPrayerTimeAlert() async {
    try {
      await _notificationService.schedulePrayerTimeAlert(
        prayerName: 'الفجر',
        prayerTime: DateTime.now().add(const Duration(seconds: 3)),
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testCustomSound() async {
    try {
      await _notificationService.showNotificationWithCustomSound(
        title: 'Custom Sound Test',
        body: 'Testing custom notification sound',
        soundPath: 'assets/sounds/notification.mp3',
        id: 996,
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testNotificationActions() async {
    try {
      await _notificationService.showNotificationWithActions(
        title: 'Action Test',
        body: 'Testing notification actions',
        actions: ['Mark as Read', 'Snooze'],
        id: 995,
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testBackgroundNotification() async {
    try {
      await _notificationService.showBackgroundNotification(
        title: 'Background Test',
        body: 'Testing background notification',
        id: 994,
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  void _resetTests() {
    setState(() {
      _initializeTestResults();
      _isRunningTests = false;
      _currentTestRound = 0;
    });
    widget.onStatusChanged(TestingStatus.notStarted);
  }
}

class NotificationTest {
  final String id;
  final String nameAr;
  final String nameEn;
  final String description;

  NotificationTest({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.description,
  });
}

enum TestResult {
  notStarted,
  inProgress,
  passed,
  failed,
}
