import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'home_screen.dart';
import 'athkar_screen.dart';
import 'prebuilt_athkar_screen.dart';
import 'tasbeeh_dua_screen.dart';
import 'prayer_times_screen.dart';
import 'qibla_screen.dart';
import 'quran_screen.dart';
import 'islamic_calendar_screen.dart';
import 'settings_screen.dart';
import 'comprehensive_testing_screen.dart';

class MainNavigation extends StatefulWidget {
  const MainNavigation({super.key});

  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const PrayerTimesScreen(),
    const QiblaScreen(),
    const QuranScreen(),
    const AthkarScreen(),
    const TasbeehDuaScreen(),
    const IslamicCalendarScreen(),
    const SettingsScreen(),
  ];

  final List<BottomNavigationBarItem> _navigationItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.home_outlined),
      activeIcon: Icon(Icons.home),
      label: 'Home',
    ),
    BottomNavigationBarItem(
      icon: Icon(MdiIcons.clockTimeEightOutline),
      activeIcon: Icon(MdiIcons.clockTimeEight),
      label: 'Prayer',
    ),
    BottomNavigationBarItem(
      icon: Icon(MdiIcons.compassOutline),
      activeIcon: Icon(MdiIcons.compass),
      label: 'Qibla',
    ),
    BottomNavigationBarItem(
      icon: Icon(MdiIcons.bookOpenPageVariantOutline),
      activeIcon: Icon(MdiIcons.bookOpenPageVariant),
      label: 'Quran',
    ),
    BottomNavigationBarItem(
      icon: Icon(MdiIcons.counter),
      activeIcon: Icon(MdiIcons.counter),
      label: 'Athkar',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: _navigationItems,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 8,
      ),
      drawer: _buildDrawer(context),
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Icon(
                  Icons.mosque,
                  color: Colors.white,
                  size: 48,
                ),
                SizedBox(height: 8),
                Text(
                  'Athkar App',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Islamic Remembrance',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          _buildDrawerItem(
            icon: MdiIcons.counter,
            title: 'Tasbeeh & Dua',
            index: 5,
          ),
          _buildDrawerItem(
            icon: MdiIcons.calendarMonth,
            title: 'Islamic Calendar',
            index: 6,
          ),
          _buildDrawerItem(
            icon: MdiIcons.libraryOutline,
            title: 'Prebuilt Athkar',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const PrebuiltAthkarScreen()),
              );
            },
          ),
          const Divider(),
          _buildDrawerItem(
            icon: Icons.settings,
            title: 'Settings',
            index: 7,
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    int? index,
    VoidCallback? onTap,
  }) {
    final isSelected = index != null && _currentIndex == index;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Theme.of(context).colorScheme.primary : null,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? Theme.of(context).colorScheme.primary : null,
          fontWeight: isSelected ? FontWeight.bold : null,
        ),
      ),
      selected: isSelected,
      onTap: onTap ?? () {
        if (index != null) {
          setState(() {
            _currentIndex = index;
          });
        }
        Navigator.pop(context);
      },
    );
  }
}
