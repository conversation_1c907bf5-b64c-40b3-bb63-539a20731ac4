import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:adhan/adhan.dart' as adhan;
import 'package:prayers_times/prayers_times.dart' as pt;
import '../models/prayer_times_models.dart';

class PrayerTimesService {
  static const String _baseUrl = 'https://api.aladhan.com/v1';
  static PrayerTimes? _cachedPrayerTimes;
  static DateTime? _lastFetchDate;

  // Jordan default coordinates (Amman)
  static const double _defaultLatitude = 31.9454;
  static const double _defaultLongitude = 35.9284;
  // Default city and timezone for Jordan (for future use)
  // static const String _defaultCity = 'Amman, Jordan';
  // static const String _defaultTimezone = 'Asia/Amman';

  /// Get today's prayer times for the current location
  static Future<PrayerTimes> getTodayPrayerTimes() async {
    // Check if we have cached data for today
    final today = DateTime.now();
    if (_cachedPrayerTimes != null && 
        _lastFetchDate != null && 
        _isSameDay(_lastFetchDate!, today)) {
      return _cachedPrayerTimes!;
    }

    try {
      // Try to get current location, fallback to Jordan
      Position? position;
      try {
        position = await _getCurrentLocation();
      } catch (e) {
        debugPrint('Could not get current location, using Jordan default: $e');
      }

      final latitude = position?.latitude ?? _defaultLatitude;
      final longitude = position?.longitude ?? _defaultLongitude;

      // Fetch prayer times from API
      final prayerTimes = await _fetchPrayerTimesFromAPI(
        latitude,
        longitude,
        today,
      );

      // Cache the result
      _cachedPrayerTimes = prayerTimes;
      _lastFetchDate = today;

      return prayerTimes;
    } catch (e) {
      // If API fails, calculate locally
      return _calculatePrayerTimesLocally();
    }
  }

  /// Get prayer times with user adjustments applied
  static Future<PrayerTimes> getAdjustedPrayerTimes() async {
    final baseTimes = await getTodayPrayerTimes();
    return await _applyUserAdjustments(baseTimes);
  }

  /// Apply user adjustments to prayer times
  static Future<PrayerTimes> _applyUserAdjustments(PrayerTimes baseTimes) async {
    final prefs = await SharedPreferences.getInstance();

    final fajrAdjustment = prefs.getInt('fajr_adjustment') ?? 0;
    final dhuhrAdjustment = prefs.getInt('dhuhr_adjustment') ?? 0;
    final asrAdjustment = prefs.getInt('asr_adjustment') ?? 0;
    final maghribAdjustment = prefs.getInt('maghrib_adjustment') ?? 0;
    final ishaAdjustment = prefs.getInt('isha_adjustment') ?? 0;

    return PrayerTimes(
      location: baseTimes.location,
      date: baseTimes.date,
      fajr: _adjustTime(baseTimes.fajr, fajrAdjustment),
      sunrise: baseTimes.sunrise, // Sunrise is not adjustable
      dhuhr: _adjustTime(baseTimes.dhuhr, dhuhrAdjustment),
      asr: _adjustTime(baseTimes.asr, asrAdjustment),
      maghrib: _adjustTime(baseTimes.maghrib, maghribAdjustment),
      isha: _adjustTime(baseTimes.isha, ishaAdjustment),
      latitude: baseTimes.latitude,
      longitude: baseTimes.longitude,
    );
  }

  /// Adjust time by minutes
  static String _adjustTime(String timeString, int adjustmentMinutes) {
    if (adjustmentMinutes == 0) return timeString;

    final parts = timeString.split(':');
    if (parts.length != 2) return timeString;

    final hour = int.tryParse(parts[0]) ?? 0;
    final minute = int.tryParse(parts[1]) ?? 0;

    final totalMinutes = hour * 60 + minute + adjustmentMinutes;
    final adjustedHour = (totalMinutes ~/ 60) % 24;
    final adjustedMinute = totalMinutes % 60;

    return '${adjustedHour.toString().padLeft(2, '0')}:${adjustedMinute.toString().padLeft(2, '0')}';
  }

  /// Save prayer time adjustment
  static Future<void> savePrayerAdjustment(String prayer, int minutes) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('${prayer.toLowerCase()}_adjustment', minutes);
  }

  /// Get prayer time adjustment
  static Future<int> getPrayerAdjustment(String prayer) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt('${prayer.toLowerCase()}_adjustment') ?? 0;
  }

  /// Get all prayer adjustments
  static Future<Map<String, int>> getAllPrayerAdjustments() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'fajr': prefs.getInt('fajr_adjustment') ?? 0,
      'dhuhr': prefs.getInt('dhuhr_adjustment') ?? 0,
      'asr': prefs.getInt('asr_adjustment') ?? 0,
      'maghrib': prefs.getInt('maghrib_adjustment') ?? 0,
      'isha': prefs.getInt('isha_adjustment') ?? 0,
    };
  }

  /// Get prayer times for a specific date and location
  static Future<PrayerTimes> getPrayerTimesForDate(
    double latitude,
    double longitude,
    DateTime date,
  ) async {
    try {
      return await _fetchPrayerTimesFromAPI(latitude, longitude, date);
    } catch (e) {
      return _calculatePrayerTimesLocally(
        latitude: latitude,
        longitude: longitude,
        date: date,
      );
    }
  }

  /// Get current location
  static Future<Position> _getCurrentLocation() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('Location services are disabled');
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('Location permissions are permanently denied');
    }

    return await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );
  }

  /// Fetch prayer times from external API
  static Future<PrayerTimes> _fetchPrayerTimesFromAPI(
    double latitude,
    double longitude,
    DateTime date,
  ) async {
    final dateString = '${date.day}-${date.month}-${date.year}';
    final url = '$_baseUrl/timings/$dateString?latitude=$latitude&longitude=$longitude&method=2';

    final response = await http.get(Uri.parse(url));

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final timings = data['data']['timings'];
      final meta = data['data']['meta'];

      return PrayerTimes(
        location: meta['city'] ?? 'Unknown Location',
        date: dateString,
        fajr: timings['Fajr'] ?? '05:00',
        sunrise: timings['Sunrise'] ?? '06:30',
        dhuhr: timings['Dhuhr'] ?? '12:00',
        asr: timings['Asr'] ?? '15:30',
        maghrib: timings['Maghrib'] ?? '18:00',
        isha: timings['Isha'] ?? '19:30',
        latitude: latitude,
        longitude: longitude,
      );
    } else {
      throw Exception('Failed to fetch prayer times from API');
    }
  }

  /// Calculate prayer times locally using astronomical calculations
  static PrayerTimes _calculatePrayerTimesLocally({
    double? latitude,
    double? longitude,
    DateTime? date,
  }) {
    // Use default values if not provided
    final lat = latitude ?? 21.4225; // Mecca coordinates as fallback
    final lng = longitude ?? 39.8262;
    final calcDate = date ?? DateTime.now();

    // Simple calculation - in a real app, use a proper prayer times library
    final times = _calculateBasicPrayerTimes(lat, lng, calcDate);

    return PrayerTimes(
      location: 'Current Location',
      date: '${calcDate.day}-${calcDate.month}-${calcDate.year}',
      fajr: times['fajr']!,
      sunrise: times['sunrise']!,
      dhuhr: times['dhuhr']!,
      asr: times['asr']!,
      maghrib: times['maghrib']!,
      isha: times['isha']!,
      latitude: lat,
      longitude: lng,
    );
  }

  /// Basic prayer times calculation
  static Map<String, String> _calculateBasicPrayerTimes(
    double latitude,
    double longitude,
    DateTime date,
  ) {
    // This is a simplified calculation
    // In a production app, use a proper Islamic prayer times library
    
    final julianDay = _getJulianDay(date);
    final declinationAngle = _getSunDeclination(julianDay);
    final equationOfTime = _getEquationOfTime(julianDay);
    
    // Calculate solar noon
    final solarNoon = 12 - (longitude / 15) - (equationOfTime / 60);
    
    // Calculate sunrise and sunset
    final hourAngle = _getHourAngle(latitude, declinationAngle, -0.833);
    final sunrise = solarNoon - (hourAngle / 15);
    final sunset = solarNoon + (hourAngle / 15);
    
    // Calculate prayer times
    final fajrAngle = -18.0; // Standard angle for Fajr
    final ishaAngle = -17.0; // Standard angle for Isha
    
    final fajrHourAngle = _getHourAngle(latitude, declinationAngle, fajrAngle);
    final ishaHourAngle = _getHourAngle(latitude, declinationAngle, ishaAngle);
    
    final fajr = solarNoon - (fajrHourAngle / 15);
    final isha = solarNoon + (ishaHourAngle / 15);
    
    // Asr calculation (shadow length = object length + 1)
    final asrAngle = math.atan(1 + math.tan((latitude - declinationAngle) * math.pi / 180));
    final asrHourAngle = _getHourAngle(latitude, declinationAngle, -asrAngle * 180 / math.pi);
    final asr = solarNoon + (asrHourAngle / 15);

    return {
      'fajr': _formatTime(fajr),
      'sunrise': _formatTime(sunrise),
      'dhuhr': _formatTime(solarNoon),
      'asr': _formatTime(asr),
      'maghrib': _formatTime(sunset),
      'isha': _formatTime(isha),
    };
  }

  static double _getJulianDay(DateTime date) {
    final a = (14 - date.month) ~/ 12;
    final y = date.year - a;
    final m = date.month + 12 * a - 3;
    
    return date.day + (153 * m + 2) ~/ 5 + 365 * y + y ~/ 4 - y ~/ 100 + y ~/ 400 - 32045;
  }

  static double _getSunDeclination(double julianDay) {
    final n = julianDay - 2451545.0;
    final l = (280.460 + 0.9856474 * n) % 360;
    final g = ((357.528 + 0.9856003 * n) % 360) * math.pi / 180;
    final lambda = (l + 1.915 * math.sin(g) + 0.020 * math.sin(2 * g)) * math.pi / 180;
    
    return math.asin(math.sin(23.439 * math.pi / 180) * math.sin(lambda)) * 180 / math.pi;
  }

  static double _getEquationOfTime(double julianDay) {
    final n = julianDay - 2451545.0;
    final l = (280.460 + 0.9856474 * n) % 360;
    final g = ((357.528 + 0.9856003 * n) % 360) * math.pi / 180;
    final lambda = (l + 1.915 * math.sin(g) + 0.020 * math.sin(2 * g)) * math.pi / 180;
    
    final alpha = math.atan2(math.cos(23.439 * math.pi / 180) * math.sin(lambda), math.cos(lambda)) * 180 / math.pi;
    return 4 * (l - alpha);
  }

  static double _getHourAngle(double latitude, double declination, double angle) {
    final latRad = latitude * math.pi / 180;
    final decRad = declination * math.pi / 180;
    final angleRad = angle * math.pi / 180;
    
    final cosH = (math.sin(angleRad) - math.sin(latRad) * math.sin(decRad)) / 
                 (math.cos(latRad) * math.cos(decRad));
    
    if (cosH > 1 || cosH < -1) {
      return 0; // Sun doesn't rise/set at this location on this date
    }
    
    return math.acos(cosH) * 180 / math.pi;
  }

  static String _formatTime(double hours) {
    final h = hours.floor();
    final m = ((hours - h) * 60).round();
    return '${h.toString().padLeft(2, '0')}:${m.toString().padLeft(2, '0')}';
  }

  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// Clear cached data
  static void clearCache() {
    _cachedPrayerTimes = null;
    _lastFetchDate = null;
  }

  /// Get next prayer time
  static String getNextPrayer(PrayerTimes prayerTimes) {
    final now = DateTime.now();
    final currentTime = now.hour * 60 + now.minute;

    final prayers = [
      ('Fajr', _parseTime(prayerTimes.fajr)),
      ('Sunrise', _parseTime(prayerTimes.sunrise)),
      ('Dhuhr', _parseTime(prayerTimes.dhuhr)),
      ('Asr', _parseTime(prayerTimes.asr)),
      ('Maghrib', _parseTime(prayerTimes.maghrib)),
      ('Isha', _parseTime(prayerTimes.isha)),
    ];

    for (final prayer in prayers) {
      if (prayer.$2 > currentTime) {
        return prayer.$1;
      }
    }

    return 'Fajr'; // Next day's Fajr
  }

  static int _parseTime(String timeString) {
    final parts = timeString.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  /// Get prayer times using Adhan package (more accurate)
  static Future<PrayerTimes?> getPrayerTimesWithAdhan(
    double latitude,
    double longitude,
    DateTime date,
  ) async {
    try {
      final coordinates = adhan.Coordinates(latitude, longitude);
      final params = adhan.CalculationMethod.muslimWorldLeague.getParameters();

      // Adjust for Jordan specifically
      if (latitude >= 29.0 && latitude <= 34.0 && longitude >= 34.0 && longitude <= 40.0) {
        params.madhab = adhan.Madhab.shafi;
        params.fajrAngle = 18.0;
        params.ishaAngle = 17.0;
      }

      final adhanPrayerTimes = adhan.PrayerTimes.today(coordinates, params);

      return PrayerTimes(
        fajr: _formatTime(adhanPrayerTimes.fajr),
        sunrise: _formatTime(adhanPrayerTimes.sunrise),
        dhuhr: _formatTime(adhanPrayerTimes.dhuhr),
        asr: _formatTime(adhanPrayerTimes.asr),
        maghrib: _formatTime(adhanPrayerTimes.maghrib),
        isha: _formatTime(adhanPrayerTimes.isha),
        date: date.toString().split(' ')[0],
        location: 'Jordan (Adhan Package)',
        latitude: latitude,
        longitude: longitude,
      );
    } catch (e) {
      debugPrint('Error calculating prayer times with Adhan: $e');
      return null;
    }
  }

  /// Get prayer times using prayers_times package (verification)
  static Future<PrayerTimes?> getPrayerTimesWithPrayersTimes(
    double latitude,
    double longitude,
    DateTime date,
  ) async {
    try {
      final location = pt.Location(latitude, longitude);
      final params = pt.CalculationParameters(
        method: pt.CalculationMethod.muslimWorldLeague,
        madhab: pt.Madhab.shafi,
        fajrAngle: 18.0,
        ishaAngle: 17.0,
      );

      final ptPrayerTimes = pt.PrayerTimes(location, date, params);

      return PrayerTimes(
        fajr: _formatTime(ptPrayerTimes.fajr),
        sunrise: _formatTime(ptPrayerTimes.sunrise),
        dhuhr: _formatTime(ptPrayerTimes.dhuhr),
        asr: _formatTime(ptPrayerTimes.asr),
        maghrib: _formatTime(ptPrayerTimes.maghrib),
        isha: _formatTime(ptPrayerTimes.isha),
        date: date.toString().split(' ')[0],
        location: 'Jordan (Prayers Times Package)',
        latitude: latitude,
        longitude: longitude,
      );
    } catch (e) {
      debugPrint('Error calculating prayer times with prayers_times: $e');
      return null;
    }
  }

  /// Format DateTime to time string
  static String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }



  /// Get enhanced prayer times with multiple calculation methods
  static Future<Map<String, PrayerTimes?>> getEnhancedPrayerTimes(
    double latitude,
    double longitude,
    DateTime date,
  ) async {
    final results = <String, PrayerTimes?>{};

    // Get prayer times from different sources
    results['adhan'] = await getPrayerTimesWithAdhan(latitude, longitude, date);
    results['prayers_times'] = await getPrayerTimesWithPrayersTimes(latitude, longitude, date);

    return results;
  }

  /// Compare prayer times from different sources
  static Map<String, String> comparePrayerTimes(Map<String, PrayerTimes?> prayerTimes) {
    final comparison = <String, String>{};

    if (prayerTimes['adhan'] != null && prayerTimes['prayers_times'] != null) {
      final adhan = prayerTimes['adhan']!;
      final pt = prayerTimes['prayers_times']!;

      comparison['fajr'] = '${adhan.fajr} vs ${pt.fajr}';
      comparison['dhuhr'] = '${adhan.dhuhr} vs ${pt.dhuhr}';
      comparison['asr'] = '${adhan.asr} vs ${pt.asr}';
      comparison['maghrib'] = '${adhan.maghrib} vs ${pt.maghrib}';
      comparison['isha'] = '${adhan.isha} vs ${pt.isha}';
    }

    return comparison;
  }
}
