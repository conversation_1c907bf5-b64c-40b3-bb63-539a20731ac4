import 'dart:convert';
import 'dart:math' as math;
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import '../models/prayer_times_models.dart';
import '../config/app_config.dart';

class PrayerTimesService {
  static const String _baseUrl = 'https://api.aladhan.com/v1';
  static PrayerTimes? _cachedPrayerTimes;
  static DateTime? _lastFetchDate;

  /// Get today's prayer times for the current location
  static Future<PrayerTimes> getTodayPrayerTimes() async {
    // Check if we have cached data for today
    final today = DateTime.now();
    if (_cachedPrayerTimes != null && 
        _lastFetchDate != null && 
        _isSameDay(_lastFetchDate!, today)) {
      return _cachedPrayerTimes!;
    }

    try {
      // Get current location
      final position = await _getCurrentLocation();
      
      // Fetch prayer times from API
      final prayerTimes = await _fetchPrayerTimesFromAPI(
        position.latitude,
        position.longitude,
        today,
      );

      // Cache the result
      _cachedPrayerTimes = prayerTimes;
      _lastFetchDate = today;

      return prayerTimes;
    } catch (e) {
      // If API fails, calculate locally
      return _calculatePrayerTimesLocally();
    }
  }

  /// Get prayer times for a specific date and location
  static Future<PrayerTimes> getPrayerTimesForDate(
    double latitude,
    double longitude,
    DateTime date,
  ) async {
    try {
      return await _fetchPrayerTimesFromAPI(latitude, longitude, date);
    } catch (e) {
      return _calculatePrayerTimesLocally(
        latitude: latitude,
        longitude: longitude,
        date: date,
      );
    }
  }

  /// Get current location
  static Future<Position> _getCurrentLocation() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('Location services are disabled');
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('Location permissions are permanently denied');
    }

    return await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );
  }

  /// Fetch prayer times from external API
  static Future<PrayerTimes> _fetchPrayerTimesFromAPI(
    double latitude,
    double longitude,
    DateTime date,
  ) async {
    final dateString = '${date.day}-${date.month}-${date.year}';
    final url = '$_baseUrl/timings/$dateString?latitude=$latitude&longitude=$longitude&method=2';

    final response = await http.get(Uri.parse(url));

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final timings = data['data']['timings'];
      final meta = data['data']['meta'];

      return PrayerTimes(
        location: meta['city'] ?? 'Unknown Location',
        date: dateString,
        fajr: timings['Fajr'] ?? '05:00',
        sunrise: timings['Sunrise'] ?? '06:30',
        dhuhr: timings['Dhuhr'] ?? '12:00',
        asr: timings['Asr'] ?? '15:30',
        maghrib: timings['Maghrib'] ?? '18:00',
        isha: timings['Isha'] ?? '19:30',
        latitude: latitude,
        longitude: longitude,
      );
    } else {
      throw Exception('Failed to fetch prayer times from API');
    }
  }

  /// Calculate prayer times locally using astronomical calculations
  static PrayerTimes _calculatePrayerTimesLocally({
    double? latitude,
    double? longitude,
    DateTime? date,
  }) {
    // Use default values if not provided
    final lat = latitude ?? 21.4225; // Mecca coordinates as fallback
    final lng = longitude ?? 39.8262;
    final calcDate = date ?? DateTime.now();

    // Simple calculation - in a real app, use a proper prayer times library
    final times = _calculateBasicPrayerTimes(lat, lng, calcDate);

    return PrayerTimes(
      location: 'Current Location',
      date: '${calcDate.day}-${calcDate.month}-${calcDate.year}',
      fajr: times['fajr']!,
      sunrise: times['sunrise']!,
      dhuhr: times['dhuhr']!,
      asr: times['asr']!,
      maghrib: times['maghrib']!,
      isha: times['isha']!,
      latitude: lat,
      longitude: lng,
    );
  }

  /// Basic prayer times calculation
  static Map<String, String> _calculateBasicPrayerTimes(
    double latitude,
    double longitude,
    DateTime date,
  ) {
    // This is a simplified calculation
    // In a production app, use a proper Islamic prayer times library
    
    final julianDay = _getJulianDay(date);
    final declinationAngle = _getSunDeclination(julianDay);
    final equationOfTime = _getEquationOfTime(julianDay);
    
    // Calculate solar noon
    final solarNoon = 12 - (longitude / 15) - (equationOfTime / 60);
    
    // Calculate sunrise and sunset
    final hourAngle = _getHourAngle(latitude, declinationAngle, -0.833);
    final sunrise = solarNoon - (hourAngle / 15);
    final sunset = solarNoon + (hourAngle / 15);
    
    // Calculate prayer times
    final fajrAngle = -18.0; // Standard angle for Fajr
    final ishaAngle = -17.0; // Standard angle for Isha
    
    final fajrHourAngle = _getHourAngle(latitude, declinationAngle, fajrAngle);
    final ishaHourAngle = _getHourAngle(latitude, declinationAngle, ishaAngle);
    
    final fajr = solarNoon - (fajrHourAngle / 15);
    final isha = solarNoon + (ishaHourAngle / 15);
    
    // Asr calculation (shadow length = object length + 1)
    final asrAngle = math.atan(1 + math.tan((latitude - declinationAngle) * math.pi / 180));
    final asrHourAngle = _getHourAngle(latitude, declinationAngle, -asrAngle * 180 / math.pi);
    final asr = solarNoon + (asrHourAngle / 15);

    return {
      'fajr': _formatTime(fajr),
      'sunrise': _formatTime(sunrise),
      'dhuhr': _formatTime(solarNoon),
      'asr': _formatTime(asr),
      'maghrib': _formatTime(sunset),
      'isha': _formatTime(isha),
    };
  }

  static double _getJulianDay(DateTime date) {
    final a = (14 - date.month) ~/ 12;
    final y = date.year - a;
    final m = date.month + 12 * a - 3;
    
    return date.day + (153 * m + 2) ~/ 5 + 365 * y + y ~/ 4 - y ~/ 100 + y ~/ 400 - 32045;
  }

  static double _getSunDeclination(double julianDay) {
    final n = julianDay - 2451545.0;
    final l = (280.460 + 0.9856474 * n) % 360;
    final g = ((357.528 + 0.9856003 * n) % 360) * math.pi / 180;
    final lambda = (l + 1.915 * math.sin(g) + 0.020 * math.sin(2 * g)) * math.pi / 180;
    
    return math.asin(math.sin(23.439 * math.pi / 180) * math.sin(lambda)) * 180 / math.pi;
  }

  static double _getEquationOfTime(double julianDay) {
    final n = julianDay - 2451545.0;
    final l = (280.460 + 0.9856474 * n) % 360;
    final g = ((357.528 + 0.9856003 * n) % 360) * math.pi / 180;
    final lambda = (l + 1.915 * math.sin(g) + 0.020 * math.sin(2 * g)) * math.pi / 180;
    
    final alpha = math.atan2(math.cos(23.439 * math.pi / 180) * math.sin(lambda), math.cos(lambda)) * 180 / math.pi;
    return 4 * (l - alpha);
  }

  static double _getHourAngle(double latitude, double declination, double angle) {
    final latRad = latitude * math.pi / 180;
    final decRad = declination * math.pi / 180;
    final angleRad = angle * math.pi / 180;
    
    final cosH = (math.sin(angleRad) - math.sin(latRad) * math.sin(decRad)) / 
                 (math.cos(latRad) * math.cos(decRad));
    
    if (cosH > 1 || cosH < -1) {
      return 0; // Sun doesn't rise/set at this location on this date
    }
    
    return math.acos(cosH) * 180 / math.pi;
  }

  static String _formatTime(double hours) {
    final h = hours.floor();
    final m = ((hours - h) * 60).round();
    return '${h.toString().padLeft(2, '0')}:${m.toString().padLeft(2, '0')}';
  }

  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// Clear cached data
  static void clearCache() {
    _cachedPrayerTimes = null;
    _lastFetchDate = null;
  }

  /// Get next prayer time
  static String getNextPrayer(PrayerTimes prayerTimes) {
    final now = DateTime.now();
    final currentTime = now.hour * 60 + now.minute;

    final prayers = [
      ('Fajr', _parseTime(prayerTimes.fajr)),
      ('Sunrise', _parseTime(prayerTimes.sunrise)),
      ('Dhuhr', _parseTime(prayerTimes.dhuhr)),
      ('Asr', _parseTime(prayerTimes.asr)),
      ('Maghrib', _parseTime(prayerTimes.maghrib)),
      ('Isha', _parseTime(prayerTimes.isha)),
    ];

    for (final prayer in prayers) {
      if (prayer.$2 > currentTime) {
        return prayer.$1;
      }
    }

    return 'Fajr'; // Next day's Fajr
  }

  static int _parseTime(String timeString) {
    final parts = timeString.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }
}
