// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quran_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Surah _$Surah<PERSON>rom<PERSON>son(Map<String, dynamic> json) => Surah(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  arabicName: json['name_arabic'] as String,
  englishName: json['name_simple'] as String,
  numberOfAyahs: (json['verses_count'] as num).toInt(),
  revelationPlace: json['revelation_place'] as String,
);

Map<String, dynamic> _$SurahTo<PERSON>son(Surah instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'name_arabic': instance.arabicName,
  'name_simple': instance.englishName,
  'verses_count': instance.numberOfAyahs,
  'revelation_place': instance.revelationPlace,
};

Ayah _$AyahFromJson(Map<String, dynamic> json) => Ayah(
  id: (json['id'] as num).toInt(),
  verseNumber: (json['verse_number'] as num).toInt(),
  textUthmani: json['text_uthmani'] as String,
  textSimple: json['text_simple'] as String,
  translation: json['translation'] as String?,
  transliteration: json['transliteration'] as String?,
  chapterId: (json['chapter_id'] as num?)?.toInt(),
);

Map<String, dynamic> _$AyahToJson(Ayah instance) => <String, dynamic>{
  'id': instance.id,
  'verse_number': instance.verseNumber,
  'text_uthmani': instance.textUthmani,
  'text_simple': instance.textSimple,
  'translation': instance.translation,
  'transliteration': instance.transliteration,
  'chapter_id': instance.chapterId,
};

Translation _$TranslationFromJson(Map<String, dynamic> json) => Translation(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  authorName: json['author_name'] as String,
  languageName: json['language_name'] as String,
  slug: json['slug'] as String,
);

Map<String, dynamic> _$TranslationToJson(Translation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'author_name': instance.authorName,
      'language_name': instance.languageName,
      'slug': instance.slug,
    };

VerseOfTheDay _$VerseOfTheDayFromJson(Map<String, dynamic> json) =>
    VerseOfTheDay(
      ayah: Ayah.fromJson(json['ayah'] as Map<String, dynamic>),
      surah: json['surah'] == null
          ? null
          : Surah.fromJson(json['surah'] as Map<String, dynamic>),
      date: DateTime.parse(json['date'] as String),
    );

Map<String, dynamic> _$VerseOfTheDayToJson(VerseOfTheDay instance) =>
    <String, dynamic>{
      'ayah': instance.ayah,
      'surah': instance.surah,
      'date': instance.date.toIso8601String(),
    };

SearchResult _$SearchResultFromJson(Map<String, dynamic> json) => SearchResult(
  id: (json['id'] as num).toInt(),
  verseNumber: (json['verse_number'] as num).toInt(),
  chapterId: (json['chapter_id'] as num).toInt(),
  text: json['text'] as String,
  translation: json['translation'] as String?,
  highlights: (json['highlights'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$SearchResultToJson(SearchResult instance) =>
    <String, dynamic>{
      'id': instance.id,
      'verse_number': instance.verseNumber,
      'chapter_id': instance.chapterId,
      'text': instance.text,
      'translation': instance.translation,
      'highlights': instance.highlights,
    };

Tafsir _$TafsirFromJson(Map<String, dynamic> json) => Tafsir(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  authorName: json['author_name'] as String,
  text: json['text'] as String,
  languageName: json['language_name'] as String,
);

Map<String, dynamic> _$TafsirToJson(Tafsir instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'author_name': instance.authorName,
  'text': instance.text,
  'language_name': instance.languageName,
};

BookmarkedVerse _$BookmarkedVerseFromJson(Map<String, dynamic> json) =>
    BookmarkedVerse(
      surahNumber: (json['surahNumber'] as num).toInt(),
      ayahNumber: (json['ayahNumber'] as num).toInt(),
      note: json['note'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      tags: json['tags'] as String?,
    );

Map<String, dynamic> _$BookmarkedVerseToJson(BookmarkedVerse instance) =>
    <String, dynamic>{
      'surahNumber': instance.surahNumber,
      'ayahNumber': instance.ayahNumber,
      'note': instance.note,
      'createdAt': instance.createdAt.toIso8601String(),
      'tags': instance.tags,
    };

ReadingProgress _$ReadingProgressFromJson(Map<String, dynamic> json) =>
    ReadingProgress(
      completedSurahs: (json['completedSurahs'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      currentSurah: (json['currentSurah'] as num).toInt(),
      currentAyah: (json['currentAyah'] as num).toInt(),
      totalAyahsRead: (json['totalAyahsRead'] as num).toInt(),
      lastReadDate: DateTime.parse(json['lastReadDate'] as String),
      totalReadingTime: json['totalReadingTime'] == null
          ? Duration.zero
          : Duration(microseconds: (json['totalReadingTime'] as num).toInt()),
    );

Map<String, dynamic> _$ReadingProgressToJson(ReadingProgress instance) =>
    <String, dynamic>{
      'completedSurahs': instance.completedSurahs,
      'currentSurah': instance.currentSurah,
      'currentAyah': instance.currentAyah,
      'totalAyahsRead': instance.totalAyahsRead,
      'lastReadDate': instance.lastReadDate.toIso8601String(),
      'totalReadingTime': instance.totalReadingTime.inMicroseconds,
    };

Reciter _$ReciterFromJson(Map<String, dynamic> json) => Reciter(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  arabicName: json['arabic_name'] as String,
  style: json['style'] as String,
  fileFormats: (json['file_formats'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$ReciterToJson(Reciter instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'arabic_name': instance.arabicName,
  'style': instance.style,
  'file_formats': instance.fileFormats,
};

QuranSettings _$QuranSettingsFromJson(Map<String, dynamic> json) =>
    QuranSettings(
      preferredTranslation:
          json['preferredTranslation'] as String? ?? 'en.sahih',
      preferredReciter: json['preferredReciter'] as String? ?? 'ar.alafasy',
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 18.0,
      showTransliteration: json['showTransliteration'] as bool? ?? true,
      showTranslation: json['showTranslation'] as bool? ?? true,
      autoPlay: json['autoPlay'] as bool? ?? false,
      playbackSpeed: (json['playbackSpeed'] as num?)?.toDouble() ?? 1.0,
      nightMode: json['nightMode'] as bool? ?? false,
    );

Map<String, dynamic> _$QuranSettingsToJson(QuranSettings instance) =>
    <String, dynamic>{
      'preferredTranslation': instance.preferredTranslation,
      'preferredReciter': instance.preferredReciter,
      'fontSize': instance.fontSize,
      'showTransliteration': instance.showTransliteration,
      'showTranslation': instance.showTranslation,
      'autoPlay': instance.autoPlay,
      'playbackSpeed': instance.playbackSpeed,
      'nightMode': instance.nightMode,
    };

QuranStudySession _$QuranStudySessionFromJson(Map<String, dynamic> json) =>
    QuranStudySession(
      id: json['id'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
      surahNumber: (json['surahNumber'] as num).toInt(),
      startAyah: (json['startAyah'] as num).toInt(),
      endAyah: (json['endAyah'] as num?)?.toInt(),
      duration: json['duration'] == null
          ? Duration.zero
          : Duration(microseconds: (json['duration'] as num).toInt()),
      notes:
          (json['notes'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$QuranStudySessionToJson(QuranStudySession instance) =>
    <String, dynamic>{
      'id': instance.id,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'surahNumber': instance.surahNumber,
      'startAyah': instance.startAyah,
      'endAyah': instance.endAyah,
      'duration': instance.duration.inMicroseconds,
      'notes': instance.notes,
      'metadata': instance.metadata,
    };
