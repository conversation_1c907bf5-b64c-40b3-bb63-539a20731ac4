import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/language_service.dart';
import '../theme/app_theme.dart';
import '../screens/home_screen.dart';
import '../screens/athkar_screen.dart';
import '../screens/quran_screen.dart';
import '../screens/hadith_screen.dart';

class UnifiedNavigation extends StatefulWidget {
  final int initialIndex;
  
  const UnifiedNavigation({
    super.key,
    this.initialIndex = 0,
  });

  @override
  State<UnifiedNavigation> createState() => _UnifiedNavigationState();
}

class _UnifiedNavigationState extends State<UnifiedNavigation> {
  late int _currentIndex;
  late PageController _pageController;

  final List<Widget> _screens = [
    const HomeScreen(),
    const AthkarScreen(),
    const QuranScreen(),
    const HadithScreen(),
    const HomeScreen(), // Placeholder for Prayer Times
  ];

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        body: PageView(
          controller: _pageController,
          onPageChanged: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          children: _screens,
        ),
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: _onTabTapped,
            type: BottomNavigationBarType.fixed,
            backgroundColor: Colors.white,
            selectedItemColor: AppTheme.primaryGreen,
            unselectedItemColor: Colors.grey[600],
            selectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 11,
            ),
            items: [
              BottomNavigationBarItem(
                icon: const Icon(Icons.home_outlined),
                activeIcon: const Icon(Icons.home),
                label: languageService.isArabic ? 'الرئيسية' : 'Home',
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.auto_stories_outlined),
                activeIcon: const Icon(Icons.auto_stories),
                label: languageService.isArabic ? 'الأذكار' : 'Athkar',
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.menu_book_outlined),
                activeIcon: const Icon(Icons.menu_book),
                label: languageService.isArabic ? 'القرآن' : 'Quran',
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.article_outlined),
                activeIcon: const Icon(Icons.article),
                label: languageService.isArabic ? 'الأحاديث' : 'Hadith',
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.access_time_outlined),
                activeIcon: const Icon(Icons.access_time),
                label: languageService.isArabic ? 'الصلاة' : 'Prayer',
              ),
            ],
          ),
        ),
        drawer: _buildUnifiedDrawer(languageService),
      ),
    );
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Widget _buildUnifiedDrawer(LanguageService languageService) {
    return Drawer(
      child: Column(
        children: [
          // Header
          Container(
            height: 200,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppTheme.primaryGreen,
                  AppTheme.primaryGreen.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(40),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.mosque,
                        size: 40,
                        color: AppTheme.primaryGreen,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      languageService.isArabic ? 'أذكار المسلم' : 'Muslim Athkar',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      languageService.isArabic ? 'تطبيق شامل للأذكار والأدعية' : 'Complete Islamic App',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Navigation Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                const SizedBox(height: 16),
                
                // Main Features Section
                _buildSectionHeader(languageService.isArabic ? 'الميزات الرئيسية' : 'Main Features', languageService),
                
                _buildDrawerItem(
                  icon: Icons.home,
                  title: languageService.isArabic ? 'الرئيسية' : 'Home',
                  onTap: () => _navigateToTab(0),
                ),
                
                _buildDrawerItem(
                  icon: Icons.auto_stories,
                  title: languageService.isArabic ? 'الأذكار والأدعية' : 'Athkar & Duas',
                  onTap: () => _navigateToTab(1),
                ),
                
                _buildDrawerItem(
                  icon: Icons.menu_book,
                  title: languageService.isArabic ? 'القرآن الكريم' : 'Holy Quran',
                  onTap: () => _navigateToTab(2),
                ),
                
                _buildDrawerItem(
                  icon: Icons.article,
                  title: languageService.isArabic ? 'الأحاديث النبوية' : 'Prophetic Hadiths',
                  onTap: () => _navigateToTab(3),
                ),
                
                _buildDrawerItem(
                  icon: Icons.access_time,
                  title: languageService.isArabic ? 'أوقات الصلاة' : 'Prayer Times',
                  onTap: () => _navigateToTab(4),
                ),

                const Divider(height: 32),

                // Additional Features Section
                _buildSectionHeader(languageService.isArabic ? 'ميزات إضافية' : 'Additional Features', languageService),
                
                _buildDrawerItem(
                  icon: Icons.explore,
                  title: languageService.isArabic ? 'اتجاه القبلة' : 'Qibla Direction',
                  onTap: () => _navigateToScreen('/qibla'),
                ),
                
                _buildDrawerItem(
                  icon: Icons.calendar_today,
                  title: languageService.isArabic ? 'التقويم الإسلامي' : 'Islamic Calendar',
                  onTap: () => _navigateToScreen('/islamic-calendar'),
                ),
                
                _buildDrawerItem(
                  icon: Icons.analytics,
                  title: languageService.isArabic ? 'الإحصائيات' : 'Statistics',
                  onTap: () => _navigateToScreen('/statistics'),
                ),

                const Divider(height: 32),

                // Settings Section
                _buildSectionHeader(languageService.isArabic ? 'الإعدادات' : 'Settings', languageService),
                
                _buildDrawerItem(
                  icon: Icons.settings,
                  title: languageService.isArabic ? 'الإعدادات' : 'Settings',
                  onTap: () => _navigateToScreen('/settings'),
                ),
                
                _buildDrawerItem(
                  icon: Icons.person,
                  title: languageService.isArabic ? 'الملف الشخصي' : 'Profile',
                  onTap: () => _navigateToScreen('/profile'),
                ),
                
                _buildDrawerItem(
                  icon: Icons.info,
                  title: languageService.isArabic ? 'حول التطبيق' : 'About',
                  onTap: () => _navigateToScreen('/about'),
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, LanguageService languageService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: iconColor ?? AppTheme.primaryGreen,
        size: 24,
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: () {
        Navigator.pop(context); // Close drawer
        onTap();
      },
      contentPadding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 4.0),
    );
  }

  void _navigateToTab(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _navigateToScreen(String route) {
    Navigator.pushNamed(context, route);
  }
}
