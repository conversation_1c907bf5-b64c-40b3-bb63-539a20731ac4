import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/language_service.dart';

import '../theme/app_theme.dart';

class PrayerCalculationSettingsScreen extends StatefulWidget {
  const PrayerCalculationSettingsScreen({super.key});

  @override
  State<PrayerCalculationSettingsScreen> createState() => _PrayerCalculationSettingsScreenState();
}

class _PrayerCalculationSettingsScreenState extends State<PrayerCalculationSettingsScreen> {
  String _selectedMethod = 'muslim_world_league';
  String _selectedMadhab = 'shafi';
  bool _isLoading = false;

  // Available calculation methods
  static const Map<String, Map<String, dynamic>> calculationMethods = {
    'muslim_world_league': {
      'name_ar': 'رابطة العالم الإسلامي',
      'name_en': 'Muslim World League',
      'description_ar': 'الطريقة المعتمدة من رابطة العالم الإسلامي (مكة المكرمة)',
      'description_en': 'Method adopted by Muslim World League (Makkah)',
      'fajr_angle': 18.0,
      'isha_angle': 17.0,
    },
    'egyptian': {
      'name_ar': 'الهيئة المصرية العامة للمساحة',
      'name_en': 'Egyptian General Authority of Survey',
      'description_ar': 'الطريقة المعتمدة في مصر',
      'description_en': 'Method used in Egypt',
      'fajr_angle': 19.5,
      'isha_angle': 17.5,
    },
    'karachi': {
      'name_ar': 'جامعة العلوم الإسلامية - كراتشي',
      'name_en': 'University of Islamic Sciences, Karachi',
      'description_ar': 'الطريقة المعتمدة في باكستان وبنغلاديش والهند',
      'description_en': 'Method used in Pakistan, Bangladesh, India',
      'fajr_angle': 18.0,
      'isha_angle': 18.0,
    },
    'isna': {
      'name_ar': 'الجمعية الإسلامية لأمريكا الشمالية',
      'name_en': 'Islamic Society of North America (ISNA)',
      'description_ar': 'الطريقة المعتمدة في أمريكا الشمالية',
      'description_en': 'Method used in North America',
      'fajr_angle': 15.0,
      'isha_angle': 15.0,
    },
    'makkah': {
      'name_ar': 'أم القرى - مكة المكرمة',
      'name_en': 'Umm Al-Qura University, Makkah',
      'description_ar': 'الطريقة المعتمدة في السعودية',
      'description_en': 'Method used in Saudi Arabia',
      'fajr_angle': 18.5,
      'isha_angle': 90.0, // 90 minutes after Maghrib
    },
    'tehran': {
      'name_ar': 'معهد الجيوفيزياء - طهران',
      'name_en': 'Institute of Geophysics, University of Tehran',
      'description_ar': 'الطريقة المعتمدة في إيران',
      'description_en': 'Method used in Iran',
      'fajr_angle': 17.7,
      'isha_angle': 14.0,
    },
    'jafari': {
      'name_ar': 'الفقه الجعفري',
      'name_en': 'Shia Ithna-Ashari, Leva Institute, Qum',
      'description_ar': 'الطريقة المعتمدة في الفقه الجعفري',
      'description_en': 'Method used in Jafari jurisprudence',
      'fajr_angle': 16.0,
      'isha_angle': 14.0,
    },
  };

  // Available madhabs
  static const Map<String, Map<String, String>> madhabs = {
    'shafi': {
      'name_ar': 'الشافعي',
      'name_en': 'Shafi',
      'description_ar': 'المذهب الشافعي (الأردن، مصر، إندونيسيا)',
      'description_en': 'Shafi school (Jordan, Egypt, Indonesia)',
    },
    'hanafi': {
      'name_ar': 'الحنفي',
      'name_en': 'Hanafi',
      'description_ar': 'المذهب الحنفي (تركيا، آسيا الوسطى)',
      'description_en': 'Hanafi school (Turkey, Central Asia)',
    },
  };

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);
    try {
      final prefs = await SharedPreferences.getInstance();
      _selectedMethod = prefs.getString('prayer_calculation_method') ?? 'muslim_world_league';
      _selectedMadhab = prefs.getString('prayer_madhab') ?? 'shafi';
    } catch (e) {
      debugPrint('Error loading prayer calculation settings: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('prayer_calculation_method', _selectedMethod);
      await prefs.setString('prayer_madhab', _selectedMadhab);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الإعدادات بنجاح'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error saving prayer calculation settings: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);


    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(languageService.isArabic ? 'طرق حساب أوقات الصلاة' : 'Prayer Calculation Methods'),
          backgroundColor: AppTheme.primaryGreen,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: Icon(languageService.backIcon),
            onPressed: () => Navigator.pop(context),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveSettings,
              tooltip: languageService.isArabic ? 'حفظ' : 'Save',
            ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : ListView(
                padding: const EdgeInsets.all(16.0),
                children: [
                  // Calculation Method Selection
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageService.isArabic ? 'طريقة الحساب' : 'Calculation Method',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            languageService.isArabic
                                ? 'اختر الطريقة المناسبة لحساب أوقات الصلاة حسب موقعك'
                                : 'Choose the appropriate method for calculating prayer times based on your location',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ...calculationMethods.entries.map((entry) {
                            final method = entry.value;
                            return Card(
                              margin: const EdgeInsets.symmetric(vertical: 4),
                              child: RadioListTile<String>(
                                title: Text(
                                  languageService.isArabic ? method['name_ar'] : method['name_en'],
                                  style: const TextStyle(fontWeight: FontWeight.w500),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      languageService.isArabic ? method['description_ar'] : method['description_en'],
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      '${languageService.isArabic ? "زاوية الفجر" : "Fajr angle"}: ${method['fajr_angle']}° | '
                                      '${languageService.isArabic ? "زاوية العشاء" : "Isha angle"}: ${method['isha_angle']}°',
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Colors.grey[600],
                                        fontFamily: 'monospace',
                                      ),
                                    ),
                                  ],
                                ),
                                value: entry.key,
                                groupValue: _selectedMethod,
                                activeColor: AppTheme.primaryGreen,
                                onChanged: (value) {
                                  setState(() => _selectedMethod = value!);
                                },
                              ),
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Madhab Selection
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageService.isArabic ? 'المذهب الفقهي' : 'Jurisprudence School',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            languageService.isArabic
                                ? 'يؤثر المذهب على حساب وقت صلاة العصر'
                                : 'The school affects the calculation of Asr prayer time',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ...madhabs.entries.map((entry) {
                            final madhab = entry.value;
                            return RadioListTile<String>(
                              title: Text(
                                languageService.isArabic ? madhab['name_ar']! : madhab['name_en']!,
                                style: const TextStyle(fontWeight: FontWeight.w500),
                              ),
                              subtitle: Text(
                                languageService.isArabic ? madhab['description_ar']! : madhab['description_en']!,
                                style: const TextStyle(fontSize: 12),
                              ),
                              value: entry.key,
                              groupValue: _selectedMadhab,
                              activeColor: AppTheme.primaryGreen,
                              onChanged: (value) {
                                setState(() => _selectedMadhab = value!);
                              },
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Current Selection Summary
                  Card(
                    color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageService.isArabic ? 'الإعدادات المختارة' : 'Selected Settings',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryGreen,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              const Icon(Icons.calculate, color: AppTheme.primaryGreen, size: 20),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '${languageService.isArabic ? "طريقة الحساب" : "Calculation Method"}: ${languageService.isArabic ? calculationMethods[_selectedMethod]!['name_ar'] : calculationMethods[_selectedMethod]!['name_en']}',
                                  style: const TextStyle(fontWeight: FontWeight.w500),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              const Icon(Icons.school, color: AppTheme.primaryGreen, size: 20),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '${languageService.isArabic ? "المذهب" : "Madhab"}: ${languageService.isArabic ? madhabs[_selectedMadhab]!['name_ar'] : madhabs[_selectedMadhab]!['name_en']}',
                                  style: const TextStyle(fontWeight: FontWeight.w500),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Information
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageService.isArabic ? 'معلومات مهمة' : 'Important Information',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            languageService.isArabic
                                ? '• للأردن: يُنصح باستخدام طريقة "رابطة العالم الإسلامي" مع المذهب الشافعي\n'
                                  '• تختلف أوقات الصلاة قليلاً حسب الطريقة المختارة\n'
                                  '• يمكنك مقارنة النتائج من الشاشة الرئيسية لأوقات الصلاة\n'
                                  '• التطبيق يستخدم حزمة Adhan للحسابات الدقيقة\n'
                                  '• يتم حفظ الإعدادات تلقائياً عند التغيير'
                                : '• For Jordan: Recommended to use "Muslim World League" with Shafi madhab\n'
                                  '• Prayer times vary slightly based on the selected method\n'
                                  '• You can compare results from the main prayer times screen\n'
                                  '• The app uses Adhan package for accurate calculations\n'
                                  '• Settings are saved automatically when changed',
                            style: TextStyle(
                              color: Colors.grey[600],
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
