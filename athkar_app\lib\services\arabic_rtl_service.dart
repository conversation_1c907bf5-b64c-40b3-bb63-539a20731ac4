import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui' as ui;

/// Comprehensive Arabic RTL service with Islamic typography support
/// Provides complete right-to-left text support and Arabic font optimization
class ArabicRTLService {
  static final ArabicRTLService _instance = ArabicRTLService._internal();
  factory ArabicRTLService() => _instance;
  ArabicRTLService._internal();

  // RTL configuration
  bool _isRTLEnabled = true;
  String _currentLocale = 'ar';
  TextDirection _textDirection = TextDirection.rtl;
  
  // Font configuration
  final Map<ArabicFontType, String> _fontFamilies = {
    ArabicFontType.quran: 'Uthmanic',
    ArabicFontType.hadith: 'Amiri',
    ArabicFontType.general: 'Cairo',
    ArabicFontType.decorative: 'Scheherazade',
  };
  
  // Typography scales
  final Map<ArabicTextStyle, TextStyle> _textStyles = {};
  
  bool _isInitialized = false;

  /// Initialize the Arabic RTL service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadArabicFonts();
      await _initializeTextStyles();
      await _configureSystemLocale();
      
      _isInitialized = true;
      debugPrint('Arabic RTL Service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Arabic RTL Service: $e');
      rethrow;
    }
  }

  /// Get text direction based on content
  TextDirection getTextDirection([String? text]) {
    if (text != null && text.isNotEmpty) {
      return _detectTextDirection(text);
    }
    return _textDirection;
  }

  /// Get appropriate text style for Arabic content
  TextStyle getArabicTextStyle(ArabicTextStyle styleType, {
    double? fontSize,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final baseStyle = _textStyles[styleType] ?? const TextStyle();
    
    return baseStyle.copyWith(
      fontSize: fontSize,
      color: color,
      fontWeight: fontWeight,
      textDirection: TextDirection.rtl,
    );
  }

  /// Get Quran text style with proper formatting
  TextStyle getQuranTextStyle({
    double fontSize = 24.0,
    Color? color,
    double? lineHeight,
  }) {
    return TextStyle(
      fontFamily: _fontFamilies[ArabicFontType.quran],
      fontSize: fontSize,
      color: color ?? const Color(0xFF2E7D32),
      height: lineHeight ?? 1.8,
      textDirection: TextDirection.rtl,
      fontFeatures: const [
        FontFeature.contextualAlternates(),
        FontFeature.enable('liga'),
        FontFeature.enable('calt'),
      ],
    );
  }

  /// Get Hadith text style with proper formatting
  TextStyle getHadithTextStyle({
    double fontSize = 18.0,
    Color? color,
    double? lineHeight,
  }) {
    return TextStyle(
      fontFamily: _fontFamilies[ArabicFontType.hadith],
      fontSize: fontSize,
      color: color ?? const Color(0xFF1B5E20),
      height: lineHeight ?? 1.6,
      textDirection: TextDirection.rtl,
      fontFeatures: const [
        FontFeature.contextualAlternates(),
        FontFeature.enable('liga'),
      ],
    );
  }

  /// Get Dua/Dhikr text style with proper formatting
  TextStyle getDuaTextStyle({
    double fontSize = 20.0,
    Color? color,
    double? lineHeight,
    bool isArabic = true,
  }) {
    return TextStyle(
      fontFamily: isArabic ? _fontFamilies[ArabicFontType.general] : null,
      fontSize: fontSize,
      color: color ?? const Color(0xFF2E7D32),
      height: lineHeight ?? 1.7,
      textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
      fontWeight: FontWeight.w500,
    );
  }

  /// Format Arabic text with proper diacritics and spacing
  String formatArabicText(String text, {
    bool preserveDiacritics = true,
    bool addTashkeel = false,
    bool normalizeSpacing = true,
  }) {
    String formattedText = text;
    
    if (normalizeSpacing) {
      // Normalize Arabic spacing
      formattedText = formattedText
          .replaceAll(RegExp(r'\s+'), ' ')
          .replaceAll('ـ', '') // Remove tatweel
          .trim();
    }
    
    if (!preserveDiacritics) {
      // Remove diacritics
      formattedText = _removeDiacritics(formattedText);
    }
    
    if (addTashkeel) {
      // Add basic tashkeel (this would need a comprehensive implementation)
      formattedText = _addBasicTashkeel(formattedText);
    }
    
    return formattedText;
  }

  /// Create RTL-aware widget wrapper
  Widget wrapWithRTL(Widget child, {
    bool forceRTL = false,
    String? text,
  }) {
    final direction = forceRTL ? TextDirection.rtl : getTextDirection(text);
    
    return Directionality(
      textDirection: direction,
      child: child,
    );
  }

  /// Create Arabic text widget with proper styling
  Widget createArabicText(
    String text, {
    ArabicTextStyle style = ArabicTextStyle.body,
    double? fontSize,
    Color? color,
    FontWeight? fontWeight,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) {
    return Text(
      text,
      style: getArabicTextStyle(
        style,
        fontSize: fontSize,
        color: color,
        fontWeight: fontWeight,
      ),
      textDirection: TextDirection.rtl,
      textAlign: textAlign ?? TextAlign.right,
      maxLines: maxLines,
      overflow: overflow,
    );
  }

  /// Create Quran verse widget with proper formatting
  Widget createQuranVerse(
    String arabicText,
    String translation, {
    double arabicFontSize = 24.0,
    double translationFontSize = 16.0,
    Color? arabicColor,
    Color? translationColor,
    bool showTranslation = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Arabic text
        Text(
          arabicText,
          style: getQuranTextStyle(
            fontSize: arabicFontSize,
            color: arabicColor,
          ),
          textDirection: TextDirection.rtl,
          textAlign: TextAlign.center,
        ),
        
        if (showTranslation) ...[
          const SizedBox(height: 12),
          // Translation
          Text(
            translation,
            style: TextStyle(
              fontSize: translationFontSize,
              color: translationColor ?? Colors.grey[700],
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  /// Create Hadith widget with proper formatting
  Widget createHadithText(
    String arabicText,
    String translation,
    String source, {
    double arabicFontSize = 18.0,
    double translationFontSize = 14.0,
    double sourceFontSize = 12.0,
    bool showTranslation = true,
    bool showSource = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Arabic text
        Text(
          arabicText,
          style: getHadithTextStyle(fontSize: arabicFontSize),
          textDirection: TextDirection.rtl,
          textAlign: TextAlign.right,
        ),
        
        if (showTranslation) ...[
          const SizedBox(height: 8),
          // Translation
          Text(
            translation,
            style: TextStyle(
              fontSize: translationFontSize,
              color: Colors.grey[700],
            ),
            textAlign: TextAlign.left,
          ),
        ],
        
        if (showSource) ...[
          const SizedBox(height: 8),
          // Source
          Text(
            source,
            style: TextStyle(
              fontSize: sourceFontSize,
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.right,
          ),
        ],
      ],
    );
  }

  /// Get RTL-aware padding
  EdgeInsets getRTLPadding({
    double? start,
    double? end,
    double? top,
    double? bottom,
  }) {
    if (_textDirection == TextDirection.rtl) {
      return EdgeInsets.only(
        right: start ?? 0,
        left: end ?? 0,
        top: top ?? 0,
        bottom: bottom ?? 0,
      );
    } else {
      return EdgeInsets.only(
        left: start ?? 0,
        right: end ?? 0,
        top: top ?? 0,
        bottom: bottom ?? 0,
      );
    }
  }

  /// Get RTL-aware margin
  EdgeInsets getRTLMargin({
    double? start,
    double? end,
    double? top,
    double? bottom,
  }) {
    return getRTLPadding(
      start: start,
      end: end,
      top: top,
      bottom: bottom,
    );
  }

  /// Detect text direction based on content
  TextDirection _detectTextDirection(String text) {
    if (text.isEmpty) return _textDirection;
    
    // Count Arabic characters
    int arabicChars = 0;
    int latinChars = 0;
    
    for (int i = 0; i < text.length; i++) {
      final char = text.codeUnitAt(i);
      
      // Arabic Unicode ranges
      if ((char >= 0x0600 && char <= 0x06FF) ||  // Arabic
          (char >= 0x0750 && char <= 0x077F) ||  // Arabic Supplement
          (char >= 0x08A0 && char <= 0x08FF) ||  // Arabic Extended-A
          (char >= 0xFB50 && char <= 0xFDFF) ||  // Arabic Presentation Forms-A
          (char >= 0xFE70 && char <= 0xFEFF)) {  // Arabic Presentation Forms-B
        arabicChars++;
      } else if ((char >= 0x0041 && char <= 0x005A) ||  // A-Z
                 (char >= 0x0061 && char <= 0x007A)) {   // a-z
        latinChars++;
      }
    }
    
    return arabicChars > latinChars ? TextDirection.rtl : TextDirection.ltr;
  }

  /// Remove Arabic diacritics
  String _removeDiacritics(String text) {
    // Remove common Arabic diacritics
    return text.replaceAll(RegExp(r'[\u064B-\u0652\u0670\u0640]'), '');
  }

  /// Add basic tashkeel (simplified implementation)
  String _addBasicTashkeel(String text) {
    // This would need a comprehensive Arabic morphology engine
    // For now, return the original text
    return text;
  }

  /// Load Arabic fonts
  Future<void> _loadArabicFonts() async {
    try {
      // In a real implementation, this would load custom Arabic fonts
      debugPrint('Loading Arabic fonts...');
    } catch (e) {
      debugPrint('Error loading Arabic fonts: $e');
    }
  }

  /// Initialize text styles
  Future<void> _initializeTextStyles() async {
    _textStyles[ArabicTextStyle.headline1] = TextStyle(
      fontFamily: _fontFamilies[ArabicFontType.general],
      fontSize: 32,
      fontWeight: FontWeight.bold,
      height: 1.5,
    );
    
    _textStyles[ArabicTextStyle.headline2] = TextStyle(
      fontFamily: _fontFamilies[ArabicFontType.general],
      fontSize: 28,
      fontWeight: FontWeight.w600,
      height: 1.4,
    );
    
    _textStyles[ArabicTextStyle.headline3] = TextStyle(
      fontFamily: _fontFamilies[ArabicFontType.general],
      fontSize: 24,
      fontWeight: FontWeight.w600,
      height: 1.4,
    );
    
    _textStyles[ArabicTextStyle.body] = TextStyle(
      fontFamily: _fontFamilies[ArabicFontType.general],
      fontSize: 16,
      fontWeight: FontWeight.normal,
      height: 1.6,
    );
    
    _textStyles[ArabicTextStyle.caption] = TextStyle(
      fontFamily: _fontFamilies[ArabicFontType.general],
      fontSize: 12,
      fontWeight: FontWeight.normal,
      height: 1.4,
    );
  }

  /// Configure system locale
  Future<void> _configureSystemLocale() async {
    try {
      // Set system locale to Arabic
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    } catch (e) {
      debugPrint('Error configuring system locale: $e');
    }
  }

  /// Toggle RTL mode
  void toggleRTL() {
    _isRTLEnabled = !_isRTLEnabled;
    _textDirection = _isRTLEnabled ? TextDirection.rtl : TextDirection.ltr;
  }

  /// Set locale
  void setLocale(String locale) {
    _currentLocale = locale;
    _isRTLEnabled = locale == 'ar';
    _textDirection = _isRTLEnabled ? TextDirection.rtl : TextDirection.ltr;
  }

  // Getters
  bool get isRTLEnabled => _isRTLEnabled;
  String get currentLocale => _currentLocale;
  TextDirection get textDirection => _textDirection;
}

/// Arabic font types for different content
enum ArabicFontType {
  quran,      // For Quranic text
  hadith,     // For Hadith text
  general,    // For general Arabic text
  decorative, // For decorative Arabic text
}

/// Arabic text styles
enum ArabicTextStyle {
  headline1,
  headline2,
  headline3,
  body,
  caption,
}

/// Extension for Arabic text style display
extension ArabicTextStyleExtension on ArabicTextStyle {
  String get displayName {
    switch (this) {
      case ArabicTextStyle.headline1:
        return 'عنوان رئيسي كبير';
      case ArabicTextStyle.headline2:
        return 'عنوان رئيسي متوسط';
      case ArabicTextStyle.headline3:
        return 'عنوان فرعي';
      case ArabicTextStyle.body:
        return 'نص أساسي';
      case ArabicTextStyle.caption:
        return 'نص توضيحي';
    }
  }
}
