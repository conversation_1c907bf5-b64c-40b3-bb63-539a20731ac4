import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../theme/app_theme.dart';

class ColorPickerWidget extends StatefulWidget {
  final Color currentColor;
  final Function(Color) onColorChanged;
  final String title;

  const ColorPickerWidget({
    super.key,
    required this.currentColor,
    required this.onColorChanged,
    required this.title,
  });

  @override
  State<ColorPickerWidget> createState() => _ColorPickerWidgetState();
}

class _ColorPickerWidgetState extends State<ColorPickerWidget> {
  // Predefined Islamic colors
  static const List<Color> islamicColors = [
    Color(0xFF2E7D32), // Islamic Green
    Color(0xFF1B5E20), // Dark Green
    Color(0xFF4CAF50), // Light Green
    Color(0xFF81C784), // Pale Green
    Color(0xFFFFB300), // Islamic Gold
    Color(0xFFFFA000), // Dark Gold
    Color(0xFFFFD54F), // Light Gold
    Color(0xFF1976D2), // Islamic Blue
    Color(0xFF0D47A1), // Dark Blue
    Color(0xFF42A5F5), // Light Blue
    Color(0xFF5D4037), // Brown
    Color(0xFF8D6E63), // Light Brown
    Color(0xFF424242), // Dark Gray
    Color(0xFF757575), // Gray
    Color(0xFF9E9E9E), // Light Gray
    Color(0xFFFFFFFF), // White
  ];

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Current Color Display
        Container(
          width: double.infinity,
          height: 60,
          decoration: BoxDecoration(
            color: widget.currentColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Center(
            child: Text(
              '#${widget.currentColor.value.toRadixString(16).substring(2).toUpperCase()}',
              style: TextStyle(
                color: _getContrastColor(widget.currentColor),
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Predefined Colors Grid
        Text(
          languageService.isArabic ? 'الألوان الإسلامية' : 'Islamic Colors',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 8),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 8,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: islamicColors.length,
          itemBuilder: (context, index) {
            final color = islamicColors[index];
            final isSelected = color.value == widget.currentColor.value;
            
            return GestureDetector(
              onTap: () => widget.onColorChanged(color),
              child: Container(
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: isSelected ? AppTheme.primaryGreen : Colors.grey[300]!,
                    width: isSelected ? 3 : 1,
                  ),
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        color: _getContrastColor(color),
                        size: 16,
                      )
                    : null,
              ),
            );
          },
        ),
        
        const SizedBox(height: 16),
        
        // Custom Color Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _showCustomColorPicker(context, languageService),
            icon: const Icon(Icons.palette),
            label: Text(
              languageService.isArabic ? 'اختيار لون مخصص' : 'Choose Custom Color',
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.primaryGreen,
              side: const BorderSide(color: AppTheme.primaryGreen),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Color _getContrastColor(Color color) {
    // Calculate luminance to determine if text should be black or white
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  void _showCustomColorPicker(BuildContext context, LanguageService languageService) {
    Color selectedColor = widget.currentColor;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(widget.title),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // RGB Sliders
              _buildColorSlider(
                'Red',
                (selectedColor.r * 255.0).round().toDouble(),
                (value) {
                  selectedColor = Color.fromARGB(
                    (selectedColor.a * 255.0).round(),
                    value.round(),
                    (selectedColor.g * 255.0).round(),
                    (selectedColor.b * 255.0).round(),
                  );
                },
                Colors.red,
              ),
              _buildColorSlider(
                'Green',
                (selectedColor.g * 255.0).round().toDouble(),
                (value) {
                  selectedColor = Color.fromARGB(
                    (selectedColor.a * 255.0).round(),
                    (selectedColor.r * 255.0).round(),
                    value.round(),
                    (selectedColor.b * 255.0).round(),
                  );
                },
                Colors.green,
              ),
              _buildColorSlider(
                'Blue',
                (selectedColor.b * 255.0).round().toDouble(),
                (value) {
                  selectedColor = Color.fromARGB(
                    (selectedColor.a * 255.0).round(),
                    (selectedColor.r * 255.0).round(),
                    (selectedColor.g * 255.0).round(),
                    value.round(),
                  );
                },
                Colors.blue,
              ),
              
              const SizedBox(height: 16),
              
              // Color Preview
              Container(
                width: double.infinity,
                height: 50,
                decoration: BoxDecoration(
                  color: selectedColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              widget.onColorChanged(selectedColor);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              foregroundColor: Colors.white,
            ),
            child: Text(languageService.isArabic ? 'تطبيق' : 'Apply'),
          ),
        ],
      ),
    );
  }

  Widget _buildColorSlider(
    String label,
    double value,
    Function(double) onChanged,
    Color color,
  ) {
    return StatefulBuilder(
      builder: (context, setState) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  label,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                Text(
                  value.round().toString(),
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Slider(
              value: value,
              min: 0,
              max: 255,
              divisions: 255,
              activeColor: color,
              onChanged: (newValue) {
                setState(() {
                  onChanged(newValue);
                });
              },
            ),
          ],
        );
      },
    );
  }
}

/// Simple color picker for quick color selection
class QuickColorPicker extends StatelessWidget {
  final Color currentColor;
  final Function(Color) onColorChanged;
  final List<Color> colors;

  const QuickColorPicker({
    super.key,
    required this.currentColor,
    required this.onColorChanged,
    this.colors = const [
      Color(0xFF2E7D32),
      Color(0xFFFFB300),
      Color(0xFF1976D2),
      Color(0xFF5D4037),
      Color(0xFF424242),
      Color(0xFFD32F2F),
      Color(0xFF7B1FA2),
      Color(0xFF388E3C),
    ],
  });

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: colors.map((color) {
        final isSelected = color.value == currentColor.value;
        return GestureDetector(
          onTap: () => onColorChanged(color),
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? Colors.black : Colors.grey[300]!,
                width: isSelected ? 3 : 1,
              ),
            ),
            child: isSelected
                ? Icon(
                    Icons.check,
                    color: _getContrastColor(color),
                    size: 20,
                  )
                : null,
          ),
        );
      }).toList(),
    );
  }

  Color _getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}
