import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/hadith_provider.dart';
import '../services/language_service.dart';

import '../theme/app_theme.dart';
import '../models/hadith_models.dart';
import 'hadith_detail_screen.dart';

class HadithSearchScreen extends StatefulWidget {
  const HadithSearchScreen({super.key});

  @override
  State<HadithSearchScreen> createState() => _HadithSearchScreenState();
}

class _HadithSearchScreenState extends State<HadithSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<HadithSearchResult> _searchResults = [];
  bool _isSearching = false;
  String _searchType = 'all';
  List<String> _selectedCollections = [];

  @override
  void initState() {
    super.initState();
    final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
    _selectedCollections = hadithProvider.collections.map((c) => c.id).toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _performSearch() async {
    if (_searchController.text.trim().isEmpty) return;

    setState(() {
      _isSearching = true;
      _searchResults.clear();
    });

    try {
      final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
      final results = await hadithProvider.searchHadiths(
        _searchController.text,
        collections: _selectedCollections,
        searchType: _searchType,
        limit: 50,
      );

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _isSearching = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في البحث: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);


    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(languageService.isArabic ? 'البحث في الأحاديث' : 'Search Hadiths'),
          backgroundColor: AppTheme.primaryGreen,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: Icon(languageService.backIcon),
            onPressed: () => Navigator.pop(context),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: _showFilterDialog,
              tooltip: languageService.isArabic ? 'تصفية' : 'Filter',
            ),
          ],
        ),
        body: Column(
          children: [
            // Search Input
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: languageService.isArabic 
                          ? 'ابحث في الأحاديث...' 
                          : 'Search in hadiths...',
                      prefixIcon: const Icon(Icons.search, color: AppTheme.primaryGreen),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchResults.clear();
                                });
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: const BorderSide(color: AppTheme.primaryGreen),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: const BorderSide(color: AppTheme.primaryGreen, width: 2),
                      ),
                    ),
                    onSubmitted: (_) => _performSearch(),
                    onChanged: (value) => setState(() {}),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isSearching ? null : _performSearch,
                          icon: _isSearching 
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                              : const Icon(Icons.search),
                          label: Text(
                            _isSearching 
                                ? (languageService.isArabic ? 'جاري البحث...' : 'Searching...')
                                : (languageService.isArabic ? 'بحث' : 'Search'),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryGreen,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Search Results
            Expanded(
              child: _buildSearchResults(languageService),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults(LanguageService languageService) {
    if (_isSearching) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppTheme.primaryGreen),
            SizedBox(height: 16),
            Text('جاري البحث في الأحاديث...'),
          ],
        ),
      );
    }

    if (_searchResults.isEmpty && _searchController.text.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              languageService.isArabic ? 'لا توجد نتائج' : 'No results found',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              languageService.isArabic 
                  ? 'جرب كلمات مختلفة أو تغيير إعدادات البحث'
                  : 'Try different keywords or change search settings',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              languageService.isArabic ? 'ابحث في الأحاديث' : 'Search in Hadiths',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              languageService.isArabic 
                  ? 'ادخل كلمة أو عبارة للبحث في مجموعات الأحاديث'
                  : 'Enter a word or phrase to search in hadith collections',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final result = _searchResults[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          elevation: 2,
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => HadithDetailScreen(
                    hadith: result.hadith,
                    collection: result.collection,
                    book: result.book,
                  ),
                ),
              );
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with collection and relevance
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          languageService.isArabic 
                              ? result.collection.arabicName 
                              : result.collection.englishName,
                          style: const TextStyle(
                            fontSize: 11,
                            color: AppTheme.primaryGreen,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${(result.relevanceScore * 100).round()}%',
                          style: const TextStyle(
                            fontSize: 11,
                            color: Colors.blue,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${result.hadith.hadithNumber}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Hadith text (truncated)
                  Text(
                    result.hadith.arabicText.length > 200 
                        ? '${result.hadith.arabicText.substring(0, 200)}...'
                        : result.hadith.arabicText,
                    style: const TextStyle(
                      fontSize: 16,
                      height: 1.6,
                      fontWeight: FontWeight.w500,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Book and narrator info
                  Row(
                    children: [
                      Icon(
                        Icons.book,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          languageService.isArabic ? result.book.arabicName : result.book.englishName,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 4),
                  
                  Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          languageService.isArabic ? result.hadith.arabicNarrator : result.hadith.narrator,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontStyle: FontStyle.italic,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _showFilterDialog() {
    final languageService = Provider.of<LanguageService>(context, listen: false);
    final hadithProvider = Provider.of<HadithProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(languageService.isArabic ? 'إعدادات البحث' : 'Search Settings'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Search Type
                Text(
                  languageService.isArabic ? 'نوع البحث' : 'Search Type',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...['all', 'arabic', 'english', 'narrator'].map((type) {
                  final labels = {
                    'all': languageService.isArabic ? 'الكل' : 'All',
                    'arabic': languageService.isArabic ? 'النص العربي' : 'Arabic Text',
                    'english': languageService.isArabic ? 'الترجمة' : 'Translation',
                    'narrator': languageService.isArabic ? 'الراوي' : 'Narrator',
                  };
                  
                  return RadioListTile<String>(
                    title: Text(labels[type]!),
                    value: type,
                    groupValue: _searchType,
                    activeColor: AppTheme.primaryGreen,
                    onChanged: (value) {
                      setDialogState(() => _searchType = value!);
                    },
                  );
                }).toList(),
                
                const SizedBox(height: 16),
                
                // Collections
                Text(
                  languageService.isArabic ? 'المجموعات' : 'Collections',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...hadithProvider.collections.map((collection) {
                  return CheckboxListTile(
                    title: Text(
                      languageService.isArabic ? collection.arabicName : collection.englishName,
                      style: const TextStyle(fontSize: 14),
                    ),
                    value: _selectedCollections.contains(collection.id),
                    activeColor: AppTheme.primaryGreen,
                    onChanged: (value) {
                      setDialogState(() {
                        if (value == true) {
                          _selectedCollections.add(collection.id);
                        } else {
                          _selectedCollections.remove(collection.id);
                        }
                      });
                    },
                  );
                }).toList(),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {});
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
              ),
              child: Text(languageService.isArabic ? 'تطبيق' : 'Apply'),
            ),
          ],
        ),
      ),
    );
  }
}
