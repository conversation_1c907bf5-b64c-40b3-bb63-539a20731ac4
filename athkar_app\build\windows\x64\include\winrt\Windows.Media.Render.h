// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Media_Render_H
#define WINRT_Windows_Media_Render_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.210806.1"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.210806.1"
#include "winrt/Windows.Media.h"
#include "winrt/impl/Windows.Media.Render.2.h"
namespace winrt::impl
{
}
WINRT_EXPORT namespace winrt::Windows::Media::Render
{
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
#endif
}
#endif
