import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../theme/app_theme.dart';
import '../models/athkar_models.dart';
import '../providers/tasbeeh_dua_provider.dart';

class DuaListWidget extends StatefulWidget {
  const DuaListWidget({super.key});

  @override
  State<DuaListWidget> createState() => _DuaListWidgetState();
}

class _DuaListWidgetState extends State<DuaListWidget> {
  String _selectedCategory = 'All';
  
  final List<String> _categories = [
    'All',
    'Daily Duas',
    'Quranic Duas',
    'Prophetic Duas',
    'Travel',
    'Health',
    'Gratitude',
    'Seeking Forgiveness',
    'Protection',
  ];

  final List<DuaData> _defaultDuas = [
    DuaData(
      title: 'Dua when waking up',
      arabicText: 'الْحَمْدُ لِلَّهِ الَّذِي أَحْيَانَا بَعْدَ مَا أَمَاتَنَا وَإِلَيْهِ النُّشُورُ',
      transliteration: '<PERSON><PERSON><PERSON> lillahil-ladhi ahyana ba\'da ma amatana wa ilayhin-nushur',
      translation: 'Praise be to Allah who gave us life after having taken it from us and unto Him is the resurrection.',
      category: 'Daily Duas',
      source: 'Hadith',
      reference: 'Bukhari',
    ),
    DuaData(
      title: 'Dua before eating',
      arabicText: 'بِسْمِ اللهِ',
      transliteration: 'Bismillah',
      translation: 'In the name of Allah',
      category: 'Daily Duas',
      source: 'Hadith',
      reference: 'Abu Dawud',
    ),
    DuaData(
      title: 'Dua after eating',
      arabicText: 'الْحَمْدُ لِلَّهِ الَّذِي أَطْعَمَنَا وَسَقَانَا وَجَعَلَنَا مُسْلِمِينَ',
      transliteration: 'Alhamdu lillahil-ladhi at\'amana wa saqana wa ja\'alana muslimin',
      translation: 'Praise be to Allah who fed us and gave us drink and made us Muslims.',
      category: 'Daily Duas',
      source: 'Hadith',
      reference: 'Abu Dawud, Tirmidhi',
    ),
    DuaData(
      title: 'Dua for guidance',
      arabicText: 'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ',
      transliteration: 'Rabbana atina fi\'d-dunya hasanatan wa fi\'l-akhirati hasanatan wa qina \'adhab an-nar',
      translation: 'Our Lord, give us good in this world and good in the next world, and save us from the punishment of the Fire.',
      category: 'Quranic Duas',
      source: 'Quran',
      reference: '2:201',
    ),
    DuaData(
      title: 'Dua for forgiveness',
      arabicText: 'رَبِّ اغْفِرْ لِي ذَنْبِي وَخَطَئِي وَجَهْلِي',
      transliteration: 'Rabbi\'ghfir li dhanbi wa khata\'i wa jahli',
      translation: 'My Lord, forgive me my sin, my error and my ignorance.',
      category: 'Seeking Forgiveness',
      source: 'Hadith',
      reference: 'Bukhari, Muslim',
    ),
    DuaData(
      title: 'Dua for traveling',
      arabicText: 'سُبْحَانَ الَّذِي سَخَّرَ لَنَا هَذَا وَمَا كُنَّا لَهُ مُقْرِنِينَ وَإِنَّا إِلَى رَبِّنَا لَمُنْقَلِبُونَ',
      transliteration: 'Subhanal-ladhi sakhkhara lana hadha wa ma kunna lahu muqrinin wa inna ila rabbina la-munqalibun',
      translation: 'Glory be to Him who has subjected this to us, and we could never have it (by our efforts). And verily, to our Lord we indeed are to return!',
      category: 'Travel',
      source: 'Quran',
      reference: '43:13-14',
    ),
    DuaData(
      title: 'Dua for protection',
      arabicText: 'أَعُوذُ بِكَلِمَاتِ اللهِ التَّامَّاتِ مِنْ شَرِّ مَا خَلَقَ',
      transliteration: 'A\'udhu bi kalimatillahit-tammati min sharri ma khalaq',
      translation: 'I seek refuge in the perfect words of Allah from the evil of what He has created.',
      category: 'Protection',
      source: 'Hadith',
      reference: 'Muslim',
    ),
    DuaData(
      title: 'Dua for health',
      arabicText: 'اللَّهُمَّ عَافِنِي فِي بَدَنِي، اللَّهُمَّ عَافِنِي فِي سَمْعِي، اللَّهُمَّ عَافِنِي فِي بَصَرِي',
      transliteration: 'Allahumma \'afini fi badani, Allahumma \'afini fi sam\'i, Allahumma \'afini fi basari',
      translation: 'O Allah, grant me health in my body. O Allah, grant me health in my hearing. O Allah, grant me health in my sight.',
      category: 'Health',
      source: 'Hadith',
      reference: 'Abu Dawud',
    ),
  ];



  @override
  Widget build(BuildContext context) {
    return Consumer<TasbeehDuaProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Error: ${provider.error}'),
                ElevatedButton(
                  onPressed: () => provider.loadDuaItems(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            _buildCategoryFilter(),
            Expanded(
              child: _buildDuaList(provider),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = category == _selectedCategory;
          
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedCategory = category;
                  });
                }
              },
              backgroundColor: Colors.grey[200],
              selectedColor: AppTheme.primaryGreen.withValues(alpha: 0.2),
              checkmarkColor: AppTheme.primaryGreen,
            ),
          );
        },
      ),
    );
  }

  Widget _buildDuaList(TasbeehDuaProvider provider) {
    final filteredDuas = _getFilteredDuas(provider);
    
    if (filteredDuas.isEmpty) {
      return const Center(
        child: Text('No duas found in this category'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredDuas.length,
      itemBuilder: (context, index) {
        return _buildDuaCard(_convertToRealDua(filteredDuas[index]));
      },
    );
  }

  Widget _buildDuaCard(DuaData dua) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and source
            Row(
              children: [
                Expanded(
                  child: Text(
                    dua.title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    dua.source,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.primaryGreen,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            if (dua.reference.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                'Reference: ${dua.reference}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Arabic text
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                ),
              ),
              child: Text(
                dua.arabicText,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  height: 1.8,
                ),
                textAlign: TextAlign.center,
                textDirection: TextDirection.rtl,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Transliteration
            if (dua.transliteration.isNotEmpty) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  dua.transliteration,
                  style: const TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 12),
            ],
            
            // Translation
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.accentGold.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                dua.translation,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton.icon(
                  onPressed: () => _copyToClipboard(dua),
                  icon: const Icon(Icons.copy, size: 16),
                  label: const Text('Copy'),
                ),
                TextButton.icon(
                  onPressed: () => _shareText(dua),
                  icon: const Icon(Icons.share, size: 16),
                  label: const Text('Share'),
                ),
                TextButton.icon(
                  onPressed: () => _addToFavorites(dua),
                  icon: const Icon(Icons.favorite_border, size: 16),
                  label: const Text('Favorite'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _copyToClipboard(DuaData dua) {
    final text = _formatDuaForSharing(dua);
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Dua copied to clipboard'),
        backgroundColor: AppTheme.primaryGreen,
        action: SnackBarAction(
          label: 'Undo',
          textColor: Colors.white,
          onPressed: () {
            // Clear clipboard
            Clipboard.setData(const ClipboardData(text: ''));
          },
        ),
      ),
    );
  }

  void _shareText(DuaData dua) {
    final text = _formatDuaForSharing(dua);
    Share.share(
      text,
      subject: 'Islamic Dua - ${dua.title}',
    );
  }

  void _addToFavorites(DuaData dua) {
    // For now, just show a message since DuaData doesn't have an ID
    // In a real implementation, you would need to add an ID to DuaData
    // or handle favorites differently
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Added to favorites'),
        backgroundColor: AppTheme.primaryGreen,
        duration: Duration(seconds: 2),
      ),
    );
  }

  List<dynamic> _getFilteredDuas(TasbeehDuaProvider provider) {
    List<dynamic> allDuas = [];

    // Add real duas from provider
    if (provider.duaItems.isNotEmpty) {
      allDuas.addAll(provider.duaItems);
    } else {
      // Fallback to default duas if no real data
      allDuas.addAll(_defaultDuas);
    }

    if (_selectedCategory == 'All') {
      return allDuas;
    }

    return allDuas.where((dua) {
      if (dua is DuaItem) {
        return dua.category == _selectedCategory;
      } else if (dua is DuaData) {
        return dua.category == _selectedCategory;
      }
      return false;
    }).toList();
  }

  DuaData _convertToRealDua(dynamic dua) {
    if (dua is DuaItem) {
      return DuaData(
        title: dua.title,
        arabicText: dua.arabicText,
        transliteration: dua.transliteration ?? '',
        translation: dua.translation ?? '',
        category: dua.category ?? '',
        source: dua.source ?? '',
        reference: dua.reference ?? '',
      );
    } else if (dua is DuaData) {
      return dua;
    } else {
      // Fallback
      return DuaData(
        title: 'Unknown Dua',
        arabicText: '',
        transliteration: '',
        translation: '',
        category: '',
        source: '',
        reference: '',
      );
    }
  }

  String _formatDuaForSharing(DuaData dua) {
    final buffer = StringBuffer();

    // Title
    buffer.writeln('🤲 ${dua.title}');
    buffer.writeln();

    // Arabic text
    buffer.writeln('📖 Arabic:');
    buffer.writeln(dua.arabicText);
    buffer.writeln();

    // Transliteration
    if (dua.transliteration.isNotEmpty) {
      buffer.writeln('🔤 Transliteration:');
      buffer.writeln(dua.transliteration);
      buffer.writeln();
    }

    // Translation
    if (dua.translation.isNotEmpty) {
      buffer.writeln('📝 Translation:');
      buffer.writeln(dua.translation);
      buffer.writeln();
    }

    // Source and reference
    if (dua.source.isNotEmpty) {
      buffer.writeln('📚 Source: ${dua.source}');
      if (dua.reference.isNotEmpty) {
        buffer.writeln('📖 Reference: ${dua.reference}');
      }
      buffer.writeln();
    }

    // App signature
    buffer.writeln('Shared from Athkar - Islamic Remembrance App');

    return buffer.toString();
  }
}

class DuaData {
  final String title;
  final String arabicText;
  final String transliteration;
  final String translation;
  final String category;
  final String source;
  final String reference;

  DuaData({
    required this.title,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.category,
    required this.source,
    required this.reference,
  });
}
