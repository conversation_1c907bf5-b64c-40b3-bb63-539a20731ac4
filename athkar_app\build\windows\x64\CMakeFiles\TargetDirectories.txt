D:/projects/12july/athkar/athkar_app/build/windows/x64/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/CMakeFiles/ZERO_CHECK.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/flutter/CMakeFiles/flutter_wrapper_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/flutter/CMakeFiles/flutter_wrapper_app.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/flutter/CMakeFiles/flutter_assemble.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/flutter/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/CMakeFiles/athkar_app.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/runner/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/app_links/CMakeFiles/app_links_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/app_links/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/app_links/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/audioplayers_windows/CMakeFiles/audioplayers_windows_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/audioplayers_windows/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/audioplayers_windows/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/battery_plus/CMakeFiles/battery_plus_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/battery_plus/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/battery_plus/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/connectivity_plus/CMakeFiles/connectivity_plus_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/connectivity_plus/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/connectivity_plus/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/file_selector_windows/CMakeFiles/file_selector_windows_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/file_selector_windows/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/file_selector_windows/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/firebase_core/CMakeFiles/firebase_core_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/firebase_core/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/firebase_core/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/firebase_core/bin/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/flutter_secure_storage_windows/CMakeFiles/flutter_secure_storage_windows_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/flutter_secure_storage_windows/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/flutter_secure_storage_windows/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/geolocator_windows/CMakeFiles/geolocator_windows_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/geolocator_windows/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/geolocator_windows/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/local_auth_windows/CMakeFiles/local_auth_windows_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/local_auth_windows/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/local_auth_windows/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/permission_handler_windows/CMakeFiles/permission_handler_windows_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/permission_handler_windows/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/permission_handler_windows/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/rive_common/CMakeFiles/rive_common_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/rive_common/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/rive_common/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/share_plus/CMakeFiles/share_plus_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/share_plus/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/share_plus/CMakeFiles/ALL_BUILD.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/url_launcher_windows/CMakeFiles/url_launcher_windows_plugin.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/url_launcher_windows/CMakeFiles/INSTALL.dir
D:/projects/12july/athkar/athkar_app/build/windows/x64/plugins/url_launcher_windows/CMakeFiles/ALL_BUILD.dir
