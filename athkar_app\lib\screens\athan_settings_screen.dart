import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/athan_service.dart';
import '../services/language_service.dart';
import '../l10n/app_localizations.dart';
import '../theme/app_theme.dart';

class AthanSettingsScreen extends StatefulWidget {
  const AthanSettingsScreen({super.key});

  @override
  State<AthanSettingsScreen> createState() => _AthanSettingsScreenState();
}

class _AthanSettingsScreenState extends State<AthanSettingsScreen> {
  final AthanService _athanService = AthanService();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeAthanService();
  }

  Future<void> _initializeAthanService() async {
    setState(() => _isLoading = true);
    await _athanService.initialize();
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    final localizations = AppLocalizations.of(context)!;

    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(languageService.isArabic ? 'إعدادات الأذان' : 'Athan Settings'),
          backgroundColor: AppTheme.primaryGreen,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: Icon(languageService.backIcon),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : ListView(
                padding: const EdgeInsets.all(16.0),
                children: [
                  // Athan Type Selection
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageService.isArabic ? 'نوع الأذان' : 'Athan Type',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ...AthanService.athanTypes.entries.map((entry) {
                            return RadioListTile<String>(
                              title: Text(entry.value),
                              value: entry.key,
                              groupValue: _athanService.currentAthanType,
                              activeColor: AppTheme.primaryGreen,
                              onChanged: (value) async {
                                if (value != null) {
                                  await _athanService.setAthanType(value);
                                  setState(() {});
                                }
                              },
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Volume Control
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageService.isArabic ? 'مستوى الصوت' : 'Volume Level',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              const Icon(Icons.volume_down),
                              Expanded(
                                child: Slider(
                                  value: _athanService.volume,
                                  min: 0.0,
                                  max: 1.0,
                                  divisions: 10,
                                  activeColor: AppTheme.primaryGreen,
                                  onChanged: (value) async {
                                    await _athanService.setVolume(value);
                                    setState(() {});
                                  },
                                ),
                              ),
                              const Icon(Icons.volume_up),
                            ],
                          ),
                          Text(
                            '${(_athanService.volume * 100).round()}%',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Test Athan
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageService.isArabic ? 'اختبار الأذان' : 'Test Athan',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: _athanService.isPlaying
                                      ? null
                                      : () async {
                                          await _athanService.testPlayAthan();
                                          setState(() {});
                                        },
                                  icon: Icon(_athanService.isPlaying
                                      ? Icons.stop
                                      : Icons.play_arrow),
                                  label: Text(
                                    _athanService.isPlaying
                                        ? (languageService.isArabic ? 'جاري التشغيل...' : 'Playing...')
                                        : (languageService.isArabic ? 'تشغيل الأذان' : 'Play Athan'),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppTheme.primaryGreen,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(vertical: 12),
                                  ),
                                ),
                              ),
                              if (_athanService.isPlaying) ...[
                                const SizedBox(width: 8),
                                ElevatedButton.icon(
                                  onPressed: () async {
                                    await _athanService.stopAthan();
                                    setState(() {});
                                  },
                                  icon: const Icon(Icons.stop),
                                  label: Text(
                                    languageService.isArabic ? 'إيقاف' : 'Stop',
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(vertical: 12),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Athan Information
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageService.isArabic ? 'معلومات الأذان' : 'Athan Information',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            languageService.isArabic
                                ? '• يتم تشغيل الأذان تلقائياً عند حلول وقت الصلاة\n'
                                  '• يمكنك اختيار نوع الأذان المفضل لديك\n'
                                  '• يمكن التحكم في مستوى الصوت حسب الحاجة\n'
                                  '• الأذان متوفر بأصوات مختلفة من الحرمين الشريفين\n'
                                  '• يعمل الأذان حتى لو كان التطبيق مغلقاً'
                                : '• Athan plays automatically at prayer times\n'
                                  '• You can choose your preferred Athan type\n'
                                  '• Volume can be adjusted as needed\n'
                                  '• Athan available in different voices from the Holy Mosques\n'
                                  '• Athan works even when the app is closed',
                            style: TextStyle(
                              color: Colors.grey[600],
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Prayer Times Integration
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageService.isArabic ? 'ربط مع أوقات الصلاة' : 'Prayer Times Integration',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SwitchListTile(
                            title: Text(
                              languageService.isArabic ? 'تفعيل الأذان التلقائي' : 'Enable Automatic Athan',
                            ),
                            subtitle: Text(
                              languageService.isArabic
                                  ? 'تشغيل الأذان تلقائياً عند حلول وقت الصلاة'
                                  : 'Play Athan automatically at prayer times',
                            ),
                            value: true, // This would be a setting
                            activeColor: AppTheme.primaryGreen,
                            onChanged: (value) {
                              // Implement automatic Athan toggle
                              setState(() {});
                            },
                          ),
                          SwitchListTile(
                            title: Text(
                              languageService.isArabic ? 'أذان الفجر مختلف' : 'Different Fajr Athan',
                            ),
                            subtitle: Text(
                              languageService.isArabic
                                  ? 'استخدام أذان مختلف لصلاة الفجر'
                                  : 'Use different Athan for Fajr prayer',
                            ),
                            value: false, // This would be a setting
                            activeColor: AppTheme.primaryGreen,
                            onChanged: (value) {
                              // Implement different Fajr Athan toggle
                              setState(() {});
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
