class PrayerTimes {
  final String location;
  final String date;
  final String fajr;
  final String sunrise;
  final String dhuhr;
  final String asr;
  final String maghrib;
  final String isha;
  final double latitude;
  final double longitude;

  PrayerTimes({
    required this.location,
    required this.date,
    required this.fajr,
    required this.sunrise,
    required this.dhuhr,
    required this.asr,
    required this.maghrib,
    required this.isha,
    required this.latitude,
    required this.longitude,
  });

  factory PrayerTimes.fromJson(Map<String, dynamic> json) {
    return PrayerTimes(
      location: json['location'] ?? 'Unknown',
      date: json['date'] ?? '',
      fajr: json['fajr'] ?? '05:00',
      sunrise: json['sunrise'] ?? '06:30',
      dhuhr: json['dhuhr'] ?? '12:00',
      asr: json['asr'] ?? '15:30',
      maghrib: json['maghrib'] ?? '18:00',
      isha: json['isha'] ?? '19:30',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'location': location,
      'date': date,
      'fajr': fajr,
      'sunrise': sunrise,
      'dhuhr': dhuhr,
      'asr': asr,
      'maghrib': maghrib,
      'isha': isha,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

class PrayerTimeCalculationMethod {
  final String name;
  final String description;
  final double fajrAngle;
  final double ishaAngle;

  const PrayerTimeCalculationMethod({
    required this.name,
    required this.description,
    required this.fajrAngle,
    required this.ishaAngle,
  });

  static const muslimWorldLeague = PrayerTimeCalculationMethod(
    name: 'Muslim World League',
    description: 'Standard method used by most Islamic organizations',
    fajrAngle: 18.0,
    ishaAngle: 17.0,
  );

  static const islamicSocietyOfNorthAmerica = PrayerTimeCalculationMethod(
    name: 'Islamic Society of North America',
    description: 'Method used in North America',
    fajrAngle: 15.0,
    ishaAngle: 15.0,
  );

  static const egyptianGeneralAuthorityOfSurvey = PrayerTimeCalculationMethod(
    name: 'Egyptian General Authority of Survey',
    description: 'Method used in Egypt',
    fajrAngle: 19.5,
    ishaAngle: 17.5,
  );

  static const ummAlQuraUniversity = PrayerTimeCalculationMethod(
    name: 'Umm Al-Qura University',
    description: 'Method used in Saudi Arabia',
    fajrAngle: 18.5,
    ishaAngle: 90.0, // 90 minutes after Maghrib
  );

  static const universityOfIslamicSciencesKarachi = PrayerTimeCalculationMethod(
    name: 'University of Islamic Sciences, Karachi',
    description: 'Method used in Pakistan',
    fajrAngle: 18.0,
    ishaAngle: 18.0,
  );

  static const instituteOfGeophysicsUniversityOfTehran = PrayerTimeCalculationMethod(
    name: 'Institute of Geophysics, University of Tehran',
    description: 'Method used in Iran',
    fajrAngle: 17.7,
    ishaAngle: 14.0,
  );

  static const shiaIthnaAshariLevaInstitute = PrayerTimeCalculationMethod(
    name: 'Shia Ithna-Ashhari, Leva Institute',
    description: 'Method used by Shia communities',
    fajrAngle: 16.0,
    ishaAngle: 14.0,
  );

  static List<PrayerTimeCalculationMethod> getAllMethods() {
    return [
      muslimWorldLeague,
      islamicSocietyOfNorthAmerica,
      egyptianGeneralAuthorityOfSurvey,
      ummAlQuraUniversity,
      universityOfIslamicSciencesKarachi,
      instituteOfGeophysicsUniversityOfTehran,
      shiaIthnaAshariLevaInstitute,
    ];
  }
}

class PrayerTimeSettings {
  final PrayerTimeCalculationMethod calculationMethod;
  final int fajrAdjustment;
  final int sunriseAdjustment;
  final int dhuhrAdjustment;
  final int asrAdjustment;
  final int maghribAdjustment;
  final int ishaAdjustment;
  final bool enableNotifications;
  final bool enableAdhan;

  PrayerTimeSettings({
    this.calculationMethod = PrayerTimeCalculationMethod.muslimWorldLeague,
    this.fajrAdjustment = 0,
    this.sunriseAdjustment = 0,
    this.dhuhrAdjustment = 0,
    this.asrAdjustment = 0,
    this.maghribAdjustment = 0,
    this.ishaAdjustment = 0,
    this.enableNotifications = true,
    this.enableAdhan = false,
  });

  PrayerTimeSettings copyWith({
    PrayerTimeCalculationMethod? calculationMethod,
    int? fajrAdjustment,
    int? sunriseAdjustment,
    int? dhuhrAdjustment,
    int? asrAdjustment,
    int? maghribAdjustment,
    int? ishaAdjustment,
    bool? enableNotifications,
    bool? enableAdhan,
  }) {
    return PrayerTimeSettings(
      calculationMethod: calculationMethod ?? this.calculationMethod,
      fajrAdjustment: fajrAdjustment ?? this.fajrAdjustment,
      sunriseAdjustment: sunriseAdjustment ?? this.sunriseAdjustment,
      dhuhrAdjustment: dhuhrAdjustment ?? this.dhuhrAdjustment,
      asrAdjustment: asrAdjustment ?? this.asrAdjustment,
      maghribAdjustment: maghribAdjustment ?? this.maghribAdjustment,
      ishaAdjustment: ishaAdjustment ?? this.ishaAdjustment,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableAdhan: enableAdhan ?? this.enableAdhan,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'calculationMethod': calculationMethod.name,
      'fajrAdjustment': fajrAdjustment,
      'sunriseAdjustment': sunriseAdjustment,
      'dhuhrAdjustment': dhuhrAdjustment,
      'asrAdjustment': asrAdjustment,
      'maghribAdjustment': maghribAdjustment,
      'ishaAdjustment': ishaAdjustment,
      'enableNotifications': enableNotifications,
      'enableAdhan': enableAdhan,
    };
  }

  factory PrayerTimeSettings.fromJson(Map<String, dynamic> json) {
    return PrayerTimeSettings(
      calculationMethod: PrayerTimeCalculationMethod.getAllMethods()
          .firstWhere(
            (method) => method.name == json['calculationMethod'],
            orElse: () => PrayerTimeCalculationMethod.muslimWorldLeague,
          ),
      fajrAdjustment: json['fajrAdjustment'] ?? 0,
      sunriseAdjustment: json['sunriseAdjustment'] ?? 0,
      dhuhrAdjustment: json['dhuhrAdjustment'] ?? 0,
      asrAdjustment: json['asrAdjustment'] ?? 0,
      maghribAdjustment: json['maghribAdjustment'] ?? 0,
      ishaAdjustment: json['ishaAdjustment'] ?? 0,
      enableNotifications: json['enableNotifications'] ?? true,
      enableAdhan: json['enableAdhan'] ?? false,
    );
  }
}
