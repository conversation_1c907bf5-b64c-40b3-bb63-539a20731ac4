import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/prayer_times_models.dart';

class AthanService {
  static final AthanService _instance = AthanService._internal();
  factory AthanService() => _instance;
  AthanService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  String _currentAthanType = 'makkah';
  double _volume = 0.8;

  // Available Athan types
  static const Map<String, String> athanTypes = {
    'makkah': 'أذان الحرم المكي',
    'madinah': 'أذان الحرم المدني',
    'egypt': 'أذان مصري',
    'turkey': 'أذان تركي',
    'morocco': 'أذان مغربي',
  };

  // Getters
  bool get isPlaying => _isPlaying;
  String get currentAthanType => _currentAthanType;
  double get volume => _volume;

  /// Initialize Athan service
  Future<void> initialize() async {
    await _loadSettings();
    _setupAudioPlayer();
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentAthanType = prefs.getString('athan_type') ?? 'makkah';
      _volume = prefs.getDouble('athan_volume') ?? 0.8;
    } catch (e) {
      debugPrint('Error loading Athan settings: $e');
    }
  }

  /// Setup audio player listeners
  void _setupAudioPlayer() {
    _audioPlayer.onPlayerStateChanged.listen((PlayerState state) {
      _isPlaying = state == PlayerState.playing;
    });

    _audioPlayer.onPlayerComplete.listen((_) {
      _isPlaying = false;
    });
  }

  /// Play Athan for specific prayer
  Future<void> playAthan(Prayer prayer) async {
    try {
      if (_isPlaying) {
        await stopAthan();
      }

      final athanFile = _getAthanFile(prayer);
      await _audioPlayer.setVolume(_volume);
      await _audioPlayer.play(AssetSource(athanFile));
      
      debugPrint('Playing Athan for ${prayer.name} with type $_currentAthanType');
    } catch (e) {
      debugPrint('Error playing Athan: $e');
      // Fallback to default notification sound
      await _playDefaultNotification();
    }
  }

  /// Get Athan file path based on prayer and type
  String _getAthanFile(Prayer prayer) {
    // For now, use the same Athan for all prayers
    // In a real app, you might have different Athan for Fajr vs other prayers
    switch (_currentAthanType) {
      case 'makkah':
        return 'audio/athan_makkah.mp3';
      case 'madinah':
        return 'audio/athan_madinah.mp3';
      case 'egypt':
        return 'audio/athan_egypt.mp3';
      case 'turkey':
        return 'audio/athan_turkey.mp3';
      case 'morocco':
        return 'audio/athan_morocco.mp3';
      default:
        return 'audio/athan_makkah.mp3';
    }
  }

  /// Play default notification sound as fallback
  Future<void> _playDefaultNotification() async {
    try {
      await _audioPlayer.play(AssetSource('audio/notification.mp3'));
    } catch (e) {
      debugPrint('Error playing default notification: $e');
    }
  }

  /// Stop current Athan
  Future<void> stopAthan() async {
    try {
      await _audioPlayer.stop();
      _isPlaying = false;
    } catch (e) {
      debugPrint('Error stopping Athan: $e');
    }
  }

  /// Pause current Athan
  Future<void> pauseAthan() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      debugPrint('Error pausing Athan: $e');
    }
  }

  /// Resume paused Athan
  Future<void> resumeAthan() async {
    try {
      await _audioPlayer.resume();
    } catch (e) {
      debugPrint('Error resuming Athan: $e');
    }
  }

  /// Set Athan type
  Future<void> setAthanType(String type) async {
    if (athanTypes.containsKey(type)) {
      _currentAthanType = type;
      await _saveSettings();
      debugPrint('Athan type changed to: $type');
    }
  }

  /// Set Athan volume
  Future<void> setVolume(double volume) async {
    _volume = volume.clamp(0.0, 1.0);
    await _audioPlayer.setVolume(_volume);
    await _saveSettings();
    debugPrint('Athan volume changed to: $_volume');
  }

  /// Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('athan_type', _currentAthanType);
      await prefs.setDouble('athan_volume', _volume);
    } catch (e) {
      debugPrint('Error saving Athan settings: $e');
    }
  }

  /// Test play current Athan type
  Future<void> testPlayAthan() async {
    await playAthan(Prayer.dhuhr); // Use Dhuhr as test prayer
  }

  /// Check if Athan files exist
  Future<bool> checkAthanFiles() async {
    // In a real app, you would check if the audio files exist
    // For now, return true assuming they exist
    return true;
  }

  /// Get available Athan types with their display names
  Map<String, String> getAvailableAthanTypes() {
    return Map.from(athanTypes);
  }

  /// Schedule Athan for prayer times
  Future<void> scheduleAthanForPrayerTimes(PrayerTimes prayerTimes) async {
    // This would integrate with the notification service
    // to schedule Athan playback at prayer times
    debugPrint('Scheduling Athan for prayer times: ${prayerTimes.date}');
    
    // Implementation would depend on your notification scheduling system
    // For now, just log the schedule
    final prayers = [
      {'name': 'Fajr', 'time': prayerTimes.fajr},
      {'name': 'Dhuhr', 'time': prayerTimes.dhuhr},
      {'name': 'Asr', 'time': prayerTimes.asr},
      {'name': 'Maghrib', 'time': prayerTimes.maghrib},
      {'name': 'Isha', 'time': prayerTimes.isha},
    ];
    
    for (final prayer in prayers) {
      debugPrint('Scheduled ${prayer['name']} Athan at ${prayer['time']}');
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    await _audioPlayer.dispose();
  }
}

/// Prayer enum for Athan service
enum Prayer {
  fajr,
  dhuhr,
  asr,
  maghrib,
  isha;

  String get name {
    switch (this) {
      case Prayer.fajr:
        return 'الفجر';
      case Prayer.dhuhr:
        return 'الظهر';
      case Prayer.asr:
        return 'العصر';
      case Prayer.maghrib:
        return 'المغرب';
      case Prayer.isha:
        return 'العشاء';
    }
  }

  String get englishName {
    switch (this) {
      case Prayer.fajr:
        return 'Fajr';
      case Prayer.dhuhr:
        return 'Dhuhr';
      case Prayer.asr:
        return 'Asr';
      case Prayer.maghrib:
        return 'Maghrib';
      case Prayer.isha:
        return 'Isha';
    }
  }
}
