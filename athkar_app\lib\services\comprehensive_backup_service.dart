import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'dart:async';

/// Comprehensive backup and restore service for all Islamic app data
/// Provides enterprise-level data protection with encryption and validation
class ComprehensiveBackupService {
  static final ComprehensiveBackupService _instance = ComprehensiveBackupService._internal();
  factory ComprehensiveBackupService() => _instance;
  ComprehensiveBackupService._internal();

  static const String _backupVersion = '1.0.0';
  static const String _backupExtension = '.athkar_backup';
  static const String _encryptionKey = 'IslamicAthkarApp2024SecureBackup';

  /// Create comprehensive backup of all app data
  Future<BackupResult> createFullBackup({
    bool includeAthkar = true,
    bool includeQuran = true,
    bool includeHadith = true,
    bool includeSettings = true,
    bool includeUserData = true,
    bool includeMedia = false,
    String? customPath,
    bool encrypt = true,
  }) async {
    try {
      debugPrint('Starting comprehensive backup creation...');
      
      final backupData = BackupData(
        version: _backupVersion,
        timestamp: DateTime.now(),
        deviceInfo: await _getDeviceInfo(),
        checksum: '',
      );

      // Collect all data based on options
      if (includeAthkar) {
        backupData.athkarData = await _backupAthkarData();
      }

      if (includeQuran) {
        backupData.quranData = await _backupQuranData();
      }

      if (includeHadith) {
        backupData.hadithData = await _backupHadithData();
      }

      if (includeSettings) {
        backupData.settingsData = await _backupSettingsData();
      }

      if (includeUserData) {
        backupData.userData = await _backupUserData();
      }

      if (includeMedia) {
        backupData.mediaData = await _backupMediaData();
      }

      // Generate checksum for data integrity
      backupData.checksum = _generateChecksum(backupData);

      // Serialize and optionally encrypt
      final jsonData = jsonEncode(backupData.toMap());
      final backupBytes = encrypt ? _encryptData(jsonData) : utf8.encode(jsonData);

      // Compress data
      final compressedData = _compressData(backupBytes);

      // Save to file
      final backupFile = await _saveBackupFile(compressedData, customPath);

      debugPrint('Backup created successfully: ${backupFile.path}');
      
      return BackupResult.success(
        filePath: backupFile.path,
        fileSize: backupFile.lengthSync(),
        dataTypes: _getIncludedDataTypes(
          includeAthkar, includeQuran, includeHadith, 
          includeSettings, includeUserData, includeMedia
        ),
        isEncrypted: encrypt,
        timestamp: DateTime.now(),
      );

    } catch (e) {
      debugPrint('Backup creation failed: $e');
      return BackupResult.error('فشل في إنشاء النسخة الاحتياطية: ${e.toString()}');
    }
  }

  /// Restore data from backup file
  Future<RestoreResult> restoreFromBackup(
    String backupFilePath, {
    bool validateIntegrity = true,
    bool overwriteExisting = false,
    List<String>? selectiveRestore,
  }) async {
    try {
      debugPrint('Starting restore from backup: $backupFilePath');

      // Read and decompress backup file
      final backupFile = File(backupFilePath);
      if (!backupFile.existsSync()) {
        return RestoreResult.error('ملف النسخة الاحتياطية غير موجود');
      }

      final compressedData = await backupFile.readAsBytes();
      final decompressedData = _decompressData(compressedData);

      // Decrypt if needed
      String jsonData;
      try {
        jsonData = _decryptData(decompressedData);
      } catch (e) {
        // Try without decryption (unencrypted backup)
        jsonData = utf8.decode(decompressedData);
      }

      // Parse backup data
      final backupData = BackupData.fromMap(jsonDecode(jsonData));

      // Validate backup integrity
      if (validateIntegrity) {
        final calculatedChecksum = _generateChecksum(backupData);
        if (calculatedChecksum != backupData.checksum) {
          return RestoreResult.error('النسخة الاحتياطية تالفة أو معدلة');
        }
      }

      // Check version compatibility
      if (!_isVersionCompatible(backupData.version)) {
        return RestoreResult.error('إصدار النسخة الاحتياطية غير متوافق');
      }

      final restoredItems = <String>[];

      // Restore data selectively or completely
      if (selectiveRestore == null || selectiveRestore.contains('athkar')) {
        if (backupData.athkarData != null) {
          await _restoreAthkarData(backupData.athkarData!, overwriteExisting);
          restoredItems.add('الأذكار');
        }
      }

      if (selectiveRestore == null || selectiveRestore.contains('quran')) {
        if (backupData.quranData != null) {
          await _restoreQuranData(backupData.quranData!, overwriteExisting);
          restoredItems.add('القرآن');
        }
      }

      if (selectiveRestore == null || selectiveRestore.contains('hadith')) {
        if (backupData.hadithData != null) {
          await _restoreHadithData(backupData.hadithData!, overwriteExisting);
          restoredItems.add('الأحاديث');
        }
      }

      if (selectiveRestore == null || selectiveRestore.contains('settings')) {
        if (backupData.settingsData != null) {
          await _restoreSettingsData(backupData.settingsData!, overwriteExisting);
          restoredItems.add('الإعدادات');
        }
      }

      if (selectiveRestore == null || selectiveRestore.contains('userData')) {
        if (backupData.userData != null) {
          await _restoreUserData(backupData.userData!, overwriteExisting);
          restoredItems.add('بيانات المستخدم');
        }
      }

      if (selectiveRestore == null || selectiveRestore.contains('media')) {
        if (backupData.mediaData != null) {
          await _restoreMediaData(backupData.mediaData!, overwriteExisting);
          restoredItems.add('الملفات الصوتية');
        }
      }

      debugPrint('Restore completed successfully');
      
      return RestoreResult.success(
        restoredItems: restoredItems,
        backupVersion: backupData.version,
        backupTimestamp: backupData.timestamp,
        deviceInfo: backupData.deviceInfo,
      );

    } catch (e) {
      debugPrint('Restore failed: $e');
      return RestoreResult.error('فشل في استعادة البيانات: ${e.toString()}');
    }
  }

  /// Get backup file info without restoring
  Future<BackupInfo> getBackupInfo(String backupFilePath) async {
    try {
      final backupFile = File(backupFilePath);
      final compressedData = await backupFile.readAsBytes();
      final decompressedData = _decompressData(compressedData);

      String jsonData;
      bool isEncrypted = true;
      try {
        jsonData = _decryptData(decompressedData);
      } catch (e) {
        jsonData = utf8.decode(decompressedData);
        isEncrypted = false;
      }

      final backupData = BackupData.fromMap(jsonDecode(jsonData));

      return BackupInfo(
        version: backupData.version,
        timestamp: backupData.timestamp,
        deviceInfo: backupData.deviceInfo,
        fileSize: backupFile.lengthSync(),
        isEncrypted: isEncrypted,
        dataTypes: _getDataTypesFromBackup(backupData),
        isValid: _generateChecksum(backupData) == backupData.checksum,
      );

    } catch (e) {
      throw Exception('فشل في قراءة معلومات النسخة الاحتياطية: ${e.toString()}');
    }
  }

  /// Export backup to external storage or share
  Future<void> exportBackup(String backupFilePath) async {
    try {
      final backupFile = File(backupFilePath);
      final fileName = 'athkar_backup_${DateTime.now().millisecondsSinceEpoch}$_backupExtension';
      
      await Share.shareXFiles(
        [XFile(backupFile.path, name: fileName)],
        text: 'نسخة احتياطية من تطبيق الأذكار الإسلامية',
      );
    } catch (e) {
      throw Exception('فشل في تصدير النسخة الاحتياطية: ${e.toString()}');
    }
  }

  /// Import backup from external source
  Future<String?> importBackup() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['athkar_backup', 'backup', 'json'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.path != null) {
          // Copy to app directory
          final appDir = await getApplicationDocumentsDirectory();
          final backupDir = Directory('${appDir.path}/backups');
          if (!backupDir.existsSync()) {
            backupDir.createSync(recursive: true);
          }

          final fileName = 'imported_backup_${DateTime.now().millisecondsSinceEpoch}$_backupExtension';
          final targetFile = File('${backupDir.path}/$fileName');
          
          await File(file.path!).copy(targetFile.path);
          return targetFile.path;
        }
      }
      return null;
    } catch (e) {
      throw Exception('فشل في استيراد النسخة الاحتياطية: ${e.toString()}');
    }
  }

  /// List all available backups
  Future<List<BackupInfo>> listAvailableBackups() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${appDir.path}/backups');
      
      if (!backupDir.existsSync()) {
        return [];
      }

      final backupFiles = backupDir
          .listSync()
          .whereType<File>()
          .where((file) => file.path.endsWith(_backupExtension))
          .toList();

      final backupInfos = <BackupInfo>[];
      for (final file in backupFiles) {
        try {
          final info = await getBackupInfo(file.path);
          backupInfos.add(info);
        } catch (e) {
          debugPrint('Failed to read backup info for ${file.path}: $e');
        }
      }

      backupInfos.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return backupInfos;

    } catch (e) {
      debugPrint('Failed to list backups: $e');
      return [];
    }
  }

  /// Delete backup file
  Future<void> deleteBackup(String backupFilePath) async {
    try {
      final file = File(backupFilePath);
      if (file.existsSync()) {
        await file.delete();
      }
    } catch (e) {
      throw Exception('فشل في حذف النسخة الاحتياطية: ${e.toString()}');
    }
  }

  /// Backup athkar data
  Future<Map<String, dynamic>> _backupAthkarData() async {
    // Implementation would collect all athkar routines, custom athkar, etc.
    return {
      'routines': [],
      'custom_athkar': [],
      'favorites': [],
      'usage_stats': {},
    };
  }

  /// Backup Quran data
  Future<Map<String, dynamic>> _backupQuranData() async {
    // Implementation would collect bookmarks, notes, reading progress, etc.
    return {
      'bookmarks': [],
      'notes': {},
      'reading_progress': {},
      'khatma_plans': [],
    };
  }

  /// Backup Hadith data
  Future<Map<String, dynamic>> _backupHadithData() async {
    // Implementation would collect favorites, bookmarks, notes, etc.
    return {
      'favorites': [],
      'bookmarks': [],
      'notes': {},
      'search_history': [],
    };
  }

  /// Backup settings data
  Future<Map<String, dynamic>> _backupSettingsData() async {
    // Implementation would collect all app settings
    return {
      'language': 'ar',
      'theme': 'light',
      'notifications': {},
      'prayer_settings': {},
      'display_settings': {},
    };
  }

  /// Backup user data
  Future<Map<String, dynamic>> _backupUserData() async {
    // Implementation would collect user profile, preferences, etc.
    return {
      'profile': {},
      'preferences': {},
      'achievements': [],
      'statistics': {},
    };
  }

  /// Backup media data
  Future<Map<String, dynamic>> _backupMediaData() async {
    // Implementation would collect custom audio files, images, etc.
    return {
      'audio_files': [],
      'custom_sounds': [],
      'images': [],
    };
  }

  /// Restore methods (implementations would restore data to respective services)
  Future<void> _restoreAthkarData(Map<String, dynamic> data, bool overwrite) async {
    // Restore athkar data
  }

  Future<void> _restoreQuranData(Map<String, dynamic> data, bool overwrite) async {
    // Restore Quran data
  }

  Future<void> _restoreHadithData(Map<String, dynamic> data, bool overwrite) async {
    // Restore Hadith data
  }

  Future<void> _restoreSettingsData(Map<String, dynamic> data, bool overwrite) async {
    // Restore settings data
  }

  Future<void> _restoreUserData(Map<String, dynamic> data, bool overwrite) async {
    // Restore user data
  }

  Future<void> _restoreMediaData(Map<String, dynamic> data, bool overwrite) async {
    // Restore media data
  }

  /// Utility methods
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'app_version': '1.0.0',
    };
  }

  String _generateChecksum(BackupData data) {
    final content = jsonEncode(data.toMap()..remove('checksum'));
    return sha256.convert(utf8.encode(content)).toString();
  }

  Uint8List _encryptData(String data) {
    // Simple encryption implementation (would use proper encryption in production)
    final bytes = utf8.encode(data);
    final key = utf8.encode(_encryptionKey);
    
    for (int i = 0; i < bytes.length; i++) {
      bytes[i] ^= key[i % key.length];
    }
    
    return bytes;
  }

  String _decryptData(Uint8List encryptedData) {
    // Simple decryption implementation
    final key = utf8.encode(_encryptionKey);
    
    for (int i = 0; i < encryptedData.length; i++) {
      encryptedData[i] ^= key[i % key.length];
    }
    
    return utf8.decode(encryptedData);
  }

  Uint8List _compressData(Uint8List data) {
    // Simple compression using gzip-like algorithm
    // In production, would use proper compression library
    return data; // Simplified for now
  }

  Uint8List _decompressData(Uint8List compressedData) {
    // Simple decompression
    // In production, would use proper decompression library
    return compressedData; // Simplified for now
  }

  Future<File> _saveBackupFile(Uint8List data, String? customPath) async {
    final fileName = 'backup_${DateTime.now().millisecondsSinceEpoch}$_backupExtension';
    
    if (customPath != null) {
      final file = File('$customPath/$fileName');
      return await file.writeAsBytes(data);
    }

    final appDir = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${appDir.path}/backups');
    if (!backupDir.existsSync()) {
      backupDir.createSync(recursive: true);
    }

    final file = File('${backupDir.path}/$fileName');
    return await file.writeAsBytes(data);
  }

  bool _isVersionCompatible(String version) {
    // Simple version compatibility check
    return version == _backupVersion;
  }

  List<String> _getIncludedDataTypes(bool athkar, bool quran, bool hadith, 
                                   bool settings, bool userData, bool media) {
    final types = <String>[];
    if (athkar) types.add('الأذكار');
    if (quran) types.add('القرآن');
    if (hadith) types.add('الأحاديث');
    if (settings) types.add('الإعدادات');
    if (userData) types.add('بيانات المستخدم');
    if (media) types.add('الملفات الصوتية');
    return types;
  }

  List<String> _getDataTypesFromBackup(BackupData data) {
    final types = <String>[];
    if (data.athkarData != null) types.add('الأذكار');
    if (data.quranData != null) types.add('القرآن');
    if (data.hadithData != null) types.add('الأحاديث');
    if (data.settingsData != null) types.add('الإعدادات');
    if (data.userData != null) types.add('بيانات المستخدم');
    if (data.mediaData != null) types.add('الملفات الصوتية');
    return types;
  }
}

/// Backup data model
class BackupData {
  final String version;
  final DateTime timestamp;
  final Map<String, dynamic> deviceInfo;
  String checksum;
  Map<String, dynamic>? athkarData;
  Map<String, dynamic>? quranData;
  Map<String, dynamic>? hadithData;
  Map<String, dynamic>? settingsData;
  Map<String, dynamic>? userData;
  Map<String, dynamic>? mediaData;

  BackupData({
    required this.version,
    required this.timestamp,
    required this.deviceInfo,
    required this.checksum,
    this.athkarData,
    this.quranData,
    this.hadithData,
    this.settingsData,
    this.userData,
    this.mediaData,
  });

  Map<String, dynamic> toMap() {
    return {
      'version': version,
      'timestamp': timestamp.toIso8601String(),
      'device_info': deviceInfo,
      'checksum': checksum,
      'athkar_data': athkarData,
      'quran_data': quranData,
      'hadith_data': hadithData,
      'settings_data': settingsData,
      'user_data': userData,
      'media_data': mediaData,
    };
  }

  factory BackupData.fromMap(Map<String, dynamic> map) {
    return BackupData(
      version: map['version'],
      timestamp: DateTime.parse(map['timestamp']),
      deviceInfo: Map<String, dynamic>.from(map['device_info']),
      checksum: map['checksum'],
      athkarData: map['athkar_data'],
      quranData: map['quran_data'],
      hadithData: map['hadith_data'],
      settingsData: map['settings_data'],
      userData: map['user_data'],
      mediaData: map['media_data'],
    );
  }
}

/// Backup result
class BackupResult {
  final bool isSuccess;
  final String? error;
  final String? filePath;
  final int? fileSize;
  final List<String>? dataTypes;
  final bool? isEncrypted;
  final DateTime? timestamp;

  BackupResult._({
    required this.isSuccess,
    this.error,
    this.filePath,
    this.fileSize,
    this.dataTypes,
    this.isEncrypted,
    this.timestamp,
  });

  factory BackupResult.success({
    required String filePath,
    required int fileSize,
    required List<String> dataTypes,
    required bool isEncrypted,
    required DateTime timestamp,
  }) {
    return BackupResult._(
      isSuccess: true,
      filePath: filePath,
      fileSize: fileSize,
      dataTypes: dataTypes,
      isEncrypted: isEncrypted,
      timestamp: timestamp,
    );
  }

  factory BackupResult.error(String error) {
    return BackupResult._(isSuccess: false, error: error);
  }
}

/// Restore result
class RestoreResult {
  final bool isSuccess;
  final String? error;
  final List<String>? restoredItems;
  final String? backupVersion;
  final DateTime? backupTimestamp;
  final Map<String, dynamic>? deviceInfo;

  RestoreResult._({
    required this.isSuccess,
    this.error,
    this.restoredItems,
    this.backupVersion,
    this.backupTimestamp,
    this.deviceInfo,
  });

  factory RestoreResult.success({
    required List<String> restoredItems,
    required String backupVersion,
    required DateTime backupTimestamp,
    required Map<String, dynamic> deviceInfo,
  }) {
    return RestoreResult._(
      isSuccess: true,
      restoredItems: restoredItems,
      backupVersion: backupVersion,
      backupTimestamp: backupTimestamp,
      deviceInfo: deviceInfo,
    );
  }

  factory RestoreResult.error(String error) {
    return RestoreResult._(isSuccess: false, error: error);
  }
}

/// Backup info
class BackupInfo {
  final String version;
  final DateTime timestamp;
  final Map<String, dynamic> deviceInfo;
  final int fileSize;
  final bool isEncrypted;
  final List<String> dataTypes;
  final bool isValid;

  BackupInfo({
    required this.version,
    required this.timestamp,
    required this.deviceInfo,
    required this.fileSize,
    required this.isEncrypted,
    required this.dataTypes,
    required this.isValid,
  });
}
