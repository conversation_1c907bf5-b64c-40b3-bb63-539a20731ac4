import '../models/islamic_calendar_models.dart';

class IslamicCalendarService {
  static final List<IslamicEvent> _predefinedEvents = [
    // Religious Events
    IslamicEvent(
      id: 'muharram_1',
      title: 'Islamic New Year',
      description: 'The first day of Muharram marks the beginning of the Islamic calendar year.',
      date: DateTime(2024, 7, 7), // Example date - should be calculated
      type: IslamicEventType.religious,
      isRecurring: true,
    ),
    IslamicEvent(
      id: 'ashura',
      title: 'Day of Ashura',
      description: 'The 10th day of Muharram, a day of fasting and remembrance.',
      date: DateTime(2024, 7, 16),
      type: IslamicEventType.religious,
      isRecurring: true,
    ),
    IslamicEvent(
      id: 'mawlid',
      title: '<PERSON><PERSON><PERSON> <PERSON>-<PERSON>',
      description: 'Celebration of the birth of Prophet <PERSON> (PBUH).',
      date: DateTime(2024, 9, 15),
      type: IslamicEventType.religious,
      isRecurring: true,
    ),
    IslamicEvent(
      id: 'isra_miraj',
      title: '<PERSON><PERSON> and <PERSON>\'raj',
      description: 'The night journey of Prophet <PERSON> (PBUH) from Mecca to Jerusalem and his ascension to heaven.',
      date: DateTime(2024, 2, 8),
      type: IslamicEventType.religious,
      isRecurring: true,
    ),
    IslamicEvent(
      id: 'ramadan_start',
      title: 'First Day of Ramadan',
      description: 'The beginning of the holy month of fasting.',
      date: DateTime(2024, 3, 11),
      type: IslamicEventType.religious,
      isRecurring: true,
    ),
    IslamicEvent(
      id: 'laylat_qadr',
      title: 'Laylat al-Qadr',
      description: 'The Night of Power, believed to be when the Quran was first revealed.',
      date: DateTime(2024, 4, 5),
      type: IslamicEventType.religious,
      isRecurring: true,
    ),
    IslamicEvent(
      id: 'eid_fitr',
      title: 'Eid al-Fitr',
      description: 'Festival of Breaking the Fast, celebrating the end of Ramadan.',
      date: DateTime(2024, 4, 10),
      type: IslamicEventType.religious,
      isRecurring: true,
    ),
    IslamicEvent(
      id: 'eid_adha',
      title: 'Eid al-Adha',
      description: 'Festival of Sacrifice, commemorating Abraham\'s willingness to sacrifice his son.',
      date: DateTime(2024, 6, 16),
      type: IslamicEventType.religious,
      isRecurring: true,
    ),
  ];

  /// Get Islamic events for a specific month
  static Future<List<IslamicEvent>> getEventsForMonth(int year, int month) async {
    // In a real app, this would fetch from an API or database
    // For now, return predefined events that fall in the specified month
    final startDate = DateTime(year, month, 1);
    final endDate = DateTime(year, month + 1, 0);

    return _predefinedEvents.where((event) {
      return event.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
             event.date.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  /// Get Islamic date for a Gregorian date
  static IslamicDate getIslamicDate(DateTime gregorianDate) {
    // This is a simplified conversion
    // In a production app, use a proper Islamic calendar library
    final islamicYear = _calculateIslamicYear(gregorianDate);
    final islamicMonth = _calculateIslamicMonth(gregorianDate);
    final islamicDay = _calculateIslamicDay(gregorianDate);

    return IslamicDate(
      day: islamicDay,
      month: islamicMonth,
      year: islamicYear,
      dayName: _getIslamicDayName(gregorianDate.weekday),
      monthName: IslamicMonth.getMonth(islamicMonth).name,
      yearName: '${islamicYear}H',
    );
  }

  /// Convert Gregorian date to Islamic date (simplified)
  static int _calculateIslamicYear(DateTime gregorianDate) {
    // Simplified calculation - Islamic year is approximately 354 days
    // This is not accurate and should use proper Islamic calendar calculations
    final islamicEpoch = DateTime(622, 7, 16); // Approximate Hijri epoch
    final daysDifference = gregorianDate.difference(islamicEpoch).inDays;
    return (daysDifference / 354.37).floor() + 1;
  }

  static int _calculateIslamicMonth(DateTime gregorianDate) {
    // Simplified calculation
    final islamicEpoch = DateTime(622, 7, 16);
    final daysDifference = gregorianDate.difference(islamicEpoch).inDays;
    final daysIntoYear = daysDifference % 354.37;
    return ((daysIntoYear / 29.53).floor() % 12) + 1;
  }

  static int _calculateIslamicDay(DateTime gregorianDate) {
    // Simplified calculation
    final islamicEpoch = DateTime(622, 7, 16);
    final daysDifference = gregorianDate.difference(islamicEpoch).inDays;
    final daysIntoMonth = daysDifference % 29.53;
    return daysIntoMonth.floor() + 1;
  }

  /// Get Islamic day name
  static String _getIslamicDayName(int weekday) {
    const dayNames = [
      'Al-Ithnayn', // Monday
      'Ath-Thulatha', // Tuesday
      'Al-Arba\'a', // Wednesday
      'Al-Khamis', // Thursday
      'Al-Jumu\'ah', // Friday
      'As-Sabt', // Saturday
      'Al-Ahad', // Sunday
    ];
    return dayNames[weekday - 1];
  }

  /// Get upcoming Islamic events
  static Future<List<IslamicEvent>> getUpcomingEvents({int limit = 10}) async {
    final now = DateTime.now();
    final allEvents = <IslamicEvent>[];

    // Get events for current and next few months
    for (int i = 0; i < 6; i++) {
      final date = DateTime(now.year, now.month + i, 1);
      final monthEvents = await getEventsForMonth(date.year, date.month);
      allEvents.addAll(monthEvents);
    }

    // Filter upcoming events and sort by date
    final upcomingEvents = allEvents
        .where((event) => event.date.isAfter(now))
        .toList()
      ..sort((a, b) => a.date.compareTo(b.date));

    return upcomingEvents.take(limit).toList();
  }

  /// Get events for a specific date
  static Future<List<IslamicEvent>> getEventsForDate(DateTime date) async {
    final events = await getEventsForMonth(date.year, date.month);
    return events.where((event) => _isSameDay(event.date, date)).toList();
  }

  /// Check if two dates are the same day
  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// Get Islamic month information
  static IslamicMonth getIslamicMonth(int monthNumber) {
    return IslamicMonth.getMonth(monthNumber);
  }

  /// Get all Islamic months
  static List<IslamicMonth> getAllIslamicMonths() {
    return IslamicMonth.months;
  }

  /// Calculate moon phase for a given date (simplified)
  static String getMoonPhase(DateTime date) {
    // Simplified moon phase calculation
    // In a real app, use proper astronomical calculations
    final daysSinceNewMoon = date.difference(DateTime(2024, 1, 11)).inDays % 29.53;
    
    if (daysSinceNewMoon < 1) return 'New Moon';
    if (daysSinceNewMoon < 7) return 'Waxing Crescent';
    if (daysSinceNewMoon < 8) return 'First Quarter';
    if (daysSinceNewMoon < 15) return 'Waxing Gibbous';
    if (daysSinceNewMoon < 16) return 'Full Moon';
    if (daysSinceNewMoon < 22) return 'Waning Gibbous';
    if (daysSinceNewMoon < 23) return 'Last Quarter';
    return 'Waning Crescent';
  }

  /// Get Islamic calendar settings
  static IslamicCalendarSettings getDefaultSettings() {
    return IslamicCalendarSettings();
  }

  /// Save Islamic calendar settings
  static Future<void> saveSettings(IslamicCalendarSettings settings) async {
    // In a real app, save to local storage or database
    // For now, this is a placeholder
  }

  /// Load Islamic calendar settings
  static Future<IslamicCalendarSettings> loadSettings() async {
    // In a real app, load from local storage or database
    // For now, return default settings
    return getDefaultSettings();
  }

  /// Add custom Islamic event
  static Future<void> addCustomEvent(IslamicEvent event) async {
    // In a real app, save to local database
    // For now, this is a placeholder
  }

  /// Remove custom Islamic event
  static Future<void> removeCustomEvent(String eventId) async {
    // In a real app, remove from local database
    // For now, this is a placeholder
  }

  /// Get Islamic holidays for a year
  static Future<List<IslamicEvent>> getIslamicHolidays(int year) async {
    return _predefinedEvents
        .where((event) => event.date.year == year && event.type == IslamicEventType.religious)
        .toList();
  }

  /// Check if a date is an Islamic holiday
  static Future<bool> isIslamicHoliday(DateTime date) async {
    final events = await getEventsForDate(date);
    return events.any((event) => event.type == IslamicEventType.religious);
  }

  /// Get Islamic date string
  static String formatIslamicDate(IslamicDate islamicDate) {
    return '${islamicDate.day} ${islamicDate.monthName} ${islamicDate.year} AH';
  }

  /// Get Gregorian date string
  static String formatGregorianDate(DateTime date) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
