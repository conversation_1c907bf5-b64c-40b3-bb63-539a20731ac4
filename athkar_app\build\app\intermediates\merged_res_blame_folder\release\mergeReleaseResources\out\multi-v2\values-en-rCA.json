{"logs": [{"outputFile": "com.islamicapps.athkar.athkar_app-mergeReleaseResources-66:/values-en-rCA/values-en-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "38,41,42,43", "startColumns": "4,4,4,4", "startOffsets": "3654,3929,4026,4135", "endColumns": "97,96,108,98", "endOffsets": "3747,4021,4130,4229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "29,30,31,32,33,34,35,57", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2762,2858,2960,3059,3158,3262,3364,5772", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "2853,2955,3054,3153,3257,3359,3475,5868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,5689", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,5767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "37,40,54,55,58,59,60", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3586,3842,5481,5555,5873,6042,6122", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "3649,3924,5550,5684,6037,6117,6193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cceca150324ee75eadce8003b387b1fb\\transformed\\biometric-1.1.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,370,499,637,764,882,1013,1113,1239,1378", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "156,246,365,494,632,759,877,1008,1108,1234,1373,1493"}, "to": {"startLines": "36,39,44,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3480,3752,4234,4353,4482,4620,4747,4865,4996,5096,5222,5361", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "3581,3837,4348,4477,4615,4742,4860,4991,5091,5217,5356,5476"}}]}]}