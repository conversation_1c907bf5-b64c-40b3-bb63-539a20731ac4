import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/athkar_provider.dart';
import '../models/athkar_models.dart';
import '../theme/app_theme.dart';
import '../services/floating_counter_service.dart';

class PracticeAthkarScreen extends StatefulWidget {
  final AthkarRoutine routine;
  final List<AthkarStep>? steps; // Optional prebuilt steps

  const PracticeAthkarScreen({
    super.key,
    required this.routine,
    this.steps,
  });

  @override
  State<PracticeAthkarScreen> createState() => _PracticeAthkarScreenState();
}

class _PracticeAthkarScreenState extends State<PracticeAthkarScreen> {
  late PageController _pageController;
  int _currentStepIndex = 0;
  List<AthkarStep> _steps = [];
  Map<String, int> _stepCounts = {};
  bool _isLoading = true;

  DateTime? _startTime;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _startTime = DateTime.now();
    _loadRoutineSteps();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadRoutineSteps() async {
    try {
      // If prebuilt steps are provided, use them directly
      if (widget.steps != null) {
        setState(() {
          _steps = widget.steps!;
          _stepCounts = {for (var step in _steps) step.id: 0};
          _isLoading = false;
        });
        return;
      }

      // Otherwise, load from database
      if (!mounted) return;
      await context.read<AthkarProvider>().loadRoutineById(widget.routine.id);
      if (!mounted) return;
      final routine = context.read<AthkarProvider>().currentRoutine;

      if (routine != null && routine.steps != null) {
        setState(() {
          _steps = routine.steps!;
          _stepCounts = {for (var step in _steps) step.id: 0};
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading routine: $e')),
        );
      }
    }
  }

  void _incrementCount() {
    if (_currentStepIndex < _steps.length) {
      final currentStep = _steps[_currentStepIndex];
      final currentCount = _stepCounts[currentStep.id] ?? 0;
      
      setState(() {
        _stepCounts[currentStep.id] = currentCount + 1;
      });

      // Haptic feedback
      HapticFeedback.lightImpact();

      // Check if step is completed
      if (_stepCounts[currentStep.id]! >= currentStep.targetCount) {
        _completeCurrentStep();
      }
    }
  }

  void _completeCurrentStep() {
    if (_currentStepIndex < _steps.length - 1) {
      // Move to next step
      _nextStep();
    } else {
      // Complete routine
      _completeRoutine();
    }
  }

  void _nextStep() {
    if (_currentStepIndex < _steps.length - 1) {
      setState(() {
        _currentStepIndex++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStepIndex > 0) {
      setState(() {
        _currentStepIndex--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _completeRoutine() {
    // Routine completion logic

    // Show completion dialog
    _showCompletionDialog();
  }

  void _showCompletionDialog() {
    final duration = DateTime.now().difference(_startTime!);
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(MdiIcons.checkCircle, color: AppTheme.primaryGreen),
            const SizedBox(width: 8),
            const Text('Routine Completed!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Congratulations! You have completed "${widget.routine.title}".'),
            const SizedBox(height: 16),
            Text('Time taken: ${minutes}m ${seconds}s'),
            Text('Steps completed: ${_steps.length}'),
            Text('Total dhikr: ${_stepCounts.values.fold(0, (a, b) => a + b)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close practice screen
            },
            child: const Text('Finish'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              _restartRoutine();
            },
            child: const Text('Practice Again'),
          ),
        ],
      ),
    );
  }

  void _restartRoutine() {
    setState(() {
      _currentStepIndex = 0;
      _stepCounts = {for (var step in _steps) step.id: 0};
      _startTime = DateTime.now();
    });
    
    _pageController.animateToPage(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _openFloatingCounter() async {
    if (_currentStepIndex < _steps.length) {
      final currentStep = _steps[_currentStepIndex];
      final success = await FloatingCounterService.startFloatingCounter(
        dhikr: currentStep.arabicText,
        initialCount: _stepCounts[currentStep.id] ?? 0,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Floating counter started. You can now minimize the app.'),
            duration: Duration(seconds: 3),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not start floating counter. Please check permissions.'),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: Text(widget.routine.title)),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_steps.isEmpty) {
      return Scaffold(
        appBar: AppBar(title: Text(widget.routine.title)),
        body: const Center(
          child: Text('No steps found in this routine'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.routine.title),
        actions: [
          IconButton(
            onPressed: _openFloatingCounter,
            icon: Icon(MdiIcons.counter),
            tooltip: 'Open Floating Counter',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentStepIndex = index;
                });
              },
              itemCount: _steps.length,
              itemBuilder: (context, index) {
                return _buildStepPage(_steps[index]);
              },
            ),
          ),
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Step ${_currentStepIndex + 1} of ${_steps.length}',
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
              Text(
                '${((_currentStepIndex + 1) / _steps.length * 100).round()}%',
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentStepIndex + 1) / _steps.length,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
          ),
        ],
      ),
    );
  }

  Widget _buildStepPage(AthkarStep step) {
    final currentCount = _stepCounts[step.id] ?? 0;
    final isCompleted = currentCount >= step.targetCount;
    final progress = currentCount / step.targetCount;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Arabic Text
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.3),
                ),
              ),
              child: Center(
                child: Text(
                  step.arabicText,
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.w600,
                    height: 1.8,
                  ),
                  textAlign: TextAlign.center,
                  textDirection: TextDirection.rtl,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Transliteration and Translation
          if (step.transliteration != null || step.translation != null)
            Expanded(
              flex: 2,
              child: Column(
                children: [
                  if (step.transliteration != null)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        step.transliteration!,
                        style: const TextStyle(
                          fontSize: 16,
                          fontStyle: FontStyle.italic,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  
                  if (step.transliteration != null && step.translation != null)
                    const SizedBox(height: 12),
                  
                  if (step.translation != null)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppTheme.accentGold.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        step.translation!,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ),
            ),

          const SizedBox(height: 24),

          // Counter Section
          Expanded(
            flex: 2,
            child: Column(
              children: [
                // Progress Circle
                SizedBox(
                  width: 120,
                  height: 120,
                  child: Stack(
                    children: [
                      // Background circle
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.grey[200],
                        ),
                      ),
                      
                      // Progress circle
                      CircularProgressIndicator(
                        value: progress.clamp(0.0, 1.0),
                        strokeWidth: 8,
                        backgroundColor: Colors.transparent,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          isCompleted ? AppTheme.primaryGreen : AppTheme.accentGold,
                        ),
                      ),
                      
                      // Count text
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '$currentCount',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: isCompleted ? AppTheme.primaryGreen : AppTheme.textDark,
                              ),
                            ),
                            Text(
                              '/ ${step.targetCount}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Tap button
                GestureDetector(
                  onTap: isCompleted ? null : _incrementCount,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isCompleted ? Colors.grey[400] : AppTheme.primaryGreen,
                      boxShadow: isCompleted ? null : [
                        BoxShadow(
                          color: AppTheme.primaryGreen.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      isCompleted ? Icons.check : Icons.touch_app,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                ),

                const SizedBox(height: 8),

                Text(
                  isCompleted ? 'Completed!' : 'Tap to count',
                  style: TextStyle(
                    fontSize: 14,
                    color: isCompleted ? AppTheme.primaryGreen : Colors.grey[600],
                    fontWeight: isCompleted ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Previous button
          ElevatedButton.icon(
            onPressed: _currentStepIndex > 0 ? _previousStep : null,
            icon: const Icon(Icons.arrow_back),
            label: const Text('Previous'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[300],
              foregroundColor: Colors.grey[700],
            ),
          ),

          // Step indicator dots
          Row(
            children: List.generate(_steps.length, (index) {
              final isCompleted = _stepCounts[_steps[index].id]! >= _steps[index].targetCount;
              final isCurrent = index == _currentStepIndex;
              
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 2),
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isCompleted 
                      ? AppTheme.primaryGreen
                      : isCurrent 
                          ? AppTheme.accentGold
                          : Colors.grey[300],
                ),
              );
            }),
          ),

          // Next button
          ElevatedButton.icon(
            onPressed: _currentStepIndex < _steps.length - 1 ? _nextStep : null,
            icon: const Icon(Icons.arrow_forward),
            label: const Text('Next'),
          ),
        ],
      ),
    );
  }
}
