import 'package:flutter/material.dart';
import '../models/athkar_models.dart';
import 'athkar_practice_popup.dart';

class PracticeAthkarScreen extends StatelessWidget {
  final AthkarRoutine routine;
  final List<AthkarStep>? steps; // Optional prebuilt steps

  const PracticeAthkarScreen({
    super.key,
    required this.routine,
    this.steps,
  });

  @override
  Widget build(BuildContext context) {
    // Immediately show the popup and return to previous screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AthkarPracticePopup(
            routine: routine,
            steps: steps,
          ),
        ).then((_) {
          // Return to previous screen when popup is closed
          if (context.mounted) {
            Navigator.of(context).pop();
          }
        });
      }
    });

    // Return a transparent scaffold while popup is being shown
    return const Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
