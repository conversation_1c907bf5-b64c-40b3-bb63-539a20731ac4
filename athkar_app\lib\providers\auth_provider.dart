import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../models/athkar_models.dart';
import '../services/supabase_sync_service.dart';

class AuthProvider extends ChangeNotifier {
  User? _user;
  UserProfile? _userProfile;
  bool _isLoading = false;
  String? _errorMessage;

  User? get user => _user;
  UserProfile? get userProfile => _userProfile;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    // Listen to auth state changes
    SupabaseConfig.auth.onAuthStateChange.listen((data) {
      final AuthChangeEvent event = data.event;
      final Session? session = data.session;

      switch (event) {
        case AuthChangeEvent.signedIn:
          _user = session?.user;
          _loadUserProfile();
          SupabaseSyncService.onAuthStateChange(data);
          break;
        case AuthChangeEvent.signedOut:
          _user = null;
          SupabaseSyncService.onAuthStateChange(data);
          _userProfile = null;
          break;
        case AuthChangeEvent.userUpdated:
          _user = session?.user;
          _loadUserProfile();
          break;
        default:
          break;
      }
      notifyListeners();
    });

    // Check if user is already signed in
    _user = SupabaseConfig.auth.currentUser;
    if (_user != null) {
      _loadUserProfile();
    }
  }

  Future<void> signInWithEmail(String email, String password) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await SupabaseConfig.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        _user = response.user;
        await _loadUserProfile();
      }
    } on AuthException catch (e) {
      _setError(e.message);
    } catch (e) {
      _setError('An unexpected error occurred');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signUpWithEmail(String email, String password, {String? fullName}) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await SupabaseConfig.auth.signUp(
        email: email,
        password: password,
        data: fullName != null ? {'full_name': fullName} : null,
      );

      if (response.user != null) {
        _user = response.user;
        // Create user profile
        await _createUserProfile(fullName);
      }
    } on AuthException catch (e) {
      _setError(e.message);
    } catch (e) {
      _setError('An unexpected error occurred');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    try {
      _setLoading(true);
      await SupabaseConfig.auth.signOut();
      _user = null;
      _userProfile = null;
    } catch (e) {
      _setError('Failed to sign out');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      _setLoading(true);
      _clearError();

      await SupabaseConfig.auth.resetPasswordForEmail(email);
    } on AuthException catch (e) {
      _setError(e.message);
    } catch (e) {
      _setError('An unexpected error occurred');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _loadUserProfile() async {
    if (_user == null) return;

    try {
      final response = await SupabaseConfig.client
          .from('user_profiles')
          .select()
          .eq('id', _user!.id)
          .maybeSingle();

      if (response != null) {
        _userProfile = UserProfile.fromJson(response);
      }
    } catch (e) {
      debugPrint('Error loading user profile: $e');
    }
  }

  Future<void> _createUserProfile(String? fullName) async {
    if (_user == null) return;

    try {
      final now = DateTime.now();
      final profileData = {
        'id': _user!.id,
        'full_name': fullName ?? _user!.userMetadata?['full_name'],
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      await SupabaseConfig.client
          .from('user_profiles')
          .insert(profileData);

      _userProfile = UserProfile.fromJson(profileData);
    } catch (e) {
      debugPrint('Error creating user profile: $e');
    }
  }

  Future<void> updateUserProfile({
    String? username,
    String? fullName,
    String? avatarUrl,
  }) async {
    if (_user == null) return;

    try {
      _setLoading(true);
      _clearError();

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (username != null) updateData['username'] = username;
      if (fullName != null) updateData['full_name'] = fullName;
      if (avatarUrl != null) updateData['avatar_url'] = avatarUrl;

      await SupabaseConfig.client
          .from('user_profiles')
          .update(updateData)
          .eq('id', _user!.id);

      await _loadUserProfile();
    } catch (e) {
      _setError('Failed to update profile');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
