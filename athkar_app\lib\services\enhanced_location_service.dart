import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';

class EnhancedLocationService {
  static final EnhancedLocationService _instance = EnhancedLocationService._internal();
  factory EnhancedLocationService() => _instance;
  EnhancedLocationService._internal();

  Position? _lastKnownPosition;
  DateTime? _lastLocationUpdate;
  bool _isLocationServiceEnabled = false;
  LocationPermission _locationPermission = LocationPermission.denied;

  // Jordan-specific location data
  static const Map<String, Map<String, dynamic>> jordanCities = {
    'amman': {'lat': 31.9454, 'lng': 35.9284, 'name_ar': 'عمان'},
    'zarqa': {'lat': 32.0728, 'lng': 36.0879, 'name_ar': 'الزرقاء'},
    'irbid': {'lat': 32.5556, 'lng': 35.8500, 'name_ar': 'إربد'},
    'russeifa': {'lat': 32.0167, 'lng': 36.0500, 'name_ar': 'الرصيفة'},
    'wadi_as_sir': {'lat': 31.9500, 'lng': 35.8167, 'name_ar': 'وادي السير'},
    'aqaba': {'lat': 29.5321, 'lng': 35.0063, 'name_ar': 'العقبة'},
    'madaba': {'lat': 31.7197, 'lng': 35.7956, 'name_ar': 'مادبا'},
    'jerash': {'lat': 32.2811, 'lng': 35.8992, 'name_ar': 'جرش'},
    'mafraq': {'lat': 32.3434, 'lng': 36.2081, 'name_ar': 'المفرق'},
    'karak': {'lat': 31.1853, 'lng': 35.7047, 'name_ar': 'الكرك'},
    'tafilah': {'lat': 30.8378, 'lng': 35.6042, 'name_ar': 'الطفيلة'},
    'maan': {'lat': 30.1962, 'lng': 35.7340, 'name_ar': 'معان'},
  };

  // Getters
  Position? get lastKnownPosition => _lastKnownPosition;
  bool get isLocationServiceEnabled => _isLocationServiceEnabled;
  LocationPermission get locationPermission => _locationPermission;

  /// Initialize location service
  Future<void> initialize() async {
    await _checkLocationService();
    await _checkLocationPermission();
    await _loadLastKnownPosition();
  }

  /// Check if location service is enabled
  Future<void> _checkLocationService() async {
    _isLocationServiceEnabled = await Geolocator.isLocationServiceEnabled();
    debugPrint('Location service enabled: $_isLocationServiceEnabled');
  }

  /// Check location permission
  Future<void> _checkLocationPermission() async {
    _locationPermission = await Geolocator.checkPermission();
    debugPrint('Location permission: $_locationPermission');
  }

  /// Load last known position from storage
  Future<void> _loadLastKnownPosition() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lat = prefs.getDouble('last_latitude');
      final lng = prefs.getDouble('last_longitude');
      final timestamp = prefs.getInt('last_location_timestamp');

      if (lat != null && lng != null && timestamp != null) {
        _lastKnownPosition = Position(
          latitude: lat,
          longitude: lng,
          timestamp: DateTime.fromMillisecondsSinceEpoch(timestamp),
          accuracy: 0,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          headingAccuracy: 0,
          speed: 0,
          speedAccuracy: 0,
        );
        _lastLocationUpdate = DateTime.fromMillisecondsSinceEpoch(timestamp);
        debugPrint('Loaded last known position: ${_lastKnownPosition!.latitude}, ${_lastKnownPosition!.longitude}');
      }
    } catch (e) {
      debugPrint('Error loading last known position: $e');
    }
  }

  /// Save position to storage
  Future<void> _savePosition(Position position) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('last_latitude', position.latitude);
      await prefs.setDouble('last_longitude', position.longitude);
      await prefs.setInt('last_location_timestamp', position.timestamp.millisecondsSinceEpoch);
      debugPrint('Saved position: ${position.latitude}, ${position.longitude}');
    } catch (e) {
      debugPrint('Error saving position: $e');
    }
  }

  /// Get current position with Jordan-specific optimizations
  Future<Position?> getCurrentPosition({bool forceRefresh = false}) async {
    try {
      // Check if we need to refresh location
      if (!forceRefresh && _lastKnownPosition != null && _lastLocationUpdate != null) {
        final timeSinceLastUpdate = DateTime.now().difference(_lastLocationUpdate!);
        if (timeSinceLastUpdate.inMinutes < 30) {
          debugPrint('Using cached location (${timeSinceLastUpdate.inMinutes} minutes old)');
          return _lastKnownPosition;
        }
      }

      // Check permissions
      if (_locationPermission == LocationPermission.denied) {
        _locationPermission = await Geolocator.requestPermission();
        if (_locationPermission == LocationPermission.denied) {
          debugPrint('Location permission denied');
          return _getDefaultJordanLocation();
        }
      }

      if (_locationPermission == LocationPermission.deniedForever) {
        debugPrint('Location permission denied forever');
        return _getDefaultJordanLocation();
      }

      // Check if location service is enabled
      if (!_isLocationServiceEnabled) {
        debugPrint('Location service disabled, using default Jordan location');
        return _getDefaultJordanLocation();
      }

      // Get current position with Jordan-optimized settings
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 15), // Shorter timeout for better UX
      );

      // Validate if position is in Jordan or nearby
      if (_isPositionInJordanRegion(position)) {
        _lastKnownPosition = position;
        _lastLocationUpdate = DateTime.now();
        await _savePosition(position);
        debugPrint('Got current position in Jordan: ${position.latitude}, ${position.longitude}');
        return position;
      } else {
        debugPrint('Position outside Jordan region, using default location');
        return _getDefaultJordanLocation();
      }
    } catch (e) {
      debugPrint('Error getting current position: $e');
      return _lastKnownPosition ?? _getDefaultJordanLocation();
    }
  }

  /// Check if position is in Jordan region
  bool _isPositionInJordanRegion(Position position) {
    // Jordan boundaries (approximate)
    const double minLat = 29.0;
    const double maxLat = 34.0;
    const double minLng = 34.0;
    const double maxLng = 40.0;

    return position.latitude >= minLat &&
           position.latitude <= maxLat &&
           position.longitude >= minLng &&
           position.longitude <= maxLng;
  }

  /// Get default Jordan location (Amman)
  Position _getDefaultJordanLocation() {
    final amman = jordanCities['amman']!;
    return Position(
      latitude: amman['lat']!,
      longitude: amman['lng']!,
      timestamp: DateTime.now(),
      accuracy: 1000, // Indicate this is a default location
      altitude: 0,
      altitudeAccuracy: 0,
      heading: 0,
      headingAccuracy: 0,
      speed: 0,
      speedAccuracy: 0,
    );
  }

  /// Get nearest Jordan city
  Map<String, dynamic>? getNearestJordanCity(Position position) {
    if (!_isPositionInJordanRegion(position)) return null;

    String? nearestCity;
    double minDistance = double.infinity;

    for (final entry in jordanCities.entries) {
      final cityData = entry.value;
      final distance = Geolocator.distanceBetween(
        position.latitude,
        position.longitude,
        cityData['lat']!,
        cityData['lng']!,
      );

      if (distance < minDistance) {
        minDistance = distance;
        nearestCity = entry.key;
      }
    }

    if (nearestCity != null) {
      final cityData = jordanCities[nearestCity]!;
      return {
        'key': nearestCity,
        'name_ar': cityData['name_ar'],
        'lat': cityData['lat'],
        'lng': cityData['lng'],
        'distance': minDistance,
      };
    }

    return null;
  }

  /// Get location by city name
  Position? getLocationByCity(String cityKey) {
    final cityData = jordanCities[cityKey];
    if (cityData == null) return null;

    return Position(
      latitude: cityData['lat']!,
      longitude: cityData['lng']!,
      timestamp: DateTime.now(),
      accuracy: 0,
      altitude: 0,
      altitudeAccuracy: 0,
      heading: 0,
      headingAccuracy: 0,
      speed: 0,
      speedAccuracy: 0,
    );
  }

  /// Get all Jordan cities
  List<Map<String, dynamic>> getAllJordanCities() {
    return jordanCities.entries.map((entry) {
      final cityData = entry.value;
      return {
        'key': entry.key,
        'name_ar': cityData['name_ar'],
        'lat': cityData['lat'],
        'lng': cityData['lng'],
      };
    }).toList();
  }

  /// Check if location needs update
  bool needsLocationUpdate() {
    if (_lastLocationUpdate == null) return true;
    final timeSinceLastUpdate = DateTime.now().difference(_lastLocationUpdate!);
    return timeSinceLastUpdate.inHours >= 1; // Update every hour
  }

  /// Force location refresh
  Future<Position?> refreshLocation() async {
    return await getCurrentPosition(forceRefresh: true);
  }

  /// Get location accuracy description
  String getLocationAccuracyDescription(Position position) {
    if (position.accuracy <= 10) {
      return 'دقة عالية جداً';
    } else if (position.accuracy <= 50) {
      return 'دقة عالية';
    } else if (position.accuracy <= 100) {
      return 'دقة متوسطة';
    } else if (position.accuracy <= 500) {
      return 'دقة منخفضة';
    } else {
      return 'موقع افتراضي';
    }
  }

  /// Check if GPS is available and accurate
  Future<bool> isGPSAccurate() async {
    try {
      final position = await getCurrentPosition();
      return position != null && position.accuracy <= 100;
    } catch (e) {
      return false;
    }
  }

  /// Get location status for UI
  Future<Map<String, dynamic>> getLocationStatus() async {
    await _checkLocationService();
    await _checkLocationPermission();

    return {
      'serviceEnabled': _isLocationServiceEnabled,
      'permission': _locationPermission.toString(),
      'hasLastKnown': _lastKnownPosition != null,
      'lastUpdate': _lastLocationUpdate?.toIso8601String(),
      'needsUpdate': needsLocationUpdate(),
    };
  }
}
