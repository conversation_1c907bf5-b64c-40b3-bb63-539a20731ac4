import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';

class FloatingWindowService {
  static const MethodChannel _channel = MethodChannel('floating_window');
  static bool _isInitialized = false;
  static bool _isWindowVisible = false;

  /// Initialize floating window service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (Platform.isAndroid) {
        // Request system alert window permission
        await _requestOverlayPermission();
      }

      _isInitialized = true;
      debugPrint('Floating window service initialized');
    } catch (e) {
      debugPrint('Error initializing floating window service: $e');
    }
  }

  /// Request overlay permission for Android
  static Future<bool> _requestOverlayPermission() async {
    try {
      final status = await Permission.systemAlertWindow.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting overlay permission: $e');
      return false;
    }
  }

  /// Check if overlay permission is granted
  static Future<bool> hasOverlayPermission() async {
    if (!Platform.isAndroid) return true;
    
    try {
      return await Permission.systemAlertWindow.isGranted;
    } catch (e) {
      debugPrint('Error checking overlay permission: $e');
      return false;
    }
  }

  /// Show floating dhikr counter
  static Future<void> showFloatingCounter({
    required String dhikrText,
    required String transliteration,
    required int targetCount,
    String? colorHex,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      if (!await hasOverlayPermission()) {
        throw Exception('Overlay permission not granted');
      }

      final result = await _channel.invokeMethod('showFloatingCounter', {
        'dhikrText': dhikrText,
        'transliteration': transliteration,
        'targetCount': targetCount,
        'colorHex': colorHex ?? '2E7D32',
        'currentCount': 0,
      });

      if (result == true) {
        _isWindowVisible = true;
        await _saveFloatingWindowState(true);
        debugPrint('Floating counter shown successfully');
      }
    } catch (e) {
      debugPrint('Error showing floating counter: $e');
      rethrow;
    }
  }

  /// Update floating counter
  static Future<void> updateFloatingCounter({
    required int currentCount,
    required int targetCount,
  }) async {
    if (!_isWindowVisible) return;

    try {
      await _channel.invokeMethod('updateFloatingCounter', {
        'currentCount': currentCount,
        'targetCount': targetCount,
        'isCompleted': currentCount >= targetCount,
      });
    } catch (e) {
      debugPrint('Error updating floating counter: $e');
    }
  }

  /// Hide floating counter
  static Future<void> hideFloatingCounter() async {
    if (!_isWindowVisible) return;

    try {
      await _channel.invokeMethod('hideFloatingCounter');
      _isWindowVisible = false;
      await _saveFloatingWindowState(false);
      debugPrint('Floating counter hidden');
    } catch (e) {
      debugPrint('Error hiding floating counter: $e');
    }
  }

  /// Show floating athkar reminder
  static Future<void> showFloatingReminder({
    required String title,
    required String message,
    int? durationSeconds,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      if (!await hasOverlayPermission()) {
        throw Exception('Overlay permission not granted');
      }

      await _channel.invokeMethod('showFloatingReminder', {
        'title': title,
        'message': message,
        'duration': durationSeconds ?? 5,
      });

      debugPrint('Floating reminder shown');
    } catch (e) {
      debugPrint('Error showing floating reminder: $e');
    }
  }

  /// Show floating prayer time notification
  static Future<void> showFloatingPrayerNotification({
    required String prayerName,
    required String timeRemaining,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      if (!await hasOverlayPermission()) {
        throw Exception('Overlay permission not granted');
      }

      await _channel.invokeMethod('showFloatingPrayerNotification', {
        'prayerName': prayerName,
        'timeRemaining': timeRemaining,
        'isUrgent': timeRemaining.contains('دقيقة') || timeRemaining.contains('minute'),
      });

      debugPrint('Floating prayer notification shown');
    } catch (e) {
      debugPrint('Error showing floating prayer notification: $e');
    }
  }

  /// Create floating quick access button
  static Future<void> showFloatingQuickAccess() async {
    if (!_isInitialized) await initialize();

    try {
      if (!await hasOverlayPermission()) {
        throw Exception('Overlay permission not granted');
      }

      await _channel.invokeMethod('showFloatingQuickAccess', {
        'iconType': 'dhikr',
        'position': 'right',
      });

      debugPrint('Floating quick access shown');
    } catch (e) {
      debugPrint('Error showing floating quick access: $e');
    }
  }

  /// Hide floating quick access button
  static Future<void> hideFloatingQuickAccess() async {
    try {
      await _channel.invokeMethod('hideFloatingQuickAccess');
      debugPrint('Floating quick access hidden');
    } catch (e) {
      debugPrint('Error hiding floating quick access: $e');
    }
  }

  /// Check if floating window is visible
  static bool get isWindowVisible => _isWindowVisible;

  /// Save floating window state
  static Future<void> _saveFloatingWindowState(bool isVisible) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('floating_window_visible', isVisible);
    } catch (e) {
      debugPrint('Error saving floating window state: $e');
    }
  }

  /// Restore floating window state
  static Future<void> restoreFloatingWindowState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final wasVisible = prefs.getBool('floating_window_visible') ?? false;
      
      if (wasVisible && await hasOverlayPermission()) {
        // Restore the last floating window if it was visible
        final lastDhikr = prefs.getString('last_floating_dhikr');
        if (lastDhikr != null) {
          // Parse and restore the floating counter
          // This would need to be implemented based on stored data
        }
      }
    } catch (e) {
      debugPrint('Error restoring floating window state: $e');
    }
  }

  /// Handle floating window callbacks
  static void setupCallbacks() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onFloatingCounterTapped':
          await _handleFloatingCounterTap();
          break;
        case 'onFloatingCounterIncrement':
          await _handleFloatingCounterIncrement(call.arguments);
          break;
        case 'onFloatingCounterCompleted':
          await _handleFloatingCounterCompleted(call.arguments);
          break;
        case 'onFloatingQuickAccessTapped':
          await _handleFloatingQuickAccessTap();
          break;
        default:
          debugPrint('Unknown method call: ${call.method}');
      }
    });
  }

  /// Handle floating counter tap
  static Future<void> _handleFloatingCounterTap() async {
    debugPrint('Floating counter tapped');
    // Open main app or specific screen
  }

  /// Handle floating counter increment
  static Future<void> _handleFloatingCounterIncrement(dynamic arguments) async {
    final currentCount = arguments['currentCount'] as int;
    debugPrint('Floating counter incremented to: $currentCount');
    
    // Save progress to database
    // Sync with Supabase if online
  }

  /// Handle floating counter completion
  static Future<void> _handleFloatingCounterCompleted(dynamic arguments) async {
    final finalCount = arguments['finalCount'] as int;
    debugPrint('Floating counter completed with count: $finalCount');
    
    // Show completion notification
    // Save completion to database
    // Sync with Supabase
  }

  /// Handle floating quick access tap
  static Future<void> _handleFloatingQuickAccessTap() async {
    debugPrint('Floating quick access tapped');
    // Show quick dhikr options or open main app
  }

  /// Dispose floating window service
  static Future<void> dispose() async {
    try {
      await hideFloatingCounter();
      await hideFloatingQuickAccess();
      _isInitialized = false;
      debugPrint('Floating window service disposed');
    } catch (e) {
      debugPrint('Error disposing floating window service: $e');
    }
  }
}
