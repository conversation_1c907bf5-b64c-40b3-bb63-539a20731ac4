/// Generated file. Do not edit.
///
/// This file contains the localized strings for the Athkar app.
/// To regenerate this file, run: flutter gen-l10n

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Athkar App';

  @override
  String get home => 'Home';

  @override
  String get prayerTimes => 'Prayer Times';

  @override
  String get qibla => 'Qibla';

  @override
  String get quran => 'Holy Quran';

  @override
  String get athkar => 'Athkar';

  @override
  String get tasbeeh => 'Tasbeeh';

  @override
  String get calendar => 'Islamic Calendar';

  @override
  String get settings => 'Settings';

  @override
  String get welcomeMessage =>
      'Peace be upon you and <PERSON>\'s mercy and blessings';

  @override
  String get currentPrayer => 'Current Prayer';

  @override
  String get nextPrayer => 'Next Prayer';

  @override
  String get prayerTimesTitle => 'Prayer Times';

  @override
  String get prayerTimesSettings => 'Prayer Times Settings';

  @override
  String get fajr => 'Fajr';

  @override
  String get sunrise => 'Sunrise';

  @override
  String get dhuhr => 'Dhuhr';

  @override
  String get asr => 'Asr';

  @override
  String get maghrib => 'Maghrib';

  @override
  String get isha => 'Isha';

  @override
  String get qiblaFinder => 'Qibla Finder';

  @override
  String get qiblaDirection => 'Qibla Direction';

  @override
  String get distanceToKaaba => 'Distance to Kaaba';

  @override
  String get holdPhoneFlat => 'Hold your phone flat and level';

  @override
  String get greenLinePointsToQibla => 'The green line points toward the Qibla';

  @override
  String get rotateUntilFacingGreenLine =>
      'Rotate yourself until facing the green line';

  @override
  String get nowFacingQibla => 'You are now facing the Qibla for prayer';

  @override
  String get holyQuran => 'Holy Quran';

  @override
  String get surahs => 'Surahs';

  @override
  String get bookmarks => 'Bookmarks';

  @override
  String get search => 'Search';

  @override
  String get searchInQuran => 'Search in Quran...';

  @override
  String get noBookmarksYet => 'No bookmarks yet';

  @override
  String get bookmarkVersesToAccess => 'Bookmark verses to access them quickly';

  @override
  String get searchTheHolyQuran => 'Search the Holy Quran';

  @override
  String get enterKeywordsToSearch => 'Enter keywords to search verses';

  @override
  String get tafseer => 'Tafseer';

  @override
  String get showTafseer => 'Show Tafseer';

  @override
  String get displayCommentaryBelowVerse =>
      'Display commentary below each verse';

  @override
  String get noTafseerAvailable => 'No Tafseer available for this verse';

  @override
  String get textType => 'Text Type';

  @override
  String get uthmaniScript => 'Uthmani Script';

  @override
  String get simpleText => 'Simple Text';

  @override
  String get original => 'Original';

  @override
  String get athkarAndDhikr => 'Athkar & Dhikr';

  @override
  String get morningAthkar => 'Morning Athkar';

  @override
  String get eveningAthkar => 'Evening Athkar';

  @override
  String get prayerAthkar => 'Prayer Athkar';

  @override
  String get dhikrCounter => 'Dhikr Counter';

  @override
  String get createNew => 'Create New';

  @override
  String get startAthkar => 'Start Athkar';

  @override
  String get completed => 'Completed';

  @override
  String get previous => 'Previous';

  @override
  String get next => 'Next';

  @override
  String get finish => 'Finish';

  @override
  String get athkarPracticeCompleted =>
      'Athkar practice completed! May Allah accept it.';

  @override
  String get islamicCalendar => 'Islamic Calendar';

  @override
  String get events => 'Events';

  @override
  String get upcomingIslamicEvents => 'Upcoming Islamic Events';

  @override
  String get noUpcomingEvents => 'No upcoming events';

  @override
  String get noEventsForThisDay => 'No events for this day';

  @override
  String get prayerTimeAdjustments => 'Prayer Time Adjustments';

  @override
  String get adjustPrayerTimes => 'Adjust Prayer Times';

  @override
  String get adjustmentDescription =>
      'You can adjust individual prayer times by adding or subtracting minutes. This is useful for local variations or personal preferences.';

  @override
  String get noAdjustment => 'No adjustment';

  @override
  String get resetAllAdjustments => 'Reset All Adjustments';

  @override
  String get resetConfirmation =>
      'Are you sure you want to reset all prayer time adjustments to zero?';

  @override
  String get cancel => 'Cancel';

  @override
  String get reset => 'Reset';

  @override
  String get allAdjustmentsReset =>
      'All prayer time adjustments have been reset';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get recentAthkar => 'Recent Athkar';

  @override
  String get favoriteAthkar => 'Favorite Athkar';

  @override
  String get progress => 'Progress';

  @override
  String get statistics => 'Statistics';

  @override
  String get sync => 'Sync';

  @override
  String get profile => 'Profile';

  @override
  String get allAthkar => 'All Athkar';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get retry => 'Retry';

  @override
  String get close => 'Close';

  @override
  String get save => 'Save';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get remove => 'Remove';

  @override
  String get confirm => 'Confirm';

  @override
  String get locationPermissionRequired => 'Location permission required';

  @override
  String get locationServicesDisabled => 'Location services are disabled';

  @override
  String get couldNotGetLocation =>
      'Could not get current location, using Jordan default';

  @override
  String get unableToLoadPrayerTimes => 'Unable to load prayer times';

  @override
  String get errorLoadingSurah => 'Error loading Surah';

  @override
  String get errorLoadingQuran => 'Error loading Quran';

  @override
  String get errorAccessingCompass => 'Error accessing compass';

  @override
  String get initializingCompass => 'Initializing compass...';

  @override
  String get signIn => 'Sign In';

  @override
  String get signOut => 'Sign Out';

  @override
  String get pleaseSignInToSync => 'Please sign in to sync your data';

  @override
  String get syncingData => 'Syncing data...';

  @override
  String get syncCompleted => 'Sync completed successfully';

  @override
  String get syncFailed => 'Sync failed';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get language => 'Language';

  @override
  String get arabic => 'Arabic';

  @override
  String get english => 'English';

  @override
  String get notifications => 'Notifications';

  @override
  String get enableNotifications => 'Enable Notifications';

  @override
  String get prayerNotifications => 'Prayer Notifications';

  @override
  String get athkarReminders => 'Athkar Reminders';

  @override
  String get aboutApp => 'About App';

  @override
  String get version => 'Version';

  @override
  String get developer => 'Developer';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get rateApp => 'Rate App';

  @override
  String get shareApp => 'Share App';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsOfService => 'Terms of Service';
}
