import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';

class ColorPickerWidget extends StatefulWidget {
  final Color initialColor;
  final Function(Color) onColorChanged;
  final String title;
  final bool showAlpha;
  final List<Color>? presetColors;

  const ColorPickerWidget({
    super.key,
    required this.initialColor,
    required this.onColorChanged,
    this.title = 'Select Color',
    this.showAlpha = false,
    this.presetColors,
  });

  @override
  State<ColorPickerWidget> createState() => _ColorPickerWidgetState();
}

class _ColorPickerWidgetState extends State<ColorPickerWidget> with TickerProviderStateMixin {
  late Color _selectedColor;
  late TabController _tabController;
  late TextEditingController _hexController;

  // Default Islamic-themed preset colors
  static const List<Color> _defaultPresetColors = [
    AppTheme.primaryGreen,
    AppTheme.lightGreen,
    AppTheme.accentGold,
    AppTheme.lightGold,
    AppTheme.darkBlue,
    AppTheme.lightBlue,
    Color(0xFF8E24AA), // Purple
    Color(0xFFD32F2F), // Red
    Color(0xFFFF6F00), // Orange
    Color(0xFF388E3C), // Green
    Color(0xFF1976D2), // Blue
    Color(0xFF7B1FA2), // Deep Purple
    Color(0xFF5D4037), // Brown
    Color(0xFF455A64), // Blue Grey
    Color(0xFF424242), // Grey
    Color(0xFF000000), // Black
  ];

  @override
  void initState() {
    super.initState();
    _selectedColor = widget.initialColor;
    _tabController = TabController(length: 3, vsync: this);
    _hexController = TextEditingController(text: _colorToHex(_selectedColor));
  }

  @override
  void dispose() {
    _tabController.dispose();
    _hexController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 350,
        height: 500,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Color preview
            Container(
              width: double.infinity,
              height: 60,
              decoration: BoxDecoration(
                color: _selectedColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Center(
                child: Text(
                  _colorToHex(_selectedColor),
                  style: TextStyle(
                    color: _getContrastColor(_selectedColor),
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Tabs
            TabBar(
              controller: _tabController,
              labelColor: AppTheme.primaryGreen,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AppTheme.primaryGreen,
              tabs: const [
                Tab(text: 'Presets'),
                Tab(text: 'Wheel'),
                Tab(text: 'Custom'),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildPresetColors(),
                  _buildColorWheel(),
                  _buildCustomInput(),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    widget.onColorChanged(_selectedColor);
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryGreen,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Select'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPresetColors() {
    final colors = widget.presetColors ?? _defaultPresetColors;
    
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: colors.length,
      itemBuilder: (context, index) {
        final color = colors[index];
        final isSelected = color.toARGB32() == _selectedColor.toARGB32();
        
        return GestureDetector(
          onTap: () => _updateColor(color),
          child: Container(
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? AppTheme.primaryGreen : Colors.grey[300]!,
                width: isSelected ? 3 : 1,
              ),
            ),
            child: isSelected
                ? Icon(
                    Icons.check,
                    color: _getContrastColor(color),
                    size: 24,
                  )
                : null,
          ),
        );
      },
    );
  }

  Widget _buildColorWheel() {
    return Column(
      children: [
        // Hue slider
        Container(
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: const LinearGradient(
              colors: [
                Colors.red,
                Colors.yellow,
                Colors.green,
                Colors.cyan,
                Colors.blue,
                Color(0xFFFF00FF), // Magenta
                Colors.red,
              ],
            ),
          ),
          child: SliderTheme(
            data: SliderTheme.of(context).copyWith(
              trackHeight: 40,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 15),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 20),
              activeTrackColor: Colors.transparent,
              inactiveTrackColor: Colors.transparent,
            ),
            child: Slider(
              value: HSVColor.fromColor(_selectedColor).hue,
              min: 0,
              max: 360,
              onChanged: (value) {
                final hsv = HSVColor.fromColor(_selectedColor);
                _updateColor(hsv.withHue(value).toColor());
              },
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Saturation slider
        Container(
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              colors: [
                HSVColor.fromColor(_selectedColor).withSaturation(0).toColor(),
                HSVColor.fromColor(_selectedColor).withSaturation(1).toColor(),
              ],
            ),
          ),
          child: SliderTheme(
            data: SliderTheme.of(context).copyWith(
              trackHeight: 40,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 15),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 20),
              activeTrackColor: Colors.transparent,
              inactiveTrackColor: Colors.transparent,
            ),
            child: Slider(
              value: HSVColor.fromColor(_selectedColor).saturation,
              min: 0,
              max: 1,
              onChanged: (value) {
                final hsv = HSVColor.fromColor(_selectedColor);
                _updateColor(hsv.withSaturation(value).toColor());
              },
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Value/Brightness slider
        Container(
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              colors: [
                Colors.black,
                HSVColor.fromColor(_selectedColor).withValue(1).toColor(),
              ],
            ),
          ),
          child: SliderTheme(
            data: SliderTheme.of(context).copyWith(
              trackHeight: 40,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 15),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 20),
              activeTrackColor: Colors.transparent,
              inactiveTrackColor: Colors.transparent,
            ),
            child: Slider(
              value: HSVColor.fromColor(_selectedColor).value,
              min: 0,
              max: 1,
              onChanged: (value) {
                final hsv = HSVColor.fromColor(_selectedColor);
                _updateColor(hsv.withValue(value).toColor());
              },
            ),
          ),
        ),
        
        if (widget.showAlpha) ...[
          const SizedBox(height: 16),
          
          // Alpha slider
          Container(
            height: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                colors: [
                  _selectedColor.withValues(alpha: 0),
                  _selectedColor.withValues(alpha: 1),
                ],
              ),
            ),
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                trackHeight: 40,
                thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 15),
                overlayShape: const RoundSliderOverlayShape(overlayRadius: 20),
                activeTrackColor: Colors.transparent,
                inactiveTrackColor: Colors.transparent,
              ),
              child: Slider(
                value: _selectedColor.a,
                min: 0,
                max: 1,
                onChanged: (value) {
                  _updateColor(_selectedColor.withValues(alpha: value));
                },
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCustomInput() {
    return Column(
      children: [
        // Hex input
        TextField(
          controller: _hexController,
          decoration: const InputDecoration(
            labelText: 'Hex Color',
            hintText: '#RRGGBB',
            prefixText: '#',
            border: OutlineInputBorder(),
          ),
          maxLength: 6,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9A-Fa-f]')),
          ],
          onChanged: (value) {
            if (value.length == 6) {
              try {
                final color = Color(int.parse('FF$value', radix: 16));
                _updateColor(color);
              } catch (e) {
                // Invalid hex color
              }
            }
          },
        ),
        
        const SizedBox(height: 16),
        
        // RGB sliders
        _buildRGBSlider('Red', (_selectedColor.r * 255.0).round(), (value) {
          _updateColor(Color.fromARGB(
            (_selectedColor.a * 255.0).round(),
            value.round(),
            (_selectedColor.g * 255.0).round(),
            (_selectedColor.b * 255.0).round(),
          ));
        }),

        const SizedBox(height: 8),

        _buildRGBSlider('Green', (_selectedColor.g * 255.0).round(), (value) {
          _updateColor(Color.fromARGB(
            (_selectedColor.a * 255.0).round(),
            (_selectedColor.r * 255.0).round(),
            value.round(),
            (_selectedColor.b * 255.0).round(),
          ));
        }),

        const SizedBox(height: 8),

        _buildRGBSlider('Blue', (_selectedColor.b * 255.0).round(), (value) {
          _updateColor(Color.fromARGB(
            (_selectedColor.a * 255.0).round(),
            (_selectedColor.r * 255.0).round(),
            (_selectedColor.g * 255.0).round(),
            value.round(),
          ));
        }),
      ],
    );
  }

  Widget _buildRGBSlider(String label, int value, Function(double) onChanged) {
    return Row(
      children: [
        SizedBox(
          width: 50,
          child: Text(label),
        ),
        Expanded(
          child: Slider(
            value: value.toDouble(),
            min: 0,
            max: 255,
            divisions: 255,
            activeColor: AppTheme.primaryGreen,
            onChanged: onChanged,
          ),
        ),
        SizedBox(
          width: 40,
          child: Text(
            value.toString(),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  void _updateColor(Color color) {
    setState(() {
      _selectedColor = color;
      _hexController.text = _colorToHex(color);
    });
  }

  String _colorToHex(Color color) {
    return color.toARGB32().toRadixString(16).substring(2).toUpperCase();
  }

  Color _getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}

// Helper function to show color picker dialog
Future<Color?> showColorPickerDialog(
  BuildContext context, {
  required Color initialColor,
  String title = 'Select Color',
  bool showAlpha = false,
  List<Color>? presetColors,
}) async {
  Color? selectedColor;
  
  await showDialog<Color>(
    context: context,
    builder: (context) => ColorPickerWidget(
      initialColor: initialColor,
      title: title,
      showAlpha: showAlpha,
      presetColors: presetColors,
      onColorChanged: (color) {
        selectedColor = color;
      },
    ),
  );
  
  return selectedColor;
}
