import 'dart:io';
import 'package:flutter/material.dart';
// import 'package:flutter/services.dart'; // Unnecessary import removed
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'language_service.dart';

/// Comprehensive permissions manager for Islamic Athkar app
class PermissionsManager {
  static final PermissionsManager _instance = PermissionsManager._internal();
  factory PermissionsManager() => _instance;
  PermissionsManager._internal();

  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  /// All permissions required by the app
  static const List<Permission> _requiredPermissions = [
    Permission.notification,
    Permission.storage,
    Permission.location,
    Permission.microphone,
    Permission.camera,
    Permission.phone,
  ];

  /// Android-specific permissions
  static const List<Permission> _androidPermissions = [
    Permission.systemAlertWindow,
    Permission.requestInstallPackages,
    Permission.manageExternalStorage,
    Permission.scheduleExactAlarm,
  ];

  /// iOS-specific permissions
  static const List<Permission> _iosPermissions = [
    Permission.appTrackingTransparency,
    Permission.reminders,
    Permission.calendarWriteOnly,
  ];

  /// Check if all required permissions are granted
  Future<bool> areAllPermissionsGranted() async {
    final permissions = await _getRequiredPermissions();
    
    for (final permission in permissions) {
      final status = await permission.status;
      if (!status.isGranted) {
        return false;
      }
    }
    return true;
  }

  /// Get platform-specific required permissions
  Future<List<Permission>> _getRequiredPermissions() async {
    List<Permission> permissions = List.from(_requiredPermissions);
    
    if (Platform.isAndroid) {
      final androidInfo = await _deviceInfo.androidInfo;
      permissions.addAll(_androidPermissions);
      
      // Add API level specific permissions
      if (androidInfo.version.sdkInt >= 30) {
        permissions.add(Permission.manageExternalStorage);
      }
      if (androidInfo.version.sdkInt >= 31) {
        permissions.add(Permission.scheduleExactAlarm);
      }
    } else if (Platform.isIOS) {
      permissions.addAll(_iosPermissions);
    }
    
    return permissions;
  }

  /// Request all required permissions with user-friendly explanations
  Future<Map<Permission, PermissionStatus>> requestAllPermissions(
    BuildContext context,
    LanguageService languageService,
  ) async {
    final permissions = await _getRequiredPermissions();
    final results = <Permission, PermissionStatus>{};
    
    for (final permission in permissions) {
      final status = await _requestPermissionWithExplanation(
        context,
        permission,
        languageService,
      );
      results[permission] = status;
    }
    
    return results;
  }

  /// Request specific permission with explanation
  Future<PermissionStatus> _requestPermissionWithExplanation(
    BuildContext context,
    Permission permission,
    LanguageService languageService,
  ) async {
    final currentStatus = await permission.status;
    
    if (currentStatus.isGranted) {
      return currentStatus;
    }
    
    // Show explanation dialog before requesting permission
    final shouldRequest = await _showPermissionExplanation(
      context,
      permission,
      languageService,
    );
    
    if (!shouldRequest) {
      return currentStatus;
    }
    
    // Request the permission
    final newStatus = await permission.request();
    
    // Handle denied permanently case
    if (newStatus.isPermanentlyDenied) {
      await _showPermissionDeniedDialog(context, permission, languageService);
    }
    
    return newStatus;
  }

  /// Show permission explanation dialog
  Future<bool> _showPermissionExplanation(
    BuildContext context,
    Permission permission,
    LanguageService languageService,
  ) async {
    final explanation = _getPermissionExplanation(permission, languageService);
    
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              _getPermissionIcon(permission),
              color: const Color(0xFF2E7D32),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                explanation['title']!,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              explanation['description']!,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF2E7D32).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Color(0xFF2E7D32),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      explanation['benefit']!,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              languageService.isArabic ? 'تخطي' : 'Skip',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7D32),
              foregroundColor: Colors.white,
            ),
            child: Text(
              languageService.isArabic ? 'السماح' : 'Allow',
            ),
          ),
        ],
      ),
    ) ?? false;
  }

  /// Show permission permanently denied dialog
  Future<void> _showPermissionDeniedDialog(
    BuildContext context,
    Permission permission,
    LanguageService languageService,
  ) async {
    final explanation = _getPermissionExplanation(permission, languageService);
    
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          languageService.isArabic ? 'صلاحية مطلوبة' : 'Permission Required',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.red,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              languageService.isArabic
                  ? 'لتفعيل ${explanation['title']}، يرجى الذهاب إلى الإعدادات وتفعيل الصلاحية يدوياً.'
                  : 'To enable ${explanation['title']}, please go to Settings and enable the permission manually.',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7D32),
              foregroundColor: Colors.white,
            ),
            child: Text(
              languageService.isArabic ? 'فتح الإعدادات' : 'Open Settings',
            ),
          ),
        ],
      ),
    );
  }

  /// Get permission explanation based on permission type and language
  Map<String, String> _getPermissionExplanation(
    Permission permission,
    LanguageService languageService,
  ) {
    if (languageService.isArabic) {
      return _getArabicExplanation(permission);
    } else {
      return _getEnglishExplanation(permission);
    }
  }

  /// Arabic explanations for permissions
  Map<String, String> _getArabicExplanation(Permission permission) {
    switch (permission) {
      case Permission.notification:
        return {
          'title': 'الإشعارات',
          'description': 'لإرسال تذكيرات الأذكار وأوقات الصلاة',
          'benefit': 'ستتلقى تذكيرات في الوقت المناسب لقراءة الأذكار والصلاة',
        };
      case Permission.storage:
        return {
          'title': 'التخزين',
          'description': 'لحفظ الأذكار والقرآن والأحاديث على الجهاز',
          'benefit': 'يمكنك استخدام التطبيق بدون إنترنت',
        };
      case Permission.location:
        return {
          'title': 'الموقع',
          'description': 'لتحديد اتجاه القبلة وأوقات الصلاة الدقيقة',
          'benefit': 'ستحصل على أوقات صلاة دقيقة واتجاه القبلة الصحيح',
        };
      case Permission.systemAlertWindow:
        return {
          'title': 'النوافذ العائمة',
          'description': 'لعرض عداد الأذكار العائم على الشاشة',
          'benefit': 'يمكنك عد الأذكار أثناء استخدام تطبيقات أخرى',
        };
      case Permission.microphone:
        return {
          'title': 'الميكروفون',
          'description': 'لتسجيل تلاوتك الشخصية للقرآن',
          'benefit': 'يمكنك تسجيل ومراجعة تلاوتك',
        };
      case Permission.camera:
        return {
          'title': 'الكاميرا',
          'description': 'لمسح رموز QR للمشاركة السريعة',
          'benefit': 'مشاركة سريعة للأذكار والآيات',
        };
      default:
        return {
          'title': 'صلاحية مطلوبة',
          'description': 'هذه الصلاحية مطلوبة لعمل التطبيق بشكل صحيح',
          'benefit': 'ستحصل على تجربة أفضل في استخدام التطبيق',
        };
    }
  }

  /// English explanations for permissions
  Map<String, String> _getEnglishExplanation(Permission permission) {
    switch (permission) {
      case Permission.notification:
        return {
          'title': 'Notifications',
          'description': 'To send Athkar reminders and prayer time alerts',
          'benefit': 'You\'ll receive timely reminders for Athkar and prayers',
        };
      case Permission.storage:
        return {
          'title': 'Storage',
          'description': 'To save Athkar, Quran, and Hadith content on your device',
          'benefit': 'You can use the app offline without internet',
        };
      case Permission.location:
        return {
          'title': 'Location',
          'description': 'To determine Qibla direction and accurate prayer times',
          'benefit': 'You\'ll get precise prayer times and correct Qibla direction',
        };
      case Permission.systemAlertWindow:
        return {
          'title': 'Overlay Windows',
          'description': 'To display floating Athkar counter on screen',
          'benefit': 'You can count Athkar while using other apps',
        };
      case Permission.microphone:
        return {
          'title': 'Microphone',
          'description': 'To record your personal Quran recitation',
          'benefit': 'You can record and review your recitation',
        };
      case Permission.camera:
        return {
          'title': 'Camera',
          'description': 'To scan QR codes for quick sharing',
          'benefit': 'Quick sharing of Athkar and verses',
        };
      default:
        return {
          'title': 'Permission Required',
          'description': 'This permission is required for the app to work properly',
          'benefit': 'You\'ll get a better experience using the app',
        };
    }
  }

  /// Get icon for permission type
  IconData _getPermissionIcon(Permission permission) {
    switch (permission) {
      case Permission.notification:
        return Icons.notifications;
      case Permission.storage:
        return Icons.storage;
      case Permission.location:
        return Icons.location_on;
      case Permission.systemAlertWindow:
        return Icons.picture_in_picture;
      case Permission.microphone:
        return Icons.mic;
      case Permission.camera:
        return Icons.camera_alt;
      case Permission.phone:
        return Icons.phone;
      default:
        return Icons.security;
    }
  }

  /// Check specific permission status
  Future<PermissionStatus> checkPermission(Permission permission) async {
    return await permission.status;
  }

  /// Request specific permission
  Future<PermissionStatus> requestPermission(Permission permission) async {
    return await permission.request();
  }

  /// Open app settings
  Future<bool> openAppSettings() async {
    return await openAppSettings();
  }

  /// Get permissions summary for display
  Future<Map<String, dynamic>> getPermissionsSummary(
    LanguageService languageService,
  ) async {
    final permissions = await _getRequiredPermissions();
    final summary = <String, dynamic>{
      'total': permissions.length,
      'granted': 0,
      'denied': 0,
      'details': <Map<String, dynamic>>[],
    };

    for (final permission in permissions) {
      final status = await permission.status;
      final explanation = _getPermissionExplanation(permission, languageService);
      
      summary['details'].add({
        'permission': permission,
        'status': status,
        'title': explanation['title'],
        'description': explanation['description'],
        'icon': _getPermissionIcon(permission),
        'isGranted': status.isGranted,
      });

      if (status.isGranted) {
        summary['granted']++;
      } else {
        summary['denied']++;
      }
    }

    return summary;
  }

  /// Show permissions status screen
  static Future<void> showPermissionsScreen(
    BuildContext context,
    LanguageService languageService,
  ) async {
    final manager = PermissionsManager();
    final summary = await manager.getPermissionsSummary(languageService);
    
    await showDialog(
      context: context,
      builder: (context) => _PermissionsStatusDialog(
        summary: summary,
        languageService: languageService,
        manager: manager,
      ),
    );
  }

  // Additional methods for testing
  Future<bool> hasNotificationPermission() async {
    try {
      return await Permission.notification.isGranted;
    } catch (e) {
      return false;
    }
  }

  Future<void> requestNotificationPermission() async {
    try {
      await Permission.notification.request();
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');
    }
  }

  Future<bool> hasOverlayPermission() async {
    try {
      return await Permission.systemAlertWindow.isGranted;
    } catch (e) {
      return false;
    }
  }

  Future<void> requestOverlayPermission() async {
    try {
      await Permission.systemAlertWindow.request();
    } catch (e) {
      debugPrint('Error requesting overlay permission: $e');
    }
  }

  Future<bool> hasLocationPermission() async {
    try {
      return await Permission.location.isGranted;
    } catch (e) {
      return false;
    }
  }

  Future<void> requestLocationPermission() async {
    try {
      await Permission.location.request();
    } catch (e) {
      debugPrint('Error requesting location permission: $e');
    }
  }

  Future<bool> hasStoragePermission() async {
    try {
      return await Permission.storage.isGranted;
    } catch (e) {
      return false;
    }
  }

  Future<void> requestStoragePermission() async {
    try {
      await Permission.storage.request();
    } catch (e) {
      debugPrint('Error requesting storage permission: $e');
    }
  }

  Future<bool> hasAudioPermission() async {
    try {
      return await Permission.microphone.isGranted;
    } catch (e) {
      return false;
    }
  }

  Future<void> requestAudioPermission() async {
    try {
      await Permission.microphone.request();
    } catch (e) {
      debugPrint('Error requesting audio permission: $e');
    }
  }

  Future<bool> hasBackgroundPermission() async {
    try {
      return await Permission.ignoreBatteryOptimizations.isGranted;
    } catch (e) {
      return false;
    }
  }

  Future<void> requestBackgroundPermission() async {
    try {
      await Permission.ignoreBatteryOptimizations.request();
    } catch (e) {
      debugPrint('Error requesting background permission: $e');
    }
  }
}

/// Permissions status dialog widget
class _PermissionsStatusDialog extends StatefulWidget {
  final Map<String, dynamic> summary;
  final LanguageService languageService;
  final PermissionsManager manager;

  const _PermissionsStatusDialog({
    required this.summary,
    required this.languageService,
    required this.manager,
  });

  @override
  State<_PermissionsStatusDialog> createState() => _PermissionsStatusDialogState();
}

class _PermissionsStatusDialogState extends State<_PermissionsStatusDialog> {
  late Map<String, dynamic> _summary;

  @override
  void initState() {
    super.initState();
    _summary = widget.summary;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.languageService.isArabic ? 'حالة الصلاحيات' : 'Permissions Status',
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Summary
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF2E7D32).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildSummaryItem(
                    widget.languageService.isArabic ? 'المجموع' : 'Total',
                    _summary['total'].toString(),
                    Colors.blue,
                  ),
                  _buildSummaryItem(
                    widget.languageService.isArabic ? 'مفعل' : 'Granted',
                    _summary['granted'].toString(),
                    Colors.green,
                  ),
                  _buildSummaryItem(
                    widget.languageService.isArabic ? 'مرفوض' : 'Denied',
                    _summary['denied'].toString(),
                    Colors.red,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // Permissions list
            Flexible(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _summary['details'].length,
                itemBuilder: (context, index) {
                  final detail = _summary['details'][index];
                  return _buildPermissionTile(detail);
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(widget.languageService.isArabic ? 'إغلاق' : 'Close'),
        ),
        ElevatedButton(
          onPressed: _refreshPermissions,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2E7D32),
            foregroundColor: Colors.white,
          ),
          child: Text(
            widget.languageService.isArabic ? 'تحديث' : 'Refresh',
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildPermissionTile(Map<String, dynamic> detail) {
    final isGranted = detail['isGranted'] as bool;
    
    return ListTile(
      leading: Icon(
        detail['icon'] as IconData,
        color: isGranted ? Colors.green : Colors.red,
      ),
      title: Text(
        detail['title'] as String,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(detail['description'] as String),
      trailing: Icon(
        isGranted ? Icons.check_circle : Icons.cancel,
        color: isGranted ? Colors.green : Colors.red,
      ),
      onTap: isGranted ? null : () => _requestPermission(detail['permission']),
    );
  }

  Future<void> _requestPermission(Permission permission) async {
    await widget.manager.requestPermission(permission);
    if (mounted) {
      _refreshPermissions();
    }
  }

  Future<void> _refreshPermissions() async {
    final newSummary = await widget.manager.getPermissionsSummary(widget.languageService);
    if (mounted) {
      setState(() {
        _summary = newSummary;
      });
    }
  }
}
