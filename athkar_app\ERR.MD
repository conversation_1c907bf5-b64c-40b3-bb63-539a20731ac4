# Flutter Analyze Issues - Islamic Athkar App

## Current Issues Count: 12 (RESOLVED: 6 unnecessary_null_comparison issues, 19 use_build_context_synchronously issues)

### BuildContext Async Gap Issues (31)

1. ✅ `lib\screens\audio_settings_screen.dart:46:30` - FIXED - Added mounted check
2. ✅ `lib\screens\audio_settings_screen.dart:54:28` - FIXED - Added mounted check
3. ✅ `lib\screens\audio_settings_screen.dart:92:32` - FIXED - Added mounted check
4. ✅ `lib\screens\audio_settings_screen.dart:100:30` - FIXED - Added mounted check
5. ✅ `lib\screens\audio_settings_screen.dart:116:28` - FIXED - Added mounted check
6. ✅ `lib\screens\audio_settings_screen.dart:129:28` - FIXED - Added mounted check
7. ✅ `lib\screens\floating_window_screen.dart:101:30` - FIXED - Added mounted check
8. ✅ `lib\screens\floating_window_screen.dart:109:28` - FIXED - Added mounted check
9. ✅ `lib\screens\floating_window_screen.dart:148:28` - FIXED - Added mounted check
10. ✅ `lib\screens\floating_window_screen.dart:155:28` - FIXED - Added mounted check
11. ✅ `lib\screens\floating_window_screen.dart:173:28` - FIXED - Added mounted check
12. ✅ `lib\screens\floating_window_screen.dart:180:28` - FIXED - Added mounted check
13. ✅ `lib\screens\floating_window_screen.dart:203:28` - FIXED - Added mounted check
14. ✅ `lib\screens\floating_window_screen.dart:210:28` - FIXED - Added mounted check
15. ✅ `lib\screens\floating_window_screen.dart:228:28` - FIXED - Added mounted check
16. ✅ `lib\screens\floating_window_screen.dart:235:28` - FIXED - Added mounted check
17. `lib\screens\hadith_favorites_screen.dart:291:11` - Don't use 'BuildContext's across async gaps - use_build_context_synchronously
18. `lib\screens\hadith_favorites_screen.dart:336:31` - Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check - use_build_context_synchronously
19. `lib\screens\hadith_favorites_screen.dart:337:38` - Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check - use_build_context_synchronously
20. `lib\screens\language_settings_screen.dart:109:48` - Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check - use_build_context_synchronously
21. `lib\screens\language_settings_screen.dart:145:48` - Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check - use_build_context_synchronously
22. `lib\screens\language_settings_screen.dart:186:50` - Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check - use_build_context_synchronously
23. ✅ `lib\screens\notification_settings_screen.dart:113:28` - FIXED - Added mounted check
24. ✅ `lib\screens\notification_settings_screen.dart:121:28` - FIXED - Added mounted check
25. ✅ `lib\screens\notification_settings_screen.dart:195:28` - FIXED - Added mounted check
26. `lib\services\permissions_service.dart:254:9` - Don't use 'BuildContext's across async gaps - use_build_context_synchronously
27. `lib\services\permissions_service.dart:268:9` - Don't use 'BuildContext's across async gaps - use_build_context_synchronously
28. `lib\widgets\settings_tabs\hadith_settings_tab.dart:433:30` - Don't use 'BuildContext's across async gaps - use_build_context_synchronously
29. `lib\widgets\settings_tabs\hadith_settings_tab.dart:437:52` - Don't use 'BuildContext's across async gaps - use_build_context_synchronously
30. `lib\widgets\settings_tabs\hadith_settings_tab.dart:438:46` - Don't use 'BuildContext's across async gaps - use_build_context_synchronously
31. `lib\widgets\settings_tabs\quran_settings_tab.dart:191:44` - Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check - use_build_context_synchronously

### Unnecessary Null Comparison Issues (0) - RESOLVED ✅

1. ✅ `lib\services\realtime_sync_service.dart:142:21` - FIXED - Replaced with proper code block
2. ✅ `lib\services\realtime_sync_service.dart:164:23` - FIXED - Replaced with proper code block
3. ✅ `lib\services\realtime_sync_service.dart:182:21` - FIXED - Replaced with proper code block
4. ✅ `lib\services\realtime_sync_service.dart:205:80` - FIXED - Removed unnecessary null check
5. ✅ `lib\services\realtime_sync_service.dart:221:29` - FIXED - Replaced with proper code block
6. ✅ `lib\services\realtime_sync_service.dart:241:29` - FIXED - Replaced with proper code block
