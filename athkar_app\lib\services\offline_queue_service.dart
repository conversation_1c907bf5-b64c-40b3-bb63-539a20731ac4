import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'connectivity_service.dart';
import 'new_supabase_service.dart';

enum OperationType {
  create,
  update,
  delete,
}

class QueuedOperation {
  final String id;
  final OperationType type;
  final String table;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  QueuedOperation({
    required this.id,
    required this.type,
    required this.table,
    required this.data,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type.name,
    'table': table,
    'data': data,
    'timestamp': timestamp.toIso8601String(),
  };

  factory QueuedOperation.fromJson(Map<String, dynamic> json) => QueuedOperation(
    id: json['id'],
    type: OperationType.values.firstWhere((e) => e.name == json['type']),
    table: json['table'],
    data: Map<String, dynamic>.from(json['data']),
    timestamp: DateTime.parse(json['timestamp']),
  );
}

class OfflineQueueService {
  static const String _queueKey = 'offline_operation_queue';
  static final List<QueuedOperation> _queue = [];
  static bool _isProcessing = false;

  // Initialize the service
  static Future<void> initialize() async {
    await _loadQueue();
    
    // Listen to connectivity changes
    ConnectivityService.addListener(_onConnectivityChanged);
    
    // Process queue if connected
    if (ConnectivityService.isConnected) {
      _processQueue();
    }
  }

  // Add operation to queue
  static Future<void> queueOperation({
    required String id,
    required OperationType type,
    required String table,
    required Map<String, dynamic> data,
  }) async {
    final operation = QueuedOperation(
      id: id,
      type: type,
      table: table,
      data: data,
      timestamp: DateTime.now(),
    );

    _queue.add(operation);
    await _saveQueue();
    
    debugPrint('Queued ${type.name} operation for $table: $id');

    // Try to process immediately if connected
    if (ConnectivityService.isConnected) {
      _processQueue();
    }
  }

  // Process the queue when connectivity is restored
  static void _onConnectivityChanged() {
    if (ConnectivityService.isConnected && _queue.isNotEmpty) {
      debugPrint('Connectivity restored, processing offline queue...');
      _processQueue();
    }
  }

  // Process all queued operations
  static Future<void> _processQueue() async {
    if (_isProcessing || _queue.isEmpty || !ConnectivityService.isConnected) {
      return;
    }

    _isProcessing = true;
    debugPrint('Processing ${_queue.length} queued operations...');

    final operationsToProcess = List<QueuedOperation>.from(_queue);
    final processedOperations = <QueuedOperation>[];

    for (final operation in operationsToProcess) {
      try {
        final success = await _processOperation(operation);
        if (success) {
          processedOperations.add(operation);
          debugPrint('Successfully processed ${operation.type.name} for ${operation.table}');
        } else {
          debugPrint('Failed to process ${operation.type.name} for ${operation.table}');
          break; // Stop processing on first failure
        }
      } catch (e) {
        debugPrint('Error processing operation ${operation.id}: $e');
        break; // Stop processing on error
      }
    }

    // Remove successfully processed operations
    for (final operation in processedOperations) {
      _queue.remove(operation);
    }

    await _saveQueue();
    _isProcessing = false;

    if (processedOperations.isNotEmpty) {
      debugPrint('Processed ${processedOperations.length} operations successfully');
      
      // Trigger a full sync to ensure consistency
      NewSupabaseService.performFullSync();
    }
  }

  // Process a single operation
  static Future<bool> _processOperation(QueuedOperation operation) async {
    try {
      // This would typically make API calls to Supabase
      // For now, we'll just simulate success
      await Future.delayed(const Duration(milliseconds: 100));
      return true;
    } catch (e) {
      debugPrint('Error processing operation: $e');
      return false;
    }
  }

  // Load queue from persistent storage
  static Future<void> _loadQueue() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getString(_queueKey);
      
      if (queueJson != null) {
        final List<dynamic> queueList = jsonDecode(queueJson);
        _queue.clear();
        _queue.addAll(queueList.map((json) => QueuedOperation.fromJson(json)));
        debugPrint('Loaded ${_queue.length} operations from offline queue');
      }
    } catch (e) {
      debugPrint('Error loading offline queue: $e');
    }
  }

  // Save queue to persistent storage
  static Future<void> _saveQueue() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = jsonEncode(_queue.map((op) => op.toJson()).toList());
      await prefs.setString(_queueKey, queueJson);
    } catch (e) {
      debugPrint('Error saving offline queue: $e');
    }
  }

  // Get queue status
  static Map<String, dynamic> getQueueStatus() {
    return {
      'queueLength': _queue.length,
      'isProcessing': _isProcessing,
      'isConnected': ConnectivityService.isConnected,
      'oldestOperation': _queue.isNotEmpty 
          ? _queue.first.timestamp.toIso8601String() 
          : null,
    };
  }

  // Clear the queue (for testing or reset)
  static Future<void> clearQueue() async {
    _queue.clear();
    await _saveQueue();
    debugPrint('Offline queue cleared');
  }

  // Force process queue (for manual sync)
  static Future<void> forceProcessQueue() async {
    if (ConnectivityService.isConnected) {
      await _processQueue();
    } else {
      debugPrint('Cannot process queue: no internet connection');
    }
  }

  // Dispose resources
  static void dispose() {
    ConnectivityService.removeListener(_onConnectivityChanged);
  }
}
