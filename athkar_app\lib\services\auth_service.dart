import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_models.dart';
import 'new_supabase_service.dart';

class AuthService {
  static SupabaseClient? _supabase;
  static SharedPreferences? _prefs;
  static bool _isInitialized = false;

  // Initialize auth service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await NewSupabaseService.initialize();
      _supabase = NewSupabaseService.client;
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;
      debugPrint('AuthService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing AuthService: $e');
    }
  }

  // Get current user
  static User? get currentUser => _supabase?.auth.currentUser;
  
  // Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;

  // Get user profile
  static Future<UserProfile?> getUserProfile() async {
    if (!isAuthenticated) return null;

    try {
      final response = await _supabase!
          .from('user_profiles')
          .select()
          .eq('id', currentUser!.id)
          .single();

      return UserProfile.fromJson(response);
    } catch (e) {
      debugPrint('Error getting user profile: $e');
      return null;
    }
  }

  // Sign up with email and password
  static Future<AuthResult> signUp({
    required String email,
    required String password,
    required String fullName,
    String? phoneNumber,
  }) async {
    try {
      final response = await _supabase!.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
          'phone_number': phoneNumber,
        },
      );

      if (response.user != null) {
        // Create user profile
        await _createUserProfile(response.user!, fullName, phoneNumber);
        
        return AuthResult(
          success: true,
          user: response.user,
          message: 'Account created successfully. Please check your email for verification.',
        );
      } else {
        return AuthResult(
          success: false,
          message: 'Failed to create account',
        );
      }
    } catch (e) {
      debugPrint('Sign up error: $e');
      return AuthResult(
        success: false,
        message: _getErrorMessage(e),
      );
    }
  }

  // Sign in with email and password
  static Future<AuthResult> signIn({
    required String email,
    required String password,
    bool rememberMe = true,
  }) async {
    try {
      final response = await _supabase!.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        if (rememberMe) {
          await _prefs?.setBool('remember_me', true);
          await _prefs?.setString('user_email', email);
        }

        return AuthResult(
          success: true,
          user: response.user,
          message: 'Signed in successfully',
        );
      } else {
        return AuthResult(
          success: false,
          message: 'Invalid credentials',
        );
      }
    } catch (e) {
      debugPrint('Sign in error: $e');
      return AuthResult(
        success: false,
        message: _getErrorMessage(e),
      );
    }
  }

  // Sign in with Google
  static Future<AuthResult> signInWithGoogle() async {
    try {
      await _supabase!.auth.signInWithOAuth(
        OAuthProvider.google,
        redirectTo: 'io.supabase.athkarapp://login-callback/',
      );

      return AuthResult(
        success: true,
        message: 'Google sign-in initiated',
      );
    } catch (e) {
      debugPrint('Google sign in error: $e');
      return AuthResult(
        success: false,
        message: _getErrorMessage(e),
      );
    }
  }

  // Sign in with Apple
  static Future<AuthResult> signInWithApple() async {
    try {
      await _supabase!.auth.signInWithOAuth(
        OAuthProvider.apple,
        redirectTo: 'io.supabase.athkarapp://login-callback/',
      );

      return AuthResult(
        success: true,
        message: 'Apple sign-in initiated',
      );
    } catch (e) {
      debugPrint('Apple sign in error: $e');
      return AuthResult(
        success: false,
        message: _getErrorMessage(e),
      );
    }
  }

  // Reset password
  static Future<AuthResult> resetPassword(String email) async {
    try {
      await _supabase!.auth.resetPasswordForEmail(
        email,
        redirectTo: 'io.supabase.athkarapp://reset-password/',
      );

      return AuthResult(
        success: true,
        message: 'Password reset email sent. Please check your inbox.',
      );
    } catch (e) {
      debugPrint('Reset password error: $e');
      return AuthResult(
        success: false,
        message: _getErrorMessage(e),
      );
    }
  }

  // Update password
  static Future<AuthResult> updatePassword(String newPassword) async {
    try {
      final response = await _supabase!.auth.updateUser(
        UserAttributes(password: newPassword),
      );

      if (response.user != null) {
        return AuthResult(
          success: true,
          message: 'Password updated successfully',
        );
      } else {
        return AuthResult(
          success: false,
          message: 'Failed to update password',
        );
      }
    } catch (e) {
      debugPrint('Update password error: $e');
      return AuthResult(
        success: false,
        message: _getErrorMessage(e),
      );
    }
  }

  // Update user profile
  static Future<AuthResult> updateProfile(UserProfile profile) async {
    try {
      // Update auth user metadata
      await _supabase!.auth.updateUser(
        UserAttributes(
          data: {
            'full_name': profile.fullName,
            'phone_number': profile.phoneNumber,
          },
        ),
      );

      // Update profile in database
      await _supabase!
          .from('user_profiles')
          .update(profile.toJson())
          .eq('id', currentUser!.id);

      return AuthResult(
        success: true,
        message: 'Profile updated successfully',
      );
    } catch (e) {
      debugPrint('Update profile error: $e');
      return AuthResult(
        success: false,
        message: _getErrorMessage(e),
      );
    }
  }

  // Sign out
  static Future<AuthResult> signOut() async {
    try {
      await _supabase!.auth.signOut();
      await _prefs?.remove('remember_me');
      await _prefs?.remove('user_email');

      return AuthResult(
        success: true,
        message: 'Signed out successfully',
      );
    } catch (e) {
      debugPrint('Sign out error: $e');
      return AuthResult(
        success: false,
        message: _getErrorMessage(e),
      );
    }
  }

  // Delete account
  static Future<AuthResult> deleteAccount() async {
    try {
      if (!isAuthenticated) {
        return AuthResult(
          success: false,
          message: 'User not authenticated',
        );
      }

      // Delete user data from database
      await _supabase!
          .from('user_profiles')
          .delete()
          .eq('id', currentUser!.id);

      // Note: Supabase doesn't provide direct user deletion from client
      // This would typically be handled by a server function
      
      await signOut();

      return AuthResult(
        success: true,
        message: 'Account deletion initiated. Please contact support if needed.',
      );
    } catch (e) {
      debugPrint('Delete account error: $e');
      return AuthResult(
        success: false,
        message: _getErrorMessage(e),
      );
    }
  }

  // Create user profile in database
  static Future<void> _createUserProfile(
    User user,
    String fullName,
    String? phoneNumber,
  ) async {
    try {
      final profile = UserProfile(
        id: user.id,
        email: user.email!,
        fullName: fullName,
        phoneNumber: phoneNumber,
        avatarUrl: user.userMetadata?['avatar_url'],
        isEmailVerified: user.emailConfirmedAt != null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _supabase!
          .from('user_profiles')
          .insert(profile.toJson());
    } catch (e) {
      debugPrint('Error creating user profile: $e');
    }
  }

  // Get user-friendly error message
  static String _getErrorMessage(dynamic error) {
    final errorStr = error.toString().toLowerCase();
    
    if (errorStr.contains('invalid login credentials')) {
      return 'Invalid email or password';
    } else if (errorStr.contains('email not confirmed')) {
      return 'Please verify your email address';
    } else if (errorStr.contains('user already registered')) {
      return 'An account with this email already exists';
    } else if (errorStr.contains('password should be at least')) {
      return 'Password should be at least 6 characters';
    } else if (errorStr.contains('invalid email')) {
      return 'Please enter a valid email address';
    } else if (errorStr.contains('network')) {
      return 'Network error. Please check your connection';
    } else {
      return 'An error occurred. Please try again';
    }
  }

  // Check if user should be remembered
  static Future<bool> shouldRememberUser() async {
    return _prefs?.getBool('remember_me') ?? false;
  }

  // Get remembered email
  static Future<String?> getRememberedEmail() async {
    return _prefs?.getString('user_email');
  }

  // Listen to auth state changes
  static Stream<AuthState> get authStateChanges => _supabase!.auth.onAuthStateChange;
}
