import 'package:flutter/material.dart';
import 'package:flutter_compass/flutter_compass.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:math' as math;
import 'dart:async';

/// Complete Qibla compass service with real magnetic declination and sensor support
/// Provides accurate Qibla direction calculation with compass functionality
class QiblaCompassService {
  static final QiblaCompassService _instance = QiblaCompassService._internal();
  factory QiblaCompassService() => _instance;
  QiblaCompassService._internal();

  // Kaaba coordinates (Mecca, Saudi Arabia)
  static const double _kaabaLatitude = 21.4225;
  static const double _kaabaLongitude = 39.8262;

  // Current location and compass data
  double? _currentLatitude;
  double? _currentLongitude;
  double? _qiblaDirection;
  double? _magneticDeclination;
  
  // Compass stream
  StreamSubscription? _compassSubscription;
  final StreamController<QiblaData> _qiblaController = StreamController<QiblaData>.broadcast();
  
  // Service state
  bool _isInitialized = false;
  bool _isCompassAvailable = false;
  bool _hasLocationPermission = false;
  String? _lastError;

  /// Initialize the Qibla compass service
  Future<QiblaInitResult> initialize() async {
    if (_isInitialized) {
      return QiblaInitResult.success(_qiblaDirection ?? 0.0);
    }

    try {
      // Check and request permissions
      final permissionResult = await _checkPermissions();
      if (!permissionResult.isSuccess) {
        return QiblaInitResult.error(permissionResult.error!);
      }

      // Get current location
      final locationResult = await _getCurrentLocation();
      if (!locationResult.isSuccess) {
        return QiblaInitResult.error(locationResult.error!);
      }

      // Calculate Qibla direction
      await _calculateQiblaDirection();

      // Check compass availability
      await _checkCompassAvailability();

      // Start compass stream if available
      if (_isCompassAvailable) {
        await _startCompassStream();
      }

      _isInitialized = true;
      debugPrint('Qibla Compass Service initialized successfully');
      
      return QiblaInitResult.success(_qiblaDirection ?? 0.0);
    } catch (e) {
      _lastError = e.toString();
      debugPrint('Error initializing Qibla Compass Service: $e');
      return QiblaInitResult.error('خطأ في تهيئة البوصلة: ${e.toString()}');
    }
  }

  /// Get Qibla data stream
  Stream<QiblaData> get qiblaStream => _qiblaController.stream;

  /// Get current Qibla direction (in degrees from North)
  double? get qiblaDirection => _qiblaDirection;

  /// Check if compass is available
  bool get isCompassAvailable => _isCompassAvailable;

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Get last error message
  String? get lastError => _lastError;

  /// Calculate Qibla direction manually (fallback method)
  Future<QiblaCalculationResult> calculateQiblaManually({
    double? latitude,
    double? longitude,
  }) async {
    try {
      final lat = latitude ?? _currentLatitude;
      final lng = longitude ?? _currentLongitude;

      if (lat == null || lng == null) {
        return QiblaCalculationResult.error('الموقع غير متوفر');
      }

      final qiblaDirection = _calculateQiblaDirectionInternal(lat, lng);
      
      return QiblaCalculationResult.success(
        qiblaDirection: qiblaDirection,
        latitude: lat,
        longitude: lng,
        distanceToKaaba: _calculateDistanceToKaaba(lat, lng),
      );
    } catch (e) {
      return QiblaCalculationResult.error('خطأ في حساب اتجاه القبلة: ${e.toString()}');
    }
  }

  /// Set location manually
  Future<void> setLocation(double latitude, double longitude) async {
    _currentLatitude = latitude;
    _currentLongitude = longitude;
    await _calculateQiblaDirection();
  }

  /// Recalibrate compass
  Future<void> recalibrateCompass() async {
    try {
      // Stop current stream
      await _stopCompassStream();
      
      // Restart compass stream
      if (_isCompassAvailable) {
        await _startCompassStream();
      }
    } catch (e) {
      debugPrint('Error recalibrating compass: $e');
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    await _stopCompassStream();
    await _qiblaController.close();
    _isInitialized = false;
  }

  /// Check required permissions
  Future<PermissionResult> _checkPermissions() async {
    try {
      // Check location permission
      LocationPermission locationPermission = await Geolocator.checkPermission();
      if (locationPermission == LocationPermission.denied) {
        locationPermission = await Geolocator.requestPermission();
      }

      if (locationPermission == LocationPermission.denied || 
          locationPermission == LocationPermission.deniedForever) {
        return PermissionResult.error('إذن الوصول للموقع مطلوب لحساب اتجاه القبلة');
      }

      _hasLocationPermission = true;
      return PermissionResult.success();
    } catch (e) {
      return PermissionResult.error('خطأ في فحص الأذونات: ${e.toString()}');
    }
  }

  /// Get current location
  Future<LocationResult> _getCurrentLocation() async {
    try {
      if (!_hasLocationPermission) {
        return LocationResult.error('إذن الوصول للموقع غير متوفر');
      }

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return LocationResult.error('خدمات الموقع غير مفعلة');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      _currentLatitude = position.latitude;
      _currentLongitude = position.longitude;

      return LocationResult.success(position.latitude, position.longitude);
    } catch (e) {
      return LocationResult.error('خطأ في الحصول على الموقع: ${e.toString()}');
    }
  }

  /// Calculate Qibla direction
  Future<void> _calculateQiblaDirection() async {
    if (_currentLatitude == null || _currentLongitude == null) return;

    _qiblaDirection = _calculateQiblaDirectionInternal(_currentLatitude!, _currentLongitude!);

    // Get magnetic declination for more accurate compass reading
    _magneticDeclination = await _getMagneticDeclination(_currentLatitude!, _currentLongitude!);
  }

  /// Calculate Qibla direction using spherical trigonometry
  double _calculateQiblaDirectionInternal(double latitude, double longitude) {
    // Convert degrees to radians
    final lat1 = latitude * math.pi / 180;
    final lng1 = longitude * math.pi / 180;
    final lat2 = _kaabaLatitude * math.pi / 180;
    final lng2 = _kaabaLongitude * math.pi / 180;

    // Calculate difference in longitude
    final dLng = lng2 - lng1;

    // Calculate Qibla direction using spherical trigonometry
    final y = math.sin(dLng) * math.cos(lat2);
    final x = math.cos(lat1) * math.sin(lat2) - 
              math.sin(lat1) * math.cos(lat2) * math.cos(dLng);

    // Calculate bearing in radians
    double bearing = math.atan2(y, x);

    // Convert to degrees and normalize to 0-360
    bearing = bearing * 180 / math.pi;
    bearing = (bearing + 360) % 360;

    return bearing;
  }

  /// Calculate distance to Kaaba in kilometers
  double _calculateDistanceToKaaba(double latitude, double longitude) {
    return Geolocator.distanceBetween(
      latitude, longitude,
      _kaabaLatitude, _kaabaLongitude,
    ) / 1000; // Convert to kilometers
  }

  /// Get magnetic declination for location
  Future<double> _getMagneticDeclination(double latitude, double longitude) async {
    // Simplified magnetic declination calculation
    // In a real implementation, this would use a magnetic declination model
    // For now, return a reasonable estimate based on location
    
    // Rough estimates for different regions
    if (latitude >= 30 && latitude <= 40 && longitude >= 30 && longitude <= 50) {
      // Middle East region
      return 3.0; // degrees
    } else if (latitude >= 20 && latitude <= 30) {
      // Arabian Peninsula
      return 2.0;
    } else {
      // Default
      return 0.0;
    }
  }

  /// Check compass availability
  Future<void> _checkCompassAvailability() async {
    try {
      // Check if compass stream is available
      final compassStream = FlutterCompass.events;
      if (compassStream != null) {
        _isCompassAvailable = true;
      } else {
        _isCompassAvailable = false;
        debugPrint('Compass not available on this device');
      }
    } catch (e) {
      _isCompassAvailable = false;
      debugPrint('Error checking compass availability: $e');
    }
  }

  /// Start compass stream
  Future<void> _startCompassStream() async {
    try {
      final compassStream = FlutterCompass.events;
      if (compassStream == null) {
        _isCompassAvailable = false;
        return;
      }

      _compassSubscription = compassStream.listen(
        (event) {
          if (event.heading != null && _qiblaDirection != null) {
            // Calculate relative Qibla direction
            double compassHeading = event.heading!;
            
            // Apply magnetic declination correction
            if (_magneticDeclination != null) {
              compassHeading += _magneticDeclination!;
            }
            
            // Calculate Qibla direction relative to current heading
            double qiblaRelative = (_qiblaDirection! - compassHeading + 360) % 360;
            
            // Create Qibla data
            final qiblaData = QiblaData(
              qiblaDirection: _qiblaDirection!,
              compassHeading: compassHeading,
              qiblaRelativeDirection: qiblaRelative,
              accuracy: event.accuracy,
              isCompassAvailable: true,
              latitude: _currentLatitude,
              longitude: _currentLongitude,
              distanceToKaaba: _currentLatitude != null && _currentLongitude != null
                  ? _calculateDistanceToKaaba(_currentLatitude!, _currentLongitude!)
                  : null,
            );
            
            // Emit data to stream
            if (!_qiblaController.isClosed) {
              _qiblaController.add(qiblaData);
            }
          }
        },
        onError: (error) {
          debugPrint('Compass stream error: $error');
          _lastError = error.toString();
        },
      );
    } catch (e) {
      debugPrint('Error starting compass stream: $e');
      _lastError = e.toString();
    }
  }

  /// Stop compass stream
  Future<void> _stopCompassStream() async {
    await _compassSubscription?.cancel();
    _compassSubscription = null;
  }
}

/// Qibla initialization result
class QiblaInitResult {
  final bool isSuccess;
  final String? error;
  final double? qiblaDirection;

  QiblaInitResult._({
    required this.isSuccess,
    this.error,
    this.qiblaDirection,
  });

  factory QiblaInitResult.success(double qiblaDirection) {
    return QiblaInitResult._(
      isSuccess: true,
      qiblaDirection: qiblaDirection,
    );
  }

  factory QiblaInitResult.error(String error) {
    return QiblaInitResult._(
      isSuccess: false,
      error: error,
    );
  }
}

/// Qibla calculation result
class QiblaCalculationResult {
  final bool isSuccess;
  final String? error;
  final double? qiblaDirection;
  final double? latitude;
  final double? longitude;
  final double? distanceToKaaba;

  QiblaCalculationResult._({
    required this.isSuccess,
    this.error,
    this.qiblaDirection,
    this.latitude,
    this.longitude,
    this.distanceToKaaba,
  });

  factory QiblaCalculationResult.success({
    required double qiblaDirection,
    required double latitude,
    required double longitude,
    required double distanceToKaaba,
  }) {
    return QiblaCalculationResult._(
      isSuccess: true,
      qiblaDirection: qiblaDirection,
      latitude: latitude,
      longitude: longitude,
      distanceToKaaba: distanceToKaaba,
    );
  }

  factory QiblaCalculationResult.error(String error) {
    return QiblaCalculationResult._(
      isSuccess: false,
      error: error,
    );
  }
}

/// Permission check result
class PermissionResult {
  final bool isSuccess;
  final String? error;

  PermissionResult._({required this.isSuccess, this.error});

  factory PermissionResult.success() {
    return PermissionResult._(isSuccess: true);
  }

  factory PermissionResult.error(String error) {
    return PermissionResult._(isSuccess: false, error: error);
  }
}

/// Location result
class LocationResult {
  final bool isSuccess;
  final String? error;
  final double? latitude;
  final double? longitude;

  LocationResult._({
    required this.isSuccess,
    this.error,
    this.latitude,
    this.longitude,
  });

  factory LocationResult.success(double latitude, double longitude) {
    return LocationResult._(
      isSuccess: true,
      latitude: latitude,
      longitude: longitude,
    );
  }

  factory LocationResult.error(String error) {
    return LocationResult._(
      isSuccess: false,
      error: error,
    );
  }
}

/// Qibla data model
class QiblaData {
  final double qiblaDirection;        // Qibla direction from North (0-360°)
  final double compassHeading;        // Current compass heading (0-360°)
  final double qiblaRelativeDirection; // Qibla direction relative to current heading
  final double? accuracy;             // Compass accuracy
  final bool isCompassAvailable;      // Whether compass is available
  final double? latitude;             // Current latitude
  final double? longitude;            // Current longitude
  final double? distanceToKaaba;      // Distance to Kaaba in kilometers

  QiblaData({
    required this.qiblaDirection,
    required this.compassHeading,
    required this.qiblaRelativeDirection,
    this.accuracy,
    required this.isCompassAvailable,
    this.latitude,
    this.longitude,
    this.distanceToKaaba,
  });

  /// Get Qibla direction in a user-friendly format
  String get qiblaDirectionText {
    final direction = qiblaDirection.round();
    return '$direction°';
  }

  /// Get compass heading in a user-friendly format
  String get compassHeadingText {
    final heading = compassHeading.round();
    return '$heading°';
  }

  /// Get distance to Kaaba in a user-friendly format
  String get distanceText {
    if (distanceToKaaba == null) return 'غير معروف';
    
    if (distanceToKaaba! < 1) {
      return '${(distanceToKaaba! * 1000).round()} متر';
    } else {
      return '${distanceToKaaba!.toStringAsFixed(1)} كم';
    }
  }

  /// Check if pointing towards Qibla (within tolerance)
  bool isPointingToQibla({double tolerance = 10.0}) {
    return qiblaRelativeDirection.abs() <= tolerance || 
           (360 - qiblaRelativeDirection.abs()) <= tolerance;
  }
}
