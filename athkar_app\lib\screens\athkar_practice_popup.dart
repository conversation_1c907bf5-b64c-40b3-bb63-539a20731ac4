import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/athkar_provider.dart';
import '../models/athkar_models.dart';
import '../theme/app_theme.dart';
import '../services/floating_counter_service.dart';

class AthkarPracticePopup extends StatefulWidget {
  final AthkarRoutine routine;
  final List<AthkarStep>? steps;

  const AthkarPracticePopup({
    super.key,
    required this.routine,
    this.steps,
  });

  @override
  State<AthkarPracticePopup> createState() => _AthkarPracticePopupState();
}

class _AthkarPracticePopupState extends State<AthkarPracticePopup>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _pulseController;
  late AnimationController _progressController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;
  
  int _currentStepIndex = 0;
  List<AthkarStep> _steps = [];
  Map<String, int> _stepCounts = {};
  bool _isLoading = true;
  DateTime? _startTime;
  Color _routineColor = AppTheme.primaryGreen;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _startTime = DateTime.now();
    
    // Initialize animations
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut),
    );
    
    _pulseController.repeat(reverse: true);
    _loadRoutineSteps();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  Future<void> _loadRoutineSteps() async {
    try {
      if (widget.steps != null) {
        _steps = widget.steps!;
      } else {
        final provider = context.read<AthkarProvider>();
        _steps = await provider.getRoutineSteps(widget.routine.id);
      }

      // Load routine color
      if (widget.routine.colorHex != null) {
        try {
          _routineColor = Color(int.parse('FF${widget.routine.colorHex!}', radix: 16));
        } catch (e) {
          _routineColor = AppTheme.primaryGreen;
        }
      }

      // Initialize step counts
      for (var step in _steps) {
        _stepCounts[step.id] = 0;
      }

      setState(() {
        _isLoading = false;
      });
      
      _progressController.forward();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              _routineColor.withValues(alpha: 0.1),
              _routineColor.withValues(alpha: 0.05),
              Colors.white.withValues(alpha: 0.95),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              _buildProgressIndicator(),
              Expanded(
                child: _isLoading ? _buildLoadingState() : _buildContent(),
              ),
              _buildBottomControls(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _routineColor,
            _routineColor.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: _routineColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white, size: 28),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.routine.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (widget.routine.description != null)
                  Text(
                    widget.routine.description!,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '${_currentStepIndex + 1}/${_steps.length}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    if (_steps.isEmpty) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: AnimatedBuilder(
        animation: _progressAnimation,
        builder: (context, child) {
          return LinearProgressIndicator(
            value: (_currentStepIndex + _progressAnimation.value) / _steps.length,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(_routineColor),
            minHeight: 6,
          );
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: _routineColor),
          const SizedBox(height: 16),
          Text(
            'Loading athkar...',
            style: TextStyle(
              color: _routineColor,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_steps.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              MdiIcons.bookOpenPageVariant,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No athkar steps found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return PageView.builder(
      controller: _pageController,
      onPageChanged: (index) {
        setState(() {
          _currentStepIndex = index;
        });
        _progressController.reset();
        _progressController.forward();
      },
      itemCount: _steps.length,
      itemBuilder: (context, index) {
        return _buildStepCard(_steps[index]);
      },
    );
  }

  Widget _buildStepCard(AthkarStep step) {
    final currentCount = _stepCounts[step.id] ?? 0;
    final isCompleted = currentCount >= step.repetitions;
    
    return Container(
      margin: const EdgeInsets.all(20),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                _routineColor.withValues(alpha: 0.05),
              ],
            ),
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Arabic Text
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: _routineColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: _routineColor.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: Text(
                  step.arabicText,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                    height: 1.8,
                    fontFamily: 'Amiri',
                  ),
                  textAlign: TextAlign.center,
                  textDirection: TextDirection.rtl,
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Translation
              if (step.translation != null)
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    step.translation!,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[700],
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              
              const SizedBox(height: 30),
              
              // Counter
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: isCompleted ? 1.0 : _pulseAnimation.value,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: isCompleted
                              ? [Colors.green, Colors.green.shade700]
                              : [_routineColor, _routineColor.withValues(alpha: 0.7)],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: (isCompleted ? Colors.green : _routineColor)
                                .withValues(alpha: 0.4),
                            blurRadius: 15,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(60),
                          onTap: isCompleted ? null : () => _incrementCount(step.id),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '$currentCount',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 32,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  '/ ${step.repetitions}',
                                  style: const TextStyle(
                                    color: Colors.white70,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 20),
              
              if (isCompleted)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.check_circle, color: Colors.green, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Completed',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        children: [
          // Previous Button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _currentStepIndex > 0 ? _previousStep : null,
              icon: const Icon(Icons.arrow_back),
              label: const Text('Previous'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[100],
                foregroundColor: Colors.grey[700],
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Next/Finish Button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _nextStep,
              icon: Icon(_currentStepIndex < _steps.length - 1 
                  ? Icons.arrow_forward 
                  : Icons.check),
              label: Text(_currentStepIndex < _steps.length - 1 
                  ? 'Next' 
                  : 'Finish'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _routineColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _incrementCount(String stepId) {
    setState(() {
      _stepCounts[stepId] = (_stepCounts[stepId] ?? 0) + 1;
    });
    
    // Haptic feedback
    HapticFeedback.lightImpact();
    
    // Check if step is completed
    final step = _steps.firstWhere((s) => s.id == stepId);
    if (_stepCounts[stepId] == step.repetitions) {
      HapticFeedback.mediumImpact();
      _pulseController.stop();
    }
  }

  void _previousStep() {
    if (_currentStepIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextStep() {
    if (_currentStepIndex < _steps.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _finishPractice();
    }
  }

  void _finishPractice() {
    // Save practice session
    final provider = context.read<AthkarProvider>();
    final duration = DateTime.now().difference(_startTime!);
    
    provider.recordPracticeSession(
      routineId: widget.routine.id,
      duration: duration,
      completedSteps: _stepCounts.values.reduce((a, b) => a + b),
    );
    
    Navigator.of(context).pop();
    
    // Show completion message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Athkar practice completed! May Allah accept it.'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }
}
