// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recommendation_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AthkarRecommendation _$AthkarRecommendationFromJson(
  Map<String, dynamic> json,
) => AthkarRecommendation(
  athkarId: json['athkarId'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  score: (json['score'] as num).toDouble(),
  reason: json['reason'] as String,
  type: $enumDecode(_$RecommendationTypeEnumMap, json['type']),
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$AthkarRecommendationToJson(
  AthkarRecommendation instance,
) => <String, dynamic>{
  'athkarId': instance.athkarId,
  'title': instance.title,
  'description': instance.description,
  'score': instance.score,
  'reason': instance.reason,
  'type': _$RecommendationTypeEnumMap[instance.type]!,
  'metadata': instance.metadata,
  'createdAt': instance.createdAt.toIso8601String(),
};

const _$RecommendationTypeEnumMap = {
  RecommendationType.personalized: 'personalized',
  RecommendationType.trending: 'trending',
  RecommendationType.similar: 'similar',
  RecommendationType.contextual: 'contextual',
};

ScheduleRecommendation _$ScheduleRecommendationFromJson(
  Map<String, dynamic> json,
) => ScheduleRecommendation(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  recommendedTime: $enumDecode(_$TimeOfDayEnumMap, json['recommendedTime']),
  duration: Duration(microseconds: (json['duration'] as num).toInt()),
  confidence: (json['confidence'] as num).toDouble(),
  reason: json['reason'] as String,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$ScheduleRecommendationToJson(
  ScheduleRecommendation instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'recommendedTime': _$TimeOfDayEnumMap[instance.recommendedTime]!,
  'duration': instance.duration.inMicroseconds,
  'confidence': instance.confidence,
  'reason': instance.reason,
  'metadata': instance.metadata,
};

const _$TimeOfDayEnumMap = {
  TimeOfDay.morning: 'morning',
  TimeOfDay.afternoon: 'afternoon',
  TimeOfDay.evening: 'evening',
  TimeOfDay.night: 'night',
};

ContentRecommendation _$ContentRecommendationFromJson(
  Map<String, dynamic> json,
) => ContentRecommendation(
  id: json['id'] as String,
  type: json['type'] as String,
  title: json['title'] as String,
  content: json['content'] as String,
  translation: json['translation'] as String?,
  transliteration: json['transliteration'] as String?,
  relevanceScore: (json['relevanceScore'] as num).toDouble(),
  reason: json['reason'] as String,
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$ContentRecommendationToJson(
  ContentRecommendation instance,
) => <String, dynamic>{
  'id': instance.id,
  'type': instance.type,
  'title': instance.title,
  'content': instance.content,
  'translation': instance.translation,
  'transliteration': instance.transliteration,
  'relevanceScore': instance.relevanceScore,
  'reason': instance.reason,
  'metadata': instance.metadata,
};

UserInteraction _$UserInteractionFromJson(Map<String, dynamic> json) =>
    UserInteraction(
      id: json['id'] as String,
      userId: json['userId'] as String,
      athkarId: json['athkarId'] as String,
      type: $enumDecode(_$InteractionTypeEnumMap, json['type']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$UserInteractionToJson(UserInteraction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'athkarId': instance.athkarId,
      'type': _$InteractionTypeEnumMap[instance.type]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$InteractionTypeEnumMap = {
  InteractionType.view: 'view',
  InteractionType.practice: 'practice',
  InteractionType.complete: 'complete',
  InteractionType.favorite: 'favorite',
  InteractionType.share: 'share',
  InteractionType.skip: 'skip',
};

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
  userId: json['userId'] as String,
  preferences: (json['preferences'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(k, (e as num).toDouble()),
  ),
  demographics: json['demographics'] as Map<String, dynamic>,
  behaviorPatterns: json['behaviorPatterns'] as Map<String, dynamic>,
  lastUpdated: json['lastUpdated'] == null
      ? null
      : DateTime.parse(json['lastUpdated'] as String),
);

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'preferences': instance.preferences,
      'demographics': instance.demographics,
      'behaviorPatterns': instance.behaviorPatterns,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

UserPatterns _$UserPatternsFromJson(Map<String, dynamic> json) => UserPatterns(
  morningActivity: (json['morningActivity'] as num).toDouble(),
  afternoonActivity: (json['afternoonActivity'] as num).toDouble(),
  eveningActivity: (json['eveningActivity'] as num).toDouble(),
  nightActivity: (json['nightActivity'] as num).toDouble(),
  categoryPreferences:
      (json['categoryPreferences'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ) ??
      const {},
  difficultyPreferences:
      (json['difficultyPreferences'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ) ??
      const {},
  averageSessionDuration:
      (json['averageSessionDuration'] as num?)?.toDouble() ?? 0.0,
);

Map<String, dynamic> _$UserPatternsToJson(UserPatterns instance) =>
    <String, dynamic>{
      'morningActivity': instance.morningActivity,
      'afternoonActivity': instance.afternoonActivity,
      'eveningActivity': instance.eveningActivity,
      'nightActivity': instance.nightActivity,
      'categoryPreferences': instance.categoryPreferences,
      'difficultyPreferences': instance.difficultyPreferences,
      'averageSessionDuration': instance.averageSessionDuration,
    };

RecommendationFeedback _$RecommendationFeedbackFromJson(
  Map<String, dynamic> json,
) => RecommendationFeedback(
  id: json['id'] as String,
  userId: json['userId'] as String,
  recommendationId: json['recommendationId'] as String,
  athkarId: json['athkarId'] as String,
  wasHelpful: json['wasHelpful'] as bool,
  feedback: json['feedback'] as String?,
  timestamp: DateTime.parse(json['timestamp'] as String),
);

Map<String, dynamic> _$RecommendationFeedbackToJson(
  RecommendationFeedback instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'recommendationId': instance.recommendationId,
  'athkarId': instance.athkarId,
  'wasHelpful': instance.wasHelpful,
  'feedback': instance.feedback,
  'timestamp': instance.timestamp.toIso8601String(),
};

PersonalizationSettings _$PersonalizationSettingsFromJson(
  Map<String, dynamic> json,
) => PersonalizationSettings(
  userId: json['userId'] as String,
  enablePersonalizedRecommendations:
      json['enablePersonalizedRecommendations'] as bool? ?? true,
  enableTrendingRecommendations:
      json['enableTrendingRecommendations'] as bool? ?? true,
  enableContextualRecommendations:
      json['enableContextualRecommendations'] as bool? ?? true,
  preferredCategories:
      (json['preferredCategories'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  excludedCategories:
      (json['excludedCategories'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  difficultyPreference:
      (json['difficultyPreference'] as num?)?.toDouble() ?? 0.5,
  preferredSessionDuration: json['preferredSessionDuration'] == null
      ? const Duration(minutes: 10)
      : Duration(
          microseconds: (json['preferredSessionDuration'] as num).toInt(),
        ),
  preferredTimes:
      (json['preferredTimes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$TimeOfDayEnumMap, e))
          .toList() ??
      const [],
);

Map<String, dynamic> _$PersonalizationSettingsToJson(
  PersonalizationSettings instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'enablePersonalizedRecommendations':
      instance.enablePersonalizedRecommendations,
  'enableTrendingRecommendations': instance.enableTrendingRecommendations,
  'enableContextualRecommendations': instance.enableContextualRecommendations,
  'preferredCategories': instance.preferredCategories,
  'excludedCategories': instance.excludedCategories,
  'difficultyPreference': instance.difficultyPreference,
  'preferredSessionDuration': instance.preferredSessionDuration.inMicroseconds,
  'preferredTimes': instance.preferredTimes
      .map((e) => _$TimeOfDayEnumMap[e]!)
      .toList(),
};

RecommendationMetrics _$RecommendationMetricsFromJson(
  Map<String, dynamic> json,
) => RecommendationMetrics(
  userId: json['userId'] as String,
  totalRecommendations: (json['totalRecommendations'] as num).toInt(),
  acceptedRecommendations: (json['acceptedRecommendations'] as num).toInt(),
  rejectedRecommendations: (json['rejectedRecommendations'] as num).toInt(),
  acceptanceRate: (json['acceptanceRate'] as num).toDouble(),
  recommendationsByType: (json['recommendationsByType'] as Map<String, dynamic>)
      .map(
        (k, e) => MapEntry(
          $enumDecode(_$RecommendationTypeEnumMap, k),
          (e as num).toInt(),
        ),
      ),
  categoryAcceptanceRates:
      (json['categoryAcceptanceRates'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
  lastUpdated: json['lastUpdated'] == null
      ? null
      : DateTime.parse(json['lastUpdated'] as String),
);

Map<String, dynamic> _$RecommendationMetricsToJson(
  RecommendationMetrics instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'totalRecommendations': instance.totalRecommendations,
  'acceptedRecommendations': instance.acceptedRecommendations,
  'rejectedRecommendations': instance.rejectedRecommendations,
  'acceptanceRate': instance.acceptanceRate,
  'recommendationsByType': instance.recommendationsByType.map(
    (k, e) => MapEntry(_$RecommendationTypeEnumMap[k]!, e),
  ),
  'categoryAcceptanceRates': instance.categoryAcceptanceRates,
  'lastUpdated': instance.lastUpdated.toIso8601String(),
};

SmartNotification _$SmartNotificationFromJson(Map<String, dynamic> json) =>
    SmartNotification(
      id: json['id'] as String,
      userId: json['userId'] as String,
      title: json['title'] as String,
      body: json['body'] as String,
      athkarId: json['athkarId'] as String?,
      optimalTime: $enumDecode(_$TimeOfDayEnumMap, json['optimalTime']),
      confidence: (json['confidence'] as num).toDouble(),
      payload: json['payload'] as Map<String, dynamic>? ?? const {},
      scheduledFor: DateTime.parse(json['scheduledFor'] as String),
      isSent: json['isSent'] as bool? ?? false,
    );

Map<String, dynamic> _$SmartNotificationToJson(SmartNotification instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'title': instance.title,
      'body': instance.body,
      'athkarId': instance.athkarId,
      'optimalTime': _$TimeOfDayEnumMap[instance.optimalTime]!,
      'confidence': instance.confidence,
      'payload': instance.payload,
      'scheduledFor': instance.scheduledFor.toIso8601String(),
      'isSent': instance.isSent,
    };

LearningModel _$LearningModelFromJson(Map<String, dynamic> json) =>
    LearningModel(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      parameters: json['parameters'] as Map<String, dynamic>,
      accuracy: (json['accuracy'] as num).toDouble(),
      trainedAt: DateTime.parse(json['trainedAt'] as String),
      trainingDataSize: (json['trainingDataSize'] as num).toInt(),
    );

Map<String, dynamic> _$LearningModelToJson(LearningModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'parameters': instance.parameters,
      'accuracy': instance.accuracy,
      'trainedAt': instance.trainedAt.toIso8601String(),
      'trainingDataSize': instance.trainingDataSize,
    };

ContextualData _$ContextualDataFromJson(Map<String, dynamic> json) =>
    ContextualData(
      timestamp: DateTime.parse(json['timestamp'] as String),
      timeOfDay: $enumDecode(_$TimeOfDayEnumMap, json['timeOfDay']),
      dayOfWeek: (json['dayOfWeek'] as num).toInt(),
      isRamadan: json['isRamadan'] as bool,
      isJumma: json['isJumma'] as bool,
      location: json['location'] as String?,
      weather: json['weather'] as String?,
      mood: $enumDecodeNullable(_$UserMoodEnumMap, json['mood']),
      state: $enumDecodeNullable(_$UserStateEnumMap, json['state']),
      additionalContext:
          json['additionalContext'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$ContextualDataToJson(ContextualData instance) =>
    <String, dynamic>{
      'timestamp': instance.timestamp.toIso8601String(),
      'timeOfDay': _$TimeOfDayEnumMap[instance.timeOfDay]!,
      'dayOfWeek': instance.dayOfWeek,
      'isRamadan': instance.isRamadan,
      'isJumma': instance.isJumma,
      'location': instance.location,
      'weather': instance.weather,
      'mood': _$UserMoodEnumMap[instance.mood],
      'state': _$UserStateEnumMap[instance.state],
      'additionalContext': instance.additionalContext,
    };

const _$UserMoodEnumMap = {
  UserMood.stressed: 'stressed',
  UserMood.grateful: 'grateful',
  UserMood.seekingGuidance: 'seekingGuidance',
  UserMood.repentant: 'repentant',
  UserMood.peaceful: 'peaceful',
  UserMood.anxious: 'anxious',
  UserMood.joyful: 'joyful',
};

const _$UserStateEnumMap = {
  UserState.traveling: 'traveling',
  UserState.sick: 'sick',
  UserState.studying: 'studying',
  UserState.working: 'working',
  UserState.praying: 'praying',
  UserState.fasting: 'fasting',
};

RecommendationExperiment _$RecommendationExperimentFromJson(
  Map<String, dynamic> json,
) => RecommendationExperiment(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  algorithmA: json['algorithmA'] as String,
  algorithmB: json['algorithmB'] as String,
  trafficSplit: (json['trafficSplit'] as num).toDouble(),
  startDate: DateTime.parse(json['startDate'] as String),
  endDate: json['endDate'] == null
      ? null
      : DateTime.parse(json['endDate'] as String),
  metrics: json['metrics'] as Map<String, dynamic>? ?? const {},
  isActive: json['isActive'] as bool? ?? true,
);

Map<String, dynamic> _$RecommendationExperimentToJson(
  RecommendationExperiment instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'algorithmA': instance.algorithmA,
  'algorithmB': instance.algorithmB,
  'trafficSplit': instance.trafficSplit,
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate?.toIso8601String(),
  'metrics': instance.metrics,
  'isActive': instance.isActive,
};
