import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quran_khatma_provider.dart';
import '../services/language_service.dart';
import '../theme/app_theme.dart';
import '../models/quran_khatma_models.dart';
import 'create_khatma_screen.dart';

class QuranKhatmaScreen extends StatefulWidget {
  const QuranKhatmaScreen({super.key});

  @override
  State<QuranKhatmaScreen> createState() => _QuranKhatmaScreenState();
}

class _QuranKhatmaScreenState extends State<QuranKhatmaScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final khatmaProvider = Provider.of<QuranKhatmaProvider>(context, listen: false);
      if (!khatmaProvider.isLoaded) {
        khatmaProvider.initialize();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(languageService.isArabic ? 'ختم القرآن' : 'Quran Khatma'),
          backgroundColor: AppTheme.primaryGreen,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: Icon(languageService.backIcon),
            onPressed: () => Navigator.pop(context),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _navigateToCreateKhatma(),
              tooltip: languageService.isArabic ? 'ختمة جديدة' : 'New Khatma',
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: [
              Tab(
                text: languageService.isArabic ? 'النشطة' : 'Active',
                icon: const Icon(Icons.play_circle_outline),
              ),
              Tab(
                text: languageService.isArabic ? 'المكتملة' : 'Completed',
                icon: const Icon(Icons.check_circle_outline),
              ),
              Tab(
                text: languageService.isArabic ? 'الإحصائيات' : 'Statistics',
                icon: const Icon(Icons.analytics_outlined),
              ),
            ],
          ),
        ),
        body: Consumer<QuranKhatmaProvider>(
          builder: (context, khatmaProvider, child) {
            if (khatmaProvider.isLoading) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(color: AppTheme.primaryGreen),
                    SizedBox(height: 16),
                    Text('جاري تحميل ختمات القرآن...'),
                  ],
                ),
              );
            }

            if (khatmaProvider.error != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      languageService.isArabic ? 'خطأ في تحميل البيانات' : 'Error loading data',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      khatmaProvider.error!,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => khatmaProvider.initialize(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryGreen,
                        foregroundColor: Colors.white,
                      ),
                      child: Text(languageService.isArabic ? 'إعادة المحاولة' : 'Retry'),
                    ),
                  ],
                ),
              );
            }

            return TabBarView(
              controller: _tabController,
              children: [
                _buildActiveKhatmasTab(khatmaProvider, languageService),
                _buildCompletedKhatmasTab(khatmaProvider, languageService),
                _buildStatisticsTab(khatmaProvider, languageService),
              ],
            );
          },
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _navigateToCreateKhatma,
          backgroundColor: AppTheme.primaryGreen,
          tooltip: languageService.isArabic ? 'ختمة جديدة' : 'New Khatma',
          child: const Icon(Icons.add, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildActiveKhatmasTab(QuranKhatmaProvider provider, LanguageService languageService) {
    final activeKhatmas = provider.khatmas.where((k) => !k.isCompleted).toList();

    if (activeKhatmas.isEmpty) {
      return _buildEmptyState(
        icon: Icons.book_outlined,
        title: languageService.isArabic ? 'لا توجد ختمات نشطة' : 'No Active Khatmas',
        subtitle: languageService.isArabic 
            ? 'ابدأ ختمة جديدة لتتبع تقدمك في قراءة القرآن'
            : 'Start a new Khatma to track your Quran reading progress',
        actionText: languageService.isArabic ? 'ختمة جديدة' : 'New Khatma',
        onAction: _navigateToCreateKhatma,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: activeKhatmas.length,
      itemBuilder: (context, index) {
        final khatma = activeKhatmas[index];
        return _buildKhatmaCard(khatma, languageService, isActive: true);
      },
    );
  }

  Widget _buildCompletedKhatmasTab(QuranKhatmaProvider provider, LanguageService languageService) {
    final completedKhatmas = provider.khatmas.where((k) => k.isCompleted).toList();

    if (completedKhatmas.isEmpty) {
      return _buildEmptyState(
        icon: Icons.check_circle_outline,
        title: languageService.isArabic ? 'لا توجد ختمات مكتملة' : 'No Completed Khatmas',
        subtitle: languageService.isArabic 
            ? 'أكمل ختمة لترى إنجازاتك هنا'
            : 'Complete a Khatma to see your achievements here',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: completedKhatmas.length,
      itemBuilder: (context, index) {
        final khatma = completedKhatmas[index];
        return _buildKhatmaCard(khatma, languageService, isActive: false);
      },
    );
  }

  Widget _buildStatisticsTab(QuranKhatmaProvider provider, LanguageService languageService) {
    final stats = provider.getKhatmaStatistics();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageService.isArabic ? 'إحصائيات ختم القرآن' : 'Khatma Statistics',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 24),
          
          // Statistics Cards
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: languageService.isArabic ? 'إجمالي الختمات' : 'Total Khatmas',
                  value: '${stats['total_khatmas']}',
                  icon: Icons.book,
                  color: AppTheme.primaryGreen,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  title: languageService.isArabic ? 'الختمات المكتملة' : 'Completed',
                  value: '${stats['completed_khatmas']}',
                  icon: Icons.check_circle,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: languageService.isArabic ? 'الختمات النشطة' : 'Active',
                  value: '${stats['active_khatmas']}',
                  icon: Icons.play_circle,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  title: languageService.isArabic ? 'أيام القراءة' : 'Reading Days',
                  value: '${stats['total_days_read']}',
                  icon: Icons.calendar_today,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Progress Chart
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    languageService.isArabic ? 'متوسط التقدم' : 'Average Progress',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  LinearProgressIndicator(
                    value: stats['average_progress'],
                    backgroundColor: Colors.grey[300],
                    valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
                    minHeight: 8,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${(stats['average_progress'] * 100).toStringAsFixed(1)}%',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.primaryGreen,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKhatmaCard(QuranKhatma khatma, LanguageService languageService, {required bool isActive}) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      elevation: 2,
      child: InkWell(
        onTap: () => _navigateToKhatmaDetail(khatma),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          languageService.isArabic ? khatma.arabicName : khatma.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getKhatmaTypeText(khatma.type, languageService),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isActive)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        languageService.isArabic ? 'نشطة' : 'Active',
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppTheme.primaryGreen,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )
                  else
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        languageService.isArabic ? 'مكتملة' : 'Completed',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.green,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Progress
              Row(
                children: [
                  Expanded(
                    child: LinearProgressIndicator(
                      value: khatma.progress,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isActive ? AppTheme.primaryGreen : Colors.green,
                      ),
                      minHeight: 6,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '${(khatma.progress * 100).toStringAsFixed(0)}%',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isActive ? AppTheme.primaryGreen : Colors.green,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Dates and days
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${khatma.totalDays} ${languageService.isArabic ? 'يوم' : 'days'}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  if (khatma.isCompleted && khatma.completedDate != null)
                    Text(
                      '${languageService.isArabic ? 'اكتملت في' : 'Completed on'} ${_formatDate(khatma.completedDate!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    )
                  else
                    Text(
                      '${languageService.isArabic ? 'الهدف' : 'Target'}: ${_formatDate(khatma.targetEndDate)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
    String? actionText,
    VoidCallback? onAction,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey[600],
                height: 1.4,
              ),
            ),
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onAction,
                icon: const Icon(Icons.add),
                label: Text(actionText),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryGreen,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getKhatmaTypeText(KhatmaType type, LanguageService languageService) {
    switch (type) {
      case KhatmaType.thirtyDays:
        return languageService.isArabic ? 'ختمة 30 يوم' : '30-Day Khatma';
      case KhatmaType.sixtyDays:
        return languageService.isArabic ? 'ختمة 60 يوم' : '60-Day Khatma';
      case KhatmaType.ramadan:
        return languageService.isArabic ? 'ختمة رمضان' : 'Ramadan Khatma';
      case KhatmaType.weekly:
        return languageService.isArabic ? 'ختمة أسبوعية' : 'Weekly Khatma';
      case KhatmaType.custom:
        return languageService.isArabic ? 'ختمة مخصصة' : 'Custom Khatma';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _navigateToCreateKhatma() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateKhatmaScreen(),
      ),
    );
  }

  void _navigateToKhatmaDetail(QuranKhatma khatma) {
    // Show khatma details in a dialog for now
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(khatma.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Progress: ${(khatma.progress * 100).toStringAsFixed(1)}%'),
            const SizedBox(height: 8),
            Text('Days: ${khatma.totalDays}'),
            const SizedBox(height: 8),
            Text('Type: ${_getKhatmaTypeText(khatma.type, Provider.of<LanguageService>(context, listen: false))}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
