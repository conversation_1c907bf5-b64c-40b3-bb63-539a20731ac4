import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/language_service.dart';
import '../../theme/app_theme.dart';

class QuranSettingsTab extends StatefulWidget {
  const QuranSettingsTab({super.key});

  @override
  State<QuranSettingsTab> createState() => _QuranSettingsTabState();
}

class _QuranSettingsTabState extends State<QuranSettingsTab> {
  String _selectedFont = 'uthmani';
  double _arabicFontSize = 18.0;
  double _translationFontSize = 14.0;
  bool _showTranslation = true;
  bool _showTransliteration = false;
  String _selectedTranslation = 'sahih_international';
  bool _enableTajweed = true;
  bool _enableWordByWord = false;

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Font Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات الخط' : 'Font Settings',
            Icons.text_fields,
          ),
          const SizedBox(height: 12),
          
          Card(
            elevation: 2,
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.font_download, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'خط المصحف' : 'Quran Font'),
                  subtitle: Text(_getFontName(_selectedFont, languageService)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showFontDialog(context, languageService),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.format_size, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'حجم الخط العربي' : 'Arabic Font Size'),
                  subtitle: Slider(
                    value: _arabicFontSize,
                    min: 12.0,
                    max: 32.0,
                    divisions: 20,
                    activeColor: AppTheme.primaryGreen,
                    label: _arabicFontSize.round().toString(),
                    onChanged: (value) {
                      setState(() {
                        _arabicFontSize = value;
                      });
                    },
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.translate, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'حجم خط الترجمة' : 'Translation Font Size'),
                  subtitle: Slider(
                    value: _translationFontSize,
                    min: 10.0,
                    max: 24.0,
                    divisions: 14,
                    activeColor: AppTheme.primaryGreen,
                    label: _translationFontSize.round().toString(),
                    onChanged: (value) {
                      setState(() {
                        _translationFontSize = value;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Display Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات العرض' : 'Display Settings',
            Icons.visibility,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.translate, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'إظهار الترجمة' : 'Show Translation'),
                  subtitle: Text(languageService.isArabic ? 'عرض ترجمة معاني القرآن' : 'Display Quran translation'),
                  value: _showTranslation,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _showTranslation = value;
                    });
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.language, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'الترجمة المختارة' : 'Selected Translation'),
                  subtitle: Text(_getTranslationName(_selectedTranslation, languageService)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: _showTranslation ? () => _showTranslationDialog(context, languageService) : null,
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.record_voice_over, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'إظهار النطق' : 'Show Transliteration'),
                  subtitle: Text(languageService.isArabic ? 'عرض النطق بالأحرف اللاتينية' : 'Display phonetic pronunciation'),
                  value: _showTransliteration,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _showTransliteration = value;
                    });
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.colorize, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'تفعيل التجويد' : 'Enable Tajweed'),
                  subtitle: Text(languageService.isArabic ? 'إظهار ألوان أحكام التجويد' : 'Show Tajweed color coding'),
                  value: _enableTajweed,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _enableTajweed = value;
                    });
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.text_snippet, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'الترجمة كلمة بكلمة' : 'Word by Word Translation'),
                  subtitle: Text(languageService.isArabic ? 'عرض ترجمة كل كلمة' : 'Show individual word meanings'),
                  value: _enableWordByWord,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _enableWordByWord = value;
                    });
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Reading Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات القراءة' : 'Reading Settings',
            Icons.menu_book,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.bookmark, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'حفظ موضع القراءة' : 'Save Reading Position'),
                  subtitle: Text(languageService.isArabic ? 'حفظ آخر آية تم قراءتها' : 'Remember last read verse'),
                  value: true,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final messenger = ScaffoldMessenger.of(context);

                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('save_reading_position', value);

                    if (mounted) {
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text(value ? 'Reading position saving enabled' : 'Reading position saving disabled'),
                          backgroundColor: AppTheme.primaryGreen,
                        ),
                      );
                      setState(() {});
                    }
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.auto_stories, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'التمرير التلقائي' : 'Auto Scroll'),
                  subtitle: Text(languageService.isArabic ? 'تمرير تلقائي أثناء القراءة' : 'Automatic scrolling while reading'),
                  value: false,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('auto_scroll_enabled', value);
                    setState(() {});
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.nights_stay, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'الوضع الليلي للقراءة' : 'Night Reading Mode'),
                  subtitle: Text(languageService.isArabic ? 'خلفية داكنة للقراءة الليلية' : 'Dark background for night reading'),
                  value: false,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('night_reading_mode', value);
                    setState(() {});
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Audio Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات الصوت' : 'Audio Settings',
            Icons.volume_up,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.person, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'القارئ المفضل' : 'Preferred Reciter'),
                  subtitle: Text(languageService.isArabic ? 'عبد الباسط عبد الصمد' : 'Abdul Basit Abdul Samad'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showReciterDialog(context, languageService),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.repeat, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'التكرار التلقائي' : 'Auto Repeat'),
                  subtitle: Text(languageService.isArabic ? 'تكرار الآية تلقائياً' : 'Automatically repeat verses'),
                  value: false,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('auto_repeat_enabled', value);
                    setState(() {});
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.download, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'التحميل التلقائي' : 'Auto Download'),
                  subtitle: Text(languageService.isArabic ? 'تحميل التلاوة تلقائياً' : 'Automatically download recitations'),
                  value: false,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('auto_download_enabled', value);
                    setState(() {});
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: AppTheme.primaryGreen, size: 24),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
      ],
    );
  }

  String _getFontName(String font, LanguageService languageService) {
    switch (font) {
      case 'uthmani':
        return languageService.isArabic ? 'الخط العثماني' : 'Uthmani Script';
      case 'naskh':
        return languageService.isArabic ? 'خط النسخ' : 'Naskh Script';
      case 'kufi':
        return languageService.isArabic ? 'الخط الكوفي' : 'Kufi Script';
      default:
        return languageService.isArabic ? 'الخط العثماني' : 'Uthmani Script';
    }
  }

  String _getTranslationName(String translation, LanguageService languageService) {
    switch (translation) {
      case 'sahih_international':
        return languageService.isArabic ? 'صحيح إنترناشيونال' : 'Sahih International';
      case 'pickthall':
        return languageService.isArabic ? 'بيكثال' : 'Pickthall';
      case 'yusuf_ali':
        return languageService.isArabic ? 'يوسف علي' : 'Yusuf Ali';
      case 'shakir':
        return languageService.isArabic ? 'شاكر' : 'Shakir';
      default:
        return languageService.isArabic ? 'صحيح إنترناشيونال' : 'Sahih International';
    }
  }

  void _showFontDialog(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'اختر خط المصحف' : 'Choose Quran Font'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildFontOption('uthmani', languageService),
            _buildFontOption('naskh', languageService),
            _buildFontOption('kufi', languageService),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildFontOption(String font, LanguageService languageService) {
    return RadioListTile<String>(
      title: Text(_getFontName(font, languageService)),
      value: font,
      groupValue: _selectedFont,
      activeColor: AppTheme.primaryGreen,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedFont = value;
          });
          Navigator.pop(context);
        }
      },
    );
  }

  void _showTranslationDialog(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'اختر الترجمة' : 'Choose Translation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTranslationOption('sahih_international', languageService),
            _buildTranslationOption('pickthall', languageService),
            _buildTranslationOption('yusuf_ali', languageService),
            _buildTranslationOption('shakir', languageService),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildTranslationOption(String translation, LanguageService languageService) {
    return RadioListTile<String>(
      title: Text(_getTranslationName(translation, languageService)),
      value: translation,
      groupValue: _selectedTranslation,
      activeColor: AppTheme.primaryGreen,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedTranslation = value;
          });
          Navigator.pop(context);
        }
      },
    );
  }

  void _showReciterDialog(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'اختر القارئ' : 'Choose Reciter'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.person),
              title: Text(languageService.isArabic ? 'عبد الباسط عبد الصمد' : 'Abdul Basit Abdul Samad'),
              trailing: Radio(value: 1, groupValue: 1, onChanged: (value) {}),
            ),
            ListTile(
              leading: const Icon(Icons.person),
              title: Text(languageService.isArabic ? 'محمد صديق المنشاوي' : 'Mohamed Siddiq Al-Minshawi'),
              trailing: Radio(value: 2, groupValue: 1, onChanged: (value) {}),
            ),
            ListTile(
              leading: const Icon(Icons.person),
              title: Text(languageService.isArabic ? 'سعد الغامدي' : 'Saad Al-Ghamdi'),
              trailing: Radio(value: 3, groupValue: 1, onChanged: (value) {}),
            ),
            ListTile(
              leading: const Icon(Icons.person),
              title: Text(languageService.isArabic ? 'ماهر المعيقلي' : 'Maher Al-Muaiqly'),
              trailing: Radio(value: 4, groupValue: 1, onChanged: (value) {}),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }
}
