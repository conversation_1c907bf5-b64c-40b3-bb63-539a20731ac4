enum IslamicEventType {
  religious,
  historical,
  cultural,
}

class IslamicEvent {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final IslamicEventType type;
  final bool isRecurring;
  final String? imageUrl;
  final Map<String, dynamic>? metadata;

  IslamicEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    required this.type,
    this.isRecurring = false,
    this.imageUrl,
    this.metadata,
  });

  factory IslamicEvent.fromJson(Map<String, dynamic> json) {
    return IslamicEvent(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      date: DateTime.parse(json['date']),
      type: IslamicEventType.values.firstWhere(
        (e) => e.toString() == 'IslamicEventType.${json['type']}',
        orElse: () => IslamicEventType.religious,
      ),
      isRecurring: json['isRecurring'] ?? false,
      imageUrl: json['imageUrl'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'type': type.toString().split('.').last,
      'isRecurring': isRecurring,
      'imageUrl': imageUrl,
      'metadata': metadata,
    };
  }
}

class IslamicDate {
  final int day;
  final int month;
  final int year;
  final String dayName;
  final String monthName;
  final String yearName;

  IslamicDate({
    required this.day,
    required this.month,
    required this.year,
    required this.dayName,
    required this.monthName,
    required this.yearName,
  });

  factory IslamicDate.fromJson(Map<String, dynamic> json) {
    return IslamicDate(
      day: json['day'] ?? 1,
      month: json['month'] ?? 1,
      year: json['year'] ?? 1445,
      dayName: json['dayName'] ?? '',
      monthName: json['monthName'] ?? '',
      yearName: json['yearName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'month': month,
      'year': year,
      'dayName': dayName,
      'monthName': monthName,
      'yearName': yearName,
    };
  }

  @override
  String toString() {
    return '$day $monthName $year AH';
  }
}

class IslamicMonth {
  final int number;
  final String name;
  final String arabicName;
  final int daysCount;
  final List<IslamicEvent> events;

  const IslamicMonth({
    required this.number,
    required this.name,
    required this.arabicName,
    required this.daysCount,
    this.events = const [],
  });

  static const List<IslamicMonth> months = [
    IslamicMonth(
      number: 1,
      name: 'Muharram',
      arabicName: 'مُحَرَّم',
      daysCount: 30,
    ),
    IslamicMonth(
      number: 2,
      name: 'Safar',
      arabicName: 'صَفَر',
      daysCount: 29,
    ),
    IslamicMonth(
      number: 3,
      name: 'Rabi\' al-awwal',
      arabicName: 'رَبِيع الأَوَّل',
      daysCount: 30,
    ),
    IslamicMonth(
      number: 4,
      name: 'Rabi\' al-thani',
      arabicName: 'رَبِيع الثَّانِي',
      daysCount: 29,
    ),
    IslamicMonth(
      number: 5,
      name: 'Jumada al-awwal',
      arabicName: 'جُمَادَىٰ الأُولَىٰ',
      daysCount: 30,
    ),
    IslamicMonth(
      number: 6,
      name: 'Jumada al-thani',
      arabicName: 'جُمَادَىٰ الآخِرَة',
      daysCount: 29,
    ),
    IslamicMonth(
      number: 7,
      name: 'Rajab',
      arabicName: 'رَجَب',
      daysCount: 30,
    ),
    IslamicMonth(
      number: 8,
      name: 'Sha\'ban',
      arabicName: 'شَعْبَان',
      daysCount: 29,
    ),
    IslamicMonth(
      number: 9,
      name: 'Ramadan',
      arabicName: 'رَمَضَان',
      daysCount: 30,
    ),
    IslamicMonth(
      number: 10,
      name: 'Shawwal',
      arabicName: 'شَوَّال',
      daysCount: 29,
    ),
    IslamicMonth(
      number: 11,
      name: 'Dhu al-Qi\'dah',
      arabicName: 'ذُو القِعْدَة',
      daysCount: 30,
    ),
    IslamicMonth(
      number: 12,
      name: 'Dhu al-Hijjah',
      arabicName: 'ذُو الحِجَّة',
      daysCount: 29,
    ),
  ];

  static IslamicMonth getMonth(int monthNumber) {
    return months.firstWhere(
      (month) => month.number == monthNumber,
      orElse: () => months.first,
    );
  }

  factory IslamicMonth.fromJson(Map<String, dynamic> json) {
    return IslamicMonth(
      number: json['number'] ?? 1,
      name: json['name'] ?? '',
      arabicName: json['arabicName'] ?? '',
      daysCount: json['daysCount'] ?? 30,
      events: (json['events'] as List<dynamic>?)
          ?.map((e) => IslamicEvent.fromJson(e))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name,
      'arabicName': arabicName,
      'daysCount': daysCount,
      'events': events.map((e) => e.toJson()).toList(),
    };
  }
}

class IslamicCalendarSettings {
  final bool showGregorianDate;
  final bool enableEventNotifications;
  final bool showMoonPhases;
  final String preferredLanguage;

  IslamicCalendarSettings({
    this.showGregorianDate = true,
    this.enableEventNotifications = true,
    this.showMoonPhases = false,
    this.preferredLanguage = 'en',
  });

  IslamicCalendarSettings copyWith({
    bool? showGregorianDate,
    bool? enableEventNotifications,
    bool? showMoonPhases,
    String? preferredLanguage,
  }) {
    return IslamicCalendarSettings(
      showGregorianDate: showGregorianDate ?? this.showGregorianDate,
      enableEventNotifications: enableEventNotifications ?? this.enableEventNotifications,
      showMoonPhases: showMoonPhases ?? this.showMoonPhases,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
    );
  }

  factory IslamicCalendarSettings.fromJson(Map<String, dynamic> json) {
    return IslamicCalendarSettings(
      showGregorianDate: json['showGregorianDate'] ?? true,
      enableEventNotifications: json['enableEventNotifications'] ?? true,
      showMoonPhases: json['showMoonPhases'] ?? false,
      preferredLanguage: json['preferredLanguage'] ?? 'en',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'showGregorianDate': showGregorianDate,
      'enableEventNotifications': enableEventNotifications,
      'showMoonPhases': showMoonPhases,
      'preferredLanguage': preferredLanguage,
    };
  }
}
