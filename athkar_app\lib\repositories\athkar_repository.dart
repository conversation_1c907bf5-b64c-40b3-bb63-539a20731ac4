import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../models/athkar_models.dart';
import 'package:uuid/uuid.dart';

class AthkarRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final _uuid = const Uuid();

  // Categories CRUD
  Future<List<AthkarCategory>> getAllCategories() async {
    final maps = await _dbHelper.query('athkar_categories', orderBy: 'name ASC');
    return maps.map((map) => _mapToAthkarCategory(map)).toList();
  }

  Future<AthkarCategory?> getCategoryById(String id) async {
    final maps = await _dbHelper.query(
      'athkar_categories',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return _mapToAthkarCategory(maps.first);
    }
    return null;
  }

  Future<String> insertCategory(AthkarCategory category) async {
    final now = DateTime.now().toIso8601String();
    final data = {
      'id': category.id.isEmpty ? _uuid.v4() : category.id,
      'name': category.name,
      'description': category.description,
      'icon': category.icon,
      'color': category.color,
      'is_default': category.isDefault ? 1 : 0,
      'created_at': now,
      'updated_at': now,
    };
    await _dbHelper.insert('athkar_categories', data);
    return data['id'] as String;
  }

  Future<int> updateCategory(AthkarCategory category) async {
    final data = {
      'name': category.name,
      'description': category.description,
      'icon': category.icon,
      'color': category.color,
      'is_default': category.isDefault ? 1 : 0,
      'updated_at': DateTime.now().toIso8601String(),
    };
    return await _dbHelper.update(
      'athkar_categories',
      data,
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  Future<int> deleteCategory(String id) async {
    return await _dbHelper.delete(
      'athkar_categories',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Routines CRUD
  Future<List<AthkarRoutine>> getAllRoutines({String? userId}) async {
    String? where;
    List<dynamic>? whereArgs;
    
    if (userId != null) {
      where = 'user_id = ? OR user_id IS NULL';
      whereArgs = [userId];
    }
    
    final maps = await _dbHelper.query(
      'athkar_routines',
      where: where,
      whereArgs: whereArgs,
      orderBy: 'is_favorite DESC, title ASC',
    );
    return maps.map((map) => _mapToAthkarRoutine(map)).toList();
  }

  Future<AthkarRoutine?> getRoutineById(String id) async {
    final maps = await _dbHelper.query(
      'athkar_routines',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      final routine = _mapToAthkarRoutine(maps.first);
      final steps = await getStepsByRoutineId(id);
      return AthkarRoutine(
        id: routine.id,
        userId: routine.userId,
        categoryId: routine.categoryId,
        title: routine.title,
        description: routine.description,
        isPublic: routine.isPublic,
        isFavorite: routine.isFavorite,
        totalSteps: routine.totalSteps,
        estimatedDuration: routine.estimatedDuration,
        createdAt: routine.createdAt,
        updatedAt: routine.updatedAt,
        steps: steps,
      );
    }
    return null;
  }

  Future<String> insertRoutine(AthkarRoutine routine) async {
    debugPrint('Repository.insertRoutine called for: ${routine.title}');
    final now = DateTime.now().toIso8601String();
    final data = {
      'id': routine.id.isEmpty ? _uuid.v4() : routine.id,
      'user_id': routine.userId,
      'category_id': routine.categoryId,
      'title': routine.title,
      'description': routine.description,
      'is_public': routine.isPublic ? 1 : 0,
      'is_favorite': routine.isFavorite ? 1 : 0,
      'total_steps': routine.totalSteps,
      'estimated_duration': routine.estimatedDuration,
      'created_at': now,
      'updated_at': now,
    };
    debugPrint('Inserting routine data: $data');
    await _dbHelper.insert('athkar_routines', data);
    debugPrint('Routine inserted successfully with ID: ${data['id']}');
    return data['id'] as String;
  }

  Future<int> updateRoutine(AthkarRoutine routine) async {
    final data = {
      'user_id': routine.userId,
      'category_id': routine.categoryId,
      'title': routine.title,
      'description': routine.description,
      'is_public': routine.isPublic ? 1 : 0,
      'is_favorite': routine.isFavorite ? 1 : 0,
      'total_steps': routine.totalSteps,
      'estimated_duration': routine.estimatedDuration,
      'updated_at': DateTime.now().toIso8601String(),
    };
    return await _dbHelper.update(
      'athkar_routines',
      data,
      where: 'id = ?',
      whereArgs: [routine.id],
    );
  }

  Future<int> deleteRoutine(String id) async {
    return await _dbHelper.delete(
      'athkar_routines',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Steps CRUD
  Future<List<AthkarStep>> getStepsByRoutineId(String routineId) async {
    final maps = await _dbHelper.query(
      'athkar_steps',
      where: 'routine_id = ?',
      whereArgs: [routineId],
      orderBy: 'step_order ASC',
    );
    return maps.map((map) => _mapToAthkarStep(map)).toList();
  }

  Future<String> insertStep(AthkarStep step) async {
    final now = DateTime.now().toIso8601String();
    final data = {
      'id': step.id.isEmpty ? _uuid.v4() : step.id,
      'routine_id': step.routineId,
      'step_order': step.stepOrder,
      'arabic_text': step.arabicText,
      'transliteration': step.transliteration,
      'translation': step.translation,
      'target_count': step.targetCount,
      'audio_url': step.audioUrl,
      'created_at': now,
      'updated_at': now,
    };
    await _dbHelper.insert('athkar_steps', data);
    return data['id'] as String;
  }

  Future<int> updateStep(AthkarStep step) async {
    final data = {
      'routine_id': step.routineId,
      'step_order': step.stepOrder,
      'arabic_text': step.arabicText,
      'transliteration': step.transliteration,
      'translation': step.translation,
      'target_count': step.targetCount,
      'audio_url': step.audioUrl,
      'updated_at': DateTime.now().toIso8601String(),
    };
    return await _dbHelper.update(
      'athkar_steps',
      data,
      where: 'id = ?',
      whereArgs: [step.id],
    );
  }

  Future<int> deleteStep(String id) async {
    return await _dbHelper.delete(
      'athkar_steps',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // User Progress methods
  Future<List<UserProgress>> getUserProgress(String userId) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_progress',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'session_date DESC',
    );

    return List.generate(maps.length, (i) {
      return UserProgress.fromJson(maps[i]);
    });
  }

  Future<String> insertUserProgress(UserProgress progress) async {
    final db = await _dbHelper.database;
    await db.insert('user_progress', progress.toJson());
    return progress.id;
  }

  Future<void> updateUserProgress(UserProgress progress) async {
    final db = await _dbHelper.database;
    await db.update(
      'user_progress',
      progress.toJson(),
      where: 'id = ?',
      whereArgs: [progress.id],
    );
  }

  Future<void> deleteUserProgress(String id) async {
    final db = await _dbHelper.database;
    await db.delete(
      'user_progress',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Helper methods to convert database maps to model objects
  AthkarCategory _mapToAthkarCategory(Map<String, dynamic> map) {
    return AthkarCategory(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      icon: map['icon'],
      color: map['color'],
      isDefault: map['is_default'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  AthkarRoutine _mapToAthkarRoutine(Map<String, dynamic> map) {
    return AthkarRoutine(
      id: map['id'],
      userId: map['user_id'],
      categoryId: map['category_id'],
      title: map['title'],
      description: map['description'],
      isPublic: map['is_public'] == 1,
      isFavorite: map['is_favorite'] == 1,
      totalSteps: map['total_steps'] ?? 0,
      estimatedDuration: map['estimated_duration'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  AthkarStep _mapToAthkarStep(Map<String, dynamic> map) {
    return AthkarStep(
      id: map['id'],
      routineId: map['routine_id'],
      stepOrder: map['step_order'],
      arabicText: map['arabic_text'],
      transliteration: map['transliteration'],
      translation: map['translation'],
      targetCount: map['target_count'] ?? 1,
      audioUrl: map['audio_url'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }
}
