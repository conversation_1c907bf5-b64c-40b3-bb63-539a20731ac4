import 'package:json_annotation/json_annotation.dart';

part 'analytics_models.g.dart';

@JsonSerializable()
class UserSession {
  final String id;
  final String userId;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration? duration;
  final int screenViews;
  final int actionsCount;
  final String deviceInfo;
  final String appVersion;
  final DateTime createdAt;

  UserSession({
    required this.id,
    required this.userId,
    required this.startTime,
    this.endTime,
    this.duration,
    this.screenViews = 0,
    this.actionsCount = 0,
    required this.deviceInfo,
    required this.appVersion,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory UserSession.fromJson(Map<String, dynamic> json) => _$UserSessionFromJson(json);
  Map<String, dynamic> toJson() => _$UserSessionToJson(this);
}

@JsonSerializable()
class UserAction {
  final String id;
  final String sessionId;
  final String userId;
  final String actionType;
  final String actionName;
  final String? screenName;
  final Map<String, dynamic> parameters;
  final DateTime timestamp;
  final DateTime createdAt;

  UserAction({
    required this.id,
    required this.sessionId,
    required this.userId,
    required this.actionType,
    required this.actionName,
    this.screenName,
    this.parameters = const {},
    required this.timestamp,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory UserAction.fromJson(Map<String, dynamic> json) => _$UserActionFromJson(json);
  Map<String, dynamic> toJson() => _$UserActionToJson(this);
}

@JsonSerializable()
class AppPerformance {
  final String id;
  final String sessionId;
  final String metricName;
  final double metricValue;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;

  AppPerformance({
    required this.id,
    required this.sessionId,
    required this.metricName,
    required this.metricValue,
    required this.timestamp,
    this.metadata = const {},
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory AppPerformance.fromJson(Map<String, dynamic> json) => _$AppPerformanceFromJson(json);
  Map<String, dynamic> toJson() => _$AppPerformanceToJson(this);
}

@JsonSerializable()
class UserBehaviorAnalytics {
  final String userId;
  final int totalSessions;
  final int totalActions;
  final Duration averageSessionDuration;
  final List<String> mostUsedFeatures;
  final Map<String, Duration> screenTimeDistribution;
  final double engagementScore;
  final double retentionRate;
  final List<int> preferredTimes;

  UserBehaviorAnalytics({
    required this.userId,
    required this.totalSessions,
    required this.totalActions,
    required this.averageSessionDuration,
    required this.mostUsedFeatures,
    required this.screenTimeDistribution,
    required this.engagementScore,
    required this.retentionRate,
    required this.preferredTimes,
  });

  factory UserBehaviorAnalytics.fromJson(Map<String, dynamic> json) => _$UserBehaviorAnalyticsFromJson(json);
  Map<String, dynamic> toJson() => _$UserBehaviorAnalyticsToJson(this);
}

@JsonSerializable()
class BusinessIntelligence {
  final int totalUsers;
  final int activeUsers;
  final int newUsers;
  final int sessionCount;
  final Duration averageSessionDuration;
  final double bounceRate;
  final Map<String, double> retentionRates;
  final Map<String, int> featureUsage;
  final List<int> userGrowth;
  final Map<String, double> engagementMetrics;

  BusinessIntelligence({
    required this.totalUsers,
    required this.activeUsers,
    required this.newUsers,
    required this.sessionCount,
    required this.averageSessionDuration,
    required this.bounceRate,
    required this.retentionRates,
    required this.featureUsage,
    required this.userGrowth,
    required this.engagementMetrics,
  });

  factory BusinessIntelligence.fromJson(Map<String, dynamic> json) => _$BusinessIntelligenceFromJson(json);
  Map<String, dynamic> toJson() => _$BusinessIntelligenceToJson(this);
}

@JsonSerializable()
class EngagementMetrics {
  final double dailyActiveUsers;
  final double weeklyActiveUsers;
  final double monthlyActiveUsers;
  final double sessionFrequency;
  final Duration averageSessionLength;
  final double retentionDay1;
  final double retentionDay7;
  final double retentionDay30;
  final double churnRate;
  final Map<String, double> featureAdoption;

  EngagementMetrics({
    required this.dailyActiveUsers,
    required this.weeklyActiveUsers,
    required this.monthlyActiveUsers,
    required this.sessionFrequency,
    required this.averageSessionLength,
    required this.retentionDay1,
    required this.retentionDay7,
    required this.retentionDay30,
    required this.churnRate,
    required this.featureAdoption,
  });

  factory EngagementMetrics.fromJson(Map<String, dynamic> json) => _$EngagementMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$EngagementMetricsToJson(this);
}

@JsonSerializable()
class UserSegment {
  final String id;
  final String name;
  final String description;
  final Map<String, dynamic> criteria;
  final List<String> userIds;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserSegment({
    required this.id,
    required this.name,
    required this.description,
    required this.criteria,
    required this.userIds,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserSegment.fromJson(Map<String, dynamic> json) => _$UserSegmentFromJson(json);
  Map<String, dynamic> toJson() => _$UserSegmentToJson(this);
}

@JsonSerializable()
class ConversionFunnel {
  final String id;
  final String name;
  final List<String> steps;
  final Map<String, int> stepCounts;
  final Map<String, double> conversionRates;
  final DateTime dateRange;

  ConversionFunnel({
    required this.id,
    required this.name,
    required this.steps,
    required this.stepCounts,
    required this.conversionRates,
    required this.dateRange,
  });

  factory ConversionFunnel.fromJson(Map<String, dynamic> json) => _$ConversionFunnelFromJson(json);
  Map<String, dynamic> toJson() => _$ConversionFunnelToJson(this);
}

@JsonSerializable()
class UserJourney {
  final String userId;
  final List<UserAction> actions;
  final Duration totalDuration;
  final String startScreen;
  final String endScreen;
  final int touchpoints;
  final List<String> path;

  UserJourney({
    required this.userId,
    required this.actions,
    required this.totalDuration,
    required this.startScreen,
    required this.endScreen,
    required this.touchpoints,
    required this.path,
  });

  factory UserJourney.fromJson(Map<String, dynamic> json) => _$UserJourneyFromJson(json);
  Map<String, dynamic> toJson() => _$UserJourneyToJson(this);
}

@JsonSerializable()
class PerformanceMetrics {
  final double appStartTime;
  final double screenLoadTime;
  final double apiResponseTime;
  final double memoryUsage;
  final double cpuUsage;
  final int crashCount;
  final double errorRate;
  final Map<String, double> customMetrics;

  PerformanceMetrics({
    required this.appStartTime,
    required this.screenLoadTime,
    required this.apiResponseTime,
    required this.memoryUsage,
    required this.cpuUsage,
    required this.crashCount,
    required this.errorRate,
    required this.customMetrics,
  });

  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) => _$PerformanceMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$PerformanceMetricsToJson(this);
}

@JsonSerializable()
class RevenueAnalytics {
  final double totalRevenue;
  final double monthlyRecurringRevenue;
  final double averageRevenuePerUser;
  final double customerLifetimeValue;
  final double churnRate;
  final Map<String, double> revenueByFeature;
  final List<double> monthlyRevenue;

  RevenueAnalytics({
    required this.totalRevenue,
    required this.monthlyRecurringRevenue,
    required this.averageRevenuePerUser,
    required this.customerLifetimeValue,
    required this.churnRate,
    required this.revenueByFeature,
    required this.monthlyRevenue,
  });

  factory RevenueAnalytics.fromJson(Map<String, dynamic> json) => _$RevenueAnalyticsFromJson(json);
  Map<String, dynamic> toJson() => _$RevenueAnalyticsToJson(this);
}

@JsonSerializable()
class CohortAnalysis {
  final String cohortId;
  final DateTime cohortDate;
  final int initialSize;
  final Map<int, int> retentionByPeriod;
  final Map<int, double> retentionRateByPeriod;
  final double averageRetention;

  CohortAnalysis({
    required this.cohortId,
    required this.cohortDate,
    required this.initialSize,
    required this.retentionByPeriod,
    required this.retentionRateByPeriod,
    required this.averageRetention,
  });

  factory CohortAnalysis.fromJson(Map<String, dynamic> json) => _$CohortAnalysisFromJson(json);
  Map<String, dynamic> toJson() => _$CohortAnalysisToJson(this);
}
