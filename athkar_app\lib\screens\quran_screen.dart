import 'package:flutter/material.dart';

import 'package:provider/provider.dart';
import '../providers/quran_provider.dart';
import '../services/language_service.dart';
import '../l10n/app_localizations.dart';
import '../theme/app_theme.dart';
import '../models/quran_models.dart';

import '../widgets/loading_overlay.dart';
import 'quran_reader_screen.dart';

class QuranScreen extends StatefulWidget {
  const QuranScreen({super.key});

  @override
  State<QuranScreen> createState() => _QuranScreenState();
}

class _QuranScreenState extends State<QuranScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearching = false;
  List<Map<String, dynamic>> _searchResults = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeQuranProvider();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _initializeQuranProvider() async {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);
    if (!quranProvider.isLoaded) {
      await quranProvider.initialize();
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults.clear();
        _isSearching = false;
      });
      return;
    }

    setState(() => _isSearching = true);

    try {
      final quranProvider = Provider.of<QuranProvider>(context, listen: false);
      final results = await quranProvider.searchQuran(query);
      
      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() => _isSearching = false);
      debugPrint('Search error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    final localizations = AppLocalizations.of(context)!;

    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(localizations.quran),
          backgroundColor: AppTheme.primaryGreen,
          foregroundColor: Colors.white,
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: [
              Tab(
                icon: const Icon(Icons.list),
                text: languageService.isArabic ? 'السور' : 'Surahs',
              ),
              Tab(
                icon: const Icon(Icons.search),
                text: languageService.isArabic ? 'البحث' : 'Search',
              ),

              Tab(
                icon: const Icon(Icons.auto_stories),
                text: languageService.isArabic ? 'الختمة' : 'Khatma',
              ),
            ],
          ),
        ),
        body: Consumer<QuranProvider>(
          builder: (context, quranProvider, child) {
            return LoadingOverlay(
              isLoading: quranProvider.isLoading,
              loadingText: languageService.isArabic ? 'جاري تحميل القرآن الكريم...' : 'Loading Quran...',
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildSurahsList(quranProvider),
                  _buildSearchTab(quranProvider),
                  _buildKhatmaTab(),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSurahsList(QuranProvider quranProvider) {
    final languageService = Provider.of<LanguageService>(context);
    
    if (!quranProvider.isLoaded) {
      return const Center(child: CircularProgressIndicator());
    }

    if (quranProvider.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              languageService.isArabic ? 'خطأ في تحميل القرآن' : 'Error loading Quran',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              quranProvider.error!,
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => quranProvider.refresh(),
              child: Text(languageService.isArabic ? 'إعادة المحاولة' : 'Retry'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Ayah of the Day Card
        Container(
          margin: const EdgeInsets.all(16),
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.wb_sunny, color: AppTheme.accentGold),
                      const SizedBox(width: 8),
                      Text(
                        languageService.isArabic ? 'آية اليوم' : 'Ayah of the Day',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryGreen,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Builder(
                    builder: (context) {
                      final ayahOfDay = quranProvider.getAyahOfTheDay();
                      final surah = quranProvider.getSurah(ayahOfDay.chapterId ?? 1);

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            ayahOfDay.textUthmani,
                            style: const TextStyle(
                              fontSize: 18,
                              fontFamily: 'Amiri',
                              height: 1.8,
                            ),
                            textAlign: TextAlign.right,
                            textDirection: TextDirection.rtl,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${surah?.arabicName ?? ''} (${ayahOfDay.chapterId}:${ayahOfDay.verseNumber})',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
        
        // Surahs List
        Expanded(
          child: ListView.builder(
            itemCount: quranProvider.surahs.length,
            itemBuilder: (context, index) {
              final surah = quranProvider.surahs[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGreen,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Center(
                      child: Text(
                        '${surah.id}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  title: Text(
                    surah.arabicName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        surah.englishName,
                        style: const TextStyle(fontSize: 14),
                      ),
                      Text(
                        '${surah.numberOfAyahs} ${languageService.isArabic ? "آية" : "verses"} • ${languageService.isArabic ? (surah.revelationType == "Meccan" ? "مكية" : "مدنية") : surah.revelationType}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  trailing: Icon(
                    languageService.forwardIcon,
                    color: AppTheme.primaryGreen,
                  ),
                  onTap: () {
                    if (!mounted) return;
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => QuranReaderScreen(
                          surah: surah,
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchTab(QuranProvider quranProvider) {
    final languageService = Provider.of<LanguageService>(context);
    
    return Column(
      children: [
        // Search Bar
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: languageService.isArabic ? 'ابحث في القرآن الكريم...' : 'Search in Quran...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                        _performSearch('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppTheme.primaryGreen),
              ),
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
              _performSearch(value);
            },
            textDirection: languageService.textDirection,
          ),
        ),
        
        // Search Results
        Expanded(
          child: _isSearching
              ? const Center(child: CircularProgressIndicator())
              : _searchResults.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchQuery.isEmpty
                                ? (languageService.isArabic ? 'ابحث في القرآن الكريم' : 'Search in the Holy Quran')
                                : (languageService.isArabic ? 'لا توجد نتائج' : 'No results found'),
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _searchResults.length,
                      itemBuilder: (context, index) {
                        final result = _searchResults[index];
                        return Card(
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                          child: ListTile(
                            title: Text(
                              (result['ayah'] as Ayah).textUthmani,
                              style: const TextStyle(
                                fontSize: 16,
                                height: 1.6,
                              ),
                              textDirection: TextDirection.rtl,
                            ),
                            subtitle: Text(
                              result['reference'] as String,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                            onTap: () {
                              if (!mounted) return;
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => QuranReaderScreen(
                                    surah: result['surah'] as Surah,
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
        ),
      ],
    );
  }



  Widget _buildKhatmaTab() {
    final languageService = Provider.of<LanguageService>(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.auto_stories,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            languageService.isArabic ? 'خطة ختم القرآن' : 'Quran Completion Plan',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            languageService.isArabic 
                ? 'خطة ختم القرآن الكريم'
                : 'Quran Completion Planner',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
