import 'package:json_annotation/json_annotation.dart';

part 'premium_models.g.dart';

@JsonSerializable()
class PremiumStatus {
  final bool isPremium;
  final String? planId;
  final String? planName;
  final List<String> features;
  final DateTime? purchaseDate;
  final DateTime? expiryDate;
  final bool isLifetime;
  final bool isCancelled;
  final String? transactionId;

  const PremiumStatus({
    required this.isPremium,
    this.planId,
    this.planName,
    this.features = const [],
    this.purchaseDate,
    this.expiryDate,
    this.isLifetime = false,
    this.isCancelled = false,
    this.transactionId,
  });

  factory PremiumStatus.free() {
    return const PremiumStatus(isPremium: false);
  }

  factory PremiumStatus.fromJson(Map<String, dynamic> json) => _$PremiumStatusFromJson(json);
  Map<String, dynamic> toJson() => _$PremiumStatusToJson(this);

  PremiumStatus copyWith({
    bool? isPremium,
    String? planId,
    String? planName,
    List<String>? features,
    DateTime? purchaseDate,
    DateTime? expiryDate,
    bool? isLifetime,
    bool? isCancelled,
    String? transactionId,
  }) {
    return PremiumStatus(
      isPremium: isPremium ?? this.isPremium,
      planId: planId ?? this.planId,
      planName: planName ?? this.planName,
      features: features ?? this.features,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      expiryDate: expiryDate ?? this.expiryDate,
      isLifetime: isLifetime ?? this.isLifetime,
      isCancelled: isCancelled ?? this.isCancelled,
      transactionId: transactionId ?? this.transactionId,
    );
  }

  bool get isActive {
    if (!isPremium) return false;
    if (isLifetime) return true;
    if (expiryDate == null) return false;
    return DateTime.now().isBefore(expiryDate!);
  }

  Duration? get timeRemaining {
    if (!isActive || isLifetime) return null;
    if (expiryDate == null) return null;
    final now = DateTime.now();
    if (now.isAfter(expiryDate!)) return Duration.zero;
    return expiryDate!.difference(now);
  }
}

@JsonSerializable()
class SubscriptionPlan {
  final String id;
  final String name;
  final String description;
  final double price;
  final String currency;
  final Duration? duration;
  final List<String> features;
  final bool isPopular;
  final bool isLifetime;
  final double? discount;
  final String? originalPrice;

  const SubscriptionPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.currency,
    this.duration,
    required this.features,
    this.isPopular = false,
    this.isLifetime = false,
    this.discount,
    this.originalPrice,
  });

  factory SubscriptionPlan.fromJson(Map<String, dynamic> json) => _$SubscriptionPlanFromJson(json);
  Map<String, dynamic> toJson() => _$SubscriptionPlanToJson(this);

  String get formattedPrice {
    return '\$${price.toStringAsFixed(2)}';
  }

  String get formattedDuration {
    if (isLifetime) return 'Lifetime';
    if (duration == null) return '';
    
    final days = duration!.inDays;
    if (days >= 365) {
      final years = days / 365;
      return '${years.toStringAsFixed(0)} year${years > 1 ? 's' : ''}';
    } else if (days >= 30) {
      final months = days / 30;
      return '${months.toStringAsFixed(0)} month${months > 1 ? 's' : ''}';
    } else {
      return '$days day${days > 1 ? 's' : ''}';
    }
  }

  double? get monthlyPrice {
    if (isLifetime || duration == null) return null;
    final months = duration!.inDays / 30;
    return price / months;
  }
}

enum PremiumFeatureCategory {
  content,
  analytics,
  sync,
  customization,
  notifications,
  social,
  support,
}

@JsonSerializable()
class PremiumFeature {
  final String id;
  final String name;
  final String description;
  final PremiumFeatureCategory category;
  final String? icon;
  final bool isNew;

  const PremiumFeature({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    this.icon,
    this.isNew = false,
  });

  factory PremiumFeature.fromJson(Map<String, dynamic> json) => _$PremiumFeatureFromJson(json);
  Map<String, dynamic> toJson() => _$PremiumFeatureToJson(this);
}

@JsonSerializable()
class UsageLimits {
  final int maxCustomAthkar;
  final int maxCustomDuas;
  final int maxBackups;
  final int maxThemes;
  final int analyticsRetentionDays;

  const UsageLimits({
    required this.maxCustomAthkar,
    required this.maxCustomDuas,
    required this.maxBackups,
    required this.maxThemes,
    required this.analyticsRetentionDays,
  });

  factory UsageLimits.unlimited() {
    return const UsageLimits(
      maxCustomAthkar: -1, // -1 means unlimited
      maxCustomDuas: -1,
      maxBackups: -1,
      maxThemes: -1,
      analyticsRetentionDays: -1,
    );
  }

  factory UsageLimits.fromJson(Map<String, dynamic> json) => _$UsageLimitsFromJson(json);
  Map<String, dynamic> toJson() => _$UsageLimitsToJson(this);

  bool get isUnlimited => maxCustomAthkar == -1;
}

@JsonSerializable()
class PromotionalOffer {
  final String id;
  final String title;
  final String description;
  final double discountPercentage;
  final DateTime validUntil;
  final List<String> planIds;
  final String? promoCode;

  const PromotionalOffer({
    required this.id,
    required this.title,
    required this.description,
    required this.discountPercentage,
    required this.validUntil,
    required this.planIds,
    this.promoCode,
  });

  factory PromotionalOffer.fromJson(Map<String, dynamic> json) => _$PromotionalOfferFromJson(json);
  Map<String, dynamic> toJson() => _$PromotionalOfferToJson(this);

  bool get isValid => DateTime.now().isBefore(validUntil);

  String get formattedDiscount {
    return '${(discountPercentage * 100).toInt()}% OFF';
  }
}

@JsonSerializable()
class PurchaseHistory {
  final String id;
  final String planId;
  final String planName;
  final double amount;
  final String currency;
  final DateTime purchaseDate;
  final DateTime? expiryDate;
  final String transactionId;
  final PurchaseStatus status;

  const PurchaseHistory({
    required this.id,
    required this.planId,
    required this.planName,
    required this.amount,
    required this.currency,
    required this.purchaseDate,
    this.expiryDate,
    required this.transactionId,
    required this.status,
  });

  factory PurchaseHistory.fromJson(Map<String, dynamic> json) => _$PurchaseHistoryFromJson(json);
  Map<String, dynamic> toJson() => _$PurchaseHistoryToJson(this);
}

enum PurchaseStatus {
  pending,
  completed,
  failed,
  refunded,
  cancelled,
}

@JsonSerializable()
class PaymentMethod {
  final String id;
  final String type; // credit_card, paypal, google_pay, apple_pay
  final String displayName;
  final String? last4Digits;
  final String? expiryMonth;
  final String? expiryYear;
  final bool isDefault;

  const PaymentMethod({
    required this.id,
    required this.type,
    required this.displayName,
    this.last4Digits,
    this.expiryMonth,
    this.expiryYear,
    this.isDefault = false,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) => _$PaymentMethodFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentMethodToJson(this);
}

@JsonSerializable()
class SubscriptionAnalytics {
  final int totalSubscribers;
  final int activeSubscribers;
  final int newSubscribers;
  final int cancelledSubscribers;
  final double monthlyRecurringRevenue;
  final double averageRevenuePerUser;
  final double churnRate;
  final Map<String, int> planDistribution;
  final Map<String, double> revenueByPlan;

  const SubscriptionAnalytics({
    required this.totalSubscribers,
    required this.activeSubscribers,
    required this.newSubscribers,
    required this.cancelledSubscribers,
    required this.monthlyRecurringRevenue,
    required this.averageRevenuePerUser,
    required this.churnRate,
    required this.planDistribution,
    required this.revenueByPlan,
  });

  factory SubscriptionAnalytics.fromJson(Map<String, dynamic> json) => _$SubscriptionAnalyticsFromJson(json);
  Map<String, dynamic> toJson() => _$SubscriptionAnalyticsToJson(this);
}

@JsonSerializable()
class FeatureUsageStats {
  final String featureId;
  final String featureName;
  final int totalUsage;
  final int uniqueUsers;
  final double usagePercentage;
  final Map<String, int> usageByPlan;

  const FeatureUsageStats({
    required this.featureId,
    required this.featureName,
    required this.totalUsage,
    required this.uniqueUsers,
    required this.usagePercentage,
    required this.usageByPlan,
  });

  factory FeatureUsageStats.fromJson(Map<String, dynamic> json) => _$FeatureUsageStatsFromJson(json);
  Map<String, dynamic> toJson() => _$FeatureUsageStatsToJson(this);
}
