// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Storage_1_H
#define WINRT_Windows_Storage_1_H
#include "winrt/impl/Windows.Foundation.0.h"
#include "winrt/impl/Windows.Storage.Streams.0.h"
#include "winrt/impl/Windows.Storage.0.h"
WINRT_EXPORT namespace winrt::Windows::Storage
{
    struct __declspec(empty_bases) IAppDataPaths :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppDataPaths>
    {
        IAppDataPaths(std::nullptr_t = nullptr) noexcept {}
        IAppDataPaths(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppDataPathsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppDataPathsStatics>
    {
        IAppDataPathsStatics(std::nullptr_t = nullptr) noexcept {}
        IAppDataPathsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IApplicationData :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationData>
    {
        IApplicationData(std::nullptr_t = nullptr) noexcept {}
        IApplicationData(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IApplicationData2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationData2>
    {
        IApplicationData2(std::nullptr_t = nullptr) noexcept {}
        IApplicationData2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IApplicationData3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationData3>
    {
        IApplicationData3(std::nullptr_t = nullptr) noexcept {}
        IApplicationData3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IApplicationDataContainer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationDataContainer>
    {
        IApplicationDataContainer(std::nullptr_t = nullptr) noexcept {}
        IApplicationDataContainer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IApplicationDataStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationDataStatics>
    {
        IApplicationDataStatics(std::nullptr_t = nullptr) noexcept {}
        IApplicationDataStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IApplicationDataStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationDataStatics2>
    {
        IApplicationDataStatics2(std::nullptr_t = nullptr) noexcept {}
        IApplicationDataStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICachedFileManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICachedFileManagerStatics>
    {
        ICachedFileManagerStatics(std::nullptr_t = nullptr) noexcept {}
        ICachedFileManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDownloadsFolderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDownloadsFolderStatics>
    {
        IDownloadsFolderStatics(std::nullptr_t = nullptr) noexcept {}
        IDownloadsFolderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDownloadsFolderStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDownloadsFolderStatics2>
    {
        IDownloadsFolderStatics2(std::nullptr_t = nullptr) noexcept {}
        IDownloadsFolderStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFileIOStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileIOStatics>
    {
        IFileIOStatics(std::nullptr_t = nullptr) noexcept {}
        IFileIOStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKnownFoldersCameraRollStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownFoldersCameraRollStatics>
    {
        IKnownFoldersCameraRollStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownFoldersCameraRollStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKnownFoldersPlaylistsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownFoldersPlaylistsStatics>
    {
        IKnownFoldersPlaylistsStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownFoldersPlaylistsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKnownFoldersSavedPicturesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownFoldersSavedPicturesStatics>
    {
        IKnownFoldersSavedPicturesStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownFoldersSavedPicturesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKnownFoldersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownFoldersStatics>
    {
        IKnownFoldersStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownFoldersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKnownFoldersStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownFoldersStatics2>
    {
        IKnownFoldersStatics2(std::nullptr_t = nullptr) noexcept {}
        IKnownFoldersStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKnownFoldersStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownFoldersStatics3>
    {
        IKnownFoldersStatics3(std::nullptr_t = nullptr) noexcept {}
        IKnownFoldersStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IKnownFoldersStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownFoldersStatics4>
    {
        IKnownFoldersStatics4(std::nullptr_t = nullptr) noexcept {}
        IKnownFoldersStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathIOStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathIOStatics>
    {
        IPathIOStatics(std::nullptr_t = nullptr) noexcept {}
        IPathIOStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISetVersionDeferral :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISetVersionDeferral>
    {
        ISetVersionDeferral(std::nullptr_t = nullptr) noexcept {}
        ISetVersionDeferral(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISetVersionRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISetVersionRequest>
    {
        ISetVersionRequest(std::nullptr_t = nullptr) noexcept {}
        ISetVersionRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageFile :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageFile>,
        impl::require<winrt::Windows::Storage::IStorageFile, winrt::Windows::Storage::IStorageItem, winrt::Windows::Storage::Streams::IRandomAccessStreamReference, winrt::Windows::Storage::Streams::IInputStreamReference>
    {
        IStorageFile(std::nullptr_t = nullptr) noexcept {}
        IStorageFile(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageFile2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageFile2>
    {
        IStorageFile2(std::nullptr_t = nullptr) noexcept {}
        IStorageFile2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageFilePropertiesWithAvailability :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageFilePropertiesWithAvailability>
    {
        IStorageFilePropertiesWithAvailability(std::nullptr_t = nullptr) noexcept {}
        IStorageFilePropertiesWithAvailability(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageFileStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageFileStatics>
    {
        IStorageFileStatics(std::nullptr_t = nullptr) noexcept {}
        IStorageFileStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageFileStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageFileStatics2>
    {
        IStorageFileStatics2(std::nullptr_t = nullptr) noexcept {}
        IStorageFileStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageFolder :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageFolder>,
        impl::require<winrt::Windows::Storage::IStorageFolder, winrt::Windows::Storage::IStorageItem>
    {
        IStorageFolder(std::nullptr_t = nullptr) noexcept {}
        IStorageFolder(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageFolder2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageFolder2>
    {
        IStorageFolder2(std::nullptr_t = nullptr) noexcept {}
        IStorageFolder2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageFolder3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageFolder3>
    {
        IStorageFolder3(std::nullptr_t = nullptr) noexcept {}
        IStorageFolder3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageFolderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageFolderStatics>
    {
        IStorageFolderStatics(std::nullptr_t = nullptr) noexcept {}
        IStorageFolderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageFolderStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageFolderStatics2>
    {
        IStorageFolderStatics2(std::nullptr_t = nullptr) noexcept {}
        IStorageFolderStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageItem>
    {
        IStorageItem(std::nullptr_t = nullptr) noexcept {}
        IStorageItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageItem2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageItem2>,
        impl::require<winrt::Windows::Storage::IStorageItem2, winrt::Windows::Storage::IStorageItem>
    {
        IStorageItem2(std::nullptr_t = nullptr) noexcept {}
        IStorageItem2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageItemProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageItemProperties>
    {
        IStorageItemProperties(std::nullptr_t = nullptr) noexcept {}
        IStorageItemProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageItemProperties2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageItemProperties2>,
        impl::require<winrt::Windows::Storage::IStorageItemProperties2, winrt::Windows::Storage::IStorageItemProperties>
    {
        IStorageItemProperties2(std::nullptr_t = nullptr) noexcept {}
        IStorageItemProperties2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageItemPropertiesWithProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageItemPropertiesWithProvider>,
        impl::require<winrt::Windows::Storage::IStorageItemPropertiesWithProvider, winrt::Windows::Storage::IStorageItemProperties>
    {
        IStorageItemPropertiesWithProvider(std::nullptr_t = nullptr) noexcept {}
        IStorageItemPropertiesWithProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibrary :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibrary>
    {
        IStorageLibrary(std::nullptr_t = nullptr) noexcept {}
        IStorageLibrary(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibrary2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibrary2>
    {
        IStorageLibrary2(std::nullptr_t = nullptr) noexcept {}
        IStorageLibrary2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibrary3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibrary3>
    {
        IStorageLibrary3(std::nullptr_t = nullptr) noexcept {}
        IStorageLibrary3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibraryChange :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryChange>
    {
        IStorageLibraryChange(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryChange(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibraryChangeReader :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryChangeReader>
    {
        IStorageLibraryChangeReader(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryChangeReader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibraryChangeReader2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryChangeReader2>
    {
        IStorageLibraryChangeReader2(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryChangeReader2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibraryChangeTracker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryChangeTracker>
    {
        IStorageLibraryChangeTracker(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryChangeTracker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibraryChangeTracker2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryChangeTracker2>
    {
        IStorageLibraryChangeTracker2(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryChangeTracker2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibraryChangeTrackerOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryChangeTrackerOptions>
    {
        IStorageLibraryChangeTrackerOptions(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryChangeTrackerOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibraryLastChangeId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryLastChangeId>
    {
        IStorageLibraryLastChangeId(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryLastChangeId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibraryLastChangeIdStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryLastChangeIdStatics>
    {
        IStorageLibraryLastChangeIdStatics(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryLastChangeIdStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibraryStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryStatics>
    {
        IStorageLibraryStatics(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageLibraryStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryStatics2>
    {
        IStorageLibraryStatics2(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageProvider>
    {
        IStorageProvider(std::nullptr_t = nullptr) noexcept {}
        IStorageProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageProvider2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageProvider2>,
        impl::require<winrt::Windows::Storage::IStorageProvider2, winrt::Windows::Storage::IStorageProvider>
    {
        IStorageProvider2(std::nullptr_t = nullptr) noexcept {}
        IStorageProvider2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorageStreamTransaction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageStreamTransaction>,
        impl::require<winrt::Windows::Storage::IStorageStreamTransaction, winrt::Windows::Foundation::IClosable>
    {
        IStorageStreamTransaction(std::nullptr_t = nullptr) noexcept {}
        IStorageStreamTransaction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStreamedFileDataRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStreamedFileDataRequest>
    {
        IStreamedFileDataRequest(std::nullptr_t = nullptr) noexcept {}
        IStreamedFileDataRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemAudioProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemAudioProperties>
    {
        ISystemAudioProperties(std::nullptr_t = nullptr) noexcept {}
        ISystemAudioProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemDataPaths :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemDataPaths>
    {
        ISystemDataPaths(std::nullptr_t = nullptr) noexcept {}
        ISystemDataPaths(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemDataPathsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemDataPathsStatics>
    {
        ISystemDataPathsStatics(std::nullptr_t = nullptr) noexcept {}
        ISystemDataPathsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemGPSProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemGPSProperties>
    {
        ISystemGPSProperties(std::nullptr_t = nullptr) noexcept {}
        ISystemGPSProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemImageProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemImageProperties>
    {
        ISystemImageProperties(std::nullptr_t = nullptr) noexcept {}
        ISystemImageProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemMediaProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemMediaProperties>
    {
        ISystemMediaProperties(std::nullptr_t = nullptr) noexcept {}
        ISystemMediaProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemMusicProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemMusicProperties>
    {
        ISystemMusicProperties(std::nullptr_t = nullptr) noexcept {}
        ISystemMusicProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemPhotoProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemPhotoProperties>
    {
        ISystemPhotoProperties(std::nullptr_t = nullptr) noexcept {}
        ISystemPhotoProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemProperties>
    {
        ISystemProperties(std::nullptr_t = nullptr) noexcept {}
        ISystemProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISystemVideoProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemVideoProperties>
    {
        ISystemVideoProperties(std::nullptr_t = nullptr) noexcept {}
        ISystemVideoProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserDataPaths :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserDataPaths>
    {
        IUserDataPaths(std::nullptr_t = nullptr) noexcept {}
        IUserDataPaths(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserDataPathsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserDataPathsStatics>
    {
        IUserDataPathsStatics(std::nullptr_t = nullptr) noexcept {}
        IUserDataPathsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
