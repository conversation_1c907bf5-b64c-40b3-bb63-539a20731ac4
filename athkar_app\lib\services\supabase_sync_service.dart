import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../database/database_helper.dart';
import '../config/app_config.dart';


class SupabaseSyncService {
  static SupabaseClient? _supabase;
  static final DatabaseHelper _dbHelper = DatabaseHelper();
  static bool _isInitialized = false;
  static bool _isSyncing = false;
  static DateTime? _lastSyncTime;

  // Initialize Supabase with proper configuration
  static Future<void> initialize() async {
    try {
      if (!_isInitialized) {
        await Supabase.initialize(
          url: AppConfig.supabaseUrl,
          anonKey: AppConfig.supabaseAnonKey,
        );
        _supabase = Supabase.instance.client;
        _isInitialized = true;
        debugPrint('Supabase initialized successfully');
      }
    } catch (e) {
      debugPrint('Error initializing Supabase: $e');
      rethrow;
    }
  }

  static SupabaseClient get client {
    if (_supabase == null) {
      throw Exception('Supabase not initialized. Call initialize() first.');
    }
    return _supabase!;
  }

  static bool get isSyncing => _isSyncing;
  static DateTime? get lastSyncTime => _lastSyncTime;

  // Initialize sync service
  static Future<void> initializeSync() async {
    try {
      // Set up real-time subscriptions if user is authenticated
      if (client.auth.currentUser != null) {
        await _setupRealtimeSubscriptions();
      }
    } catch (e) {
      debugPrint('Error initializing sync service: $e');
    }
  }

  // Full sync - upload local data and download remote data
  static Future<bool> performFullSync() async {
    if (_isSyncing) {
      debugPrint('Sync already in progress');
      return false;
    }

    try {
      _isSyncing = true;
      debugPrint('Starting full sync...');

      final user = client.auth.currentUser;
      if (user == null) {
        debugPrint('User not authenticated, skipping sync');
        return false;
      }

      // Upload local data to Supabase
      await _uploadLocalData(user.id);

      // Download remote data from Supabase
      await _downloadRemoteData(user.id);

      _lastSyncTime = DateTime.now();
      debugPrint('Full sync completed successfully');
      return true;
    } catch (e) {
      debugPrint('Error during full sync: $e');
      return false;
    } finally {
      _isSyncing = false;
    }
  }

  // Upload local data to Supabase
  static Future<void> _uploadLocalData(String userId) async {
    try {
      // Upload athkar routines
      await _uploadRoutines(userId);
      
      // Upload athkar steps
      await _uploadSteps(userId);
      
      // Upload user progress
      await _uploadProgress(userId);
      
      // Upload tasbeeh items
      await _uploadTasbeehItems(userId);
      
      // Upload tasbeeh sessions
      await _uploadTasbeehSessions(userId);
      
      // Upload dua items
      await _uploadDuaItems(userId);

      debugPrint('Local data uploaded successfully');
    } catch (e) {
      debugPrint('Error uploading local data: $e');
      rethrow;
    }
  }

  // Download remote data from Supabase
  static Future<void> _downloadRemoteData(String userId) async {
    try {
      // Download athkar routines
      await _downloadRoutines(userId);
      
      // Download athkar steps
      await _downloadSteps(userId);
      
      // Download user progress
      await _downloadProgress(userId);
      
      // Download tasbeeh items
      await _downloadTasbeehItems(userId);
      
      // Download tasbeeh sessions
      await _downloadTasbeehSessions(userId);
      
      // Download dua items
      await _downloadDuaItems(userId);

      debugPrint('Remote data downloaded successfully');
    } catch (e) {
      debugPrint('Error downloading remote data: $e');
      rethrow;
    }
  }

  // Upload routines
  static Future<void> _uploadRoutines(String userId) async {
    final localRoutines = await _dbHelper.query(
      'athkar_routines',
      where: 'user_id = ?',
      whereArgs: [userId],
    );

    for (final routine in localRoutines) {
      try {
        await client.from('athkar_routines').upsert(routine);
      } catch (e) {
        debugPrint('Error uploading routine ${routine['id']}: $e');
      }
    }
  }

  // Upload steps
  static Future<void> _uploadSteps(String userId) async {
    final localSteps = await _dbHelper.query('athkar_steps');

    for (final step in localSteps) {
      try {
        await client.from('athkar_steps').upsert(step);
      } catch (e) {
        debugPrint('Error uploading step ${step['id']}: $e');
      }
    }
  }

  // Upload progress
  static Future<void> _uploadProgress(String userId) async {
    final localProgress = await _dbHelper.query(
      'user_progress',
      where: 'user_id = ?',
      whereArgs: [userId],
    );

    for (final progress in localProgress) {
      try {
        await client.from('user_progress').upsert(progress);
      } catch (e) {
        debugPrint('Error uploading progress ${progress['id']}: $e');
      }
    }
  }

  // Upload tasbeeh items
  static Future<void> _uploadTasbeehItems(String userId) async {
    final localItems = await _dbHelper.query(
      'tasbeeh_items',
      where: 'is_default = 0', // Only upload custom items
    );

    for (final item in localItems) {
      try {
        await client.from('tasbeeh_items').upsert(item);
      } catch (e) {
        debugPrint('Error uploading tasbeeh item ${item['id']}: $e');
      }
    }
  }

  // Upload tasbeeh sessions
  static Future<void> _uploadTasbeehSessions(String userId) async {
    final localSessions = await _dbHelper.query(
      'tasbeeh_sessions',
      where: 'user_id = ?',
      whereArgs: [userId],
    );

    for (final session in localSessions) {
      try {
        await client.from('tasbeeh_sessions').upsert(session);
      } catch (e) {
        debugPrint('Error uploading tasbeeh session ${session['id']}: $e');
      }
    }
  }

  // Upload dua items
  static Future<void> _uploadDuaItems(String userId) async {
    final localItems = await _dbHelper.query(
      'dua_items',
      where: 'is_default = 0', // Only upload custom items
    );

    for (final item in localItems) {
      try {
        await client.from('dua_items').upsert(item);
      } catch (e) {
        debugPrint('Error uploading dua item ${item['id']}: $e');
      }
    }
  }

  // Download routines
  static Future<void> _downloadRoutines(String userId) async {
    final response = await client
        .from('athkar_routines')
        .select()
        .eq('user_id', userId);

    for (final routine in response) {
      try {
        await _dbHelper.insert('athkar_routines', routine);
      } catch (e) {
        // Handle conflicts by updating
        await _dbHelper.update(
          'athkar_routines',
          routine,
          where: 'id = ?',
          whereArgs: [routine['id']],
        );
      }
    }
  }

  // Download steps
  static Future<void> _downloadSteps(String userId) async {
    final response = await client.from('athkar_steps').select();

    for (final step in response) {
      try {
        await _dbHelper.insert('athkar_steps', step);
      } catch (e) {
        // Handle conflicts by updating
        await _dbHelper.update(
          'athkar_steps',
          step,
          where: 'id = ?',
          whereArgs: [step['id']],
        );
      }
    }
  }

  // Download progress
  static Future<void> _downloadProgress(String userId) async {
    final response = await client
        .from('user_progress')
        .select()
        .eq('user_id', userId);

    for (final progress in response) {
      try {
        await _dbHelper.insert('user_progress', progress);
      } catch (e) {
        // Handle conflicts by updating
        await _dbHelper.update(
          'user_progress',
          progress,
          where: 'id = ?',
          whereArgs: [progress['id']],
        );
      }
    }
  }

  // Download tasbeeh items
  static Future<void> _downloadTasbeehItems(String userId) async {
    final response = await client
        .from('tasbeeh_items')
        .select()
        .eq('is_default', false);

    for (final item in response) {
      try {
        await _dbHelper.insert('tasbeeh_items', item);
      } catch (e) {
        // Handle conflicts by updating
        await _dbHelper.update(
          'tasbeeh_items',
          item,
          where: 'id = ?',
          whereArgs: [item['id']],
        );
      }
    }
  }

  // Download tasbeeh sessions
  static Future<void> _downloadTasbeehSessions(String userId) async {
    final response = await client
        .from('tasbeeh_sessions')
        .select()
        .eq('user_id', userId);

    for (final session in response) {
      try {
        await _dbHelper.insert('tasbeeh_sessions', session);
      } catch (e) {
        // Handle conflicts by updating
        await _dbHelper.update(
          'tasbeeh_sessions',
          session,
          where: 'id = ?',
          whereArgs: [session['id']],
        );
      }
    }
  }

  // Download dua items
  static Future<void> _downloadDuaItems(String userId) async {
    final response = await client
        .from('dua_items')
        .select()
        .eq('is_default', false);

    for (final item in response) {
      try {
        await _dbHelper.insert('dua_items', item);
      } catch (e) {
        // Handle conflicts by updating
        await _dbHelper.update(
          'dua_items',
          item,
          where: 'id = ?',
          whereArgs: [item['id']],
        );
      }
    }
  }

  // Set up real-time subscriptions
  static Future<void> _setupRealtimeSubscriptions() async {
    try {
      final user = client.auth.currentUser;
      if (user == null) return;

      // Subscribe to athkar routines changes
      client
          .from('athkar_routines')
          .stream(primaryKey: ['id'])
          .eq('user_id', user.id)
          .listen((data) {
            _handleRealtimeUpdate('athkar_routines', data);
          });

      // Subscribe to tasbeeh sessions changes
      client
          .from('tasbeeh_sessions')
          .stream(primaryKey: ['id'])
          .eq('user_id', user.id)
          .listen((data) {
            _handleRealtimeUpdate('tasbeeh_sessions', data);
          });

      debugPrint('Real-time subscriptions set up successfully');
    } catch (e) {
      debugPrint('Error setting up real-time subscriptions: $e');
    }
  }

  // Handle real-time updates
  static void _handleRealtimeUpdate(String table, List<Map<String, dynamic>> data) {
    try {
      for (final record in data) {
        _dbHelper.insert(table, record).catchError((e) {
          // Handle conflicts by updating
          return _dbHelper.update(
            table,
            record,
            where: 'id = ?',
            whereArgs: [record['id']],
          );
        });
      }
      debugPrint('Real-time update applied to $table');
    } catch (e) {
      debugPrint('Error handling real-time update for $table: $e');
    }
  }

  // Sync on authentication state change
  static Future<void> onAuthStateChange(AuthState state) async {
    if (state.event == AuthChangeEvent.signedIn) {
      await _setupRealtimeSubscriptions();
      await performFullSync();
    } else if (state.event == AuthChangeEvent.signedOut) {
      // Clean up subscriptions
      debugPrint('User signed out, cleaning up sync subscriptions');
    }
  }

  // Manual sync trigger
  static Future<bool> syncNow() async {
    return await performFullSync();
  }

  // Check if sync is needed (based on last sync time)
  static bool isSyncNeeded() {
    if (_lastSyncTime == null) return true;

    final timeSinceLastSync = DateTime.now().difference(_lastSyncTime!);
    return timeSinceLastSync.inHours >= 1; // Sync every hour
  }

  // Enable sync (setup subscriptions and perform initial sync)
  static Future<void> enableSync() async {
    try {
      await _setupRealtimeSubscriptions();
      await performFullSync();
    } catch (e) {
      debugPrint('Error enabling sync: $e');
      rethrow;
    }
  }

  // Disable sync (cleanup subscriptions)
  static Future<void> disableSync() async {
    try {
      // Cancel all active subscriptions
      if (_isInitialized && _supabase != null) {
        // Note: Supabase Flutter automatically handles subscription cleanup
        // when the client is disposed or when the app is closed
        debugPrint('Sync disabled - subscriptions will be cleaned up automatically');
      }
      _isSyncing = false;
      debugPrint('Sync disabled');
    } catch (e) {
      debugPrint('Error disabling sync: $e');
      rethrow;
    }
  }
}
