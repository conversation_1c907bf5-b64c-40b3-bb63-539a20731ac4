import 'package:flutter/material.dart';
import 'dart:math';
import 'authentic_islamic_content_service.dart';

/// Advanced AI-powered search service for Islamic content
/// Provides intelligent search across Quran, Hadith, and Athkar with smart suggestions
class AISearchService {
  static final AISearchService _instance = AISearchService._instance();
  factory AISearchService() => _instance;
  AISearchService._instance();

  final AuthenticIslamicContentService _contentService = AuthenticIslamicContentService();
  
  // Search history and analytics
  final List<SearchQuery> _searchHistory = [];
  final Map<String, int> _searchFrequency = {};
  final Map<String, List<String>> _searchSuggestions = {};
  
  bool _isInitialized = false;

  /// Initialize the AI search service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _contentService.initialize();
      await _buildSearchIndex();
      await _loadSearchSuggestions();
      
      _isInitialized = true;
      debugPrint('AI Search Service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing AI Search Service: $e');
      rethrow;
    }
  }

  /// Perform intelligent search across all Islamic content
  Future<SearchResult> search(String query, {
    List<IslamicContentCategory>? categories,
    SearchType searchType = SearchType.comprehensive,
    int maxResults = 50,
  }) async {
    if (!_isInitialized) await initialize();
    
    final searchQuery = SearchQuery(
      query: query,
      timestamp: DateTime.now(),
      categories: categories,
      searchType: searchType,
    );
    
    _recordSearch(searchQuery);
    
    try {
      final results = await _performSearch(query, categories, searchType, maxResults);
      final suggestions = await _generateSuggestions(query);
      
      return SearchResult(
        query: query,
        results: results,
        suggestions: suggestions,
        searchTime: DateTime.now().difference(searchQuery.timestamp),
        totalResults: results.length,
      );
    } catch (e) {
      debugPrint('Error performing search: $e');
      return SearchResult(
        query: query,
        results: [],
        suggestions: [],
        searchTime: const Duration(milliseconds: 0),
        totalResults: 0,
        error: e.toString(),
      );
    }
  }

  /// Get intelligent search suggestions based on query
  Future<List<String>> getSuggestions(String query) async {
    if (query.isEmpty) return _getPopularSearches();
    
    final suggestions = <String>[];
    
    // Add exact matches from search history
    suggestions.addAll(_searchHistory
        .where((s) => s.query.toLowerCase().contains(query.toLowerCase()))
        .map((s) => s.query)
        .take(5));
    
    // Add semantic suggestions
    suggestions.addAll(await _getSemanticSuggestions(query));
    
    // Add category-based suggestions
    suggestions.addAll(_getCategorySuggestions(query));
    
    // Remove duplicates and limit results
    return suggestions.toSet().take(10).toList();
  }

  /// Get search analytics and insights
  SearchAnalytics getSearchAnalytics() {
    final totalSearches = _searchHistory.length;
    final uniqueQueries = _searchFrequency.keys.length;
    final averageQueryLength = _searchHistory.isEmpty 
        ? 0.0 
        : _searchHistory.map((s) => s.query.length).reduce((a, b) => a + b) / totalSearches;
    
    final popularQueries = _searchFrequency.entries
        .toList()
        ..sort((a, b) => b.value.compareTo(a.value));
    
    final categoryUsage = <IslamicContentCategory, int>{};
    for (final search in _searchHistory) {
      if (search.categories != null) {
        for (final category in search.categories!) {
          categoryUsage[category] = (categoryUsage[category] ?? 0) + 1;
        }
      }
    }
    
    return SearchAnalytics(
      totalSearches: totalSearches,
      uniqueQueries: uniqueQueries,
      averageQueryLength: averageQueryLength,
      popularQueries: popularQueries.take(10).map((e) => e.key).toList(),
      categoryUsage: categoryUsage,
      searchHistory: List.unmodifiable(_searchHistory),
    );
  }

  /// Clear search history
  void clearSearchHistory() {
    _searchHistory.clear();
    _searchFrequency.clear();
  }

  /// Perform the actual search operation
  Future<List<SearchResultItem>> _performSearch(
    String query,
    List<IslamicContentCategory>? categories,
    SearchType searchType,
    int maxResults,
  ) async {
    final results = <SearchResultItem>[];
    final queryLower = query.toLowerCase();
    
    // Get content to search
    final contentToSearch = categories != null
        ? categories.expand((cat) => _contentService.getContentByCategory(cat)).toList()
        : _getAllContent();
    
    for (final content in contentToSearch) {
      final relevanceScore = _calculateRelevanceScore(content, queryLower, searchType);
      
      if (relevanceScore > 0) {
        results.add(SearchResultItem(
          content: content,
          relevanceScore: relevanceScore,
          matchedFields: _getMatchedFields(content, queryLower),
          highlightedText: _highlightMatches(content, queryLower),
        ));
      }
    }
    
    // Sort by relevance score
    results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
    
    return results.take(maxResults).toList();
  }

  /// Calculate relevance score for content
  double _calculateRelevanceScore(IslamicContent content, String query, SearchType searchType) {
    double score = 0.0;
    
    // Exact matches get highest score
    if (content.arabicText.toLowerCase().contains(query)) score += 10.0;
    if (content.transliteration.toLowerCase().contains(query)) score += 8.0;
    if (content.translation.toLowerCase().contains(query)) score += 6.0;
    
    // Partial matches
    final queryWords = query.split(' ');
    for (final word in queryWords) {
      if (word.length < 2) continue;
      
      if (content.arabicText.toLowerCase().contains(word)) score += 3.0;
      if (content.transliteration.toLowerCase().contains(word)) score += 2.0;
      if (content.translation.toLowerCase().contains(word)) score += 1.5;
      
      // Check benefits
      for (final benefit in content.benefits) {
        if (benefit.toLowerCase().contains(word)) score += 1.0;
      }
    }
    
    // Boost score based on search frequency
    final frequency = _searchFrequency[content.id] ?? 0;
    score += frequency * 0.1;
    
    // Boost score for popular content
    if (content.repetitions > 10) score += 0.5;
    
    return score;
  }

  /// Get all content from the service
  List<IslamicContent> _getAllContent() {
    final allContent = <IslamicContent>[];
    
    for (final category in IslamicContentCategory.values) {
      allContent.addAll(_contentService.getContentByCategory(category));
    }
    
    return allContent;
  }

  /// Get matched fields for highlighting
  List<String> _getMatchedFields(IslamicContent content, String query) {
    final matchedFields = <String>[];
    
    if (content.arabicText.toLowerCase().contains(query)) {
      matchedFields.add('arabicText');
    }
    if (content.transliteration.toLowerCase().contains(query)) {
      matchedFields.add('transliteration');
    }
    if (content.translation.toLowerCase().contains(query)) {
      matchedFields.add('translation');
    }
    
    return matchedFields;
  }

  /// Highlight matches in text
  String _highlightMatches(IslamicContent content, String query) {
    // Simple highlighting - in a real app, this would be more sophisticated
    return content.translation.replaceAllMapped(
      RegExp(query, caseSensitive: false),
      (match) => '**${match.group(0)}**',
    );
  }

  /// Generate intelligent suggestions
  Future<List<String>> _generateSuggestions(String query) async {
    final suggestions = <String>[];
    
    // Add related terms
    suggestions.addAll(_getRelatedTerms(query));
    
    // Add category suggestions
    suggestions.addAll(_getCategorySuggestions(query));
    
    return suggestions.take(5).toList();
  }

  /// Get semantic suggestions based on query
  Future<List<String>> _getSemanticSuggestions(String query) async {
    final suggestions = <String>[];
    final queryLower = query.toLowerCase();
    
    // Islamic concept mappings
    final conceptMappings = {
      'morning': ['صباح', 'فجر', 'أذكار الصباح'],
      'evening': ['مساء', 'مغرب', 'أذكار المساء'],
      'sleep': ['نوم', 'منام', 'أدعية النوم'],
      'food': ['طعام', 'أكل', 'أدعية الطعام'],
      'travel': ['سفر', 'رحلة', 'أدعية السفر'],
      'forgiveness': ['استغفار', 'توبة', 'مغفرة'],
      'protection': ['حماية', 'أمان', 'حفظ'],
      'gratitude': ['شكر', 'حمد', 'امتنان'],
    };
    
    for (final entry in conceptMappings.entries) {
      if (queryLower.contains(entry.key)) {
        suggestions.addAll(entry.value);
      }
    }
    
    return suggestions;
  }

  /// Get category-based suggestions
  List<String> _getCategorySuggestions(String query) {
    final suggestions = <String>[];
    final queryLower = query.toLowerCase();
    
    for (final category in IslamicContentCategory.values) {
      if (category.displayNameEnglish.toLowerCase().contains(queryLower) ||
          category.displayNameArabic.contains(query)) {
        suggestions.add(category.displayNameEnglish);
        suggestions.add(category.displayNameArabic);
      }
    }
    
    return suggestions;
  }

  /// Get related terms for a query
  List<String> _getRelatedTerms(String query) {
    // This would typically use a more sophisticated NLP approach
    final relatedTerms = <String>[];
    final queryLower = query.toLowerCase();
    
    // Simple related terms mapping
    final termMappings = {
      'allah': ['الله', 'god', 'lord', 'رب'],
      'prophet': ['نبي', 'رسول', 'muhammad', 'محمد'],
      'prayer': ['صلاة', 'دعاء', 'salah', 'dua'],
      'quran': ['قرآن', 'كتاب', 'book', 'holy'],
    };
    
    for (final entry in termMappings.entries) {
      if (queryLower.contains(entry.key)) {
        relatedTerms.addAll(entry.value);
      }
    }
    
    return relatedTerms;
  }

  /// Get popular searches
  List<String> _getPopularSearches() {
    return _searchFrequency.entries
        .toList()
        ..sort((a, b) => b.value.compareTo(a.value))
        .take(10)
        .map((e) => e.key)
        .toList();
  }

  /// Record a search for analytics
  void _recordSearch(SearchQuery searchQuery) {
    _searchHistory.add(searchQuery);
    _searchFrequency[searchQuery.query] = (_searchFrequency[searchQuery.query] ?? 0) + 1;
    
    // Keep only last 1000 searches
    if (_searchHistory.length > 1000) {
      _searchHistory.removeAt(0);
    }
  }

  /// Build search index for faster searching
  Future<void> _buildSearchIndex() async {
    // In a real implementation, this would build an inverted index
    debugPrint('Building search index...');
  }

  /// Load search suggestions from storage
  Future<void> _loadSearchSuggestions() async {
    // In a real implementation, this would load from persistent storage
    debugPrint('Loading search suggestions...');
  }
}

/// Represents a search query
class SearchQuery {
  final String query;
  final DateTime timestamp;
  final List<IslamicContentCategory>? categories;
  final SearchType searchType;

  SearchQuery({
    required this.query,
    required this.timestamp,
    this.categories,
    required this.searchType,
  });
}

/// Represents search results
class SearchResult {
  final String query;
  final List<SearchResultItem> results;
  final List<String> suggestions;
  final Duration searchTime;
  final int totalResults;
  final String? error;

  SearchResult({
    required this.query,
    required this.results,
    required this.suggestions,
    required this.searchTime,
    required this.totalResults,
    this.error,
  });
}

/// Represents a single search result item
class SearchResultItem {
  final IslamicContent content;
  final double relevanceScore;
  final List<String> matchedFields;
  final String highlightedText;

  SearchResultItem({
    required this.content,
    required this.relevanceScore,
    required this.matchedFields,
    required this.highlightedText,
  });
}

/// Search analytics data
class SearchAnalytics {
  final int totalSearches;
  final int uniqueQueries;
  final double averageQueryLength;
  final List<String> popularQueries;
  final Map<IslamicContentCategory, int> categoryUsage;
  final List<SearchQuery> searchHistory;

  SearchAnalytics({
    required this.totalSearches,
    required this.uniqueQueries,
    required this.averageQueryLength,
    required this.popularQueries,
    required this.categoryUsage,
    required this.searchHistory,
  });
}

/// Types of search
enum SearchType {
  comprehensive,
  exact,
  semantic,
  phonetic,
}
