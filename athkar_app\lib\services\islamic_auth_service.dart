import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/new_supabase_service.dart';
import '../providers/hadith_provider.dart';

class IslamicAuthService {
  static final IslamicAuthService _instance = IslamicAuthService._internal();
  factory IslamicAuthService() => _instance;
  IslamicAuthService._internal();

  bool _isInitialized = false;
  String? _currentUserId;
  Map<String, dynamic> _userPreferences = {};

  // Getters
  bool get isInitialized => _isInitialized;
  String? get currentUserId => _currentUserId;
  bool get isAuthenticated => NewSupabaseService.isAuthenticated;

  /// Initialize Islamic authentication service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadUserPreferences();
      await _checkAuthenticationStatus();
      _isInitialized = true;
      debugPrint('IslamicAuthService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing IslamicAuthService: $e');
    }
  }

  /// Check authentication status and sync data if needed
  Future<void> _checkAuthenticationStatus() async {
    try {
      if (NewSupabaseService.isAuthenticated) {
        final user = NewSupabaseService.client.auth.currentUser;
        _currentUserId = user?.id;
        
        if (_currentUserId != null) {
          await _syncUserDataFromSupabase();
          debugPrint('User authenticated: $_currentUserId');
        }
      } else {
        _currentUserId = null;
        debugPrint('User not authenticated');
      }
    } catch (e) {
      debugPrint('Error checking authentication status: $e');
    }
  }

  /// Handle user login
  Future<void> onUserLogin(String userId) async {
    try {
      _currentUserId = userId;
      await _syncUserDataFromSupabase();
      await _syncLocalDataToSupabase();
      debugPrint('User login handled: $userId');
    } catch (e) {
      debugPrint('Error handling user login: $e');
    }
  }

  /// Handle user logout
  Future<void> onUserLogout() async {
    try {
      // Sync any pending local data before logout
      await _syncLocalDataToSupabase();
      
      _currentUserId = null;
      _userPreferences.clear();
      
      // Clear user-specific cached data
      await _clearUserSpecificCache();
      
      debugPrint('User logout handled');
    } catch (e) {
      debugPrint('Error handling user logout: $e');
    }
  }

  /// Sync user data from Supabase
  Future<void> _syncUserDataFromSupabase() async {
    if (!isAuthenticated || _currentUserId == null) return;

    try {
      // Sync Hadith favorites
      final hadithProvider = HadithProvider();
      await hadithProvider.syncFavoritesFromSupabase();

      // Sync user preferences
      await _syncUserPreferencesFromSupabase();

      debugPrint('Synced user data from Supabase');
    } catch (e) {
      debugPrint('Error syncing user data from Supabase: $e');
    }
  }

  /// Sync local data to Supabase
  Future<void> _syncLocalDataToSupabase() async {
    if (!isAuthenticated || _currentUserId == null) return;

    try {
      // Sync Hadith favorites
      final hadithProvider = HadithProvider();
      await hadithProvider.syncAllFavoritesToSupabase();

      // Sync user preferences
      await _syncUserPreferencesToSupabase();

      debugPrint('Synced local data to Supabase');
    } catch (e) {
      debugPrint('Error syncing local data to Supabase: $e');
    }
  }

  /// Load user preferences from local storage
  Future<void> _loadUserPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final prefsJson = prefs.getString('islamic_user_preferences');
      
      if (prefsJson != null) {
        // Parse preferences (simplified for demonstration)
        _userPreferences = {'loaded': true};
        debugPrint('Loaded user preferences from local storage');
      }
    } catch (e) {
      debugPrint('Error loading user preferences: $e');
    }
  }

  /// Sync user preferences from Supabase
  Future<void> _syncUserPreferencesFromSupabase() async {
    if (!isAuthenticated || _currentUserId == null) return;

    try {
      final response = await NewSupabaseService.client
          .from('user_islamic_preferences')
          .select('*')
          .eq('user_id', _currentUserId!)
          .maybeSingle();

      if (response != null) {
        _userPreferences = Map<String, dynamic>.from(response);
        await _saveUserPreferences();
        debugPrint('Synced user preferences from Supabase');
      }
    } catch (e) {
      debugPrint('Error syncing user preferences from Supabase: $e');
    }
  }

  /// Sync user preferences to Supabase
  Future<void> _syncUserPreferencesToSupabase() async {
    if (!isAuthenticated || _currentUserId == null) return;

    try {
      await NewSupabaseService.client.from('user_islamic_preferences').upsert({
        'user_id': _currentUserId!,
        'preferences': _userPreferences,
        'updated_at': DateTime.now().toIso8601String(),
      });

      debugPrint('Synced user preferences to Supabase');
    } catch (e) {
      debugPrint('Error syncing user preferences to Supabase: $e');
    }
  }

  /// Save user preferences to local storage
  Future<void> _saveUserPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('islamic_user_preferences', _userPreferences.toString());
      debugPrint('Saved user preferences to local storage');
    } catch (e) {
      debugPrint('Error saving user preferences: $e');
    }
  }

  /// Clear user-specific cached data
  Future<void> _clearUserSpecificCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Clear user-specific keys
      final keysToRemove = <String>[];
      for (final key in prefs.getKeys()) {
        if (key.contains('user_') || 
            key.contains('favorite') || 
            key.contains('bookmark') ||
            key.contains('preference')) {
          keysToRemove.add(key);
        }
      }
      
      for (final key in keysToRemove) {
        await prefs.remove(key);
      }
      
      debugPrint('Cleared ${keysToRemove.length} user-specific cache entries');
    } catch (e) {
      debugPrint('Error clearing user-specific cache: $e');
    }
  }

  /// Get user preference
  T? getUserPreference<T>(String key, {T? defaultValue}) {
    try {
      final value = _userPreferences[key];
      if (value is T) {
        return value;
      }
      return defaultValue;
    } catch (e) {
      debugPrint('Error getting user preference $key: $e');
      return defaultValue;
    }
  }

  /// Set user preference
  Future<void> setUserPreference<T>(String key, T value) async {
    try {
      _userPreferences[key] = value;
      await _saveUserPreferences();
      
      // Sync to Supabase if authenticated
      if (isAuthenticated) {
        await _syncUserPreferencesToSupabase();
      }
      
      debugPrint('Set user preference $key: $value');
    } catch (e) {
      debugPrint('Error setting user preference $key: $e');
    }
  }

  /// Ensure data isolation between users
  Future<void> ensureDataIsolation() async {
    try {
      if (!isAuthenticated) {
        // For anonymous users, ensure they only see their local data
        debugPrint('Anonymous user - using local data only');
        return;
      }

      // For authenticated users, ensure proper data isolation
      final currentUser = NewSupabaseService.client.auth.currentUser;
      if (currentUser?.id != _currentUserId) {
        // User changed, clear old data and load new user's data
        await _clearUserSpecificCache();
        _currentUserId = currentUser?.id;
        await _syncUserDataFromSupabase();
        debugPrint('Data isolation ensured for user: $_currentUserId');
      }
    } catch (e) {
      debugPrint('Error ensuring data isolation: $e');
    }
  }

  /// Force sync all Islamic data
  Future<void> forceSyncAllData() async {
    if (!isAuthenticated) {
      debugPrint('Cannot sync - user not authenticated');
      return;
    }

    try {
      debugPrint('Starting force sync of all Islamic data...');
      
      // Sync Hadith data
      final hadithProvider = HadithProvider();
      await hadithProvider.syncFavoritesFromSupabase();
      await hadithProvider.syncAllFavoritesToSupabase();
      
      // Sync user preferences
      await _syncUserPreferencesFromSupabase();
      await _syncUserPreferencesToSupabase();
      
      debugPrint('Force sync completed successfully');
    } catch (e) {
      debugPrint('Error during force sync: $e');
      throw Exception('Failed to sync Islamic data: $e');
    }
  }

  /// Get sync status
  Future<Map<String, dynamic>> getSyncStatus() async {
    final status = <String, dynamic>{
      'authenticated': isAuthenticated,
      'user_id': _currentUserId,
      'last_sync': null,
      'pending_syncs': 0,
      'offline_mode': !isAuthenticated,
    };

    try {
      if (isAuthenticated) {
        final prefs = await SharedPreferences.getInstance();
        status['last_sync'] = prefs.getString('last_islamic_sync');
        
        // Count pending syncs (simplified)
        final hadithProvider = HadithProvider();
        status['hadith_favorites'] = hadithProvider.favorites.length;
      }
    } catch (e) {
      debugPrint('Error getting sync status: $e');
    }

    return status;
  }

  /// Test multi-user data isolation
  Future<bool> testDataIsolation() async {
    try {
      // This would be used in testing to ensure users only see their own data
      if (!isAuthenticated) {
        debugPrint('Data isolation test: Anonymous user - PASSED');
        return true;
      }

      // Test that user can only access their own favorites
      final hadithProvider = HadithProvider();
      final favorites = hadithProvider.favorites;
      
      for (final favorite in favorites) {
        if (favorite.userId != _currentUserId) {
          debugPrint('Data isolation test: Found favorite from different user - FAILED');
          return false;
        }
      }

      debugPrint('Data isolation test: All data belongs to current user - PASSED');
      return true;
    } catch (e) {
      debugPrint('Data isolation test error: $e');
      return false;
    }
  }
}
