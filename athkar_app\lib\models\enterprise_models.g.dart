// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enterprise_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Organization _$OrganizationFromJson(Map<String, dynamic> json) => Organization(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  type: $enumDecode(_$OrganizationTypeEnumMap, json['type']),
  logoUrl: json['logoUrl'] as String?,
  website: json['website'] as String?,
  contactEmail: json['contactEmail'] as String?,
  phone: json['phone'] as String?,
  address: json['address'] as String?,
  subscriptionPlan: json['subscriptionPlan'] as String? ?? 'basic',
  maxUsers: (json['maxUsers'] as num?)?.toInt() ?? 100,
  currentUsers: (json['currentUsers'] as num?)?.toInt() ?? 0,
  features:
      (json['features'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  settings: json['settings'] as Map<String, dynamic>? ?? const {},
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$OrganizationToJson(Organization instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$OrganizationTypeEnumMap[instance.type]!,
      'logoUrl': instance.logoUrl,
      'website': instance.website,
      'contactEmail': instance.contactEmail,
      'phone': instance.phone,
      'address': instance.address,
      'subscriptionPlan': instance.subscriptionPlan,
      'maxUsers': instance.maxUsers,
      'currentUsers': instance.currentUsers,
      'features': instance.features,
      'settings': instance.settings,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$OrganizationTypeEnumMap = {
  OrganizationType.mosque: 'mosque',
  OrganizationType.islamicCenter: 'islamicCenter',
  OrganizationType.school: 'school',
  OrganizationType.university: 'university',
  OrganizationType.company: 'company',
  OrganizationType.nonprofit: 'nonprofit',
  OrganizationType.government: 'government',
};

OrganizationUser _$OrganizationUserFromJson(Map<String, dynamic> json) =>
    OrganizationUser(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      userId: json['userId'] as String,
      role: $enumDecode(_$UserRoleEnumMap, json['role']),
      permissions:
          (json['permissions'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$PermissionEnumMap, e))
              .toList() ??
          const [],
      department: json['department'] as String?,
      position: json['position'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      invitedBy: json['invitedBy'] as String?,
      joinedAt: json['joinedAt'] == null
          ? null
          : DateTime.parse(json['joinedAt'] as String),
      lastActive: json['lastActive'] == null
          ? null
          : DateTime.parse(json['lastActive'] as String),
    );

Map<String, dynamic> _$OrganizationUserToJson(OrganizationUser instance) =>
    <String, dynamic>{
      'id': instance.id,
      'organizationId': instance.organizationId,
      'userId': instance.userId,
      'role': _$UserRoleEnumMap[instance.role]!,
      'permissions': instance.permissions
          .map((e) => _$PermissionEnumMap[e]!)
          .toList(),
      'department': instance.department,
      'position': instance.position,
      'isActive': instance.isActive,
      'invitedBy': instance.invitedBy,
      'joinedAt': instance.joinedAt.toIso8601String(),
      'lastActive': instance.lastActive?.toIso8601String(),
    };

const _$UserRoleEnumMap = {
  UserRole.superAdmin: 'superAdmin',
  UserRole.admin: 'admin',
  UserRole.manager: 'manager',
  UserRole.moderator: 'moderator',
  UserRole.user: 'user',
  UserRole.guest: 'guest',
};

const _$PermissionEnumMap = {
  Permission.manageUsers: 'manageUsers',
  Permission.manageRoles: 'manageRoles',
  Permission.managePrograms: 'managePrograms',
  Permission.viewReports: 'viewReports',
  Permission.manageSettings: 'manageSettings',
  Permission.moderateContent: 'moderateContent',
  Permission.viewAnalytics: 'viewAnalytics',
  Permission.exportData: 'exportData',
};

Department _$DepartmentFromJson(Map<String, dynamic> json) => Department(
  id: json['id'] as String,
  organizationId: json['organizationId'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  headUserId: json['headUserId'] as String?,
  memberCount: (json['memberCount'] as num?)?.toInt() ?? 0,
  budget: (json['budget'] as num?)?.toDouble() ?? 0.0,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$DepartmentToJson(Department instance) =>
    <String, dynamic>{
      'id': instance.id,
      'organizationId': instance.organizationId,
      'name': instance.name,
      'description': instance.description,
      'headUserId': instance.headUserId,
      'memberCount': instance.memberCount,
      'budget': instance.budget,
      'createdAt': instance.createdAt.toIso8601String(),
    };

Team _$TeamFromJson(Map<String, dynamic> json) => Team(
  id: json['id'] as String,
  organizationId: json['organizationId'] as String,
  departmentId: json['departmentId'] as String?,
  name: json['name'] as String,
  description: json['description'] as String,
  leaderUserId: json['leaderUserId'] as String?,
  memberCount: (json['memberCount'] as num?)?.toInt() ?? 0,
  goals:
      (json['goals'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$TeamToJson(Team instance) => <String, dynamic>{
  'id': instance.id,
  'organizationId': instance.organizationId,
  'departmentId': instance.departmentId,
  'name': instance.name,
  'description': instance.description,
  'leaderUserId': instance.leaderUserId,
  'memberCount': instance.memberCount,
  'goals': instance.goals,
  'createdAt': instance.createdAt.toIso8601String(),
};

OrganizationProgram _$OrganizationProgramFromJson(Map<String, dynamic> json) =>
    OrganizationProgram(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      targetAudience: json['targetAudience'] as String,
      durationDays: (json['durationDays'] as num).toInt(),
      athkarRoutines:
          (json['athkarRoutines'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      goals:
          (json['goals'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      metrics: json['metrics'] as Map<String, dynamic>? ?? const {},
      isActive: json['isActive'] as bool? ?? true,
      createdBy: json['createdBy'] as String,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$OrganizationProgramToJson(
  OrganizationProgram instance,
) => <String, dynamic>{
  'id': instance.id,
  'organizationId': instance.organizationId,
  'name': instance.name,
  'description': instance.description,
  'type': instance.type,
  'targetAudience': instance.targetAudience,
  'durationDays': instance.durationDays,
  'athkarRoutines': instance.athkarRoutines,
  'goals': instance.goals,
  'metrics': instance.metrics,
  'isActive': instance.isActive,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt.toIso8601String(),
};

ProgramEnrollment _$ProgramEnrollmentFromJson(Map<String, dynamic> json) =>
    ProgramEnrollment(
      id: json['id'] as String,
      programId: json['programId'] as String,
      userId: json['userId'] as String,
      enrolledBy: json['enrolledBy'] as String,
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
      status: json['status'] as String? ?? 'active',
      startedAt: json['startedAt'] == null
          ? null
          : DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
    );

Map<String, dynamic> _$ProgramEnrollmentToJson(ProgramEnrollment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'programId': instance.programId,
      'userId': instance.userId,
      'enrolledBy': instance.enrolledBy,
      'progress': instance.progress,
      'status': instance.status,
      'startedAt': instance.startedAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
    };

OrganizationReport _$OrganizationReportFromJson(Map<String, dynamic> json) =>
    OrganizationReport(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      type: $enumDecode(_$ReportTypeEnumMap, json['type']),
      title: json['title'] as String,
      data: json['data'] as Map<String, dynamic>,
      generatedBy: json['generatedBy'] as String,
      generatedAt: DateTime.parse(json['generatedAt'] as String),
      periodStart: DateTime.parse(json['periodStart'] as String),
      periodEnd: DateTime.parse(json['periodEnd'] as String),
    );

Map<String, dynamic> _$OrganizationReportToJson(OrganizationReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'organizationId': instance.organizationId,
      'type': _$ReportTypeEnumMap[instance.type]!,
      'title': instance.title,
      'data': instance.data,
      'generatedBy': instance.generatedBy,
      'generatedAt': instance.generatedAt.toIso8601String(),
      'periodStart': instance.periodStart.toIso8601String(),
      'periodEnd': instance.periodEnd.toIso8601String(),
    };

const _$ReportTypeEnumMap = {
  ReportType.userActivity: 'userActivity',
  ReportType.programProgress: 'programProgress',
  ReportType.departmentPerformance: 'departmentPerformance',
  ReportType.compliance: 'compliance',
};

AuditLog _$AuditLogFromJson(Map<String, dynamic> json) => AuditLog(
  id: json['id'] as String,
  organizationId: json['organizationId'] as String,
  userId: json['userId'] as String,
  action: json['action'] as String,
  resourceType: json['resourceType'] as String,
  resourceId: json['resourceId'] as String,
  details: json['details'] as Map<String, dynamic>? ?? const {},
  ipAddress: json['ipAddress'] as String?,
  userAgent: json['userAgent'] as String?,
  timestamp: json['timestamp'] == null
      ? null
      : DateTime.parse(json['timestamp'] as String),
);

Map<String, dynamic> _$AuditLogToJson(AuditLog instance) => <String, dynamic>{
  'id': instance.id,
  'organizationId': instance.organizationId,
  'userId': instance.userId,
  'action': instance.action,
  'resourceType': instance.resourceType,
  'resourceId': instance.resourceId,
  'details': instance.details,
  'ipAddress': instance.ipAddress,
  'userAgent': instance.userAgent,
  'timestamp': instance.timestamp.toIso8601String(),
};

OrganizationSetting _$OrganizationSettingFromJson(Map<String, dynamic> json) =>
    OrganizationSetting(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      category: json['category'] as String,
      key: json['key'] as String,
      value: json['value'] as String,
      updatedBy: json['updatedBy'] as String,
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$OrganizationSettingToJson(
  OrganizationSetting instance,
) => <String, dynamic>{
  'id': instance.id,
  'organizationId': instance.organizationId,
  'category': instance.category,
  'key': instance.key,
  'value': instance.value,
  'updatedBy': instance.updatedBy,
  'updatedAt': instance.updatedAt.toIso8601String(),
};

UserInvitation _$UserInvitationFromJson(Map<String, dynamic> json) =>
    UserInvitation(
      id: json['id'] as String,
      organizationId: json['organizationId'] as String,
      email: json['email'] as String,
      role: $enumDecode(_$UserRoleEnumMap, json['role']),
      department: json['department'] as String?,
      position: json['position'] as String?,
      permissions:
          (json['permissions'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$PermissionEnumMap, e))
              .toList() ??
          const [],
      invitedBy: json['invitedBy'] as String,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      acceptedAt: json['acceptedAt'] == null
          ? null
          : DateTime.parse(json['acceptedAt'] as String),
      isExpired: json['isExpired'] as bool? ?? false,
    );

Map<String, dynamic> _$UserInvitationToJson(UserInvitation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'organizationId': instance.organizationId,
      'email': instance.email,
      'role': _$UserRoleEnumMap[instance.role]!,
      'department': instance.department,
      'position': instance.position,
      'permissions': instance.permissions
          .map((e) => _$PermissionEnumMap[e]!)
          .toList(),
      'invitedBy': instance.invitedBy,
      'createdAt': instance.createdAt.toIso8601String(),
      'acceptedAt': instance.acceptedAt?.toIso8601String(),
      'isExpired': instance.isExpired,
    };

OrganizationAnalytics _$OrganizationAnalyticsFromJson(
  Map<String, dynamic> json,
) => OrganizationAnalytics(
  organizationId: json['organizationId'] as String,
  totalUsers: (json['totalUsers'] as num).toInt(),
  activeUsers: (json['activeUsers'] as num).toInt(),
  userEngagement: json['userEngagement'] as Map<String, dynamic>,
  programMetrics: json['programMetrics'] as Map<String, dynamic>,
  departmentMetrics: json['departmentMetrics'] as Map<String, dynamic>,
  generatedAt: DateTime.parse(json['generatedAt'] as String),
);

Map<String, dynamic> _$OrganizationAnalyticsToJson(
  OrganizationAnalytics instance,
) => <String, dynamic>{
  'organizationId': instance.organizationId,
  'totalUsers': instance.totalUsers,
  'activeUsers': instance.activeUsers,
  'userEngagement': instance.userEngagement,
  'programMetrics': instance.programMetrics,
  'departmentMetrics': instance.departmentMetrics,
  'generatedAt': instance.generatedAt.toIso8601String(),
};

ComplianceMetrics _$ComplianceMetricsFromJson(Map<String, dynamic> json) =>
    ComplianceMetrics(
      organizationId: json['organizationId'] as String,
      overallComplianceScore: (json['overallComplianceScore'] as num)
          .toDouble(),
      departmentScores: (json['departmentScores'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      programCompletionRates:
          (json['programCompletionRates'] as Map<String, dynamic>).map(
            (k, e) => MapEntry(k, (e as num).toDouble()),
          ),
      nonCompliantUsers: (json['nonCompliantUsers'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      violationCounts: Map<String, int>.from(json['violationCounts'] as Map),
      calculatedAt: DateTime.parse(json['calculatedAt'] as String),
    );

Map<String, dynamic> _$ComplianceMetricsToJson(ComplianceMetrics instance) =>
    <String, dynamic>{
      'organizationId': instance.organizationId,
      'overallComplianceScore': instance.overallComplianceScore,
      'departmentScores': instance.departmentScores,
      'programCompletionRates': instance.programCompletionRates,
      'nonCompliantUsers': instance.nonCompliantUsers,
      'violationCounts': instance.violationCounts,
      'calculatedAt': instance.calculatedAt.toIso8601String(),
    };

OrganizationSubscription _$OrganizationSubscriptionFromJson(
  Map<String, dynamic> json,
) => OrganizationSubscription(
  id: json['id'] as String,
  organizationId: json['organizationId'] as String,
  planId: json['planId'] as String,
  planName: json['planName'] as String,
  monthlyPrice: (json['monthlyPrice'] as num).toDouble(),
  maxUsers: (json['maxUsers'] as num).toInt(),
  features:
      (json['features'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  startDate: json['startDate'] == null
      ? null
      : DateTime.parse(json['startDate'] as String),
  endDate: json['endDate'] == null
      ? null
      : DateTime.parse(json['endDate'] as String),
  isActive: json['isActive'] as bool? ?? true,
  paymentMethod: json['paymentMethod'] as String,
  nextBillingDate: json['nextBillingDate'] == null
      ? null
      : DateTime.parse(json['nextBillingDate'] as String),
);

Map<String, dynamic> _$OrganizationSubscriptionToJson(
  OrganizationSubscription instance,
) => <String, dynamic>{
  'id': instance.id,
  'organizationId': instance.organizationId,
  'planId': instance.planId,
  'planName': instance.planName,
  'monthlyPrice': instance.monthlyPrice,
  'maxUsers': instance.maxUsers,
  'features': instance.features,
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate?.toIso8601String(),
  'isActive': instance.isActive,
  'paymentMethod': instance.paymentMethod,
  'nextBillingDate': instance.nextBillingDate?.toIso8601String(),
};

AdminDashboardData _$AdminDashboardDataFromJson(Map<String, dynamic> json) =>
    AdminDashboardData(
      organizationId: json['organizationId'] as String,
      totalUsers: (json['totalUsers'] as num).toInt(),
      activeUsers: (json['activeUsers'] as num).toInt(),
      totalPrograms: (json['totalPrograms'] as num).toInt(),
      activePrograms: (json['activePrograms'] as num).toInt(),
      averageEngagement: (json['averageEngagement'] as num).toDouble(),
      usersByDepartment: Map<String, int>.from(
        json['usersByDepartment'] as Map,
      ),
      programCompletionRates:
          (json['programCompletionRates'] as Map<String, dynamic>).map(
            (k, e) => MapEntry(k, (e as num).toDouble()),
          ),
      recentActivity: (json['recentActivity'] as List<dynamic>)
          .map((e) => e as Map<String, dynamic>)
          .toList(),
      systemHealth: json['systemHealth'] as Map<String, dynamic>,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$AdminDashboardDataToJson(AdminDashboardData instance) =>
    <String, dynamic>{
      'organizationId': instance.organizationId,
      'totalUsers': instance.totalUsers,
      'activeUsers': instance.activeUsers,
      'totalPrograms': instance.totalPrograms,
      'activePrograms': instance.activePrograms,
      'averageEngagement': instance.averageEngagement,
      'usersByDepartment': instance.usersByDepartment,
      'programCompletionRates': instance.programCompletionRates,
      'recentActivity': instance.recentActivity,
      'systemHealth': instance.systemHealth,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };
