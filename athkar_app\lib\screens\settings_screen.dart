import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/auth_provider.dart';
import '../providers/theme_provider.dart';
import '../theme/app_theme.dart';
import '../services/notification_scheduler.dart';
import '../services/data_export_service.dart';
import 'floating_counter_settings_screen.dart';
import '../services/supabase_sync_service.dart';
import 'edit_profile_screen.dart';
import 'font_size_settings_screen.dart';
import 'language_settings_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  bool _supabaseSync = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        children: [
          _buildProfileSection(),
          const Divider(),
          _buildNotificationSettings(),
          const Divider(),
          _buildAppSettings(),
          const Divider(),
          _buildSyncSettings(),
          const Divider(),
          _buildAboutSection(),
          const Divider(),
          _buildSignOutSection(),
        ],
      ),
    );
  }

  Widget _buildProfileSection() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.user;
        final profile = authProvider.userProfile;

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Profile',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  CircleAvatar(
                    radius: 32,
                    backgroundColor: AppTheme.primaryGreen,
                    child: profile?.avatarUrl != null
                        ? ClipOval(
                            child: Image.network(
                              profile!.avatarUrl!,
                              width: 64,
                              height: 64,
                              fit: BoxFit.cover,
                            ),
                          )
                        : const Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 32,
                          ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          profile?.fullName ?? user?.email?.split('@').first ?? 'User',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (user?.email != null)
                          Text(
                            user!.email!,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const EditProfileScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.edit),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNotificationSettings() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notifications',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('Enable Notifications'),
            subtitle: const Text('Receive athkar reminders'),
            value: _notificationsEnabled,
            onChanged: (value) {
              setState(() {
                _notificationsEnabled = value;
              });
            },
            secondary: const Icon(Icons.notifications),
          ),
          SwitchListTile(
            title: const Text('Sound'),
            subtitle: const Text('Play notification sounds'),
            value: _soundEnabled,
            onChanged: _notificationsEnabled
                ? (value) {
                    setState(() {
                      _soundEnabled = value;
                    });
                  }
                : null,
            secondary: const Icon(Icons.volume_up),
          ),
          SwitchListTile(
            title: const Text('Vibration'),
            subtitle: const Text('Vibrate on notifications'),
            value: _vibrationEnabled,
            onChanged: _notificationsEnabled
                ? (value) {
                    setState(() {
                      _vibrationEnabled = value;
                    });
                  }
                : null,
            secondary: const Icon(Icons.vibration),
          ),
          ListTile(
            title: const Text('Notification Schedule'),
            subtitle: const Text('Manage reminder times'),
            leading: const Icon(Icons.schedule),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showNotificationSettings();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAppSettings() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'App Settings',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return ListTile(
                title: const Text('Theme'),
                subtitle: Text(_getThemeLabel(themeProvider.themeMode)),
                leading: const Icon(Icons.palette),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _showThemeDialog(context, themeProvider),
              );
            },
          ),
          ListTile(
            title: const Text('Language'),
            subtitle: const Text('English'),
            leading: const Icon(Icons.language),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: _navigateToLanguageSettings,
          ),
          ListTile(
            title: const Text('Font Size'),
            subtitle: const Text('Medium'),
            leading: const Icon(Icons.text_fields),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: _navigateToFontSizeSettings,
          ),
          ListTile(
            title: const Text('Floating Counter'),
            subtitle: const Text('Configure dhikr counter overlay'),
            leading: Icon(MdiIcons.counter),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: _navigateToFloatingCounterSettings,
          ),
        ],
      ),
    );
  }

  Widget _buildSyncSettings() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Data & Sync',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('Cloud Sync'),
            subtitle: const Text('Sync data across devices'),
            value: _supabaseSync,
            onChanged: (value) {
              setState(() {
                _supabaseSync = value;
              });
              _toggleSupabaseSync(value);
            },
            secondary: const Icon(Icons.cloud_sync),
          ),
          ListTile(
            title: const Text('Backup Data'),
            subtitle: const Text('Create local backup'),
            leading: const Icon(Icons.backup),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _exportData();
            },
          ),
          ListTile(
            title: const Text('Restore Data'),
            subtitle: const Text('Restore from backup'),
            leading: const Icon(Icons.restore),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showImportOptions();
            },
          ),
          ListTile(
            title: const Text('Export Data'),
            subtitle: const Text('Export athkar to file'),
            leading: const Icon(Icons.file_download),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: _exportData,
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'About',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ListTile(
            title: const Text('App Version'),
            subtitle: const Text('1.0.0'),
            leading: const Icon(Icons.info),
          ),
          ListTile(
            title: const Text('Privacy Policy'),
            leading: const Icon(Icons.privacy_tip),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: _showPrivacyPolicy,
          ),
          ListTile(
            title: const Text('Terms of Service'),
            leading: const Icon(Icons.description),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: _showTermsOfService,
          ),
          ListTile(
            title: const Text('Contact Support'),
            leading: const Icon(Icons.support),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: _openSupportContact,
          ),
          ListTile(
            title: const Text('Rate App'),
            leading: const Icon(Icons.star),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: _openAppStoreRating,
          ),
        ],
      ),
    );
  }

  Widget _buildSignOutSection() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (!authProvider.isAuthenticated) {
          return const SizedBox.shrink();
        }

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _showSignOutDialog(authProvider),
                  icon: const Icon(Icons.logout),
                  label: const Text('Sign Out'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showSignOutDialog(AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              authProvider.signOut();
              Navigator.pop(context);
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Settings'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SwitchListTile(
                title: const Text('Morning Reminders'),
                subtitle: const Text('7:00 AM daily'),
                value: true,
                onChanged: (value) async {
                  if (value) {
                    await NotificationScheduler.scheduleDailyReminders(
                      enableMorning: true,
                      enableEvening: false,
                    );
                  } else {
                    await NotificationScheduler.cancelDailyReminders();
                  }
                },
              ),
              SwitchListTile(
                title: const Text('Evening Reminders'),
                subtitle: const Text('6:00 PM daily'),
                value: true,
                onChanged: (value) async {
                  if (value) {
                    await NotificationScheduler.scheduleDailyReminders(
                      enableMorning: false,
                      enableEvening: true,
                    );
                  } else {
                    await NotificationScheduler.cancelDailyReminders();
                  }
                },
              ),
              SwitchListTile(
                title: const Text('Tasbeeh Reminders'),
                subtitle: const Text('Multiple times daily'),
                value: false,
                onChanged: (value) async {
                  if (value) {
                    await NotificationScheduler.scheduleTasbeehReminders(
                      reminderTimes: [
                        const TimeOfDay(hour: 9, minute: 0),
                        const TimeOfDay(hour: 15, minute: 0),
                        const TimeOfDay(hour: 21, minute: 0),
                      ],
                    );
                  } else {
                    await NotificationScheduler.cancelTasbeehReminders();
                  }
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _exportData() async {
    final authProvider = context.read<AuthProvider>();
    final userId = authProvider.user?.id;

    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please sign in to export data')),
      );
      return;
    }

    try {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Exporting data...'),
            ],
          ),
        ),
      );

      final success = await DataExportService.exportToFile(userId);

      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'Data exported successfully' : 'Export failed'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Export error: $e')),
        );
      }
    }
  }

  void _showImportOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Data'),
        content: const Text('Import functionality will be available in a future update. For now, you can restore data through Supabase sync.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _getThemeLabel(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return 'Light';
      case AppThemeMode.dark:
        return 'Dark';
      case AppThemeMode.system:
        return 'System';
    }
  }

  void _showThemeDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AppThemeMode.values.map((mode) {
            return RadioListTile<AppThemeMode>(
              title: Text(_getThemeLabel(mode)),
              subtitle: Text(_getThemeDescription(mode)),
              value: mode,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  String _getThemeDescription(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return 'Always use light theme';
      case AppThemeMode.dark:
        return 'Always use dark theme';
      case AppThemeMode.system:
        return 'Follow system setting';
    }
  }

  void _navigateToLanguageSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LanguageSettingsScreen(),
      ),
    );
  }

  void _navigateToFontSizeSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FontSizeSettingsScreen(),
      ),
    );
  }

  void _navigateToFloatingCounterSettings() {
    // Create floating counter settings screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FloatingCounterSettingsScreen(),
      ),
    );
  }

  Future<void> _toggleSupabaseSync(bool value) async {
    setState(() {
      _supabaseSync = value;
    });

    try {
      if (value) {
        await SupabaseSyncService.enableSync();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Cloud sync enabled'),
              backgroundColor: AppTheme.primaryGreen,
            ),
          );
        }
      } else {
        await SupabaseSyncService.disableSync();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Cloud sync disabled'),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _supabaseSync = !value; // Revert on error
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to ${value ? 'enable' : 'disable'} sync: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  Future<void> _showPrivacyPolicy() async {
    const url = 'https://your-app-website.com/privacy-policy';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open privacy policy'),
          ),
        );
      }
    }
  }

  Future<void> _showTermsOfService() async {
    const url = 'https://your-app-website.com/terms-of-service';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open terms of service'),
          ),
        );
      }
    }
  }

  Future<void> _openSupportContact() async {
    const email = '<EMAIL>';
    final url = 'mailto:$email?subject=Athkar App Support';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open email client'),
          ),
        );
      }
    }
  }

  Future<void> _openAppStoreRating() async {
    // For now, just show a message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please rate us on the app store!'),
        ),
      );
    }
  }
}
