<dependencies>
  <compile
      roots="__local_aars__:D:\projects\12july\athkar\athkar_app\build\app\intermediates\flutter\release\libs.jar:unspecified@jar,:@@:camera_android_camerax::release,:@@:firebase_analytics::release,:@@:shared_preferences_android::release,:@@:webview_flutter_android::release,:@@:workmanager_android::release,:@@:local_auth_android::release,:@@:app_links::release,:@@:app_settings::release,:@@:audioplayers_android::release,:@@:battery_plus::release,:@@:connectivity_plus::release,:@@:device_info_plus::release,:@@:file_picker::release,:@@:firebase_messaging::release,:@@:firebase_core::release,:@@:firebase_crashlytics::release,:@@:flutter_keyboard_visibility::release,:@@:flutter_local_notifications::release,:@@:flutter_native_splash::release,:@@:flutter_overlay_window::release,:@@:flutter_plugin_android_lifecycle::release,:@@:flutter_secure_storage::release,:@@:geolocator_android::release,:@@:google_sign_in_android::release,:@@:image_picker_android::release,:@@:in_app_purchase_android::release,:@@:network_info_plus::release,:@@:package_info_plus::release,:@@:path_provider_android::release,:@@:permission_handler_android::release,:@@:sensors_plus::release,:@@:share_plus::release,:@@:sign_in_with_apple::release,:@@:sqflite_android::release,:@@:url_launcher_android::release,:@@:vibration::release,:@@:video_player_android::release,:@@:wakelock_plus::release,io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,androidx.biometric:biometric:1.1.0@aar,androidx.fragment:fragment:1.7.1@aar,androidx.activity:activity:1.9.3@aar,androidx.loader:loader:1.1.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.window:window:1.2.0@aar,androidx.window:window-java:1.2.0@aar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.2.0@jar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.0@jar,io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,org.jetbrains:annotations:23.0.0@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,com.getkeepsafe.relinker:relinker:1.4.5@aar">
    <dependency
        name="__local_aars__:D:\projects\12july\athkar\athkar_app\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
        simpleName="__local_aars__:D:\projects\12july\athkar\athkar_app\build\app\intermediates\flutter\release\libs.jar"/>
    <dependency
        name=":@@:camera_android_camerax::release"
        simpleName="artifacts::camera_android_camerax"/>
    <dependency
        name=":@@:firebase_analytics::release"
        simpleName="artifacts::firebase_analytics"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="artifacts::shared_preferences_android"/>
    <dependency
        name=":@@:webview_flutter_android::release"
        simpleName="artifacts::webview_flutter_android"/>
    <dependency
        name=":@@:workmanager_android::release"
        simpleName="artifacts::workmanager_android"/>
    <dependency
        name=":@@:local_auth_android::release"
        simpleName="artifacts::local_auth_android"/>
    <dependency
        name=":@@:app_links::release"
        simpleName="artifacts::app_links"/>
    <dependency
        name=":@@:app_settings::release"
        simpleName="artifacts::app_settings"/>
    <dependency
        name=":@@:audioplayers_android::release"
        simpleName="artifacts::audioplayers_android"/>
    <dependency
        name=":@@:battery_plus::release"
        simpleName="artifacts::battery_plus"/>
    <dependency
        name=":@@:connectivity_plus::release"
        simpleName="artifacts::connectivity_plus"/>
    <dependency
        name=":@@:device_info_plus::release"
        simpleName="artifacts::device_info_plus"/>
    <dependency
        name=":@@:file_picker::release"
        simpleName="artifacts::file_picker"/>
    <dependency
        name=":@@:firebase_messaging::release"
        simpleName="artifacts::firebase_messaging"/>
    <dependency
        name=":@@:firebase_core::release"
        simpleName="artifacts::firebase_core"/>
    <dependency
        name=":@@:firebase_crashlytics::release"
        simpleName="artifacts::firebase_crashlytics"/>
    <dependency
        name=":@@:flutter_keyboard_visibility::release"
        simpleName="artifacts::flutter_keyboard_visibility"/>
    <dependency
        name=":@@:flutter_local_notifications::release"
        simpleName="artifacts::flutter_local_notifications"/>
    <dependency
        name=":@@:flutter_native_splash::release"
        simpleName="artifacts::flutter_native_splash"/>
    <dependency
        name=":@@:flutter_overlay_window::release"
        simpleName="artifacts::flutter_overlay_window"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="artifacts::flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:flutter_secure_storage::release"
        simpleName="artifacts::flutter_secure_storage"/>
    <dependency
        name=":@@:geolocator_android::release"
        simpleName="artifacts::geolocator_android"/>
    <dependency
        name=":@@:google_sign_in_android::release"
        simpleName="artifacts::google_sign_in_android"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="artifacts::image_picker_android"/>
    <dependency
        name=":@@:in_app_purchase_android::release"
        simpleName="artifacts::in_app_purchase_android"/>
    <dependency
        name=":@@:network_info_plus::release"
        simpleName="artifacts::network_info_plus"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="artifacts::package_info_plus"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="artifacts::path_provider_android"/>
    <dependency
        name=":@@:permission_handler_android::release"
        simpleName="artifacts::permission_handler_android"/>
    <dependency
        name=":@@:sensors_plus::release"
        simpleName="artifacts::sensors_plus"/>
    <dependency
        name=":@@:share_plus::release"
        simpleName="artifacts::share_plus"/>
    <dependency
        name=":@@:sign_in_with_apple::release"
        simpleName="artifacts::sign_in_with_apple"/>
    <dependency
        name=":@@:sqflite_android::release"
        simpleName="artifacts::sqflite_android"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="artifacts::url_launcher_android"/>
    <dependency
        name=":@@:vibration::release"
        simpleName="artifacts::vibration"/>
    <dependency
        name=":@@:video_player_android::release"
        simpleName="artifacts::video_player_android"/>
    <dependency
        name=":@@:wakelock_plus::release"
        simpleName="artifacts::wakelock_plus"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.biometric:biometric:1.1.0@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.2.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
  </compile>
  <package
      roots="__local_aars__:D:\projects\12july\athkar\athkar_app\build\app\intermediates\flutter\release\libs.jar:unspecified@jar,:@@:app_settings::release,:@@:battery_plus::release,:@@:camera_android_camerax::release,:@@:device_info_plus::release,:@@:firebase_analytics::release,:@@:network_info_plus::release,:@@:wakelock_plus::release,:@@:package_info_plus::release,:@@:sensors_plus::release,:@@:share_plus::release,:@@:shared_preferences_android::release,:@@:webview_flutter_android::release,:@@:workmanager_android::release,:@@:audioplayers_android::release,:@@:file_picker::release,:@@:flutter_local_notifications::release,:@@:geolocator_android::release,:@@:image_picker_android::release,:@@:local_auth_android::release,:@@:url_launcher_android::release,:@@:app_links::release,:@@:connectivity_plus::release,:@@:firebase_crashlytics::release,:@@:firebase_messaging::release,:@@:firebase_core::release,:@@:flutter_keyboard_visibility::release,:@@:flutter_native_splash::release,:@@:flutter_overlay_window::release,:@@:flutter_plugin_android_lifecycle::release,:@@:flutter_secure_storage::release,:@@:google_sign_in_android::release,:@@:in_app_purchase_android::release,:@@:path_provider_android::release,:@@:permission_handler_android::release,:@@:sign_in_with_apple::release,:@@:sqflite_android::release,:@@:vibration::release,:@@:video_player_android::release,io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,androidx.camera:camera-video:1.5.0-beta01@aar,androidx.camera:camera-lifecycle:1.5.0-beta01@aar,androidx.camera:camera-camera2:1.5.0-beta01@aar,androidx.camera:camera-core:1.5.0-beta01@aar,androidx.media:media:1.1.0@aar,com.android.billingclient:billing:7.1.1@aar,androidx.biometric:biometric:1.1.0@aar,com.google.android.gms:play-services-auth:21.0.0@aar,com.google.android.gms:play-services-location:21.2.0@aar,com.google.firebase:firebase-messaging:24.1.2@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.gms:play-services-fido:20.0.1@aar,com.google.firebase:firebase-analytics:22.5.0@aar,com.google.android.gms:play-services-measurement:22.5.0@aar,com.google.android.gms:play-services-measurement-sdk:22.5.0@aar,com.google.android.gms:play-services-measurement-impl:22.5.0@aar,com.google.android.gms:play-services-base:18.5.0@aar,androidx.preference:preference:1.2.1@aar,androidx.appcompat:appcompat:1.2.0@aar,androidx.fragment:fragment-ktx:1.7.1@aar,com.google.firebase:firebase-crashlytics:19.4.4@aar,com.google.android.gms:play-services-measurement-api:22.5.0@aar,com.google.firebase:firebase-sessions:2.1.2@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.firebase:firebase-installations-interop:17.2.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.android.gms:play-services-cloud-messaging:17.2.0@aar,androidx.work:work-runtime:2.9.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.recyclerview:recyclerview:1.0.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.1.0@aar,androidx.activity:activity-ktx:1.9.3@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-service:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.room:room-ktx:2.5.0@aar,androidx.window:window-java:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.window:window:1.2.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,androidx.concurrent:concurrent-futures-ktx:1.1.0@jar,androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar,androidx.datastore:datastore-preferences-proto:1.1.3@jar,androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar,androidx.datastore:datastore-core-okio-jvm:1.1.3@jar,androidx.datastore:datastore-core-android:1.1.3@aar,androidx.datastore:datastore-preferences-android:1.1.3@aar,androidx.datastore:datastore-android:1.1.3@aar,androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar,androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar,androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.firebase:firebase-measurement-connector:20.0.1@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:22.5.0@aar,com.google.android.gms:play-services-measurement-base:22.5.0@aar,com.google.android.gms:play-services-basement:18.5.0@aar,androidx.fragment:fragment:1.7.1@aar,androidx.fragment:fragment:1.7.1@aar,androidx.activity:activity:1.9.3@aar,androidx.activity:activity:1.9.3@aar,androidx.browser:browser:1.8.0@aar,androidx.media3:media3-extractor:1.4.1@aar,androidx.media3:media3-container:1.4.1@aar,androidx.media3:media3-datasource:1.4.1@aar,androidx.media3:media3-decoder:1.4.1@aar,androidx.media3:media3-database:1.4.1@aar,androidx.media3:media3-common:1.4.1@aar,androidx.media3:media3-exoplayer-hls:1.4.1@aar,androidx.media3:media3-exoplayer-dash:1.4.1@aar,androidx.media3:media3-exoplayer-rtsp:1.4.1@aar,androidx.media3:media3-exoplayer-smoothstreaming:1.4.1@aar,androidx.media3:media3-exoplayer:1.4.1@aar,androidx.webkit:webkit:1.12.1@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.appcompat:appcompat-resources:1.2.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.transition:transition:1.4.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.core:core-ktx:1.13.1@aar,androidx.room:room-runtime:2.5.0@aar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.tracing:tracing-ktx:1.2.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.security:security-crypto:1.1.0-alpha06@aar,com.google.crypto.tink:tink-android:1.9.0@jar,androidx.exifinterface:exifinterface:1.3.7@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.1.0@jar,androidx.collection:collection:1.2.0@jar,androidx.sqlite:sqlite-framework:2.3.0@aar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-datatransport:19.0.0@aar,com.google.android.datatransport:transport-backend-cct:3.3.0@aar,com.google.android.datatransport:transport-runtime:3.3.0@aar,com.google.android.datatransport:transport-api:3.2.0@aar,com.google.firebase:firebase-config-interop:16.0.1@aar,com.google.firebase:firebase-encoders-json:18.0.1@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.room:room-common:2.5.0@jar,androidx.sqlite:sqlite:2.3.0@aar,androidx.window.extensions.core:core:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.9.1@jar,com.squareup.okio:okio-jvm:3.4.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.0@jar,io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,org.jetbrains:annotations:23.0.0@jar,org.microg:safe-parcel:1.7.0@aar,com.google.guava:guava:33.4.0-android@jar,com.google.code.gson:gson:2.10.1@jar,com.getkeepsafe.relinker:relinker:1.4.5@aar,com.google.auto.value:auto-value-annotations:1.6.3@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,org.jspecify:jspecify:1.0.0@jar,com.google.guava:failureaccess:1.0.2@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.43.0@jar,com.google.errorprone:error_prone_annotations:2.36.0@jar,com.google.j2objc:j2objc-annotations:3.0.0@jar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar">
    <dependency
        name="__local_aars__:D:\projects\12july\athkar\athkar_app\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
        simpleName="__local_aars__:D:\projects\12july\athkar\athkar_app\build\app\intermediates\flutter\release\libs.jar"/>
    <dependency
        name=":@@:app_settings::release"
        simpleName="artifacts::app_settings"/>
    <dependency
        name=":@@:battery_plus::release"
        simpleName="artifacts::battery_plus"/>
    <dependency
        name=":@@:camera_android_camerax::release"
        simpleName="artifacts::camera_android_camerax"/>
    <dependency
        name=":@@:device_info_plus::release"
        simpleName="artifacts::device_info_plus"/>
    <dependency
        name=":@@:firebase_analytics::release"
        simpleName="artifacts::firebase_analytics"/>
    <dependency
        name=":@@:network_info_plus::release"
        simpleName="artifacts::network_info_plus"/>
    <dependency
        name=":@@:wakelock_plus::release"
        simpleName="artifacts::wakelock_plus"/>
    <dependency
        name=":@@:package_info_plus::release"
        simpleName="artifacts::package_info_plus"/>
    <dependency
        name=":@@:sensors_plus::release"
        simpleName="artifacts::sensors_plus"/>
    <dependency
        name=":@@:share_plus::release"
        simpleName="artifacts::share_plus"/>
    <dependency
        name=":@@:shared_preferences_android::release"
        simpleName="artifacts::shared_preferences_android"/>
    <dependency
        name=":@@:webview_flutter_android::release"
        simpleName="artifacts::webview_flutter_android"/>
    <dependency
        name=":@@:workmanager_android::release"
        simpleName="artifacts::workmanager_android"/>
    <dependency
        name=":@@:audioplayers_android::release"
        simpleName="artifacts::audioplayers_android"/>
    <dependency
        name=":@@:file_picker::release"
        simpleName="artifacts::file_picker"/>
    <dependency
        name=":@@:flutter_local_notifications::release"
        simpleName="artifacts::flutter_local_notifications"/>
    <dependency
        name=":@@:geolocator_android::release"
        simpleName="artifacts::geolocator_android"/>
    <dependency
        name=":@@:image_picker_android::release"
        simpleName="artifacts::image_picker_android"/>
    <dependency
        name=":@@:local_auth_android::release"
        simpleName="artifacts::local_auth_android"/>
    <dependency
        name=":@@:url_launcher_android::release"
        simpleName="artifacts::url_launcher_android"/>
    <dependency
        name=":@@:app_links::release"
        simpleName="artifacts::app_links"/>
    <dependency
        name=":@@:connectivity_plus::release"
        simpleName="artifacts::connectivity_plus"/>
    <dependency
        name=":@@:firebase_crashlytics::release"
        simpleName="artifacts::firebase_crashlytics"/>
    <dependency
        name=":@@:firebase_messaging::release"
        simpleName="artifacts::firebase_messaging"/>
    <dependency
        name=":@@:firebase_core::release"
        simpleName="artifacts::firebase_core"/>
    <dependency
        name=":@@:flutter_keyboard_visibility::release"
        simpleName="artifacts::flutter_keyboard_visibility"/>
    <dependency
        name=":@@:flutter_native_splash::release"
        simpleName="artifacts::flutter_native_splash"/>
    <dependency
        name=":@@:flutter_overlay_window::release"
        simpleName="artifacts::flutter_overlay_window"/>
    <dependency
        name=":@@:flutter_plugin_android_lifecycle::release"
        simpleName="artifacts::flutter_plugin_android_lifecycle"/>
    <dependency
        name=":@@:flutter_secure_storage::release"
        simpleName="artifacts::flutter_secure_storage"/>
    <dependency
        name=":@@:google_sign_in_android::release"
        simpleName="artifacts::google_sign_in_android"/>
    <dependency
        name=":@@:in_app_purchase_android::release"
        simpleName="artifacts::in_app_purchase_android"/>
    <dependency
        name=":@@:path_provider_android::release"
        simpleName="artifacts::path_provider_android"/>
    <dependency
        name=":@@:permission_handler_android::release"
        simpleName="artifacts::permission_handler_android"/>
    <dependency
        name=":@@:sign_in_with_apple::release"
        simpleName="artifacts::sign_in_with_apple"/>
    <dependency
        name=":@@:sqflite_android::release"
        simpleName="artifacts::sqflite_android"/>
    <dependency
        name=":@@:vibration::release"
        simpleName="artifacts::vibration"/>
    <dependency
        name=":@@:video_player_android::release"
        simpleName="artifacts::video_player_android"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="androidx.camera:camera-video:1.5.0-beta01@aar"
        simpleName="androidx.camera:camera-video"/>
    <dependency
        name="androidx.camera:camera-lifecycle:1.5.0-beta01@aar"
        simpleName="androidx.camera:camera-lifecycle"/>
    <dependency
        name="androidx.camera:camera-camera2:1.5.0-beta01@aar"
        simpleName="androidx.camera:camera-camera2"/>
    <dependency
        name="androidx.camera:camera-core:1.5.0-beta01@aar"
        simpleName="androidx.camera:camera-core"/>
    <dependency
        name="androidx.media:media:1.1.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="com.android.billingclient:billing:7.1.1@aar"
        simpleName="com.android.billingclient:billing"/>
    <dependency
        name="androidx.biometric:biometric:1.1.0@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.0.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="com.google.android.gms:play-services-location:21.2.0@aar"
        simpleName="com.google.android.gms:play-services-location"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.1.2@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.0.1@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.firebase:firebase-analytics:22.5.0@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:22.5.0@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:22.5.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:22.5.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="androidx.preference:preference:1.2.1@aar"
        simpleName="androidx.preference:preference"/>
    <dependency
        name="androidx.appcompat:appcompat:1.2.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.7.1@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="com.google.firebase:firebase-crashlytics:19.4.4@aar"
        simpleName="com.google.firebase:firebase-crashlytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:22.5.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name="com.google.firebase:firebase-sessions:2.1.2@aar"
        simpleName="com.google.firebase:firebase-sessions"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="androidx.work:work-runtime:2.9.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.0.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity-ktx:1.9.3@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.room:room-ktx:2.5.0@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures-ktx"/>
    <dependency
        name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-external-protobuf"/>
    <dependency
        name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-proto"/>
    <dependency
        name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-preferences-core-jvm"/>
    <dependency
        name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
        simpleName="androidx.datastore:datastore-core-okio-jvm"/>
    <dependency
        name="androidx.datastore:datastore-core-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-core-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-preferences-android"/>
    <dependency
        name="androidx.datastore:datastore-android:1.1.3@aar"
        simpleName="androidx.datastore:datastore-android"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:20.0.1@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:22.5.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:22.5.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.3@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.browser:browser:1.8.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.media3:media3-extractor:1.4.1@aar"
        simpleName="androidx.media3:media3-extractor"/>
    <dependency
        name="androidx.media3:media3-container:1.4.1@aar"
        simpleName="androidx.media3:media3-container"/>
    <dependency
        name="androidx.media3:media3-datasource:1.4.1@aar"
        simpleName="androidx.media3:media3-datasource"/>
    <dependency
        name="androidx.media3:media3-decoder:1.4.1@aar"
        simpleName="androidx.media3:media3-decoder"/>
    <dependency
        name="androidx.media3:media3-database:1.4.1@aar"
        simpleName="androidx.media3:media3-database"/>
    <dependency
        name="androidx.media3:media3-common:1.4.1@aar"
        simpleName="androidx.media3:media3-common"/>
    <dependency
        name="androidx.media3:media3-exoplayer-hls:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer-hls"/>
    <dependency
        name="androidx.media3:media3-exoplayer-dash:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer-dash"/>
    <dependency
        name="androidx.media3:media3-exoplayer-rtsp:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer-rtsp"/>
    <dependency
        name="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer-smoothstreaming"/>
    <dependency
        name="androidx.media3:media3-exoplayer:1.4.1@aar"
        simpleName="androidx.media3:media3-exoplayer"/>
    <dependency
        name="androidx.webkit:webkit:1.12.1@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.2.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.transition:transition:1.4.1@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.room:room-runtime:2.5.0@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.2.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.security:security-crypto:1.1.0-alpha06@aar"
        simpleName="androidx.security:security-crypto"/>
    <dependency
        name="com.google.crypto.tink:tink-android:1.9.0@jar"
        simpleName="com.google.crypto.tink:tink-android"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.7@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection:1.2.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.3.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:19.0.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.3.0@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.3.0@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.2.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-config-interop:16.0.1@aar"
        simpleName="com.google.firebase:firebase-config-interop"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.1@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.room:room-common:2.5.0@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.sqlite:sqlite:2.3.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.window.extensions.core:core:1.0.0@aar"
        simpleName="androidx.window.extensions.core:core"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.4.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:armeabi_v7a_release"/>
    <dependency
        name="io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:arm64_v8a_release"/>
    <dependency
        name="io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:x86_64_release"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="org.microg:safe-parcel:1.7.0@aar"
        simpleName="org.microg:safe-parcel"/>
    <dependency
        name="com.google.guava:guava:33.4.0-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
    <dependency
        name="com.google.auto.value:auto-value-annotations:1.6.3@jar"
        simpleName="com.google.auto.value:auto-value-annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.2@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.43.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.36.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:3.0.0@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
  </package>
</dependencies>
