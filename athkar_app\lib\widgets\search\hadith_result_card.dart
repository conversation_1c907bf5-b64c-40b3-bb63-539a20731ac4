import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../models/search_models.dart';
import '../../theme/app_theme.dart';

class HadithResultCard extends StatelessWidget {
  final HadithSearchResult result;

  const HadithResultCard({
    super.key,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // Show hadith details in a dialog
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text(result.collectionName),
              content: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (result.arabicText.isNotEmpty) ...[
                      Text(
                        result.arabicText,
                        style: const TextStyle(
                          fontSize: 16,
                          fontFamily: 'Amiri',
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                      const SizedBox(height: 12),
                    ],
                    if (result.translation.isNotEmpty) ...[
                      Text(
                        result.translation,
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 8),
                    ],
                    if (result.narrator.isNotEmpty)
                      Text(
                        'الراوي: ${result.narrator}',
                        style: TextStyle(
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                          color: Colors.grey[600],
                        ),
                      ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إغلاق'),
                ),
              ],
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.book,
                      color: AppTheme.primaryGreen,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          result.collectionName,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryGreen,
                          ),
                        ),
                        Text(
                          '${languageService.isArabic ? 'حديث رقم' : 'Hadith No.'} ${result.hadithNumber}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (result.grade.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getGradeColor(result.grade).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _getGradeColor(result.grade),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        result.grade,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: _getGradeColor(result.grade),
                        ),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Arabic Text
              if (result.arabicText.isNotEmpty) ...[
                Text(
                  result.arabicText,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.5,
                    fontFamily: 'Amiri',
                  ),
                  textDirection: TextDirection.rtl,
                  textAlign: TextAlign.right,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],
              
              // Translation
              if (result.translation.isNotEmpty) ...[
                Text(
                  result.translation,
                  style: TextStyle(
                    fontSize: 14,
                    height: 1.4,
                    color: Colors.grey[800],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],
              
              // Narrator
              if (result.narrator.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      size: 14,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        result.narrator,
                        style: TextStyle(
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
              
              const SizedBox(height: 12),
              
              // Matched Fields
              if (result.matchedFields.isNotEmpty)
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: result.matchedFields.map((field) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getFieldName(field, languageService.isArabic),
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey[700],
                        ),
                      ),
                    );
                  }).toList(),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getGradeColor(String grade) {
    switch (grade.toLowerCase()) {
      case 'صحيح':
      case 'sahih':
        return Colors.green;
      case 'حسن':
      case 'hasan':
        return Colors.blue;
      case 'ضعيف':
      case 'daeef':
        return Colors.orange;
      case 'موضوع':
      case 'mawdoo':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getFieldName(String field, bool isArabic) {
    switch (field) {
      case 'arabic':
        return isArabic ? 'النص العربي' : 'Arabic Text';
      case 'translation':
        return isArabic ? 'الترجمة' : 'Translation';
      case 'narrator':
        return isArabic ? 'الراوي' : 'Narrator';
      default:
        return field;
    }
  }
}
