import 'package:json_annotation/json_annotation.dart';

part 'community_models.g.dart';

enum CommunityRole {
  member,
  moderator,
  admin,
}

enum ChallengeType {
  dailyAthkar,
  weeklyGoal,
  monthlyChallenge,
  streakChallenge,
  communityGoal,
}

enum LeaderboardType {
  athkarCount,
  streakDays,
  experiencePoints,
  challengesCompleted,
}

enum LeaderboardPeriod {
  daily,
  weekly,
  monthly,
  yearly,
  allTime,
}

enum PostType {
  text,
  achievement,
  question,
  inspiration,
  athkarShare,
}

enum InteractionType {
  like,
  share,
  comment,
  report,
}

@JsonSerializable()
class UserProfile {
  final String id;
  final String userId;
  final String displayName;
  final String? bio;
  final String? avatarUrl;
  final int level;
  final int experiencePoints;
  final int totalAthkarCompleted;
  final int streakDays;
  final List<String> badges;
  final PrivacySettings privacySettings;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProfile({
    required this.id,
    required this.userId,
    required this.displayName,
    this.bio,
    this.avatarUrl,
    this.level = 1,
    this.experiencePoints = 0,
    this.totalAthkarCompleted = 0,
    this.streakDays = 0,
    this.badges = const [],
    PrivacySettings? privacySettings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : privacySettings = privacySettings ?? PrivacySettings.defaultSettings(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory UserProfile.fromJson(Map<String, dynamic> json) => _$UserProfileFromJson(json);
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);
}

@JsonSerializable()
class PrivacySettings {
  final bool showProfile;
  final bool showProgress;
  final bool showBadges;
  final bool allowMessages;
  final bool showOnLeaderboard;

  const PrivacySettings({
    this.showProfile = true,
    this.showProgress = true,
    this.showBadges = true,
    this.allowMessages = true,
    this.showOnLeaderboard = true,
  });

  factory PrivacySettings.defaultSettings() => const PrivacySettings();

  factory PrivacySettings.fromJson(Map<String, dynamic> json) => _$PrivacySettingsFromJson(json);
  Map<String, dynamic> toJson() => _$PrivacySettingsToJson(this);
}

@JsonSerializable()
class Community {
  final String id;
  final String name;
  final String description;
  final String category;
  final String? imageUrl;
  final int memberCount;
  final bool isPrivate;
  final String adminUserId;
  final DateTime createdAt;
  final DateTime updatedAt;

  Community({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    this.imageUrl,
    this.memberCount = 0,
    this.isPrivate = false,
    required this.adminUserId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory Community.fromJson(Map<String, dynamic> json) => _$CommunityFromJson(json);
  Map<String, dynamic> toJson() => _$CommunityToJson(this);
}

@JsonSerializable()
class CommunityMember {
  final String id;
  final String communityId;
  final String userId;
  final CommunityRole role;
  final DateTime joinedAt;
  final bool isActive;

  CommunityMember({
    required this.id,
    required this.communityId,
    required this.userId,
    this.role = CommunityRole.member,
    DateTime? joinedAt,
    this.isActive = true,
  }) : joinedAt = joinedAt ?? DateTime.now();

  factory CommunityMember.fromJson(Map<String, dynamic> json) => _$CommunityMemberFromJson(json);
  Map<String, dynamic> toJson() => _$CommunityMemberToJson(this);
}

@JsonSerializable()
class Challenge {
  final String id;
  final String title;
  final String description;
  final ChallengeType type;
  final int targetValue;
  final int durationDays;
  final DateTime startDate;
  final DateTime endDate;
  final int rewardPoints;
  final String? badgeId;
  final bool isGlobal;
  final String? communityId;
  final String createdBy;
  final int participantCount;
  final DateTime createdAt;

  Challenge({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.targetValue,
    required this.durationDays,
    required this.startDate,
    required this.endDate,
    required this.rewardPoints,
    this.badgeId,
    this.isGlobal = false,
    this.communityId,
    required this.createdBy,
    this.participantCount = 0,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory Challenge.fromJson(Map<String, dynamic> json) => _$ChallengeFromJson(json);
  Map<String, dynamic> toJson() => _$ChallengeToJson(this);

  bool get isActive {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate);
  }

  Duration get timeRemaining {
    final now = DateTime.now();
    if (now.isAfter(endDate)) return Duration.zero;
    return endDate.difference(now);
  }
}

@JsonSerializable()
class ChallengeParticipant {
  final String id;
  final String challengeId;
  final String userId;
  final int currentProgress;
  final bool isCompleted;
  final DateTime joinedAt;
  final DateTime? completedAt;

  ChallengeParticipant({
    required this.id,
    required this.challengeId,
    required this.userId,
    this.currentProgress = 0,
    this.isCompleted = false,
    DateTime? joinedAt,
    this.completedAt,
  }) : joinedAt = joinedAt ?? DateTime.now();

  factory ChallengeParticipant.fromJson(Map<String, dynamic> json) => _$ChallengeParticipantFromJson(json);
  Map<String, dynamic> toJson() => _$ChallengeParticipantToJson(this);
}

@JsonSerializable()
class LeaderboardEntry {
  final String id;
  final LeaderboardType type;
  final LeaderboardPeriod period;
  final String userId;
  final int score;
  final int rank;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;

  LeaderboardEntry({
    required this.id,
    required this.type,
    required this.period,
    required this.userId,
    required this.score,
    required this.rank,
    this.metadata = const {},
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) => _$LeaderboardEntryFromJson(json);
  Map<String, dynamic> toJson() => _$LeaderboardEntryToJson(this);
}

@JsonSerializable()
class SocialPost {
  final String id;
  final String userId;
  final String? communityId;
  final String content;
  final PostType type;
  final String? athkarId;
  final String? imageUrl;
  final int likeCount;
  final int commentCount;
  final int shareCount;
  final bool isPinned;
  final DateTime createdAt;
  final DateTime updatedAt;

  SocialPost({
    required this.id,
    required this.userId,
    this.communityId,
    required this.content,
    required this.type,
    this.athkarId,
    this.imageUrl,
    this.likeCount = 0,
    this.commentCount = 0,
    this.shareCount = 0,
    this.isPinned = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory SocialPost.fromJson(Map<String, dynamic> json) => _$SocialPostFromJson(json);
  Map<String, dynamic> toJson() => _$SocialPostToJson(this);
}

@JsonSerializable()
class PostInteraction {
  final String id;
  final String postId;
  final String userId;
  final InteractionType type;
  final DateTime createdAt;

  PostInteraction({
    required this.id,
    required this.postId,
    required this.userId,
    required this.type,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory PostInteraction.fromJson(Map<String, dynamic> json) => _$PostInteractionFromJson(json);
  Map<String, dynamic> toJson() => _$PostInteractionToJson(this);
}

@JsonSerializable()
class Comment {
  final String id;
  final String postId;
  final String userId;
  final String content;
  final String? parentCommentId;
  final int likeCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  Comment({
    required this.id,
    required this.postId,
    required this.userId,
    required this.content,
    this.parentCommentId,
    this.likeCount = 0,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory Comment.fromJson(Map<String, dynamic> json) => _$CommentFromJson(json);
  Map<String, dynamic> toJson() => _$CommentToJson(this);
}

@JsonSerializable()
class Badge {
  final String id;
  final String name;
  final String description;
  final String iconUrl;
  final String category;
  final int rarity; // 1-5, 5 being rarest
  final Map<String, dynamic> criteria;

  const Badge({
    required this.id,
    required this.name,
    required this.description,
    required this.iconUrl,
    required this.category,
    required this.rarity,
    this.criteria = const {},
  });

  factory Badge.fromJson(Map<String, dynamic> json) => _$BadgeFromJson(json);
  Map<String, dynamic> toJson() => _$BadgeToJson(this);
}

@JsonSerializable()
class Achievement {
  final String id;
  final String userId;
  final String badgeId;
  final DateTime unlockedAt;
  final Map<String, dynamic> metadata;

  Achievement({
    required this.id,
    required this.userId,
    required this.badgeId,
    DateTime? unlockedAt,
    this.metadata = const {},
  }) : unlockedAt = unlockedAt ?? DateTime.now();

  factory Achievement.fromJson(Map<String, dynamic> json) => _$AchievementFromJson(json);
  Map<String, dynamic> toJson() => _$AchievementToJson(this);
}

@JsonSerializable()
class Friendship {
  final String id;
  final String userId1;
  final String userId2;
  final FriendshipStatus status;
  final DateTime createdAt;
  final DateTime? acceptedAt;

  Friendship({
    required this.id,
    required this.userId1,
    required this.userId2,
    this.status = FriendshipStatus.pending,
    DateTime? createdAt,
    this.acceptedAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory Friendship.fromJson(Map<String, dynamic> json) => _$FriendshipFromJson(json);
  Map<String, dynamic> toJson() => _$FriendshipToJson(this);
}

enum FriendshipStatus {
  pending,
  accepted,
  blocked,
}

@JsonSerializable()
class CommunityStats {
  final String communityId;
  final int totalMembers;
  final int activeMembers;
  final int totalPosts;
  final int totalChallenges;
  final double engagementRate;
  final DateTime lastUpdated;

  CommunityStats({
    required this.communityId,
    required this.totalMembers,
    required this.activeMembers,
    required this.totalPosts,
    required this.totalChallenges,
    required this.engagementRate,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  factory CommunityStats.fromJson(Map<String, dynamic> json) => _$CommunityStatsFromJson(json);
  Map<String, dynamic> toJson() => _$CommunityStatsToJson(this);
}
