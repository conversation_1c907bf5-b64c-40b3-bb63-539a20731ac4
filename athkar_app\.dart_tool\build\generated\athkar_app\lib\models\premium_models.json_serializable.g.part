// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PremiumStatus _$PremiumStatusFromJson(Map<String, dynamic> json) =>
    PremiumStatus(
      isPremium: json['isPremium'] as bool,
      planId: json['planId'] as String?,
      planName: json['planName'] as String?,
      features:
          (json['features'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      purchaseDate: json['purchaseDate'] == null
          ? null
          : DateTime.parse(json['purchaseDate'] as String),
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
      isLifetime: json['isLifetime'] as bool? ?? false,
      isCancelled: json['isCancelled'] as bool? ?? false,
      transactionId: json['transactionId'] as String?,
    );

Map<String, dynamic> _$PremiumStatusToJson(PremiumStatus instance) =>
    <String, dynamic>{
      'isPremium': instance.isPremium,
      'planId': instance.planId,
      'planName': instance.planName,
      'features': instance.features,
      'purchaseDate': instance.purchaseDate?.toIso8601String(),
      'expiryDate': instance.expiryDate?.toIso8601String(),
      'isLifetime': instance.isLifetime,
      'isCancelled': instance.isCancelled,
      'transactionId': instance.transactionId,
    };

SubscriptionPlan _$SubscriptionPlanFromJson(Map<String, dynamic> json) =>
    SubscriptionPlan(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      duration: json['duration'] == null
          ? null
          : Duration(microseconds: (json['duration'] as num).toInt()),
      features: (json['features'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      isPopular: json['isPopular'] as bool? ?? false,
      isLifetime: json['isLifetime'] as bool? ?? false,
      discount: (json['discount'] as num?)?.toDouble(),
      originalPrice: json['originalPrice'] as String?,
    );

Map<String, dynamic> _$SubscriptionPlanToJson(SubscriptionPlan instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'price': instance.price,
      'currency': instance.currency,
      'duration': instance.duration?.inMicroseconds,
      'features': instance.features,
      'isPopular': instance.isPopular,
      'isLifetime': instance.isLifetime,
      'discount': instance.discount,
      'originalPrice': instance.originalPrice,
    };

PremiumFeature _$PremiumFeatureFromJson(Map<String, dynamic> json) =>
    PremiumFeature(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: $enumDecode(_$PremiumFeatureCategoryEnumMap, json['category']),
      icon: json['icon'] as String?,
      isNew: json['isNew'] as bool? ?? false,
    );

Map<String, dynamic> _$PremiumFeatureToJson(PremiumFeature instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'category': _$PremiumFeatureCategoryEnumMap[instance.category]!,
      'icon': instance.icon,
      'isNew': instance.isNew,
    };

const _$PremiumFeatureCategoryEnumMap = {
  PremiumFeatureCategory.content: 'content',
  PremiumFeatureCategory.analytics: 'analytics',
  PremiumFeatureCategory.sync: 'sync',
  PremiumFeatureCategory.customization: 'customization',
  PremiumFeatureCategory.notifications: 'notifications',
  PremiumFeatureCategory.social: 'social',
  PremiumFeatureCategory.support: 'support',
};

UsageLimits _$UsageLimitsFromJson(Map<String, dynamic> json) => UsageLimits(
  maxCustomAthkar: (json['maxCustomAthkar'] as num).toInt(),
  maxCustomDuas: (json['maxCustomDuas'] as num).toInt(),
  maxBackups: (json['maxBackups'] as num).toInt(),
  maxThemes: (json['maxThemes'] as num).toInt(),
  analyticsRetentionDays: (json['analyticsRetentionDays'] as num).toInt(),
);

Map<String, dynamic> _$UsageLimitsToJson(UsageLimits instance) =>
    <String, dynamic>{
      'maxCustomAthkar': instance.maxCustomAthkar,
      'maxCustomDuas': instance.maxCustomDuas,
      'maxBackups': instance.maxBackups,
      'maxThemes': instance.maxThemes,
      'analyticsRetentionDays': instance.analyticsRetentionDays,
    };

PromotionalOffer _$PromotionalOfferFromJson(Map<String, dynamic> json) =>
    PromotionalOffer(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      discountPercentage: (json['discountPercentage'] as num).toDouble(),
      validUntil: DateTime.parse(json['validUntil'] as String),
      planIds: (json['planIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      promoCode: json['promoCode'] as String?,
    );

Map<String, dynamic> _$PromotionalOfferToJson(PromotionalOffer instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'discountPercentage': instance.discountPercentage,
      'validUntil': instance.validUntil.toIso8601String(),
      'planIds': instance.planIds,
      'promoCode': instance.promoCode,
    };

PurchaseHistory _$PurchaseHistoryFromJson(Map<String, dynamic> json) =>
    PurchaseHistory(
      id: json['id'] as String,
      planId: json['planId'] as String,
      planName: json['planName'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      purchaseDate: DateTime.parse(json['purchaseDate'] as String),
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
      transactionId: json['transactionId'] as String,
      status: $enumDecode(_$PurchaseStatusEnumMap, json['status']),
    );

Map<String, dynamic> _$PurchaseHistoryToJson(PurchaseHistory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'planId': instance.planId,
      'planName': instance.planName,
      'amount': instance.amount,
      'currency': instance.currency,
      'purchaseDate': instance.purchaseDate.toIso8601String(),
      'expiryDate': instance.expiryDate?.toIso8601String(),
      'transactionId': instance.transactionId,
      'status': _$PurchaseStatusEnumMap[instance.status]!,
    };

const _$PurchaseStatusEnumMap = {
  PurchaseStatus.pending: 'pending',
  PurchaseStatus.completed: 'completed',
  PurchaseStatus.failed: 'failed',
  PurchaseStatus.refunded: 'refunded',
  PurchaseStatus.cancelled: 'cancelled',
};

PaymentMethod _$PaymentMethodFromJson(Map<String, dynamic> json) =>
    PaymentMethod(
      id: json['id'] as String,
      type: json['type'] as String,
      displayName: json['displayName'] as String,
      last4Digits: json['last4Digits'] as String?,
      expiryMonth: json['expiryMonth'] as String?,
      expiryYear: json['expiryYear'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
    );

Map<String, dynamic> _$PaymentMethodToJson(PaymentMethod instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'displayName': instance.displayName,
      'last4Digits': instance.last4Digits,
      'expiryMonth': instance.expiryMonth,
      'expiryYear': instance.expiryYear,
      'isDefault': instance.isDefault,
    };

SubscriptionAnalytics _$SubscriptionAnalyticsFromJson(
  Map<String, dynamic> json,
) => SubscriptionAnalytics(
  totalSubscribers: (json['totalSubscribers'] as num).toInt(),
  activeSubscribers: (json['activeSubscribers'] as num).toInt(),
  newSubscribers: (json['newSubscribers'] as num).toInt(),
  cancelledSubscribers: (json['cancelledSubscribers'] as num).toInt(),
  monthlyRecurringRevenue: (json['monthlyRecurringRevenue'] as num).toDouble(),
  averageRevenuePerUser: (json['averageRevenuePerUser'] as num).toDouble(),
  churnRate: (json['churnRate'] as num).toDouble(),
  planDistribution: Map<String, int>.from(json['planDistribution'] as Map),
  revenueByPlan: (json['revenueByPlan'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(k, (e as num).toDouble()),
  ),
);

Map<String, dynamic> _$SubscriptionAnalyticsToJson(
  SubscriptionAnalytics instance,
) => <String, dynamic>{
  'totalSubscribers': instance.totalSubscribers,
  'activeSubscribers': instance.activeSubscribers,
  'newSubscribers': instance.newSubscribers,
  'cancelledSubscribers': instance.cancelledSubscribers,
  'monthlyRecurringRevenue': instance.monthlyRecurringRevenue,
  'averageRevenuePerUser': instance.averageRevenuePerUser,
  'churnRate': instance.churnRate,
  'planDistribution': instance.planDistribution,
  'revenueByPlan': instance.revenueByPlan,
};

FeatureUsageStats _$FeatureUsageStatsFromJson(Map<String, dynamic> json) =>
    FeatureUsageStats(
      featureId: json['featureId'] as String,
      featureName: json['featureName'] as String,
      totalUsage: (json['totalUsage'] as num).toInt(),
      uniqueUsers: (json['uniqueUsers'] as num).toInt(),
      usagePercentage: (json['usagePercentage'] as num).toDouble(),
      usageByPlan: Map<String, int>.from(json['usageByPlan'] as Map),
    );

Map<String, dynamic> _$FeatureUsageStatsToJson(FeatureUsageStats instance) =>
    <String, dynamic>{
      'featureId': instance.featureId,
      'featureName': instance.featureName,
      'totalUsage': instance.totalUsage,
      'uniqueUsers': instance.uniqueUsers,
      'usagePercentage': instance.usagePercentage,
      'usageByPlan': instance.usageByPlan,
    };
