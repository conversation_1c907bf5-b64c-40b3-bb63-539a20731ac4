import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/hadith_provider.dart';
import '../services/language_service.dart';

import '../theme/app_theme.dart';
import '../models/hadith_models.dart';
import 'hadith_collection_screen.dart';
import 'hadith_search_screen.dart';
import 'hadith_favorites_screen.dart';

class HadithScreen extends StatefulWidget {
  const HadithScreen({super.key});

  @override
  State<HadithScreen> createState() => _HadithScreenState();
}

class _HadithScreenState extends State<HadithScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  HadithData? _hadithOfTheDay;
  bool _isLoadingHadithOfDay = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeHadithProvider();
    _loadHadithOfTheDay();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeHadithProvider() async {
    final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
    if (!hadithProvider.isLoaded) {
      await hadithProvider.initialize();
    }
  }

  Future<void> _loadHadithOfTheDay() async {
    setState(() => _isLoadingHadithOfDay = true);
    try {
      final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
      final hadith = await hadithProvider.getHadithOfTheDay();
      setState(() => _hadithOfTheDay = hadith);
    } catch (e) {
      debugPrint('Error loading hadith of the day: $e');
    } finally {
      setState(() => _isLoadingHadithOfDay = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);


    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(languageService.isArabic ? 'الأحاديث النبوية' : 'Prophetic Hadiths'),
          backgroundColor: AppTheme.primaryGreen,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: Icon(languageService.backIcon),
            onPressed: () {
              if (mounted) Navigator.pop(context);
            },
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                if (!mounted) return;
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const HadithSearchScreen(),
                  ),
                );
              },
              tooltip: languageService.isArabic ? 'البحث' : 'Search',
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () async {
                final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
                await hadithProvider.refresh();
                await _loadHadithOfTheDay();
              },
              tooltip: languageService.isArabic ? 'تحديث' : 'Refresh',
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: [
              Tab(
                icon: const Icon(Icons.library_books),
                text: languageService.isArabic ? 'المجموعات' : 'Collections',
              ),
              Tab(
                icon: const Icon(Icons.favorite),
                text: languageService.isArabic ? 'المفضلة' : 'Favorites',
              ),
              Tab(
                icon: const Icon(Icons.today),
                text: languageService.isArabic ? 'اليوم' : 'Today',
              ),
            ],
          ),
        ),
        body: Consumer<HadithProvider>(
          builder: (context, hadithProvider, child) {
            if (hadithProvider.isLoading) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(color: AppTheme.primaryGreen),
                    SizedBox(height: 16),
                    Text('جاري تحميل الأحاديث...'),
                  ],
                ),
              );
            }

            if (hadithProvider.error != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      languageService.isArabic ? 'خطأ في تحميل الأحاديث' : 'Error loading Hadiths',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      hadithProvider.error!,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => hadithProvider.refresh(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryGreen,
                        foregroundColor: Colors.white,
                      ),
                      child: Text(languageService.isArabic ? 'إعادة المحاولة' : 'Retry'),
                    ),
                  ],
                ),
              );
            }

            return TabBarView(
              controller: _tabController,
              children: [
                _buildCollectionsTab(hadithProvider, languageService),
                _buildFavoritesTab(hadithProvider, languageService),
                _buildTodayTab(hadithProvider, languageService),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildCollectionsTab(HadithProvider hadithProvider, LanguageService languageService) {
    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: hadithProvider.collections.length,
      itemBuilder: (context, index) {
        final collection = hadithProvider.collections[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          elevation: 2,
          child: ListTile(
            leading: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen,
                borderRadius: BorderRadius.circular(25),
              ),
              child: Center(
                child: Text(
                  '${index + 1}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
            ),
            title: Text(
              languageService.isArabic ? collection.arabicName : collection.englishName,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageService.isArabic ? collection.arabicAuthor : collection.author,
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 4),
                Text(
                  '${collection.totalBooks} ${languageService.isArabic ? "كتاب" : "books"} • '
                  '${collection.totalHadiths} ${languageService.isArabic ? "حديث" : "hadiths"}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            trailing: Icon(
              languageService.isArabic ? Icons.arrow_back_ios : Icons.arrow_forward_ios,
              color: AppTheme.primaryGreen,
            ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => HadithCollectionScreen(collection: collection),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildFavoritesTab(HadithProvider hadithProvider, LanguageService languageService) {
    return const HadithFavoritesScreen();
  }

  Widget _buildTodayTab(HadithProvider hadithProvider, LanguageService languageService) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hadith of the Day Card
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.today,
                        color: AppTheme.primaryGreen,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        languageService.isArabic ? 'حديث اليوم' : 'Hadith of the Day',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryGreen,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_isLoadingHadithOfDay)
                    const Center(
                      child: CircularProgressIndicator(color: AppTheme.primaryGreen),
                    )
                  else if (_hadithOfTheDay != null)
                    _buildHadithCard(_hadithOfTheDay!, languageService)
                  else
                    Center(
                      child: Column(
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            languageService.isArabic ? 'لا يوجد حديث متاح' : 'No hadith available',
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Quick Actions
          Text(
            languageService.isArabic ? 'إجراءات سريعة' : 'Quick Actions',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildQuickActionCard(
                  icon: Icons.shuffle,
                  title: languageService.isArabic ? 'حديث عشوائي' : 'Random Hadith',
                  onTap: () async {
                    final randomHadith = await hadithProvider.getRandomHadith();
                    if (randomHadith != null && mounted) {
                      _showHadithDialog(randomHadith, languageService);
                    }
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickActionCard(
                  icon: Icons.search,
                  title: languageService.isArabic ? 'البحث المتقدم' : 'Advanced Search',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const HadithSearchScreen(),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHadithCard(HadithData hadith, LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            hadith.arabicText,
            style: const TextStyle(
              fontSize: 18,
              height: 1.8,
              fontWeight: FontWeight.w500,
            ),
            textDirection: TextDirection.rtl,
          ),
          const SizedBox(height: 12),
          if (hadith.englishText.isNotEmpty) ...[
            Text(
              hadith.englishText,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.5,
              ),
            ),
            const SizedBox(height: 12),
          ],
          Row(
            children: [
              Icon(
                Icons.person,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  languageService.isArabic ? hadith.arabicNarrator : hadith.narrator,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.book,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Text(
                hadith.reference,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              Consumer<HadithProvider>(
                builder: (context, hadithProvider, child) {
                  return IconButton(
                    icon: Icon(
                      hadithProvider.isInFavorites(
                        hadith.collectionId,
                        hadith.bookNumber,
                        hadith.hadithNumber,
                      )
                          ? Icons.favorite
                          : Icons.favorite_border,
                      color: AppTheme.primaryGreen,
                    ),
                    onPressed: () => _toggleFavorite(hadith),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: AppTheme.primaryGreen,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showHadithDialog(HadithData hadith, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'حديث عشوائي' : 'Random Hadith'),
        content: SingleChildScrollView(
          child: _buildHadithCard(hadith, languageService),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إغلاق' : 'Close'),
          ),
        ],
      ),
    );
  }

  void _toggleFavorite(HadithData hadith) async {
    final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
    final languageService = Provider.of<LanguageService>(context, listen: false);
    
    if (hadithProvider.isInFavorites(hadith.collectionId, hadith.bookNumber, hadith.hadithNumber)) {
      final favoriteId = '${hadith.collectionId}_${hadith.bookNumber}_${hadith.hadithNumber}';
      await hadithProvider.removeFromFavorites(favoriteId);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageService.isArabic ? 'تم إزالة الحديث من المفضلة' : 'Hadith removed from favorites'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      await hadithProvider.addToFavorites(
        hadith,
        languageService.isArabic ? 'حديث مفضل' : 'Favorite Hadith',
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageService.isArabic ? 'تم إضافة الحديث للمفضلة' : 'Hadith added to favorites'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    }
  }
}
