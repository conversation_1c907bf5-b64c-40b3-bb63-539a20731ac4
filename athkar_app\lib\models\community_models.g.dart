// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'community_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
  id: json['id'] as String,
  userId: json['userId'] as String,
  displayName: json['displayName'] as String,
  bio: json['bio'] as String?,
  avatarUrl: json['avatarUrl'] as String?,
  level: (json['level'] as num?)?.toInt() ?? 1,
  experiencePoints: (json['experiencePoints'] as num?)?.toInt() ?? 0,
  totalAthkarCompleted: (json['totalAthkarCompleted'] as num?)?.toInt() ?? 0,
  streakDays: (json['streakDays'] as num?)?.toInt() ?? 0,
  badges:
      (json['badges'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  privacySettings: json['privacySettings'] == null
      ? null
      : PrivacySettings.fromJson(
          json['privacySettings'] as Map<String, dynamic>,
        ),
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'displayName': instance.displayName,
      'bio': instance.bio,
      'avatarUrl': instance.avatarUrl,
      'level': instance.level,
      'experiencePoints': instance.experiencePoints,
      'totalAthkarCompleted': instance.totalAthkarCompleted,
      'streakDays': instance.streakDays,
      'badges': instance.badges,
      'privacySettings': instance.privacySettings,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

PrivacySettings _$PrivacySettingsFromJson(Map<String, dynamic> json) =>
    PrivacySettings(
      showProfile: json['showProfile'] as bool? ?? true,
      showProgress: json['showProgress'] as bool? ?? true,
      showBadges: json['showBadges'] as bool? ?? true,
      allowMessages: json['allowMessages'] as bool? ?? true,
      showOnLeaderboard: json['showOnLeaderboard'] as bool? ?? true,
    );

Map<String, dynamic> _$PrivacySettingsToJson(PrivacySettings instance) =>
    <String, dynamic>{
      'showProfile': instance.showProfile,
      'showProgress': instance.showProgress,
      'showBadges': instance.showBadges,
      'allowMessages': instance.allowMessages,
      'showOnLeaderboard': instance.showOnLeaderboard,
    };

Community _$CommunityFromJson(Map<String, dynamic> json) => Community(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  category: json['category'] as String,
  imageUrl: json['imageUrl'] as String?,
  memberCount: (json['memberCount'] as num?)?.toInt() ?? 0,
  isPrivate: json['isPrivate'] as bool? ?? false,
  adminUserId: json['adminUserId'] as String,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$CommunityToJson(Community instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'category': instance.category,
  'imageUrl': instance.imageUrl,
  'memberCount': instance.memberCount,
  'isPrivate': instance.isPrivate,
  'adminUserId': instance.adminUserId,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

CommunityMember _$CommunityMemberFromJson(Map<String, dynamic> json) =>
    CommunityMember(
      id: json['id'] as String,
      communityId: json['communityId'] as String,
      userId: json['userId'] as String,
      role:
          $enumDecodeNullable(_$CommunityRoleEnumMap, json['role']) ??
          CommunityRole.member,
      joinedAt: json['joinedAt'] == null
          ? null
          : DateTime.parse(json['joinedAt'] as String),
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$CommunityMemberToJson(CommunityMember instance) =>
    <String, dynamic>{
      'id': instance.id,
      'communityId': instance.communityId,
      'userId': instance.userId,
      'role': _$CommunityRoleEnumMap[instance.role]!,
      'joinedAt': instance.joinedAt.toIso8601String(),
      'isActive': instance.isActive,
    };

const _$CommunityRoleEnumMap = {
  CommunityRole.member: 'member',
  CommunityRole.moderator: 'moderator',
  CommunityRole.admin: 'admin',
};

Challenge _$ChallengeFromJson(Map<String, dynamic> json) => Challenge(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  type: $enumDecode(_$ChallengeTypeEnumMap, json['type']),
  targetValue: (json['targetValue'] as num).toInt(),
  durationDays: (json['durationDays'] as num).toInt(),
  startDate: DateTime.parse(json['startDate'] as String),
  endDate: DateTime.parse(json['endDate'] as String),
  rewardPoints: (json['rewardPoints'] as num).toInt(),
  badgeId: json['badgeId'] as String?,
  isGlobal: json['isGlobal'] as bool? ?? false,
  communityId: json['communityId'] as String?,
  createdBy: json['createdBy'] as String,
  participantCount: (json['participantCount'] as num?)?.toInt() ?? 0,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$ChallengeToJson(Challenge instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'type': _$ChallengeTypeEnumMap[instance.type]!,
  'targetValue': instance.targetValue,
  'durationDays': instance.durationDays,
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate.toIso8601String(),
  'rewardPoints': instance.rewardPoints,
  'badgeId': instance.badgeId,
  'isGlobal': instance.isGlobal,
  'communityId': instance.communityId,
  'createdBy': instance.createdBy,
  'participantCount': instance.participantCount,
  'createdAt': instance.createdAt.toIso8601String(),
};

const _$ChallengeTypeEnumMap = {
  ChallengeType.dailyAthkar: 'dailyAthkar',
  ChallengeType.weeklyGoal: 'weeklyGoal',
  ChallengeType.monthlyChallenge: 'monthlyChallenge',
  ChallengeType.streakChallenge: 'streakChallenge',
  ChallengeType.communityGoal: 'communityGoal',
};

ChallengeParticipant _$ChallengeParticipantFromJson(
  Map<String, dynamic> json,
) => ChallengeParticipant(
  id: json['id'] as String,
  challengeId: json['challengeId'] as String,
  userId: json['userId'] as String,
  currentProgress: (json['currentProgress'] as num?)?.toInt() ?? 0,
  isCompleted: json['isCompleted'] as bool? ?? false,
  joinedAt: json['joinedAt'] == null
      ? null
      : DateTime.parse(json['joinedAt'] as String),
  completedAt: json['completedAt'] == null
      ? null
      : DateTime.parse(json['completedAt'] as String),
);

Map<String, dynamic> _$ChallengeParticipantToJson(
  ChallengeParticipant instance,
) => <String, dynamic>{
  'id': instance.id,
  'challengeId': instance.challengeId,
  'userId': instance.userId,
  'currentProgress': instance.currentProgress,
  'isCompleted': instance.isCompleted,
  'joinedAt': instance.joinedAt.toIso8601String(),
  'completedAt': instance.completedAt?.toIso8601String(),
};

LeaderboardEntry _$LeaderboardEntryFromJson(Map<String, dynamic> json) =>
    LeaderboardEntry(
      id: json['id'] as String,
      type: $enumDecode(_$LeaderboardTypeEnumMap, json['type']),
      period: $enumDecode(_$LeaderboardPeriodEnumMap, json['period']),
      userId: json['userId'] as String,
      score: (json['score'] as num).toInt(),
      rank: (json['rank'] as num).toInt(),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$LeaderboardEntryToJson(LeaderboardEntry instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$LeaderboardTypeEnumMap[instance.type]!,
      'period': _$LeaderboardPeriodEnumMap[instance.period]!,
      'userId': instance.userId,
      'score': instance.score,
      'rank': instance.rank,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$LeaderboardTypeEnumMap = {
  LeaderboardType.athkarCount: 'athkarCount',
  LeaderboardType.streakDays: 'streakDays',
  LeaderboardType.experiencePoints: 'experiencePoints',
  LeaderboardType.challengesCompleted: 'challengesCompleted',
};

const _$LeaderboardPeriodEnumMap = {
  LeaderboardPeriod.daily: 'daily',
  LeaderboardPeriod.weekly: 'weekly',
  LeaderboardPeriod.monthly: 'monthly',
  LeaderboardPeriod.yearly: 'yearly',
  LeaderboardPeriod.allTime: 'allTime',
};

SocialPost _$SocialPostFromJson(Map<String, dynamic> json) => SocialPost(
  id: json['id'] as String,
  userId: json['userId'] as String,
  communityId: json['communityId'] as String?,
  content: json['content'] as String,
  type: $enumDecode(_$PostTypeEnumMap, json['type']),
  athkarId: json['athkarId'] as String?,
  imageUrl: json['imageUrl'] as String?,
  likeCount: (json['likeCount'] as num?)?.toInt() ?? 0,
  commentCount: (json['commentCount'] as num?)?.toInt() ?? 0,
  shareCount: (json['shareCount'] as num?)?.toInt() ?? 0,
  isPinned: json['isPinned'] as bool? ?? false,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$SocialPostToJson(SocialPost instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'communityId': instance.communityId,
      'content': instance.content,
      'type': _$PostTypeEnumMap[instance.type]!,
      'athkarId': instance.athkarId,
      'imageUrl': instance.imageUrl,
      'likeCount': instance.likeCount,
      'commentCount': instance.commentCount,
      'shareCount': instance.shareCount,
      'isPinned': instance.isPinned,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$PostTypeEnumMap = {
  PostType.text: 'text',
  PostType.achievement: 'achievement',
  PostType.question: 'question',
  PostType.inspiration: 'inspiration',
  PostType.athkarShare: 'athkarShare',
};

PostInteraction _$PostInteractionFromJson(Map<String, dynamic> json) =>
    PostInteraction(
      id: json['id'] as String,
      postId: json['postId'] as String,
      userId: json['userId'] as String,
      type: $enumDecode(_$InteractionTypeEnumMap, json['type']),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$PostInteractionToJson(PostInteraction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'postId': instance.postId,
      'userId': instance.userId,
      'type': _$InteractionTypeEnumMap[instance.type]!,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$InteractionTypeEnumMap = {
  InteractionType.like: 'like',
  InteractionType.share: 'share',
  InteractionType.comment: 'comment',
  InteractionType.report: 'report',
};

Comment _$CommentFromJson(Map<String, dynamic> json) => Comment(
  id: json['id'] as String,
  postId: json['postId'] as String,
  userId: json['userId'] as String,
  content: json['content'] as String,
  parentCommentId: json['parentCommentId'] as String?,
  likeCount: (json['likeCount'] as num?)?.toInt() ?? 0,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$CommentToJson(Comment instance) => <String, dynamic>{
  'id': instance.id,
  'postId': instance.postId,
  'userId': instance.userId,
  'content': instance.content,
  'parentCommentId': instance.parentCommentId,
  'likeCount': instance.likeCount,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

Badge _$BadgeFromJson(Map<String, dynamic> json) => Badge(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  iconUrl: json['iconUrl'] as String,
  category: json['category'] as String,
  rarity: (json['rarity'] as num).toInt(),
  criteria: json['criteria'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$BadgeToJson(Badge instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'iconUrl': instance.iconUrl,
  'category': instance.category,
  'rarity': instance.rarity,
  'criteria': instance.criteria,
};

Achievement _$AchievementFromJson(Map<String, dynamic> json) => Achievement(
  id: json['id'] as String,
  userId: json['userId'] as String,
  badgeId: json['badgeId'] as String,
  unlockedAt: json['unlockedAt'] == null
      ? null
      : DateTime.parse(json['unlockedAt'] as String),
  metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
);

Map<String, dynamic> _$AchievementToJson(Achievement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'badgeId': instance.badgeId,
      'unlockedAt': instance.unlockedAt.toIso8601String(),
      'metadata': instance.metadata,
    };

Friendship _$FriendshipFromJson(Map<String, dynamic> json) => Friendship(
  id: json['id'] as String,
  userId1: json['userId1'] as String,
  userId2: json['userId2'] as String,
  status:
      $enumDecodeNullable(_$FriendshipStatusEnumMap, json['status']) ??
      FriendshipStatus.pending,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  acceptedAt: json['acceptedAt'] == null
      ? null
      : DateTime.parse(json['acceptedAt'] as String),
);

Map<String, dynamic> _$FriendshipToJson(Friendship instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId1': instance.userId1,
      'userId2': instance.userId2,
      'status': _$FriendshipStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'acceptedAt': instance.acceptedAt?.toIso8601String(),
    };

const _$FriendshipStatusEnumMap = {
  FriendshipStatus.pending: 'pending',
  FriendshipStatus.accepted: 'accepted',
  FriendshipStatus.blocked: 'blocked',
};

CommunityStats _$CommunityStatsFromJson(Map<String, dynamic> json) =>
    CommunityStats(
      communityId: json['communityId'] as String,
      totalMembers: (json['totalMembers'] as num).toInt(),
      activeMembers: (json['activeMembers'] as num).toInt(),
      totalPosts: (json['totalPosts'] as num).toInt(),
      totalChallenges: (json['totalChallenges'] as num).toInt(),
      engagementRate: (json['engagementRate'] as num).toDouble(),
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$CommunityStatsToJson(CommunityStats instance) =>
    <String, dynamic>{
      'communityId': instance.communityId,
      'totalMembers': instance.totalMembers,
      'activeMembers': instance.activeMembers,
      'totalPosts': instance.totalPosts,
      'totalChallenges': instance.totalChallenges,
      'engagementRate': instance.engagementRate,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };
