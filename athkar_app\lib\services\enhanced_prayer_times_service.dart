import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/prayer_times_models.dart';
import '../services/notification_service.dart';

class EnhancedPrayerTimesService {
  static const String _baseUrl = 'https://api.aladhan.com/v1';
  static PrayerTimes? _cachedPrayerTimes;
  static DateTime? _lastFetchDate;
  static SharedPreferences? _prefs;
  
  // Jordan default coordinates (Amman)
  static const double _defaultLatitude = 31.9454;
  static const double _defaultLongitude = 35.9284;
  static const String _defaultCity = 'Amman, Jordan';
  
  // Jordan cities with coordinates
  static const Map<String, Map<String, double>> jordanCities = {
    'Amman': {'lat': 31.9454, 'lng': 35.9284},
    'Zarqa': {'lat': 32.0728, 'lng': 36.0876},
    'Irbid': {'lat': 32.5556, 'lng': 35.8500},
    'Russeifa': {'lat': 32.0167, 'lng': 36.0500},
    'Wadi as-Sir': {'lat': 31.9500, 'lng': 35.8167},
    'Aqaba': {'lat': 29.5320, 'lng': 35.0063},
    'Madaba': {'lat': 31.7167, 'lng': 35.7833},
    'As-Salt': {'lat': 32.0389, 'lng': 35.7272},
    'Mafraq': {'lat': 32.3436, 'lng': 36.2081},
    'Jerash': {'lat': 32.2811, 'lng': 35.8997},
    'Karak': {'lat': 31.1856, 'lng': 35.7050},
    'Tafilah': {'lat': 30.8378, 'lng': 35.6042},
    'Maan': {'lat': 30.1962, 'lng': 35.7340},
    'Ajloun': {'lat': 32.3328, 'lng': 35.7517},
  };

  static Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Get today's prayer times with adjustments
  static Future<PrayerTimes> getTodayPrayerTimes() async {
    final today = DateTime.now();
    
    // Check cache first
    if (_cachedPrayerTimes != null && 
        _lastFetchDate != null && 
        _isSameDay(_lastFetchDate!, today)) {
      return await _applyUserAdjustments(_cachedPrayerTimes!);
    }

    try {
      // Try to get current location, fallback to Jordan
      Position? position;
      try {
        position = await _getCurrentLocation();
      } catch (e) {
        debugPrint('Could not get current location, using Jordan default: $e');
        position = null;
      }
      
      final latitude = position?.latitude ?? _defaultLatitude;
      final longitude = position?.longitude ?? _defaultLongitude;
      
      // Try Jordan database first
      PrayerTimes? prayerTimes = await _getFromJordanDatabase(latitude, longitude, today);
      
      // Fallback to API if not in Jordan or database fails
      prayerTimes ??= await _fetchPrayerTimesFromAPI(latitude, longitude, today);
      
      if (prayerTimes != null) {
        _cachedPrayerTimes = prayerTimes;
        _lastFetchDate = today;
        return await _applyUserAdjustments(prayerTimes);
      }
    } catch (e) {
      debugPrint('Error fetching prayer times: $e');
    }

    // Final fallback to local calculation
    return await _applyUserAdjustments(_calculatePrayerTimesLocally());
  }

  /// Get prayer times from Jordan pre-calculated database
  static Future<PrayerTimes?> _getFromJordanDatabase(double lat, double lng, DateTime date) async {
    try {
      // Find closest Jordan city
      String? closestCity = _findClosestJordanCity(lat, lng);
      if (closestCity == null) return null;

      // Load pre-calculated times from assets
      final String data = await rootBundle.loadString('assets/data/jordan_prayer_times_2024.json');
      final Map<String, dynamic> jordanData = json.decode(data);
      
      final String dateKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      
      if (jordanData[closestCity] != null && jordanData[closestCity][dateKey] != null) {
        final dayData = jordanData[closestCity][dateKey];
        
        return PrayerTimes(
          location: '$closestCity, Jordan',
          date: date.toIso8601String().split('T')[0],
          fajr: dayData['fajr'] as String,
          sunrise: dayData['sunrise'] as String,
          dhuhr: dayData['dhuhr'] as String,
          asr: dayData['asr'] as String,
          maghrib: dayData['maghrib'] as String,
          isha: dayData['isha'] as String,
          latitude: lat,
          longitude: lng,
        );
      }
    } catch (e) {
      debugPrint('Error loading from Jordan database: $e');
    }
    return null;
  }

  /// Find closest Jordan city
  static String? _findClosestJordanCity(double lat, double lng) {
    double minDistance = double.infinity;
    String? closestCity;
    
    for (final entry in jordanCities.entries) {
      final cityLat = entry.value['lat']!;
      final cityLng = entry.value['lng']!;
      final distance = _calculateDistance(lat, lng, cityLat, cityLng);
      
      if (distance < minDistance) {
        minDistance = distance;
        closestCity = entry.key;
      }
    }
    
    // Only return if within reasonable distance (100km)
    return minDistance <= 100 ? closestCity : null;
  }

  /// Calculate distance between two points
  static double _calculateDistance(double lat1, double lng1, double lat2, double lng2) {
    const double earthRadius = 6371; // km
    final double dLat = _toRadians(lat2 - lat1);
    final double dLng = _toRadians(lng2 - lng1);
    
    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_toRadians(lat1)) * math.cos(_toRadians(lat2)) *
        math.sin(dLng / 2) * math.sin(dLng / 2);
    
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  static double _toRadians(double degrees) => degrees * (math.pi / 180);

  /// Get current location with multiple methods
  static Future<Position> _getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('Location services are disabled');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('Location permissions are permanently denied');
    }

    // Try high accuracy first, then fallback to lower accuracy
    try {
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );
    } catch (e) {
      debugPrint('High accuracy location failed, trying medium accuracy: $e');
      try {
        return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.medium,
          timeLimit: const Duration(seconds: 15),
        );
      } catch (e) {
        debugPrint('Medium accuracy location failed, trying low accuracy: $e');
        return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.low,
          timeLimit: const Duration(seconds: 20),
        );
      }
    }
  }

  /// Fetch prayer times from API
  static Future<PrayerTimes?> _fetchPrayerTimesFromAPI(double lat, double lng, DateTime date) async {
    try {
      final timestamp = date.millisecondsSinceEpoch ~/ 1000;
      final url = '$_baseUrl/timings/$timestamp?latitude=$lat&longitude=$lng&method=4';
      
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final timings = data['data']['timings'];
        final meta = data['data']['meta'];
        
        return PrayerTimes(
          location: meta['timezone'] ?? 'Unknown',
          date: date.toIso8601String().split('T')[0],
          fajr: timings['Fajr'] as String,
          sunrise: timings['Sunrise'] as String,
          dhuhr: timings['Dhuhr'] as String,
          asr: timings['Asr'] as String,
          maghrib: timings['Maghrib'] as String,
          isha: timings['Isha'] as String,
          latitude: lat,
          longitude: lng,
        );
      }
    } catch (e) {
      debugPrint('Error fetching from API: $e');
    }
    return null;
  }

  /// Apply user adjustments to prayer times
  static Future<PrayerTimes> _applyUserAdjustments(PrayerTimes baseTimes) async {
    final fajrAdjustment = _prefs?.getInt('fajr_adjustment') ?? 0;
    final dhuhrAdjustment = _prefs?.getInt('dhuhr_adjustment') ?? 0;
    final asrAdjustment = _prefs?.getInt('asr_adjustment') ?? 0;
    final maghribAdjustment = _prefs?.getInt('maghrib_adjustment') ?? 0;
    final ishaAdjustment = _prefs?.getInt('isha_adjustment') ?? 0;

    return PrayerTimes(
      location: baseTimes.location,
      date: baseTimes.date,
      fajr: _adjustTime(baseTimes.fajr, fajrAdjustment),
      sunrise: baseTimes.sunrise, // Sunrise is not adjustable
      dhuhr: _adjustTime(baseTimes.dhuhr, dhuhrAdjustment),
      asr: _adjustTime(baseTimes.asr, asrAdjustment),
      maghrib: _adjustTime(baseTimes.maghrib, maghribAdjustment),
      isha: _adjustTime(baseTimes.isha, ishaAdjustment),
      latitude: baseTimes.latitude,
      longitude: baseTimes.longitude,
    );
  }

  /// Adjust time by minutes
  static String _adjustTime(String timeString, int adjustmentMinutes) {
    if (adjustmentMinutes == 0) return timeString;

    final parts = timeString.split(':');
    if (parts.length != 2) return timeString;

    final hour = int.tryParse(parts[0]) ?? 0;
    final minute = int.tryParse(parts[1]) ?? 0;

    final totalMinutes = hour * 60 + minute + adjustmentMinutes;
    final adjustedHour = (totalMinutes ~/ 60) % 24;
    final adjustedMinute = totalMinutes % 60;

    return '${adjustedHour.toString().padLeft(2, '0')}:${adjustedMinute.toString().padLeft(2, '0')}';
  }

  /// Get prayer times with both original and adjusted times for verification
  static Future<Map<String, Map<String, String>>> getPrayerTimesWithAdjustments() async {
    final originalTimes = await _getOriginalPrayerTimes();
    final adjustedTimes = await getTodayPrayerTimes();
    
    return {
      'original': {
        'fajr': originalTimes.fajr,
        'dhuhr': originalTimes.dhuhr,
        'asr': originalTimes.asr,
        'maghrib': originalTimes.maghrib,
        'isha': originalTimes.isha,
      },
      'adjusted': {
        'fajr': adjustedTimes.fajr,
        'dhuhr': adjustedTimes.dhuhr,
        'asr': adjustedTimes.asr,
        'maghrib': adjustedTimes.maghrib,
        'isha': adjustedTimes.isha,
      },
    };
  }

  /// Get original prayer times without adjustments
  static Future<PrayerTimes> _getOriginalPrayerTimes() async {
    if (_cachedPrayerTimes != null) {
      return _cachedPrayerTimes!;
    }
    
    // Temporarily get times without applying adjustments
    final today = DateTime.now();
    Position? position;
    try {
      position = await _getCurrentLocation();
    } catch (e) {
      debugPrint('Could not get current location: $e');
    }
    
    final latitude = position?.latitude ?? _defaultLatitude;
    final longitude = position?.longitude ?? _defaultLongitude;
    
    return await _fetchPrayerTimesFromAPI(latitude, longitude, today) ?? 
           _calculatePrayerTimesLocally();
  }

  /// Save prayer time adjustment
  static Future<void> savePrayerAdjustment(String prayer, int minutes) async {
    await _prefs?.setInt('${prayer.toLowerCase()}_adjustment', minutes);
  }

  /// Get prayer time adjustment
  static Future<int> getPrayerAdjustment(String prayer) async {
    return _prefs?.getInt('${prayer.toLowerCase()}_adjustment') ?? 0;
  }

  /// Get all prayer adjustments
  static Future<Map<String, int>> getAllPrayerAdjustments() async {
    return {
      'fajr': _prefs?.getInt('fajr_adjustment') ?? 0,
      'dhuhr': _prefs?.getInt('dhuhr_adjustment') ?? 0,
      'asr': _prefs?.getInt('asr_adjustment') ?? 0,
      'maghrib': _prefs?.getInt('maghrib_adjustment') ?? 0,
      'isha': _prefs?.getInt('isha_adjustment') ?? 0,
    };
  }

  /// Schedule prayer notifications
  static Future<void> schedulePrayerNotifications() async {
    final prayerTimes = await getTodayPrayerTimes();
    final now = DateTime.now();
    
    final prayers = [
      ('Fajr', prayerTimes.fajr),
      ('Dhuhr', prayerTimes.dhuhr),
      ('Asr', prayerTimes.asr),
      ('Maghrib', prayerTimes.maghrib),
      ('Isha', prayerTimes.isha),
    ];

    for (final prayer in prayers) {
      final prayerTime = _parseTimeToDateTime(prayer.$2, now);
      if (prayerTime.isAfter(now)) {
        await NotificationService.schedulePrayerNotification(
          prayer.$1,
          prayerTime,
        );
      }
    }
  }

  /// Parse time string to DateTime
  static DateTime _parseTimeToDateTime(String timeString, DateTime date) {
    final parts = timeString.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);
    
    return DateTime(date.year, date.month, date.day, hour, minute);
  }

  /// Calculate prayer times locally (fallback)
  static PrayerTimes _calculatePrayerTimesLocally() {
    final now = DateTime.now();
    return PrayerTimes(
      location: _defaultCity,
      date: now.toIso8601String().split('T')[0],
      fajr: '05:30',
      sunrise: '06:45',
      dhuhr: '12:15',
      asr: '15:30',
      maghrib: '18:00',
      isha: '19:30',
      latitude: _defaultLatitude,
      longitude: _defaultLongitude,
    );
  }

  /// Check if two dates are the same day
  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}
