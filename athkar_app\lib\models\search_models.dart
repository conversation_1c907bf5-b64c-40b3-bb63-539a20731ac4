/// Base class for search results
abstract class SearchResult {
  final String id;
  final String title;
  final String content;
  final String query;
  final double relevanceScore;
  final DateTime searchedAt;

  SearchResult({
    required this.id,
    required this.title,
    required this.content,
    required this.query,
    required this.relevanceScore,
    required this.searchedAt,
  });
}

/// Hadith search result model
class HadithSearchResult extends SearchResult {
  final String collectionId;
  final String collectionName;
  final String bookNumber;
  final String hadithNumber;
  final String arabicText;
  final String translation;
  final String narrator;
  final String grade;
  final List<String> matchedFields;
  final Map<String, List<int>> highlightPositions;

  HadithSearchResult({
    required super.id,
    required super.title,
    required super.content,
    required super.query,
    required super.relevanceScore,
    required super.searchedAt,
    required this.collectionId,
    required this.collectionName,
    required this.bookNumber,
    required this.hadithNumber,
    required this.arabicText,
    required this.translation,
    required this.narrator,
    required this.grade,
    required this.matchedFields,
    required this.highlightPositions,
  });

  factory HadithSearchResult.fromMap(Map<String, dynamic> map, String query) {
    final arabicText = map['arabic_text'] as String? ?? '';
    final translation = map['translation'] as String? ?? '';
    final narrator = map['narrator'] as String? ?? '';
    
    // Calculate relevance score
    final relevanceScore = _calculateHadithRelevance(
      query: query,
      arabicText: arabicText,
      translation: translation,
      narrator: narrator,
    );
    
    // Find matched fields and highlight positions
    final matchedFields = <String>[];
    final highlightPositions = <String, List<int>>{};
    
    _findMatches(query, 'arabic', arabicText, matchedFields, highlightPositions);
    _findMatches(query, 'translation', translation, matchedFields, highlightPositions);
    _findMatches(query, 'narrator', narrator, matchedFields, highlightPositions);

    return HadithSearchResult(
      id: map['id'].toString(),
      title: 'حديث رقم ${map['hadith_number'] ?? ''}',
      content: arabicText.isNotEmpty ? arabicText : translation,
      query: query,
      relevanceScore: relevanceScore,
      searchedAt: DateTime.now(),
      collectionId: map['collection_id'] as String? ?? '',
      collectionName: map['collection_name'] as String? ?? '',
      bookNumber: map['book_number'] as String? ?? '',
      hadithNumber: map['hadith_number'] as String? ?? '',
      arabicText: arabicText,
      translation: translation,
      narrator: narrator,
      grade: map['grade'] as String? ?? '',
      matchedFields: matchedFields,
      highlightPositions: highlightPositions,
    );
  }

  static double _calculateHadithRelevance({
    required String query,
    required String arabicText,
    required String translation,
    required String narrator,
  }) {
    double score = 0.0;
    final queryLower = query.toLowerCase();
    
    // Arabic text matches (highest weight)
    if (arabicText.contains(query)) {
      score += 10.0;
    }
    
    // Translation matches (medium weight)
    if (translation.toLowerCase().contains(queryLower)) {
      score += 7.0;
    }
    
    // Narrator matches (lower weight)
    if (narrator.toLowerCase().contains(queryLower)) {
      score += 3.0;
    }
    
    // Exact word matches get bonus
    final words = queryLower.split(' ');
    for (final word in words) {
      if (word.length >= 3) {
        if (arabicText.contains(word)) score += 2.0;
        if (translation.toLowerCase().contains(word)) score += 1.5;
        if (narrator.toLowerCase().contains(word)) score += 1.0;
      }
    }
    
    return score;
  }

  static void _findMatches(
    String query,
    String fieldName,
    String fieldContent,
    List<String> matchedFields,
    Map<String, List<int>> highlightPositions,
  ) {
    if (fieldContent.isEmpty) return;
    
    final queryLower = query.toLowerCase();
    final contentLower = fieldContent.toLowerCase();
    
    if (contentLower.contains(queryLower)) {
      matchedFields.add(fieldName);
      
      final positions = <int>[];
      int index = 0;
      while (index < contentLower.length) {
        final foundIndex = contentLower.indexOf(queryLower, index);
        if (foundIndex == -1) break;
        positions.add(foundIndex);
        index = foundIndex + queryLower.length;
      }
      
      if (positions.isNotEmpty) {
        highlightPositions[fieldName] = positions;
      }
    }
  }
}

/// Quran search result model
class QuranSearchResult extends SearchResult {
  final int surahNumber;
  final String surahName;
  final int ayahNumber;
  final String arabicText;
  final String translation;
  final String tafseer;
  final List<String> matchedFields;
  final Map<String, List<int>> highlightPositions;

  QuranSearchResult({
    required super.id,
    required super.title,
    required super.content,
    required super.query,
    required super.relevanceScore,
    required super.searchedAt,
    required this.surahNumber,
    required this.surahName,
    required this.ayahNumber,
    required this.arabicText,
    required this.translation,
    required this.tafseer,
    required this.matchedFields,
    required this.highlightPositions,
  });

  factory QuranSearchResult.fromMap(Map<String, dynamic> map, String query) {
    final arabicText = map['arabic_text'] as String? ?? '';
    final translation = map['translation'] as String? ?? '';
    final tafseer = map['tafseer'] as String? ?? '';
    
    // Calculate relevance score
    final relevanceScore = _calculateQuranRelevance(
      query: query,
      arabicText: arabicText,
      translation: translation,
      tafseer: tafseer,
    );
    
    // Find matched fields and highlight positions
    final matchedFields = <String>[];
    final highlightPositions = <String, List<int>>{};
    
    HadithSearchResult._findMatches(query, 'arabic', arabicText, matchedFields, highlightPositions);
    HadithSearchResult._findMatches(query, 'translation', translation, matchedFields, highlightPositions);
    HadithSearchResult._findMatches(query, 'tafseer', tafseer, matchedFields, highlightPositions);

    return QuranSearchResult(
      id: map['id'].toString(),
      title: '${map['surah_name'] ?? 'سورة'} - آية ${map['ayah_number'] ?? ''}',
      content: arabicText.isNotEmpty ? arabicText : translation,
      query: query,
      relevanceScore: relevanceScore,
      searchedAt: DateTime.now(),
      surahNumber: map['surah_number'] as int? ?? 0,
      surahName: map['surah_name'] as String? ?? '',
      ayahNumber: map['ayah_number'] as int? ?? 0,
      arabicText: arabicText,
      translation: translation,
      tafseer: tafseer,
      matchedFields: matchedFields,
      highlightPositions: highlightPositions,
    );
  }

  static double _calculateQuranRelevance({
    required String query,
    required String arabicText,
    required String translation,
    required String tafseer,
  }) {
    double score = 0.0;
    final queryLower = query.toLowerCase();
    
    // Arabic text matches (highest weight)
    if (arabicText.contains(query)) {
      score += 15.0;
    }
    
    // Translation matches (high weight)
    if (translation.toLowerCase().contains(queryLower)) {
      score += 10.0;
    }
    
    // Tafseer matches (medium weight)
    if (tafseer.toLowerCase().contains(queryLower)) {
      score += 5.0;
    }
    
    // Exact word matches get bonus
    final words = queryLower.split(' ');
    for (final word in words) {
      if (word.length >= 3) {
        if (arabicText.contains(word)) score += 3.0;
        if (translation.toLowerCase().contains(word)) score += 2.0;
        if (tafseer.toLowerCase().contains(word)) score += 1.0;
      }
    }
    
    return score;
  }
}

/// Athkar search result model
class AthkarSearchResult extends SearchResult {
  final String categoryId;
  final String categoryName;
  final String arabicText;
  final String translation;
  final int repetitions;
  final List<String> matchedFields;
  final Map<String, List<int>> highlightPositions;

  AthkarSearchResult({
    required super.id,
    required super.title,
    required super.content,
    required super.query,
    required super.relevanceScore,
    required super.searchedAt,
    required this.categoryId,
    required this.categoryName,
    required this.arabicText,
    required this.translation,
    required this.repetitions,
    required this.matchedFields,
    required this.highlightPositions,
  });

  factory AthkarSearchResult.fromMap(Map<String, dynamic> map, String query) {
    final arabicText = map['arabic_text'] as String? ?? '';
    final translation = map['translation'] as String? ?? '';
    final title = map['title'] as String? ?? '';
    final description = map['description'] as String? ?? '';
    
    // Calculate relevance score
    final relevanceScore = _calculateAthkarRelevance(
      query: query,
      arabicText: arabicText,
      translation: translation,
      title: title,
      description: description,
    );
    
    // Find matched fields and highlight positions
    final matchedFields = <String>[];
    final highlightPositions = <String, List<int>>{};
    
    HadithSearchResult._findMatches(query, 'arabic', arabicText, matchedFields, highlightPositions);
    HadithSearchResult._findMatches(query, 'translation', translation, matchedFields, highlightPositions);
    HadithSearchResult._findMatches(query, 'title', title, matchedFields, highlightPositions);
    HadithSearchResult._findMatches(query, 'description', description, matchedFields, highlightPositions);

    return AthkarSearchResult(
      id: map['id'].toString(),
      title: title.isNotEmpty ? title : 'ذكر',
      content: arabicText.isNotEmpty ? arabicText : translation,
      query: query,
      relevanceScore: relevanceScore,
      searchedAt: DateTime.now(),
      categoryId: map['category_id'] as String? ?? '',
      categoryName: map['category_name'] as String? ?? '',
      arabicText: arabicText,
      translation: translation,
      repetitions: map['repetitions'] as int? ?? 1,
      matchedFields: matchedFields,
      highlightPositions: highlightPositions,
    );
  }

  static double _calculateAthkarRelevance({
    required String query,
    required String arabicText,
    required String translation,
    required String title,
    required String description,
  }) {
    double score = 0.0;
    final queryLower = query.toLowerCase();
    
    // Title matches (highest weight)
    if (title.toLowerCase().contains(queryLower)) {
      score += 12.0;
    }
    
    // Arabic text matches (high weight)
    if (arabicText.contains(query)) {
      score += 10.0;
    }
    
    // Translation matches (medium weight)
    if (translation.toLowerCase().contains(queryLower)) {
      score += 7.0;
    }
    
    // Description matches (lower weight)
    if (description.toLowerCase().contains(queryLower)) {
      score += 3.0;
    }
    
    return score;
  }
}

/// Search filter options
class SearchFilters {
  final List<String>? collections;
  final List<int>? surahs;
  final List<String>? categories;
  final String? narrator;
  final String? grade;
  final bool includeArabic;
  final bool includeTranslation;
  final bool includeTafseer;
  final bool includeNarrator;

  const SearchFilters({
    this.collections,
    this.surahs,
    this.categories,
    this.narrator,
    this.grade,
    this.includeArabic = true,
    this.includeTranslation = true,
    this.includeTafseer = true,
    this.includeNarrator = true,
  });

  SearchFilters copyWith({
    List<String>? collections,
    List<int>? surahs,
    List<String>? categories,
    String? narrator,
    String? grade,
    bool? includeArabic,
    bool? includeTranslation,
    bool? includeTafseer,
    bool? includeNarrator,
  }) {
    return SearchFilters(
      collections: collections ?? this.collections,
      surahs: surahs ?? this.surahs,
      categories: categories ?? this.categories,
      narrator: narrator ?? this.narrator,
      grade: grade ?? this.grade,
      includeArabic: includeArabic ?? this.includeArabic,
      includeTranslation: includeTranslation ?? this.includeTranslation,
      includeTafseer: includeTafseer ?? this.includeTafseer,
      includeNarrator: includeNarrator ?? this.includeNarrator,
    );
  }
}

/// Search history item
class SearchHistoryItem {
  final String query;
  final int searchCount;
  final DateTime lastSearched;
  final String searchType; // 'hadith', 'quran', 'athkar'

  SearchHistoryItem({
    required this.query,
    required this.searchCount,
    required this.lastSearched,
    required this.searchType,
  });

  factory SearchHistoryItem.fromMap(Map<String, dynamic> map) {
    return SearchHistoryItem(
      query: map['query'] as String,
      searchCount: map['search_count'] as int,
      lastSearched: DateTime.parse(map['last_searched'] as String),
      searchType: map['search_type'] as String? ?? 'general',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'query': query,
      'search_count': searchCount,
      'last_searched': lastSearched.toIso8601String(),
      'search_type': searchType,
    };
  }
}
