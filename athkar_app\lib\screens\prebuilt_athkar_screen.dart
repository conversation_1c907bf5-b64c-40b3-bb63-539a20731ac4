import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../services/language_service.dart';
import 'prebuilt_category_screen.dart';

class PrebuiltAthkarScreen extends StatefulWidget {
  const PrebuiltAthkarScreen({super.key});

  @override
  State<PrebuiltAthkarScreen> createState() => _PrebuiltAthkarScreenState();
}

class _PrebuiltAthkarScreenState extends State<PrebuiltAthkarScreen> {
  final List<PrebuiltAthkarCategory> _categories = [
    PrebuiltAthkarCategory(
      id: 'morning',
      title: 'Morning Athkar',
      subtitle: 'Start your day with remembrance',
      icon: MdiIcons.weatherSunny,
      color: const Color(0xFFFFB74D),
      routinesCount: 5,
    ),
    PrebuiltAthkarCategory(
      id: 'evening',
      title: 'Evening Athkar',
      subtitle: 'End your day with gratitude',
      icon: MdiIcons.weatherSunset,
      color: const Color(0xFFFF8A65),
      routinesCount: 4,
    ),
    PrebuiltAthkarCategory(
      id: 'prayer',
      title: 'After Prayer Athkar',
      subtitle: 'Supplications after Salah',
      icon: MdiIcons.mosque,
      color: const Color(0xFF81C784),
      routinesCount: 8,
    ),
    PrebuiltAthkarCategory(
      id: 'sleep',
      title: 'Before Sleep',
      subtitle: 'Peaceful night supplications',
      icon: MdiIcons.sleep,
      color: const Color(0xFF9575CD),
      routinesCount: 3,
    ),
    PrebuiltAthkarCategory(
      id: 'travel',
      title: 'Travel Duas',
      subtitle: 'Protection during journeys',
      icon: MdiIcons.airplane,
      color: const Color(0xFF64B5F6),
      routinesCount: 6,
    ),
    PrebuiltAthkarCategory(
      id: 'general',
      title: 'General Dhikr',
      subtitle: 'Daily remembrance of Allah',
      icon: MdiIcons.counter,
      color: const Color(0xFFF06292),
      routinesCount: 12,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Athkar Library'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () {
              _showDownloadDialog();
            },
          ),
        ],
      ),
      body: CustomScrollView(
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.85,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final category = _categories[index];
                  return _buildCategoryCard(category);
                },
                childCount: _categories.length,
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Featured Collections',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildFeaturedCollection(
                    'Complete Daily Athkar',
                    'Morning, Evening, and Prayer athkar in one collection',
                    MdiIcons.bookOpenPageVariant,
                    AppTheme.generalGradient,
                  ),
                  const SizedBox(height: 12),
                  _buildFeaturedCollection(
                    'Fortress of the Muslim',
                    'Complete collection from Hisn al-Muslim',
                    MdiIcons.shield,
                    AppTheme.nightGradient,
                  ),
                  const SizedBox(height: 12),
                  _buildFeaturedCollection(
                    'Quranic Duas',
                    'Supplications from the Holy Quran',
                    MdiIcons.bookOpen,
                    AppTheme.morningGradient,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(PrebuiltAthkarCategory category) {
    return GestureDetector(
      onTap: () => _openCategory(category),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: category.color.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Card(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  category.color.withValues(alpha: 0.8),
                  category.color,
                ],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    category.icon,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const Spacer(),
                Text(
                  category.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  category.subtitle,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 12,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${category.routinesCount} routines',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedCollection(
    String title,
    String description,
    IconData icon,
    List<Color> gradient,
  ) {
    return GestureDetector(
      onTap: () {
        // Navigate to morning athkar as featured collection
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const PrebuiltCategoryScreen(
              categoryId: 'morning',
              categoryTitle: 'Featured Collection',
              categoryColor: Color(0xFFFFB74D),
            ),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: gradient.map((c) => c.withValues(alpha: 0.1)).toList(),
          ),
          border: Border.all(
            color: gradient.first.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: gradient.first.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: gradient.first,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _openCategory(PrebuiltAthkarCategory category) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PrebuiltCategoryScreen(
          categoryId: category.id,
          categoryTitle: category.title,
          categoryColor: category.color,
        ),
      ),
    );
  }

  void _showDownloadDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Download Athkar'),
        content: const Text(
          'Download additional athkar collections from our online library.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final messenger = ScaffoldMessenger.of(context);
              Navigator.pop(context);
              // Implement download functionality
              try {
                // Show loading indicator
                messenger.showSnackBar(
                  const SnackBar(content: Text('Downloading athkar...')),
                );

                // Simulate download process
                await Future.delayed(const Duration(seconds: 2));

                // Show success message
                messenger.showSnackBar(
                  const SnackBar(content: Text('Athkar downloaded successfully!')),
                );
              } catch (e) {
                messenger.showSnackBar(
                  SnackBar(content: Text('Download failed: $e')),
                );
              }
            },
            child: const Text('Browse Library'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Prebuilt Athkar'),
        content: TextField(
          decoration: InputDecoration(
            hintText: Provider.of<LanguageService>(context, listen: false).isArabic ? 'ابحث في الأذكار...' : 'Search athkar...',
            prefixIcon: const Icon(Icons.search),
            border: const OutlineInputBorder(),
          ),
          enabled: false,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

class PrebuiltAthkarCategory {
  final String id;
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final int routinesCount;

  PrebuiltAthkarCategory({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.routinesCount,
  });
}
