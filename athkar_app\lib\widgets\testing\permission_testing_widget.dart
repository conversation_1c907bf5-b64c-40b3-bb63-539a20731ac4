import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../services/permissions_manager.dart';
import '../../theme/app_theme.dart';
import '../../screens/comprehensive_testing_screen.dart';

class PermissionTestingWidget extends StatefulWidget {
  final Function(TestingStatus) onStatusChanged;

  const PermissionTestingWidget({
    super.key,
    required this.onStatusChanged,
  });

  @override
  State<PermissionTestingWidget> createState() => _PermissionTestingWidgetState();
}

class _PermissionTestingWidgetState extends State<PermissionTestingWidget> {
  final PermissionsManager _permissionsManager = PermissionsManager();
  
  final Map<String, TestResult> _testResults = {};
  bool _isRunningTests = false;
  int _currentTestRound = 0;
  final int _totalRounds = 5;

  final List<PermissionTest> _permissionTests = [
    PermissionTest(
      id: 'notification_permission',
      nameAr: 'صلاحية الإشعارات',
      nameEn: 'Notification Permission',
      description: 'Test notification permission handling',
    ),
    PermissionTest(
      id: 'overlay_permission',
      nameAr: 'صلاحية النوافذ العائمة',
      nameEn: 'Overlay Permission',
      description: 'Test system overlay permission',
    ),
    PermissionTest(
      id: 'location_permission',
      nameAr: 'صلاحية الموقع',
      nameEn: 'Location Permission',
      description: 'Test location access permission',
    ),
    PermissionTest(
      id: 'storage_permission',
      nameAr: 'صلاحية التخزين',
      nameEn: 'Storage Permission',
      description: 'Test storage access permission',
    ),
    PermissionTest(
      id: 'audio_permission',
      nameAr: 'صلاحية الصوت',
      nameEn: 'Audio Permission',
      description: 'Test audio recording permission',
    ),
    PermissionTest(
      id: 'background_permission',
      nameAr: 'صلاحية العمل الخلفي',
      nameEn: 'Background Permission',
      description: 'Test background execution permission',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeTestResults();
  }

  void _initializeTestResults() {
    for (final test in _permissionTests) {
      _testResults[test.id] = TestResult.notStarted;
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(languageService),
          const SizedBox(height: 24),
          _buildTestControls(languageService),
          const SizedBox(height: 24),
          _buildPermissionOverview(languageService),
          const SizedBox(height: 24),
          _buildTestResults(languageService),
          const SizedBox(height: 24),
          if (_isRunningTests) _buildRoundProgress(languageService),
        ],
      ),
    );
  }

  Widget _buildHeader(LanguageService languageService) {
    final passedTests = _testResults.values.where((result) => result == TestResult.passed).length;
    final totalTests = _testResults.length;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.indigo,
            Colors.indigo.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.security, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  languageService.isArabic ? 'اختبار نظام الصلاحيات' : 'Permission System Testing',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            languageService.isArabic 
                ? 'اختبار شامل لجميع صلاحيات النظام المطلوبة'
                : 'Comprehensive testing of all required system permissions',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageService.isArabic ? 'التقدم' : 'Progress',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '$passedTests / $totalTests',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: passedTests / totalTests,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildTestControls(LanguageService languageService) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isRunningTests ? null : _runAllTests,
            icon: _isRunningTests 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(
              _isRunningTests
                  ? (languageService.isArabic ? 'جاري التشغيل...' : 'Running...')
                  : (languageService.isArabic ? 'تشغيل جميع الاختبارات' : 'Run All Tests'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.indigo,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: _isRunningTests ? null : _resetTests,
          icon: const Icon(Icons.refresh),
          label: Text(languageService.isArabic ? 'إعادة تعيين' : 'Reset'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildPermissionOverview(LanguageService languageService) {
    return FutureBuilder<Map<String, bool>>(
      future: _getPermissionStatuses(),
      builder: (context, snapshot) {
        final permissions = snapshot.data ?? {};
        final grantedCount = permissions.values.where((granted) => granted).length;
        final totalCount = permissions.length;
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: grantedCount == totalCount 
                ? Colors.green.withValues(alpha: 0.1) 
                : Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: grantedCount == totalCount ? Colors.green : Colors.orange,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    grantedCount == totalCount ? Icons.check_circle : Icons.warning,
                    color: grantedCount == totalCount ? Colors.green : Colors.orange,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      languageService.isArabic ? 'نظرة عامة على الصلاحيات' : 'Permission Overview',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: grantedCount == totalCount ? Colors.green : Colors.orange,
                      ),
                    ),
                  ),
                  Text(
                    '$grantedCount / $totalCount',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: grantedCount == totalCount ? Colors.green : Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: totalCount > 0 ? grantedCount / totalCount : 0,
                backgroundColor: Colors.grey.withValues(alpha: 0.3),
                valueColor: AlwaysStoppedAnimation<Color>(
                  grantedCount == totalCount ? Colors.green : Colors.orange,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTestResults(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'نتائج الاختبارات' : 'Test Results',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.indigo,
          ),
        ),
        const SizedBox(height: 12),
        ...(_permissionTests.map((test) {
          final result = _testResults[test.id] ?? TestResult.notStarted;
          return _buildTestResultCard(test, result, languageService);
        }).toList()),
      ],
    );
  }

  Widget _buildTestResultCard(PermissionTest test, TestResult result, LanguageService languageService) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (result) {
      case TestResult.passed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = languageService.isArabic ? 'نجح' : 'Passed';
        break;
      case TestResult.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = languageService.isArabic ? 'فشل' : 'Failed';
        break;
      case TestResult.inProgress:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = languageService.isArabic ? 'قيد التشغيل' : 'Running';
        break;
      case TestResult.notStarted:
        statusColor = Colors.grey;
        statusIcon = Icons.radio_button_unchecked;
        statusText = languageService.isArabic ? 'لم يبدأ' : 'Not Started';
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          languageService.isArabic ? test.nameAr : test.nameEn,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(test.description),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            if (result == TestResult.inProgress)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        onTap: () => _runSingleTest(test.id),
      ),
    );
  }

  Widget _buildRoundProgress(LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.indigo.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.indigo.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            languageService.isArabic 
                ? 'جولة الاختبار ${_currentTestRound + 1} من $_totalRounds'
                : 'Test Round ${_currentTestRound + 1} of $_totalRounds',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.indigo,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentTestRound + 1) / _totalRounds,
            backgroundColor: Colors.indigo.withValues(alpha: 0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.indigo),
          ),
        ],
      ),
    );
  }

  Future<Map<String, bool>> _getPermissionStatuses() async {
    return {
      'notification': await _permissionsManager.hasNotificationPermission(),
      'overlay': await _permissionsManager.hasOverlayPermission(),
      'location': await _permissionsManager.hasLocationPermission(),
      'storage': await _permissionsManager.hasStoragePermission(),
      'audio': await _permissionsManager.hasAudioPermission(),
      'background': await _permissionsManager.hasBackgroundPermission(),
    };
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTests = true;
      _currentTestRound = 0;
    });

    widget.onStatusChanged(TestingStatus.inProgress);

    // Run 5 rounds of testing as per requirements
    for (int round = 0; round < _totalRounds; round++) {
      setState(() {
        _currentTestRound = round;
      });

      for (final test in _permissionTests) {
        await _runSingleTestInternal(test.id);
        await Future.delayed(const Duration(milliseconds: 500));
      }

      await Future.delayed(const Duration(seconds: 1));
    }

    setState(() {
      _isRunningTests = false;
    });

    final allPassed = _testResults.values.every((result) => result == TestResult.passed);
    widget.onStatusChanged(allPassed ? TestingStatus.passed : TestingStatus.failed);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            allPassed
                ? (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'جميع اختبارات الصلاحيات نجحت!'
                    : 'All permission tests passed!')
                : (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'بعض اختبارات الصلاحيات فشلت'
                    : 'Some permission tests failed'),
          ),
          backgroundColor: allPassed ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _runSingleTest(String testId) async {
    await _runSingleTestInternal(testId);
  }

  Future<void> _runSingleTestInternal(String testId) async {
    setState(() {
      _testResults[testId] = TestResult.inProgress;
    });

    try {
      bool testPassed = false;

      switch (testId) {
        case 'notification_permission':
          testPassed = await _testNotificationPermission();
          break;
        case 'overlay_permission':
          testPassed = await _testOverlayPermission();
          break;
        case 'location_permission':
          testPassed = await _testLocationPermission();
          break;
        case 'storage_permission':
          testPassed = await _testStoragePermission();
          break;
        case 'audio_permission':
          testPassed = await _testAudioPermission();
          break;
        case 'background_permission':
          testPassed = await _testBackgroundPermission();
          break;
      }

      setState(() {
        _testResults[testId] = testPassed ? TestResult.passed : TestResult.failed;
      });
    } catch (e) {
      setState(() {
        _testResults[testId] = TestResult.failed;
      });
    }
  }

  Future<bool> _testNotificationPermission() async {
    try {
      final hasPermission = await _permissionsManager.hasNotificationPermission();
      if (!hasPermission) {
        await _permissionsManager.requestNotificationPermission();
      }
      return await _permissionsManager.hasNotificationPermission();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testOverlayPermission() async {
    try {
      final hasPermission = await _permissionsManager.hasOverlayPermission();
      if (!hasPermission) {
        await _permissionsManager.requestOverlayPermission();
      }
      return await _permissionsManager.hasOverlayPermission();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testLocationPermission() async {
    try {
      final hasPermission = await _permissionsManager.hasLocationPermission();
      if (!hasPermission) {
        await _permissionsManager.requestLocationPermission();
      }
      return await _permissionsManager.hasLocationPermission();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testStoragePermission() async {
    try {
      final hasPermission = await _permissionsManager.hasStoragePermission();
      if (!hasPermission) {
        await _permissionsManager.requestStoragePermission();
      }
      return await _permissionsManager.hasStoragePermission();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testAudioPermission() async {
    try {
      final hasPermission = await _permissionsManager.hasAudioPermission();
      if (!hasPermission) {
        await _permissionsManager.requestAudioPermission();
      }
      return await _permissionsManager.hasAudioPermission();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testBackgroundPermission() async {
    try {
      final hasPermission = await _permissionsManager.hasBackgroundPermission();
      if (!hasPermission) {
        await _permissionsManager.requestBackgroundPermission();
      }
      return await _permissionsManager.hasBackgroundPermission();
    } catch (e) {
      return false;
    }
  }

  void _resetTests() {
    setState(() {
      _initializeTestResults();
      _isRunningTests = false;
      _currentTestRound = 0;
    });
    widget.onStatusChanged(TestingStatus.notStarted);
  }
}

class PermissionTest {
  final String id;
  final String nameAr;
  final String nameEn;
  final String description;

  PermissionTest({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.description,
  });
}

enum TestResult {
  notStarted,
  inProgress,
  passed,
  failed,
}
