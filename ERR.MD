[{"resource": "/d:/projects/12july/athkar/athkar_app/lib/services/realtime_sync_service.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unnecessary_null_comparison", "target": {"$mid": 1, "path": "/diagnostics/unnecessary_null_comparison", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The operand can't be 'null', so the condition is always 'true'.\nRemove the condition.", "source": "dart", "startLineNumber": 142, "startColumn": 21, "endLineNumber": 142, "endColumn": 28, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/services/realtime_sync_service.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unnecessary_null_comparison", "target": {"$mid": 1, "path": "/diagnostics/unnecessary_null_comparison", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The operand can't be 'null', so the condition is always 'true'.\nRemove the condition.", "source": "dart", "startLineNumber": 164, "startColumn": 23, "endLineNumber": 164, "endColumn": 30, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/services/realtime_sync_service.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unnecessary_null_comparison", "target": {"$mid": 1, "path": "/diagnostics/unnecessary_null_comparison", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The operand can't be 'null', so the condition is always 'true'.\nRemove the condition.", "source": "dart", "startLineNumber": 182, "startColumn": 21, "endLineNumber": 182, "endColumn": 28, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/services/realtime_sync_service.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unnecessary_null_comparison", "target": {"$mid": 1, "path": "/diagnostics/unnecessary_null_comparison", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The operand can't be 'null', so the condition is always 'true'.\nRemove the condition.", "source": "dart", "startLineNumber": 205, "startColumn": 80, "endLineNumber": 205, "endColumn": 87, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/services/realtime_sync_service.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unnecessary_null_comparison", "target": {"$mid": 1, "path": "/diagnostics/unnecessary_null_comparison", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The operand can't be 'null', so the condition is always 'true'.\nRemove the condition.", "source": "dart", "startLineNumber": 221, "startColumn": 29, "endLineNumber": 221, "endColumn": 36, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/services/realtime_sync_service.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "unnecessary_null_comparison", "target": {"$mid": 1, "path": "/diagnostics/unnecessary_null_comparison", "scheme": "https", "authority": "dart.dev"}}, "severity": 4, "message": "The operand can't be 'null', so the condition is always 'true'.\nRemove the condition.", "source": "dart", "startLineNumber": 241, "startColumn": 29, "endLineNumber": 241, "endColumn": 36, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/audio_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 46, "startColumn": 30, "endLineNumber": 46, "endColumn": 37, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/audio_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 54, "startColumn": 28, "endLineNumber": 54, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/audio_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 92, "startColumn": 32, "endLineNumber": 92, "endColumn": 39, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/audio_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 100, "startColumn": 30, "endLineNumber": 100, "endColumn": 37, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/audio_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 116, "startColumn": 28, "endLineNumber": 116, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/audio_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 129, "startColumn": 28, "endLineNumber": 129, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/floating_window_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 101, "startColumn": 30, "endLineNumber": 101, "endColumn": 37, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/floating_window_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 109, "startColumn": 28, "endLineNumber": 109, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/floating_window_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 148, "startColumn": 28, "endLineNumber": 148, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/floating_window_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 155, "startColumn": 28, "endLineNumber": 155, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/floating_window_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 173, "startColumn": 28, "endLineNumber": 173, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/floating_window_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 180, "startColumn": 28, "endLineNumber": 180, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/floating_window_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 203, "startColumn": 28, "endLineNumber": 203, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/floating_window_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 210, "startColumn": 28, "endLineNumber": 210, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/floating_window_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 228, "startColumn": 28, "endLineNumber": 228, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/floating_window_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 235, "startColumn": 28, "endLineNumber": 235, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/hadith_favorites_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 291, "startColumn": 11, "endLineNumber": 291, "endColumn": 18, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/hadith_favorites_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check.\nGuard a 'State.context' use with a 'mounted' check on the State, and other BuildContext use with a 'mounted' check on the BuildContext.", "source": "dart", "startLineNumber": 336, "startColumn": 31, "endLineNumber": 336, "endColumn": 38, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/hadith_favorites_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check.\nGuard a 'State.context' use with a 'mounted' check on the State, and other BuildContext use with a 'mounted' check on the BuildContext.", "source": "dart", "startLineNumber": 337, "startColumn": 38, "endLineNumber": 337, "endColumn": 45, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/language_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check.\nGuard a 'State.context' use with a 'mounted' check on the State, and other BuildContext use with a 'mounted' check on the BuildContext.", "source": "dart", "startLineNumber": 109, "startColumn": 48, "endLineNumber": 109, "endColumn": 55, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/language_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check.\nGuard a 'State.context' use with a 'mounted' check on the State, and other BuildContext use with a 'mounted' check on the BuildContext.", "source": "dart", "startLineNumber": 145, "startColumn": 48, "endLineNumber": 145, "endColumn": 55, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/language_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check.\nGuard a 'State.context' use with a 'mounted' check on the State, and other BuildContext use with a 'mounted' check on the BuildContext.", "source": "dart", "startLineNumber": 186, "startColumn": 50, "endLineNumber": 186, "endColumn": 57, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/notification_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 113, "startColumn": 28, "endLineNumber": 113, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/notification_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 121, "startColumn": 28, "endLineNumber": 121, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/notification_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 195, "startColumn": 28, "endLineNumber": 195, "endColumn": 35, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/prebuilt_content_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement add to routine functionality", "source": "dart", "startLineNumber": 646, "startColumn": 8, "endLineNumber": 646, "endColumn": 52, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/services/permissions_service.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 254, "startColumn": 9, "endLineNumber": 254, "endColumn": 16, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/services/permissions_service.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 268, "startColumn": 9, "endLineNumber": 268, "endColumn": 16, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Connect to actual setting", "source": "dart", "startLineNumber": 131, "startColumn": 35, "endLineNumber": 131, "endColumn": 66, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement notification toggle", "source": "dart", "startLineNumber": 134, "startColumn": 24, "endLineNumber": 134, "endColumn": 59, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Connect to actual setting", "source": "dart", "startLineNumber": 142, "startColumn": 35, "endLineNumber": 142, "endColumn": 66, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement vibration toggle", "source": "dart", "startLineNumber": 145, "startColumn": 24, "endLineNumber": 145, "endColumn": 56, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Connect to actual setting", "source": "dart", "startLineNumber": 153, "startColumn": 35, "endLineNumber": 153, "endColumn": 66, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement sound toggle", "source": "dart", "startLineNumber": 156, "startColumn": 24, "endLineNumber": 156, "endColumn": 52, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Connect to actual setting", "source": "dart", "startLineNumber": 180, "startColumn": 35, "endLineNumber": 180, "endColumn": 66, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement auto refresh toggle", "source": "dart", "startLineNumber": 183, "startColumn": 24, "endLineNumber": 183, "endColumn": 59, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement Arabic search toggle", "source": "dart", "startLineNumber": 198, "startColumn": 24, "endLineNumber": 198, "endColumn": 60, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement translation search toggle", "source": "dart", "startLineNumber": 209, "startColumn": 24, "endLineNumber": 209, "endColumn": 65, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement narrator search toggle", "source": "dart", "startLineNumber": 220, "startColumn": 24, "endLineNumber": 220, "endColumn": 62, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement favorites sync toggle", "source": "dart", "startLineNumber": 247, "startColumn": 24, "endLineNumber": 247, "endColumn": 61, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement favorites backup toggle", "source": "dart", "startLineNumber": 258, "startColumn": 24, "endLineNumber": 258, "endColumn": 63, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement daily hadith toggle", "source": "dart", "startLineNumber": 301, "startColumn": 24, "endLineNumber": 301, "endColumn": 59, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Save selected time", "source": "dart", "startLineNumber": 432, "startColumn": 12, "endLineNumber": 432, "endColumn": 36, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 433, "startColumn": 30, "endLineNumber": 433, "endColumn": 37, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 437, "startColumn": 52, "endLineNumber": 437, "endColumn": 59, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 438, "startColumn": 46, "endLineNumber": 438, "endColumn": 53, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/prayer_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement auto location toggle", "source": "dart", "startLineNumber": 88, "startColumn": 24, "endLineNumber": 88, "endColumn": 60, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/prayer_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement pre-prayer reminder toggle", "source": "dart", "startLineNumber": 182, "startColumn": 24, "endLineNumber": 182, "endColumn": 66, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/prayer_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement post-prayer reminder toggle", "source": "dart", "startLineNumber": 193, "startColumn": 24, "endLineNumber": 193, "endColumn": 67, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/prayer_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement compass calibration toggle", "source": "dart", "startLineNumber": 220, "startColumn": 24, "endLineNumber": 220, "endColumn": 66, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/prayer_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement qibla vibration toggle", "source": "dart", "startLineNumber": 231, "startColumn": 24, "endLineNumber": 231, "endColumn": 62, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/quran_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement save reading position toggle", "source": "dart", "startLineNumber": 186, "startColumn": 24, "endLineNumber": 186, "endColumn": 68, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/quran_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement auto scroll toggle", "source": "dart", "startLineNumber": 197, "startColumn": 24, "endLineNumber": 197, "endColumn": 58, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/quran_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement night reading mode toggle", "source": "dart", "startLineNumber": 208, "startColumn": 24, "endLineNumber": 208, "endColumn": 65, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/quran_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement auto repeat toggle", "source": "dart", "startLineNumber": 243, "startColumn": 24, "endLineNumber": 243, "endColumn": 58, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/quran_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement auto download toggle", "source": "dart", "startLineNumber": 254, "startColumn": 24, "endLineNumber": 254, "endColumn": 60, "extensionID": "Dart-Code.dart-code"}]