[{"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/hadith_favorites_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 291, "startColumn": 11, "endLineNumber": 291, "endColumn": 18, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/hadith_favorites_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check.\nGuard a 'State.context' use with a 'mounted' check on the State, and other BuildContext use with a 'mounted' check on the BuildContext.", "source": "dart", "startLineNumber": 336, "startColumn": 31, "endLineNumber": 336, "endColumn": 38, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/hadith_favorites_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check.\nGuard a 'State.context' use with a 'mounted' check on the State, and other BuildContext use with a 'mounted' check on the BuildContext.", "source": "dart", "startLineNumber": 337, "startColumn": 38, "endLineNumber": 337, "endColumn": 45, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/language_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check.\nGuard a 'State.context' use with a 'mounted' check on the State, and other BuildContext use with a 'mounted' check on the BuildContext.", "source": "dart", "startLineNumber": 109, "startColumn": 48, "endLineNumber": 109, "endColumn": 55, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/language_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check.\nGuard a 'State.context' use with a 'mounted' check on the State, and other BuildContext use with a 'mounted' check on the BuildContext.", "source": "dart", "startLineNumber": 145, "startColumn": 48, "endLineNumber": 145, "endColumn": 55, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/screens/language_settings_screen.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check.\nGuard a 'State.context' use with a 'mounted' check on the State, and other BuildContext use with a 'mounted' check on the BuildContext.", "source": "dart", "startLineNumber": 186, "startColumn": 50, "endLineNumber": 186, "endColumn": 57, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/services/permissions_service.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 254, "startColumn": 9, "endLineNumber": 254, "endColumn": 16, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/services/permissions_service.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 268, "startColumn": 9, "endLineNumber": 268, "endColumn": 16, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Connect to actual setting", "source": "dart", "startLineNumber": 131, "startColumn": 35, "endLineNumber": 131, "endColumn": 66, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement notification toggle", "source": "dart", "startLineNumber": 134, "startColumn": 24, "endLineNumber": 134, "endColumn": 59, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Connect to actual setting", "source": "dart", "startLineNumber": 142, "startColumn": 35, "endLineNumber": 142, "endColumn": 66, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement vibration toggle", "source": "dart", "startLineNumber": 145, "startColumn": 24, "endLineNumber": 145, "endColumn": 56, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Connect to actual setting", "source": "dart", "startLineNumber": 153, "startColumn": 35, "endLineNumber": 153, "endColumn": 66, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement sound toggle", "source": "dart", "startLineNumber": 156, "startColumn": 24, "endLineNumber": 156, "endColumn": 52, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Connect to actual setting", "source": "dart", "startLineNumber": 180, "startColumn": 35, "endLineNumber": 180, "endColumn": 66, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/general_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement auto refresh toggle", "source": "dart", "startLineNumber": 183, "startColumn": 24, "endLineNumber": 183, "endColumn": 59, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement Arabic search toggle", "source": "dart", "startLineNumber": 198, "startColumn": 24, "endLineNumber": 198, "endColumn": 60, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement translation search toggle", "source": "dart", "startLineNumber": 209, "startColumn": 24, "endLineNumber": 209, "endColumn": 65, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement narrator search toggle", "source": "dart", "startLineNumber": 220, "startColumn": 24, "endLineNumber": 220, "endColumn": 62, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement favorites sync toggle", "source": "dart", "startLineNumber": 247, "startColumn": 24, "endLineNumber": 247, "endColumn": 61, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement favorites backup toggle", "source": "dart", "startLineNumber": 258, "startColumn": 24, "endLineNumber": 258, "endColumn": 63, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Implement daily hadith toggle", "source": "dart", "startLineNumber": 301, "startColumn": 24, "endLineNumber": 301, "endColumn": 59, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": "todo", "severity": 2, "message": "TODO: Save selected time", "source": "dart", "startLineNumber": 432, "startColumn": 12, "endLineNumber": 432, "endColumn": 36, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 433, "startColumn": 30, "endLineNumber": 433, "endColumn": 37, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 437, "startColumn": 52, "endLineNumber": 437, "endColumn": 59, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/hadith_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps.\nTry rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.", "source": "dart", "startLineNumber": 438, "startColumn": 46, "endLineNumber": 438, "endColumn": 53, "extensionID": "Dart-Code.dart-code"}, {"resource": "/d:/projects/12july/athkar/athkar_app/lib/widgets/settings_tabs/quran_settings_tab.dart", "owner": "_generated_diagnostic_collection_name_#1", "code": {"value": "use_build_context_synchronously", "target": {"$mid": 1, "path": "/diagnostics/use_build_context_synchronously", "scheme": "https", "authority": "dart.dev"}}, "severity": 2, "message": "Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check.\nGuard a 'State.context' use with a 'mounted' check on the State, and other BuildContext use with a 'mounted' check on the BuildContext.", "source": "dart", "startLineNumber": 191, "startColumn": 44, "endLineNumber": 191, "endColumn": 51, "extensionID": "Dart-Code.dart-code"}]