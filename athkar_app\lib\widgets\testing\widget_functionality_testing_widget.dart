import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../services/widget_manager.dart';
import '../../theme/app_theme.dart';
import '../../screens/comprehensive_testing_screen.dart';

class WidgetFunctionalityTestingWidget extends StatefulWidget {
  final Function(TestingStatus) onStatusChanged;

  const WidgetFunctionalityTestingWidget({
    super.key,
    required this.onStatusChanged,
  });

  @override
  State<WidgetFunctionalityTestingWidget> createState() => _WidgetFunctionalityTestingWidgetState();
}

class _WidgetFunctionalityTestingWidgetState extends State<WidgetFunctionalityTestingWidget> {
  final WidgetManager _widgetManager = WidgetManager();
  
  final Map<String, TestResult> _testResults = {};
  bool _isRunningTests = false;
  int _currentTestRound = 0;
  final int _totalRounds = 5;

  final List<WidgetTest> _widgetTests = [
    WidgetTest(
      id: 'routine_counter_widget',
      nameAr: 'ودجت عداد الروتين',
      nameEn: 'Routine Counter Widget',
      description: 'Test routine counter widget functionality',
    ),
    WidgetTest(
      id: 'ayah_of_day_widget',
      nameAr: 'ودجت آية اليوم',
      nameEn: 'Ayah of Day Widget',
      description: 'Test daily Ayah widget display',
    ),
    WidgetTest(
      id: 'athkar_reminder_widget',
      nameAr: 'ودجت تذكير الأذكار',
      nameEn: 'Athkar Reminder Widget',
      description: 'Test Athkar reminder widget',
    ),
    WidgetTest(
      id: 'quran_progress_widget',
      nameAr: 'ودجت تقدم القرآن',
      nameEn: 'Quran Progress Widget',
      description: 'Test Quran reading progress widget',
    ),
    WidgetTest(
      id: 'prayer_times_widget',
      nameAr: 'ودجت أوقات الصلاة',
      nameEn: 'Prayer Times Widget',
      description: 'Test prayer times widget',
    ),
    WidgetTest(
      id: 'islamic_calendar_widget',
      nameAr: 'ودجت التقويم الهجري',
      nameEn: 'Islamic Calendar Widget',
      description: 'Test Islamic calendar widget',
    ),
    WidgetTest(
      id: 'dhikr_counter_widget',
      nameAr: 'ودجت عداد الذكر',
      nameEn: 'Dhikr Counter Widget',
      description: 'Test dhikr counter widget',
    ),
    WidgetTest(
      id: 'statistics_widget',
      nameAr: 'ودجت الإحصائيات',
      nameEn: 'Statistics Widget',
      description: 'Test statistics widget',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeTestResults();
  }

  void _initializeTestResults() {
    for (final test in _widgetTests) {
      _testResults[test.id] = TestResult.notStarted;
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(languageService),
          const SizedBox(height: 24),
          _buildTestControls(languageService),
          const SizedBox(height: 24),
          _buildTestResults(languageService),
          const SizedBox(height: 24),
          if (_isRunningTests) _buildRoundProgress(languageService),
        ],
      ),
    );
  }

  Widget _buildHeader(LanguageService languageService) {
    final passedTests = _testResults.values.where((result) => result == TestResult.passed).length;
    final totalTests = _testResults.length;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue,
            Colors.blue.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.widgets, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  languageService.isArabic ? 'اختبار وظائف الودجت' : 'Widget Functionality Testing',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            languageService.isArabic 
                ? 'اختبار شامل لجميع أنواع الودجت الإسلامية'
                : 'Comprehensive testing of all Islamic widget types',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageService.isArabic ? 'التقدم' : 'Progress',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '$passedTests / $totalTests',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: passedTests / totalTests,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildTestControls(LanguageService languageService) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isRunningTests ? null : _runAllTests,
            icon: _isRunningTests 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(
              _isRunningTests
                  ? (languageService.isArabic ? 'جاري التشغيل...' : 'Running...')
                  : (languageService.isArabic ? 'تشغيل جميع الاختبارات' : 'Run All Tests'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: _isRunningTests ? null : _resetTests,
          icon: const Icon(Icons.refresh),
          label: Text(languageService.isArabic ? 'إعادة تعيين' : 'Reset'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildTestResults(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'نتائج الاختبارات' : 'Test Results',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 12),
        ...(_widgetTests.map((test) {
          final result = _testResults[test.id] ?? TestResult.notStarted;
          return _buildTestResultCard(test, result, languageService);
        }).toList()),
      ],
    );
  }

  Widget _buildTestResultCard(WidgetTest test, TestResult result, LanguageService languageService) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (result) {
      case TestResult.passed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = languageService.isArabic ? 'نجح' : 'Passed';
        break;
      case TestResult.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = languageService.isArabic ? 'فشل' : 'Failed';
        break;
      case TestResult.inProgress:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = languageService.isArabic ? 'قيد التشغيل' : 'Running';
        break;
      case TestResult.notStarted:
        statusColor = Colors.grey;
        statusIcon = Icons.radio_button_unchecked;
        statusText = languageService.isArabic ? 'لم يبدأ' : 'Not Started';
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          languageService.isArabic ? test.nameAr : test.nameEn,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(test.description),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            if (result == TestResult.inProgress)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        onTap: () => _runSingleTest(test.id),
      ),
    );
  }

  Widget _buildRoundProgress(LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            languageService.isArabic 
                ? 'جولة الاختبار ${_currentTestRound + 1} من $_totalRounds'
                : 'Test Round ${_currentTestRound + 1} of $_totalRounds',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentTestRound + 1) / _totalRounds,
            backgroundColor: Colors.blue.withValues(alpha: 0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
        ],
      ),
    );
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTests = true;
      _currentTestRound = 0;
    });

    widget.onStatusChanged(TestingStatus.inProgress);

    // Run 5 rounds of testing as per requirements
    for (int round = 0; round < _totalRounds; round++) {
      setState(() {
        _currentTestRound = round;
      });

      for (final test in _widgetTests) {
        await _runSingleTestInternal(test.id);
        await Future.delayed(const Duration(milliseconds: 500));
      }

      await Future.delayed(const Duration(seconds: 1));
    }

    setState(() {
      _isRunningTests = false;
    });

    final allPassed = _testResults.values.every((result) => result == TestResult.passed);
    widget.onStatusChanged(allPassed ? TestingStatus.passed : TestingStatus.failed);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            allPassed
                ? (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'جميع اختبارات الودجت نجحت!'
                    : 'All widget tests passed!')
                : (Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'بعض اختبارات الودجت فشلت'
                    : 'Some widget tests failed'),
          ),
          backgroundColor: allPassed ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _runSingleTest(String testId) async {
    await _runSingleTestInternal(testId);
  }

  Future<void> _runSingleTestInternal(String testId) async {
    setState(() {
      _testResults[testId] = TestResult.inProgress;
    });

    try {
      bool testPassed = false;

      switch (testId) {
        case 'routine_counter_widget':
          testPassed = await _testRoutineCounterWidget();
          break;
        case 'ayah_of_day_widget':
          testPassed = await _testAyahOfDayWidget();
          break;
        case 'athkar_reminder_widget':
          testPassed = await _testAthkarReminderWidget();
          break;
        case 'quran_progress_widget':
          testPassed = await _testQuranProgressWidget();
          break;
        case 'prayer_times_widget':
          testPassed = await _testPrayerTimesWidget();
          break;
        case 'islamic_calendar_widget':
          testPassed = await _testIslamicCalendarWidget();
          break;
        case 'dhikr_counter_widget':
          testPassed = await _testDhikrCounterWidget();
          break;
        case 'statistics_widget':
          testPassed = await _testStatisticsWidget();
          break;
      }

      setState(() {
        _testResults[testId] = testPassed ? TestResult.passed : TestResult.failed;
      });
    } catch (e) {
      setState(() {
        _testResults[testId] = TestResult.failed;
      });
    }
  }

  Future<bool> _testRoutineCounterWidget() async {
    try {
      await _widgetManager.createWidget(
        WidgetType.routineCounter,
        WidgetSize.medium2x1,
        {'routineName': 'أذكار الصباح', 'targetCount': 33},
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testAyahOfDayWidget() async {
    try {
      await _widgetManager.createWidget(
        WidgetType.ayahOfDay,
        WidgetSize.large2x2,
        {'showTranslation': true, 'fontSize': 14},
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testAthkarReminderWidget() async {
    try {
      await _widgetManager.createWidget(
        WidgetType.athkarReminder,
        WidgetSize.medium2x1,
        {'reminderType': 'morning', 'showNextReminder': true},
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testQuranProgressWidget() async {
    try {
      await _widgetManager.createWidget(
        WidgetType.quranProgress,
        WidgetSize.large2x2,
        {'showDailyGoal': true, 'progressType': 'percentage'},
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testPrayerTimesWidget() async {
    try {
      await _widgetManager.createWidget(
        WidgetType.prayerTimes,
        WidgetSize.extraLarge4x2,
        {'showAllPrayers': true, 'highlightNext': true},
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testIslamicCalendarWidget() async {
    try {
      await _widgetManager.createWidget(
        WidgetType.islamicCalendar,
        WidgetSize.large2x2,
        {'showEvents': true, 'calendarType': 'hijri'},
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testDhikrCounterWidget() async {
    try {
      await _widgetManager.createWidget(
        WidgetType.dhikrCounter,
        WidgetSize.small1x1,
        {'dhikrText': 'سبحان الله', 'targetCount': 100},
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _testStatisticsWidget() async {
    try {
      await _widgetManager.createWidget(
        WidgetType.statistics,
        WidgetSize.large2x2,
        {'showStreaks': true, 'chartType': 'weekly'},
      );
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  void _resetTests() {
    setState(() {
      _initializeTestResults();
      _isRunningTests = false;
      _currentTestRound = 0;
    });
    widget.onStatusChanged(TestingStatus.notStarted);
  }
}

class WidgetTest {
  final String id;
  final String nameAr;
  final String nameEn;
  final String description;

  WidgetTest({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.description,
  });
}

enum TestResult {
  notStarted,
  inProgress,
  passed,
  failed,
}
