/// Generated file. Do not edit.
///
/// This file contains the localized strings for the Athkar app.
/// To regenerate this file, run: flutter gen-l10n

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'تطبيق الأذكار';

  @override
  String get home => 'الرئيسية';

  @override
  String get prayerTimes => 'أوقات الصلاة';

  @override
  String get qibla => 'القبلة';

  @override
  String get quran => 'القرآن الكريم';

  @override
  String get athkar => 'الأذكار';

  @override
  String get tasbeeh => 'التسبيح';

  @override
  String get calendar => 'التقويم الإسلامي';

  @override
  String get settings => 'الإعدادات';

  @override
  String get welcomeMessage => 'السلام عليكم ورحمة الله وبركاته';

  @override
  String get currentPrayer => 'الصلاة الحالية';

  @override
  String get nextPrayer => 'الصلاة التالية';

  @override
  String get prayerTimesTitle => 'أوقات الصلاة';

  @override
  String get prayerTimesSettings => 'إعدادات أوقات الصلاة';

  @override
  String get fajr => 'الفجر';

  @override
  String get sunrise => 'الشروق';

  @override
  String get dhuhr => 'الظهر';

  @override
  String get asr => 'العصر';

  @override
  String get maghrib => 'المغرب';

  @override
  String get isha => 'العشاء';

  @override
  String get qiblaFinder => 'محدد القبلة';

  @override
  String get qiblaDirection => 'اتجاه القبلة';

  @override
  String get distanceToKaaba => 'المسافة إلى الكعبة';

  @override
  String get holdPhoneFlat => 'امسك هاتفك بشكل مسطح ومستوي';

  @override
  String get greenLinePointsToQibla => 'الخط الأخضر يشير إلى القبلة';

  @override
  String get rotateUntilFacingGreenLine => 'استدر حتى تواجه الخط الأخضر';

  @override
  String get nowFacingQibla => 'أنت الآن تواجه القبلة للصلاة';

  @override
  String get holyQuran => 'القرآن الكريم';

  @override
  String get surahs => 'السور';

  @override
  String get bookmarks => 'المفضلة';

  @override
  String get search => 'البحث';

  @override
  String get searchInQuran => 'البحث في القرآن...';

  @override
  String get noBookmarksYet => 'لا توجد مفضلة بعد';

  @override
  String get bookmarkVersesToAccess =>
      'ضع إشارة مرجعية على الآيات للوصول إليها بسرعة';

  @override
  String get searchTheHolyQuran => 'ابحث في القرآن الكريم';

  @override
  String get enterKeywordsToSearch => 'أدخل كلمات مفتاحية للبحث';

  @override
  String get tafseer => 'التفسير';

  @override
  String get showTafseer => 'إظهار التفسير';

  @override
  String get displayCommentaryBelowVerse => 'عرض التفسير أسفل كل آية';

  @override
  String get noTafseerAvailable => 'لا يوجد تفسير متاح لهذه الآية';

  @override
  String get textType => 'نوع النص';

  @override
  String get uthmaniScript => 'الرسم العثماني';

  @override
  String get simpleText => 'النص المبسط';

  @override
  String get original => 'الأصلي';

  @override
  String get athkarAndDhikr => 'الأذكار والذكر';

  @override
  String get morningAthkar => 'أذكار الصباح';

  @override
  String get eveningAthkar => 'أذكار المساء';

  @override
  String get prayerAthkar => 'أذكار الصلاة';

  @override
  String get dhikrCounter => 'عداد الذكر';

  @override
  String get createNew => 'إنشاء جديد';

  @override
  String get startAthkar => 'بدء الأذكار';

  @override
  String get completed => 'مكتمل';

  @override
  String get previous => 'السابق';

  @override
  String get next => 'التالي';

  @override
  String get finish => 'إنهاء';

  @override
  String get athkarPracticeCompleted =>
      'تم إكمال ممارسة الأذكار! تقبل الله منك';

  @override
  String get islamicCalendar => 'التقويم الإسلامي';

  @override
  String get events => 'الأحداث';

  @override
  String get upcomingIslamicEvents => 'الأحداث الإسلامية القادمة';

  @override
  String get noUpcomingEvents => 'لا توجد أحداث قادمة';

  @override
  String get noEventsForThisDay => 'لا توجد أحداث لهذا اليوم';

  @override
  String get prayerTimeAdjustments => 'تعديلات أوقات الصلاة';

  @override
  String get adjustPrayerTimes => 'تعديل أوقات الصلاة';

  @override
  String get adjustmentDescription =>
      'يمكنك تعديل أوقات الصلاة الفردية بإضافة أو طرح دقائق. هذا مفيد للاختلافات المحلية أو التفضيلات الشخصية.';

  @override
  String get noAdjustment => 'بدون تعديل';

  @override
  String get resetAllAdjustments => 'إعادة تعيين جميع التعديلات';

  @override
  String get resetConfirmation =>
      'هل أنت متأكد من أنك تريد إعادة تعيين جميع تعديلات أوقات الصلاة إلى الصفر؟';

  @override
  String get cancel => 'إلغاء';

  @override
  String get reset => 'إعادة تعيين';

  @override
  String get allAdjustmentsReset => 'تم إعادة تعيين جميع تعديلات أوقات الصلاة';

  @override
  String get quickActions => 'الإجراءات السريعة';

  @override
  String get recentAthkar => 'الأذكار الحديثة';

  @override
  String get favoriteAthkar => 'الأذكار المفضلة';

  @override
  String get progress => 'التقدم';

  @override
  String get statistics => 'الإحصائيات';

  @override
  String get sync => 'مزامنة';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get allAthkar => 'جميع الأذكار';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get close => 'إغلاق';

  @override
  String get save => 'حفظ';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get add => 'إضافة';

  @override
  String get remove => 'إزالة';

  @override
  String get confirm => 'تأكيد';

  @override
  String get locationPermissionRequired => 'مطلوب إذن الموقع';

  @override
  String get locationServicesDisabled => 'خدمات الموقع معطلة';

  @override
  String get couldNotGetLocation =>
      'لا يمكن الحصول على الموقع، استخدام الموقع الافتراضي للأردن';

  @override
  String get unableToLoadPrayerTimes => 'غير قادر على تحميل أوقات الصلاة';

  @override
  String get errorLoadingSurah => 'خطأ في تحميل السورة';

  @override
  String get errorLoadingQuran => 'خطأ في تحميل القرآن';

  @override
  String get errorAccessingCompass => 'خطأ في الوصول إلى البوصلة';

  @override
  String get initializingCompass => 'تهيئة البوصلة...';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get signOut => 'تسجيل الخروج';

  @override
  String get pleaseSignInToSync => 'يرجى تسجيل الدخول لمزامنة بياناتك';

  @override
  String get syncingData => 'مزامنة البيانات...';

  @override
  String get syncCompleted => 'تمت المزامنة بنجاح';

  @override
  String get syncFailed => 'فشلت المزامنة';

  @override
  String get darkMode => 'الوضع المظلم';

  @override
  String get language => 'اللغة';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'الإنجليزية';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get enableNotifications => 'تفعيل الإشعارات';

  @override
  String get prayerNotifications => 'إشعارات الصلاة';

  @override
  String get athkarReminders => 'تذكيرات الأذكار';

  @override
  String get aboutApp => 'حول التطبيق';

  @override
  String get version => 'الإصدار';

  @override
  String get developer => 'المطور';

  @override
  String get contactUs => 'اتصل بنا';

  @override
  String get rateApp => 'قيم التطبيق';

  @override
  String get shareApp => 'شارك التطبيق';

  @override
  String get privacyPolicy => 'سياسة الخصوصية';

  @override
  String get termsOfService => 'شروط الخدمة';
}
