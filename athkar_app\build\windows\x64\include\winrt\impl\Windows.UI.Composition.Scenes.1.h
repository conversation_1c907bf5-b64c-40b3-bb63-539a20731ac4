// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Composition_Scenes_1_H
#define WINRT_Windows_UI_Composition_Scenes_1_H
#include "winrt/impl/Windows.UI.Composition.Scenes.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Composition::Scenes
{
    struct __declspec(empty_bases) ISceneBoundingBox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneBoundingBox>
    {
        ISceneBoundingBox(std::nullptr_t = nullptr) noexcept {}
        ISceneBoundingBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneComponent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneComponent>
    {
        ISceneComponent(std::nullptr_t = nullptr) noexcept {}
        ISceneComponent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneComponentCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneComponentCollection>
    {
        ISceneComponentCollection(std::nullptr_t = nullptr) noexcept {}
        ISceneComponentCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneComponentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneComponentFactory>
    {
        ISceneComponentFactory(std::nullptr_t = nullptr) noexcept {}
        ISceneComponentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneMaterial :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMaterial>
    {
        ISceneMaterial(std::nullptr_t = nullptr) noexcept {}
        ISceneMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneMaterialFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMaterialFactory>
    {
        ISceneMaterialFactory(std::nullptr_t = nullptr) noexcept {}
        ISceneMaterialFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneMaterialInput :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMaterialInput>
    {
        ISceneMaterialInput(std::nullptr_t = nullptr) noexcept {}
        ISceneMaterialInput(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneMaterialInputFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMaterialInputFactory>
    {
        ISceneMaterialInputFactory(std::nullptr_t = nullptr) noexcept {}
        ISceneMaterialInputFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneMesh :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMesh>
    {
        ISceneMesh(std::nullptr_t = nullptr) noexcept {}
        ISceneMesh(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneMeshMaterialAttributeMap :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMeshMaterialAttributeMap>
    {
        ISceneMeshMaterialAttributeMap(std::nullptr_t = nullptr) noexcept {}
        ISceneMeshMaterialAttributeMap(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneMeshRendererComponent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMeshRendererComponent>
    {
        ISceneMeshRendererComponent(std::nullptr_t = nullptr) noexcept {}
        ISceneMeshRendererComponent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneMeshRendererComponentStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMeshRendererComponentStatics>
    {
        ISceneMeshRendererComponentStatics(std::nullptr_t = nullptr) noexcept {}
        ISceneMeshRendererComponentStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneMeshStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMeshStatics>
    {
        ISceneMeshStatics(std::nullptr_t = nullptr) noexcept {}
        ISceneMeshStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneMetallicRoughnessMaterial :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMetallicRoughnessMaterial>
    {
        ISceneMetallicRoughnessMaterial(std::nullptr_t = nullptr) noexcept {}
        ISceneMetallicRoughnessMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneMetallicRoughnessMaterialStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneMetallicRoughnessMaterialStatics>
    {
        ISceneMetallicRoughnessMaterialStatics(std::nullptr_t = nullptr) noexcept {}
        ISceneMetallicRoughnessMaterialStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneModelTransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneModelTransform>
    {
        ISceneModelTransform(std::nullptr_t = nullptr) noexcept {}
        ISceneModelTransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneNode :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneNode>
    {
        ISceneNode(std::nullptr_t = nullptr) noexcept {}
        ISceneNode(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneNodeCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneNodeCollection>
    {
        ISceneNodeCollection(std::nullptr_t = nullptr) noexcept {}
        ISceneNodeCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneNodeStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneNodeStatics>
    {
        ISceneNodeStatics(std::nullptr_t = nullptr) noexcept {}
        ISceneNodeStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneObject :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneObject>
    {
        ISceneObject(std::nullptr_t = nullptr) noexcept {}
        ISceneObject(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneObjectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneObjectFactory>
    {
        ISceneObjectFactory(std::nullptr_t = nullptr) noexcept {}
        ISceneObjectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScenePbrMaterial :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScenePbrMaterial>
    {
        IScenePbrMaterial(std::nullptr_t = nullptr) noexcept {}
        IScenePbrMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScenePbrMaterialFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScenePbrMaterialFactory>
    {
        IScenePbrMaterialFactory(std::nullptr_t = nullptr) noexcept {}
        IScenePbrMaterialFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneRendererComponent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneRendererComponent>
    {
        ISceneRendererComponent(std::nullptr_t = nullptr) noexcept {}
        ISceneRendererComponent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneRendererComponentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneRendererComponentFactory>
    {
        ISceneRendererComponentFactory(std::nullptr_t = nullptr) noexcept {}
        ISceneRendererComponentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneSurfaceMaterialInput :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneSurfaceMaterialInput>
    {
        ISceneSurfaceMaterialInput(std::nullptr_t = nullptr) noexcept {}
        ISceneSurfaceMaterialInput(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneSurfaceMaterialInputStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneSurfaceMaterialInputStatics>
    {
        ISceneSurfaceMaterialInputStatics(std::nullptr_t = nullptr) noexcept {}
        ISceneSurfaceMaterialInputStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneVisual>
    {
        ISceneVisual(std::nullptr_t = nullptr) noexcept {}
        ISceneVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISceneVisualStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISceneVisualStatics>
    {
        ISceneVisualStatics(std::nullptr_t = nullptr) noexcept {}
        ISceneVisualStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
