// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['full_name'] as String,
      phoneNumber: json['phone_number'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      isEmailVerified: json['is_email_verified'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      preferredLanguage: json['preferred_language'] as String? ?? 'ar',
      darkMode: json['dark_mode'] as bool? ?? false,
      notificationsEnabled: json['notifications_enabled'] as bool? ?? true,
      prayerNotifications: json['prayer_notifications'] as bool? ?? true,
      athkarReminders: json['athkar_reminders'] as bool? ?? true,
      madhab: json['madhab'] as String?,
      qiblaCalculationMethod: json['qibla_calculation_method'] as String?,
      prayerTimeAdjustments: (json['prayer_time_adjustments'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as int),
      ),
    );

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'full_name': instance.fullName,
      'phone_number': instance.phoneNumber,
      'avatar_url': instance.avatarUrl,
      'is_email_verified': instance.isEmailVerified,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'preferred_language': instance.preferredLanguage,
      'dark_mode': instance.darkMode,
      'notifications_enabled': instance.notificationsEnabled,
      'prayer_notifications': instance.prayerNotifications,
      'athkar_reminders': instance.athkarReminders,
      'madhab': instance.madhab,
      'qibla_calculation_method': instance.qiblaCalculationMethod,
      'prayer_time_adjustments': instance.prayerTimeAdjustments,
    };

UserStats _$UserStatsFromJson(Map<String, dynamic> json) => UserStats(
      userId: json['user_id'] as String,
      totalAthkarCompleted: json['total_athkar_completed'] as int? ?? 0,
      totalPrayersTracked: json['total_prayers_tracked'] as int? ?? 0,
      currentStreak: json['current_streak'] as int? ?? 0,
      longestStreak: json['longest_streak'] as int? ?? 0,
      lastActivity: DateTime.parse(json['last_activity'] as String),
      monthlyStats: (json['monthly_stats'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as int),
          ) ??
          const {},
      categoryStats: (json['category_stats'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as int),
          ) ??
          const {},
    );

Map<String, dynamic> _$UserStatsToJson(UserStats instance) => <String, dynamic>{
      'user_id': instance.userId,
      'total_athkar_completed': instance.totalAthkarCompleted,
      'total_prayers_tracked': instance.totalPrayersTracked,
      'current_streak': instance.currentStreak,
      'longest_streak': instance.longestStreak,
      'last_activity': instance.lastActivity.toIso8601String(),
      'monthly_stats': instance.monthlyStats,
      'category_stats': instance.categoryStats,
    };

UserBookmark _$UserBookmarkFromJson(Map<String, dynamic> json) => UserBookmark(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      type: json['type'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      reference: json['reference'] as String?,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$UserBookmarkToJson(UserBookmark instance) => <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'type': instance.type,
      'title': instance.title,
      'content': instance.content,
      'reference': instance.reference,
      'notes': instance.notes,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

SyncStatus _$SyncStatusFromJson(Map<String, dynamic> json) => SyncStatus(
      userId: json['user_id'] as String,
      lastSyncTime: DateTime.parse(json['last_sync_time'] as String),
      isOnline: json['is_online'] as bool? ?? false,
      pendingChanges: json['pending_changes'] as int? ?? 0,
      lastSyncByTable: (json['last_sync_by_table'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, DateTime.parse(e as String)),
          ) ??
          const {},
      failedSyncs: (json['failed_syncs'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
    );

Map<String, dynamic> _$SyncStatusToJson(SyncStatus instance) => <String, dynamic>{
      'user_id': instance.userId,
      'last_sync_time': instance.lastSyncTime.toIso8601String(),
      'is_online': instance.isOnline,
      'pending_changes': instance.pendingChanges,
      'last_sync_by_table': instance.lastSyncByTable.map((k, e) => MapEntry(k, e.toIso8601String())),
      'failed_syncs': instance.failedSyncs,
    };

UserPreferences _$UserPreferencesFromJson(Map<String, dynamic> json) => UserPreferences(
      userId: json['user_id'] as String,
      language: json['language'] as String? ?? 'ar',
      darkMode: json['dark_mode'] as bool? ?? false,
      notificationsEnabled: json['notifications_enabled'] as bool? ?? true,
      autoSync: json['auto_sync'] as bool? ?? true,
      syncFrequencyMinutes: json['sync_frequency_minutes'] as int? ?? 30,
      customSettings: json['custom_settings'] as Map<String, dynamic>? ?? const {},
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$UserPreferencesToJson(UserPreferences instance) => <String, dynamic>{
      'user_id': instance.userId,
      'language': instance.language,
      'dark_mode': instance.darkMode,
      'notifications_enabled': instance.notificationsEnabled,
      'auto_sync': instance.autoSync,
      'sync_frequency_minutes': instance.syncFrequencyMinutes,
      'custom_settings': instance.customSettings,
      'updated_at': instance.updatedAt.toIso8601String(),
    };
