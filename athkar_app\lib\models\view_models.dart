import 'package:flutter/material.dart';

/// Enumeration for different view modes
enum ViewMode {
  list,
  grid,
  card,
  compact,
  detailed,
  minimal,
}

/// Enumeration for Quran-specific view modes
enum QuranViewMode {
  bookView,
  textOnly,
  withTafseer,
  verseByVerse,
  continuous,
  mushaf,
}

/// Enumeration for layout density options
enum LayoutDensity {
  comfortable,
  compact,
  spacious,
}

/// Enumeration for font families
enum FontFamily {
  system,
  amiri,
  notoSansArabic,
  scheherazade,
  cairo,
  tajawal,
}

/// Model for view configuration
class ViewConfiguration {
  final ViewMode viewMode;
  final LayoutDensity layoutDensity;
  final double fontSize;
  final FontFamily fontFamily;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;
  final bool showArabic;
  final bool showTranslation;
  final bool showTransliteration;
  final bool enableAnimations;
  final double animationSpeed;
  final int gridColumns;
  final double cardElevation;
  final double borderRadius;
  final EdgeInsets contentPadding;

  const ViewConfiguration({
    this.viewMode = ViewMode.list,
    this.layoutDensity = LayoutDensity.comfortable,
    this.fontSize = 16.0,
    this.fontFamily = FontFamily.system,
    this.primaryColor = const Color(0xFF2E7D32),
    this.secondaryColor = const Color(0xFFFFB300),
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
    this.showArabic = true,
    this.showTranslation = true,
    this.showTransliteration = false,
    this.enableAnimations = true,
    this.animationSpeed = 1.0,
    this.gridColumns = 2,
    this.cardElevation = 2.0,
    this.borderRadius = 8.0,
    this.contentPadding = const EdgeInsets.all(16.0),
  });

  ViewConfiguration copyWith({
    ViewMode? viewMode,
    LayoutDensity? layoutDensity,
    double? fontSize,
    FontFamily? fontFamily,
    Color? primaryColor,
    Color? secondaryColor,
    Color? backgroundColor,
    Color? textColor,
    bool? showArabic,
    bool? showTranslation,
    bool? showTransliteration,
    bool? enableAnimations,
    double? animationSpeed,
    int? gridColumns,
    double? cardElevation,
    double? borderRadius,
    EdgeInsets? contentPadding,
  }) {
    return ViewConfiguration(
      viewMode: viewMode ?? this.viewMode,
      layoutDensity: layoutDensity ?? this.layoutDensity,
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      primaryColor: primaryColor ?? this.primaryColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      showArabic: showArabic ?? this.showArabic,
      showTranslation: showTranslation ?? this.showTranslation,
      showTransliteration: showTransliteration ?? this.showTransliteration,
      enableAnimations: enableAnimations ?? this.enableAnimations,
      animationSpeed: animationSpeed ?? this.animationSpeed,
      gridColumns: gridColumns ?? this.gridColumns,
      cardElevation: cardElevation ?? this.cardElevation,
      borderRadius: borderRadius ?? this.borderRadius,
      contentPadding: contentPadding ?? this.contentPadding,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'viewMode': viewMode.index,
      'layoutDensity': layoutDensity.index,
      'fontSize': fontSize,
      'fontFamily': fontFamily.index,
      'primaryColor': primaryColor.toARGB32().toRadixString(16),
      'secondaryColor': secondaryColor.toARGB32().toRadixString(16),
      'backgroundColor': backgroundColor.toARGB32().toRadixString(16),
      'textColor': textColor.toARGB32().toRadixString(16),
      'showArabic': showArabic,
      'showTranslation': showTranslation,
      'showTransliteration': showTransliteration,
      'enableAnimations': enableAnimations,
      'animationSpeed': animationSpeed,
      'gridColumns': gridColumns,
      'cardElevation': cardElevation,
      'borderRadius': borderRadius,
      'contentPadding': {
        'left': contentPadding.left,
        'top': contentPadding.top,
        'right': contentPadding.right,
        'bottom': contentPadding.bottom,
      },
    };
  }

  factory ViewConfiguration.fromMap(Map<String, dynamic> map) {
    return ViewConfiguration(
      viewMode: ViewMode.values[map['viewMode'] ?? 0],
      layoutDensity: LayoutDensity.values[map['layoutDensity'] ?? 0],
      fontSize: map['fontSize']?.toDouble() ?? 16.0,
      fontFamily: FontFamily.values[map['fontFamily'] ?? 0],
      primaryColor: Color(int.tryParse(map['primaryColor'] ?? '2E7D32', radix: 16) ?? 0xFF2E7D32),
      secondaryColor: Color(int.tryParse(map['secondaryColor'] ?? 'FFB300', radix: 16) ?? 0xFFFFB300),
      backgroundColor: Color(int.tryParse(map['backgroundColor'] ?? 'FFFFFF', radix: 16) ?? 0xFFFFFFFF),
      textColor: Color(int.tryParse(map['textColor'] ?? '000000', radix: 16) ?? 0xFF000000),
      showArabic: map['showArabic'] ?? true,
      showTranslation: map['showTranslation'] ?? true,
      showTransliteration: map['showTransliteration'] ?? false,
      enableAnimations: map['enableAnimations'] ?? true,
      animationSpeed: map['animationSpeed']?.toDouble() ?? 1.0,
      gridColumns: map['gridColumns'] ?? 2,
      cardElevation: map['cardElevation']?.toDouble() ?? 2.0,
      borderRadius: map['borderRadius']?.toDouble() ?? 8.0,
      contentPadding: map['contentPadding'] != null
          ? EdgeInsets.only(
              left: map['contentPadding']['left']?.toDouble() ?? 16.0,
              top: map['contentPadding']['top']?.toDouble() ?? 16.0,
              right: map['contentPadding']['right']?.toDouble() ?? 16.0,
              bottom: map['contentPadding']['bottom']?.toDouble() ?? 16.0,
            )
          : const EdgeInsets.all(16.0),
    );
  }
}

/// Model for Quran-specific view configuration
class QuranViewConfiguration extends ViewConfiguration {
  final QuranViewMode quranViewMode;
  final bool showVerseNumbers;
  final bool showSurahHeaders;
  final bool showJuzMarkers;
  final bool showTafseer;
  @override
  final bool showTransliteration;
  final double arabicFontSize;
  final double translationFontSize;
  final double tafseerFontSize;
  final Color verseNumberColor;
  final Color surahHeaderColor;
  final bool enableWordByWordTranslation;
  final bool enableAudioPlayback;
  final bool showBookmarks;

  const QuranViewConfiguration({
    super.viewMode,
    super.layoutDensity,
    super.fontSize,
    super.fontFamily,
    super.primaryColor,
    super.secondaryColor,
    super.backgroundColor,
    super.textColor,
    super.showArabic,
    super.showTranslation,
    super.enableAnimations,
    super.animationSpeed,
    super.contentPadding,
    this.quranViewMode = QuranViewMode.bookView,
    this.showVerseNumbers = true,
    this.showSurahHeaders = true,
    this.showJuzMarkers = true,
    this.showTafseer = false,
    this.showTransliteration = false,
    this.arabicFontSize = 18.0,
    this.translationFontSize = 14.0,
    this.tafseerFontSize = 12.0,
    this.verseNumberColor = const Color(0xFF2E7D32),
    this.surahHeaderColor = const Color(0xFFFFB300),
    this.enableWordByWordTranslation = false,
    this.enableAudioPlayback = true,
    this.showBookmarks = true,
  });

  QuranViewConfiguration copyWithQuran({
    ViewMode? viewMode,
    LayoutDensity? layoutDensity,
    double? fontSize,
    FontFamily? fontFamily,
    Color? primaryColor,
    Color? secondaryColor,
    Color? backgroundColor,
    Color? textColor,
    bool? showArabic,
    bool? showTranslation,
    bool? enableAnimations,
    double? animationSpeed,
    EdgeInsets? contentPadding,
    QuranViewMode? quranViewMode,
    bool? showVerseNumbers,
    bool? showSurahHeaders,
    bool? showJuzMarkers,
    bool? showTafseer,
    bool? showTransliteration,
    double? arabicFontSize,
    double? translationFontSize,
    double? tafseerFontSize,
    Color? verseNumberColor,
    Color? surahHeaderColor,
    bool? enableWordByWordTranslation,
    bool? enableAudioPlayback,
    bool? showBookmarks,
  }) {
    return QuranViewConfiguration(
      viewMode: viewMode ?? this.viewMode,
      layoutDensity: layoutDensity ?? this.layoutDensity,
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      primaryColor: primaryColor ?? this.primaryColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      showArabic: showArabic ?? this.showArabic,
      showTranslation: showTranslation ?? this.showTranslation,
      enableAnimations: enableAnimations ?? this.enableAnimations,
      animationSpeed: animationSpeed ?? this.animationSpeed,
      contentPadding: contentPadding ?? this.contentPadding,
      quranViewMode: quranViewMode ?? this.quranViewMode,
      showVerseNumbers: showVerseNumbers ?? this.showVerseNumbers,
      showSurahHeaders: showSurahHeaders ?? this.showSurahHeaders,
      showJuzMarkers: showJuzMarkers ?? this.showJuzMarkers,
      showTafseer: showTafseer ?? this.showTafseer,
      showTransliteration: showTransliteration ?? this.showTransliteration,
      arabicFontSize: arabicFontSize ?? this.arabicFontSize,
      translationFontSize: translationFontSize ?? this.translationFontSize,
      tafseerFontSize: tafseerFontSize ?? this.tafseerFontSize,
      verseNumberColor: verseNumberColor ?? this.verseNumberColor,
      surahHeaderColor: surahHeaderColor ?? this.surahHeaderColor,
      enableWordByWordTranslation: enableWordByWordTranslation ?? this.enableWordByWordTranslation,
      enableAudioPlayback: enableAudioPlayback ?? this.enableAudioPlayback,
      showBookmarks: showBookmarks ?? this.showBookmarks,
    );
  }

  factory QuranViewConfiguration.fromMap(Map<String, dynamic> map) {
    return QuranViewConfiguration(
      viewMode: ViewMode.values[map['viewMode'] ?? 0],
      layoutDensity: LayoutDensity.values[map['layoutDensity'] ?? 0],
      fontSize: map['fontSize']?.toDouble() ?? 16.0,
      fontFamily: FontFamily.values[map['fontFamily'] ?? 0],
      primaryColor: Color(int.tryParse(map['primaryColor'] ?? '2E7D32', radix: 16) ?? 0xFF2E7D32),
      secondaryColor: Color(int.tryParse(map['secondaryColor'] ?? 'FFB300', radix: 16) ?? 0xFFFFB300),
      backgroundColor: Color(int.tryParse(map['backgroundColor'] ?? 'FFFFFF', radix: 16) ?? 0xFFFFFFFF),
      textColor: Color(int.tryParse(map['textColor'] ?? '000000', radix: 16) ?? 0xFF000000),
      showArabic: map['showArabic'] ?? true,
      showTranslation: map['showTranslation'] ?? true,
      enableAnimations: map['enableAnimations'] ?? true,
      animationSpeed: map['animationSpeed']?.toDouble() ?? 1.0,
      contentPadding: map['contentPadding'] != null
          ? EdgeInsets.only(
              left: map['contentPadding']['left']?.toDouble() ?? 16.0,
              top: map['contentPadding']['top']?.toDouble() ?? 16.0,
              right: map['contentPadding']['right']?.toDouble() ?? 16.0,
              bottom: map['contentPadding']['bottom']?.toDouble() ?? 16.0,
            )
          : const EdgeInsets.all(16.0),
      quranViewMode: QuranViewMode.values[map['quranViewMode'] ?? 0],
      showVerseNumbers: map['showVerseNumbers'] ?? true,
      showSurahHeaders: map['showSurahHeaders'] ?? true,
      showJuzMarkers: map['showJuzMarkers'] ?? true,
      showTafseer: map['showTafseer'] ?? false,
      showTransliteration: map['showTransliteration'] ?? false,
      arabicFontSize: map['arabicFontSize']?.toDouble() ?? 18.0,
      translationFontSize: map['translationFontSize']?.toDouble() ?? 14.0,
      tafseerFontSize: map['tafseerFontSize']?.toDouble() ?? 12.0,
      verseNumberColor: Color(int.tryParse(map['verseNumberColor'] ?? '2E7D32', radix: 16) ?? 0xFF2E7D32),
      surahHeaderColor: Color(int.tryParse(map['surahHeaderColor'] ?? 'FFB300', radix: 16) ?? 0xFFFFB300),
      enableWordByWordTranslation: map['enableWordByWordTranslation'] ?? false,
      enableAudioPlayback: map['enableAudioPlayback'] ?? true,
      showBookmarks: map['showBookmarks'] ?? true,
    );
  }
}

/// Extension methods for ViewMode
extension ViewModeExtension on ViewMode {
  String get displayName {
    switch (this) {
      case ViewMode.list:
        return 'قائمة';
      case ViewMode.grid:
        return 'شبكة';
      case ViewMode.card:
        return 'بطاقات';
      case ViewMode.compact:
        return 'مضغوط';
      case ViewMode.detailed:
        return 'مفصل';
      case ViewMode.minimal:
        return 'بسيط';
    }
  }

  IconData get icon {
    switch (this) {
      case ViewMode.list:
        return Icons.list;
      case ViewMode.grid:
        return Icons.grid_view;
      case ViewMode.card:
        return Icons.view_agenda;
      case ViewMode.compact:
        return Icons.view_compact;
      case ViewMode.detailed:
        return Icons.view_stream;
      case ViewMode.minimal:
        return Icons.view_headline;
    }
  }
}

/// Extension methods for FontFamily
extension FontFamilyExtension on FontFamily {
  String get fontName {
    switch (this) {
      case FontFamily.system:
        return 'System';
      case FontFamily.amiri:
        return 'Amiri';
      case FontFamily.notoSansArabic:
        return 'Noto Sans Arabic';
      case FontFamily.scheherazade:
        return 'Scheherazade';
      case FontFamily.cairo:
        return 'Cairo';
      case FontFamily.tajawal:
        return 'Tajawal';
    }
  }

  String get displayName {
    switch (this) {
      case FontFamily.system:
        return 'النظام';
      case FontFamily.amiri:
        return 'أميري';
      case FontFamily.notoSansArabic:
        return 'نوتو العربية';
      case FontFamily.scheherazade:
        return 'شهرزاد';
      case FontFamily.cairo:
        return 'القاهرة';
      case FontFamily.tajawal:
        return 'تجوال';
    }
  }
}
