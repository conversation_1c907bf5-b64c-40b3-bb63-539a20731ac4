import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../providers/tasbeeh_dua_provider.dart';

class TasbeehCounterWidget extends StatefulWidget {
  const TasbeehCounterWidget({super.key});

  @override
  State<TasbeehCounterWidget> createState() => _TasbeehCounterWidgetState();
}

class _TasbeehCounterWidgetState extends State<TasbeehCounterWidget>
    with TickerProviderStateMixin {
  int _currentCount = 0;
  int _targetCount = 33;
  int _selectedTasbeehIndex = 0;
  bool _isSessionActive = false;
  bool _showNamesOfAllah = false;
  bool _isPressed = false;
  DateTime? _sessionStartTime;

  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  final List<TasbeehData> _defaultTasbeeh = [
    TasbeehData(
      arabicText: 'سُبْحَانَ اللهِ',
      transliteration: 'Subhan Allah',
      translation: 'Glory be to Allah',
    ),
    TasbeehData(
      arabicText: 'الْحَمْدُ لِلَّهِ',
      transliteration: 'Alhamdulillah',
      translation: 'Praise be to Allah',
    ),
    TasbeehData(
      arabicText: 'اللهُ أَكْبَرُ',
      transliteration: 'Allahu Akbar',
      translation: 'Allah is Greatest',
    ),
    TasbeehData(
      arabicText: 'لَا إِلَهَ إِلَّا اللهُ',
      transliteration: 'La ilaha illa Allah',
      translation: 'There is no god but Allah',
    ),
    TasbeehData(
      arabicText: 'أَسْتَغْفِرُ اللهَ',
      transliteration: 'Astaghfirullah',
      translation: 'I seek forgiveness from Allah',
    ),
  ];

  final List<TasbeehData> _namesOfAllah = [
    TasbeehData(arabicText: 'الرَّحْمَنُ', transliteration: 'Ar-Rahman', translation: 'The Most Gracious'),
    TasbeehData(arabicText: 'الرَّحِيمُ', transliteration: 'Ar-Raheem', translation: 'The Most Merciful'),
    TasbeehData(arabicText: 'الْمَلِكُ', transliteration: 'Al-Malik', translation: 'The King'),
    TasbeehData(arabicText: 'الْقُدُّوسُ', transliteration: 'Al-Quddus', translation: 'The Most Holy'),
    TasbeehData(arabicText: 'السَّلَامُ', transliteration: 'As-Salaam', translation: 'The Source of Peace'),
    TasbeehData(arabicText: 'الْمُؤْمِنُ', transliteration: 'Al-Mu\'min', translation: 'The Guardian of Faith'),
    TasbeehData(arabicText: 'الْمُهَيْمِنُ', transliteration: 'Al-Muhaymin', translation: 'The Protector'),
    TasbeehData(arabicText: 'الْعَزِيزُ', transliteration: 'Al-Aziz', translation: 'The Mighty'),
    TasbeehData(arabicText: 'الْجَبَّارُ', transliteration: 'Al-Jabbar', translation: 'The Compeller'),
    TasbeehData(arabicText: 'الْمُتَكَبِّرُ', transliteration: 'Al-Mutakabbir', translation: 'The Supreme'),
    TasbeehData(arabicText: 'الْخَالِقُ', transliteration: 'Al-Khaliq', translation: 'The Creator'),
    TasbeehData(arabicText: 'الْبَارِئُ', transliteration: 'Al-Bari\'', translation: 'The Originator'),
    TasbeehData(arabicText: 'الْمُصَوِّرُ', transliteration: 'Al-Musawwir', translation: 'The Fashioner'),
    TasbeehData(arabicText: 'الْغَفَّارُ', transliteration: 'Al-Ghaffar', translation: 'The Great Forgiver'),
    TasbeehData(arabicText: 'الْقَهَّارُ', transliteration: 'Al-Qahhar', translation: 'The Subduer'),
    TasbeehData(arabicText: 'الْوَهَّابُ', transliteration: 'Al-Wahhab', translation: 'The Bestower'),
    TasbeehData(arabicText: 'الرَّزَّاقُ', transliteration: 'Ar-Razzaq', translation: 'The Provider'),
    TasbeehData(arabicText: 'الْفَتَّاحُ', transliteration: 'Al-Fattah', translation: 'The Opener'),
    TasbeehData(arabicText: 'الْعَلِيمُ', transliteration: 'Al-Aleem', translation: 'The All-Knowing'),
    TasbeehData(arabicText: 'الْقَابِضُ', transliteration: 'Al-Qabid', translation: 'The Restrainer'),
    TasbeehData(arabicText: 'الْبَاسِطُ', transliteration: 'Al-Basit', translation: 'The Expander'),
    TasbeehData(arabicText: 'الْخَافِضُ', transliteration: 'Al-Khafid', translation: 'The Abaser'),
    TasbeehData(arabicText: 'الرَّافِعُ', transliteration: 'Ar-Rafi', translation: 'The Exalter'),
    TasbeehData(arabicText: 'الْمُعِزُّ', transliteration: 'Al-Mu\'izz', translation: 'The Honorer'),
    TasbeehData(arabicText: 'الْمُذِلُّ', transliteration: 'Al-Mudhill', translation: 'The Humiliator'),
    TasbeehData(arabicText: 'السَّمِيعُ', transliteration: 'As-Sami', translation: 'The All-Hearing'),
    TasbeehData(arabicText: 'الْبَصِيرُ', transliteration: 'Al-Basir', translation: 'The All-Seeing'),
    TasbeehData(arabicText: 'الْحَكَمُ', transliteration: 'Al-Hakam', translation: 'The Judge'),
    TasbeehData(arabicText: 'الْعَدْلُ', transliteration: 'Al-Adl', translation: 'The Just'),
  ];

  final List<int> _targetOptions = [33, 99, 100, 1000];

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Initialize animations
    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOutCubic,
    ));

    // Start pulse animation
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TasbeehDuaProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Error: ${provider.error}'),
                ElevatedButton(
                  onPressed: () => provider.loadTasbeehItems(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              _buildTasbeehTypeToggle(),
              const SizedBox(height: 16),
              _buildTasbeehSelector(provider),
              const SizedBox(height: 20),
              _buildTargetSelector(),
              const SizedBox(height: 30),
              Expanded(
                child: _buildCounterSection(provider),
              ),
              _buildActionButtons(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTasbeehTypeToggle() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Text(
              'Tasbeeh Type:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            SegmentedButton<bool>(
              segments: [
                ButtonSegment<bool>(
                  value: false,
                  label: const Text('Regular'),
                  icon: Icon(MdiIcons.counter),
                ),
                ButtonSegment<bool>(
                  value: true,
                  label: const Text('99 Names'),
                  icon: Icon(MdiIcons.star),
                ),
              ],
              selected: {_showNamesOfAllah},
              onSelectionChanged: (Set<bool> selection) {
                setState(() {
                  _showNamesOfAllah = selection.first;
                  _selectedTasbeehIndex = 0;
                  if (_isSessionActive) {
                    _resetCounter();
                  }
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTasbeehSelector(TasbeehDuaProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Select Tasbeeh',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _showNamesOfAllah ? _namesOfAllah.length : provider.tasbeehItems.length,
                itemBuilder: (context, index) {
                  final tasbeeh = _showNamesOfAllah
                      ? _namesOfAllah[index]
                      : (provider.tasbeehItems.isNotEmpty
                          ? TasbeehData(
                              arabicText: provider.tasbeehItems[index].arabicText,
                              transliteration: provider.tasbeehItems[index].transliteration ?? '',
                              translation: provider.tasbeehItems[index].translation ?? '',
                            )
                          : _defaultTasbeeh[index % _defaultTasbeeh.length]);
                  final isSelected = index == _selectedTasbeehIndex;
                  
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedTasbeehIndex = index;
                        if (_isSessionActive) {
                          _resetCounter();
                        }
                      });
                    },
                    child: Container(
                      width: 200,
                      margin: const EdgeInsets.only(right: 12),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? AppTheme.primaryGreen.withValues(alpha: 0.1)
                            : Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected 
                              ? AppTheme.primaryGreen 
                              : Colors.grey.withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            tasbeeh.arabicText,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                            textDirection: TextDirection.rtl,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            tasbeeh.transliteration,
                            style: const TextStyle(
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            tasbeeh.translation,
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTargetSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Target Count',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: _targetOptions.map((target) {
                final isSelected = target == _targetCount;
                return ChoiceChip(
                  label: Text('$target'),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _targetCount = target;
                        if (_isSessionActive) {
                          _resetCounter();
                        }
                      });
                    }
                  },
                  selectedColor: AppTheme.primaryGreen.withValues(alpha: 0.2),
                  checkmarkColor: AppTheme.primaryGreen,
                );
              }).toList(),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('Custom: '),
                Expanded(
                  child: TextField(
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      hintText: 'Enter custom count',
                      isDense: true,
                    ),
                    onSubmitted: (value) {
                      final customTarget = int.tryParse(value);
                      if (customTarget != null && customTarget > 0) {
                        setState(() {
                          _targetCount = customTarget;
                          if (_isSessionActive) {
                            _resetCounter();
                          }
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCounterSection(TasbeehDuaProvider provider) {
    final progress = _targetCount > 0 ? _currentCount / _targetCount : 0.0;
    final isCompleted = _currentCount >= _targetCount;
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Arabic Text Display
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppTheme.primaryGreen.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppTheme.primaryGreen.withValues(alpha: 0.3),
            ),
          ),
          child: Text(
            _getCurrentTasbeehText(provider),
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
            textDirection: TextDirection.rtl,
          ),
        ),
        
        const SizedBox(height: 30),
        
        // Enhanced Progress Circle with Animations
        SizedBox(
          width: 220,
          height: 220,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Outer glow effect
              Container(
                width: 220,
                height: 220,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: (isCompleted ? AppTheme.accentGold : AppTheme.primaryGreen)
                          .withValues(alpha: 0.2),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
              ),
              // Background circle with gradient
              Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      Colors.white,
                      Colors.grey[50]!,
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
              ),
              // Background progress track
              SizedBox(
                width: 180,
                height: 180,
                child: CircularProgressIndicator(
                  value: 1.0,
                  strokeWidth: 12,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Colors.grey[200]!,
                  ),
                ),
              ),
              // Animated progress circle
              AnimatedBuilder(
                animation: _progressAnimation,
                builder: (context, child) {
                  return SizedBox(
                    width: 180,
                    height: 180,
                    child: CircularProgressIndicator(
                      value: (progress * _progressAnimation.value).clamp(0.0, 1.0),
                      strokeWidth: 12,
                      backgroundColor: Colors.transparent,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isCompleted ? AppTheme.accentGold : AppTheme.primaryGreen,
                      ),
                      strokeCap: StrokeCap.round,
                    ),
                  );
                },
              ),
              // Inner decorative circle
              Container(
                width: 140,
                height: 140,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      (isCompleted ? AppTheme.accentGold : AppTheme.primaryGreen)
                          .withValues(alpha: 0.1),
                      (isCompleted ? AppTheme.accentGold : AppTheme.primaryGreen)
                          .withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
              // Count display with enhanced styling
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AnimatedDefaultTextStyle(
                    duration: const Duration(milliseconds: 300),
                    style: TextStyle(
                      fontSize: isCompleted ? 52 : 48,
                      fontWeight: FontWeight.bold,
                      color: isCompleted ? AppTheme.accentGold : AppTheme.primaryGreen,
                      shadows: [
                        Shadow(
                          color: (isCompleted ? AppTheme.accentGold : AppTheme.primaryGreen)
                              .withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Text('$_currentCount'),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'of $_targetCount',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (isCompleted) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppTheme.accentGold.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppTheme.accentGold.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        'Completed!',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.accentGold,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              // Completion celebration effect
              if (isCompleted)
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Container(
                      width: 200 + (_pulseAnimation.value * 40),
                      height: 200 + (_pulseAnimation.value * 40),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppTheme.accentGold.withValues(
                            alpha: 0.3 * (1 - _pulseAnimation.value),
                          ),
                          width: 3,
                        ),
                      ),
                    );
                  },
                ),
            ],
          ),
        ),
        
        const SizedBox(height: 30),
        
        // Enhanced Tap Button with Animation
        AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          child: GestureDetector(
            onTap: isCompleted ? null : _incrementCounter,
            onTapDown: (_) => setState(() => _isPressed = true),
            onTapUp: (_) => setState(() => _isPressed = false),
            onTapCancel: () => setState(() => _isPressed = false),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 150),
              width: _isPressed ? 95 : 120,
              height: _isPressed ? 95 : 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: isCompleted
                  ? LinearGradient(
                      colors: [Colors.grey[400]!, Colors.grey[500]!],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : LinearGradient(
                      colors: [
                        AppTheme.primaryGreen,
                        AppTheme.primaryGreen.withValues(alpha: 0.8),
                        AppTheme.accentGold.withValues(alpha: 0.3),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                boxShadow: isCompleted ? null : [
                  BoxShadow(
                    color: AppTheme.primaryGreen.withValues(alpha: 0.4),
                    blurRadius: _isPressed ? 8 : 20,
                    offset: Offset(0, _isPressed ? 3 : 8),
                    spreadRadius: _isPressed ? 0 : 2,
                  ),
                  BoxShadow(
                    color: AppTheme.accentGold.withValues(alpha: 0.2),
                    blurRadius: _isPressed ? 4 : 12,
                    offset: Offset(0, _isPressed ? 1 : 4),
                  ),
                ],
                border: Border.all(
                  color: isCompleted
                    ? Colors.transparent
                    : AppTheme.accentGold.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Ripple effect background
                  if (!isCompleted && _isPressed)
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                  // Main icon
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      isCompleted ? Icons.check_circle : Icons.touch_app,
                      key: ValueKey(isCompleted),
                      color: Colors.white,
                      size: isCompleted ? 45 : 40,
                    ),
                  ),
                  // Pulse animation for active state
                  if (!isCompleted && _isSessionActive)
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Container(
                          width: 120 + (_pulseAnimation.value * 20),
                          height: 120 + (_pulseAnimation.value * 20),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: AppTheme.accentGold.withValues(
                                alpha: 0.3 * (1 - _pulseAnimation.value),
                              ),
                              width: 2,
                            ),
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        Text(
          isCompleted ? 'Completed!' : 'Tap to count',
          style: TextStyle(
            fontSize: 16,
            color: isCompleted ? AppTheme.primaryGreen : Colors.grey[600],
            fontWeight: isCompleted ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Reset Button
          ElevatedButton.icon(
            onPressed: _resetCounter,
            icon: const Icon(Icons.refresh),
            label: const Text('Reset'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
          
          // Session Button
          ElevatedButton.icon(
            onPressed: _toggleSession,
            icon: Icon(_isSessionActive ? Icons.stop : Icons.play_arrow),
            label: Text(_isSessionActive ? 'End Session' : 'Start Session'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _isSessionActive ? Colors.red : AppTheme.primaryGreen,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _incrementCounter() {
    if (!_isSessionActive) {
      _startSession();
    }
    
    setState(() {
      _currentCount++;
    });
    
    // Haptic feedback
    HapticFeedback.lightImpact();
    
    // Check if completed
    if (_currentCount >= _targetCount) {
      _onSessionCompleted();
    }
  }

  void _resetCounter() {
    setState(() {
      _currentCount = 0;
      _isSessionActive = false;
      _sessionStartTime = null;
    });
  }

  void _startSession() {
    setState(() {
      _isSessionActive = true;
      _sessionStartTime = DateTime.now();
    });
  }

  void _toggleSession() {
    if (_isSessionActive) {
      _endSession();
    } else {
      _startSession();
    }
  }

  void _endSession() {
    if (_isSessionActive && _sessionStartTime != null) {
      final duration = DateTime.now().difference(_sessionStartTime!);
      _showSessionSummary(duration);
    }
    
    setState(() {
      _isSessionActive = false;
      _sessionStartTime = null;
    });
  }

  void _onSessionCompleted() {
    if (_sessionStartTime != null) {
      final duration = DateTime.now().difference(_sessionStartTime!);
      _showCompletionDialog(duration);
    }
  }

  void _showSessionSummary(Duration duration) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Session Summary'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Tasbeeh: ${_getCurrentTasbeehTransliteration()}'),
            Text('Count: $_currentCount / $_targetCount'),
            Text('Duration: ${duration.inMinutes}m ${duration.inSeconds % 60}s'),
            if (_currentCount >= _targetCount)
              const Text('Status: Completed ✓', style: TextStyle(color: AppTheme.primaryGreen)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showCompletionDialog(Duration duration) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(MdiIcons.checkCircle, color: AppTheme.primaryGreen),
            const SizedBox(width: 8),
            const Text('Tasbeeh Completed!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Congratulations! You have completed $_targetCount repetitions.'),
            const SizedBox(height: 16),
            Text('Tasbeeh: ${_getCurrentTasbeehTransliteration()}'),
            Text('Time taken: ${duration.inMinutes}m ${duration.inSeconds % 60}s'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _resetCounter();
            },
            child: const Text('Continue'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _resetCounter();
            },
            child: const Text('Start Again'),
          ),
        ],
      ),
    );
  }

  String _getCurrentTasbeehText(TasbeehDuaProvider provider) {
    if (_showNamesOfAllah) {
      return _namesOfAllah[_selectedTasbeehIndex].arabicText;
    } else if (provider.tasbeehItems.isNotEmpty) {
      return provider.tasbeehItems[_selectedTasbeehIndex].arabicText;
    } else {
      return _defaultTasbeeh[_selectedTasbeehIndex % _defaultTasbeeh.length].arabicText;
    }
  }

  String _getCurrentTasbeehTransliteration() {
    if (_showNamesOfAllah) {
      return _namesOfAllah[_selectedTasbeehIndex].transliteration;
    } else {
      return _defaultTasbeeh[_selectedTasbeehIndex % _defaultTasbeeh.length].transliteration;
    }
  }
}

class TasbeehData {
  final String arabicText;
  final String transliteration;
  final String translation;

  TasbeehData({
    required this.arabicText,
    required this.transliteration,
    required this.translation,
  });
}
