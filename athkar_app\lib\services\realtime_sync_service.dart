import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../database/database_helper.dart';
import '../models/athkar_models.dart';
// import '../models/user_models.dart' as user_models; // Unused for now
import '../services/new_supabase_service.dart';
import 'dart:async';

class RealtimeSyncService {
  static final DatabaseHelper _dbHelper = DatabaseHelper();
  static SupabaseClient? _supabase;
  static bool _isInitialized = false;
  static bool _isSyncing = false;
  
  // Real-time subscriptions
  static RealtimeChannel? _routinesChannel;
  static RealtimeChannel? _stepsChannel;
  static RealtimeChannel? _progressChannel;
  static RealtimeChannel? _settingsChannel;
  
  // Stream controllers for real-time updates
  static final StreamController<AthkarRoutine> _routineUpdatesController = 
      StreamController<AthkarRoutine>.broadcast();
  static final StreamController<AthkarStep> _stepUpdatesController = 
      StreamController<AthkarStep>.broadcast();
  static final StreamController<Map<String, dynamic>> _progressUpdatesController = 
      StreamController<Map<String, dynamic>>.broadcast();
  static final StreamController<Map<String, dynamic>> _settingsUpdatesController = 
      StreamController<Map<String, dynamic>>.broadcast();

  // Public streams
  static Stream<AthkarRoutine> get routineUpdates => _routineUpdatesController.stream;
  static Stream<AthkarStep> get stepUpdates => _stepUpdatesController.stream;
  static Stream<Map<String, dynamic>> get progressUpdates => _progressUpdatesController.stream;
  static Stream<Map<String, dynamic>> get settingsUpdates => _settingsUpdatesController.stream;

  /// Initialize real-time sync service
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await NewSupabaseService.initialize();
      _supabase = NewSupabaseService.client;
      
      await _setupRealtimeSubscriptions();
      
      _isInitialized = true;
      debugPrint('Realtime sync service initialized successfully');
      
    } catch (e) {
      debugPrint('Error initializing realtime sync service: $e');
      rethrow;
    }
  }

  /// Setup real-time subscriptions for all tables
  static Future<void> _setupRealtimeSubscriptions() async {
    final user = _supabase!.auth.currentUser;
    if (user == null) {
      debugPrint('No authenticated user for real-time subscriptions');
      return;
    }

    try {
      // Subscribe to athkar_routines changes
      _routinesChannel = _supabase!
          .channel('athkar_routines_${user.id}')
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: 'athkar_routines',
            filter: PostgresChangeFilter(
              type: PostgresChangeFilterType.eq,
              column: 'user_id',
              value: user.id,
            ),
            callback: _handleRoutineChange,
          )
          .subscribe();

      // Subscribe to athkar_steps changes
      _stepsChannel = _supabase!
          .channel('athkar_steps_${user.id}')
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: 'athkar_steps',
            filter: PostgresChangeFilter(
              type: PostgresChangeFilterType.eq,
              column: 'user_id',
              value: user.id,
            ),
            callback: _handleStepChange,
          )
          .subscribe();

      // Subscribe to user_progress changes
      _progressChannel = _supabase!
          .channel('user_progress_${user.id}')
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: 'user_progress',
            filter: PostgresChangeFilter(
              type: PostgresChangeFilterType.eq,
              column: 'user_id',
              value: user.id,
            ),
            callback: _handleProgressChange,
          )
          .subscribe();

      // Subscribe to user_settings changes
      _settingsChannel = _supabase!
          .channel('user_settings_${user.id}')
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: 'user_settings',
            filter: PostgresChangeFilter(
              type: PostgresChangeFilterType.eq,
              column: 'user_id',
              value: user.id,
            ),
            callback: _handleSettingsChange,
          )
          .subscribe();

      debugPrint('Real-time subscriptions setup completed');
      
    } catch (e) {
      debugPrint('Error setting up real-time subscriptions: $e');
    }
  }

  /// Handle routine changes from real-time subscription
  static void _handleRoutineChange(PostgresChangePayload payload) {
    try {
      debugPrint('Routine change detected: ${payload.eventType}');

      final routineData = payload.newRecord;
      // Process the record if it exists
      {
        final routine = AthkarRoutine(
          id: routineData['id'],
          title: routineData['title'],
          description: routineData['description'],
          categoryId: routineData['category_id'],
          estimatedDuration: routineData['estimated_duration'],
          colorHex: routineData['color_hex'],
          createdAt: DateTime.parse(routineData['created_at']),
          updatedAt: DateTime.parse(routineData['updated_at']),
        );

        // Update local database
        _updateLocalRoutine(routine, payload.eventType);

        // Notify listeners
        _routineUpdatesController.add(routine);
      }

      if (payload.eventType == PostgresChangeEvent.delete) {
        final oldRecord = payload.oldRecord;
        // Process deletion
        {
          // Handle deletion
          final routineId = oldRecord['id'];
          _deleteLocalRoutine(routineId);
        }
      }

    } catch (e) {
      debugPrint('Error handling routine change: $e');
    }
  }

  /// Handle step changes from real-time subscription
  static void _handleStepChange(PostgresChangePayload payload) {
    try {
      debugPrint('Step change detected: ${payload.eventType}');
      
      final newRecord = payload.newRecord;
      // Process the record
      {
        final stepData = newRecord;
        final step = AthkarStep(
          id: stepData['id'],
          routineId: stepData['routine_id'],
          stepOrder: stepData['step_order'],
          arabicText: stepData['arabic_text'],
          transliteration: stepData['transliteration'],
          translation: stepData['translation'],
          targetCount: stepData['target_count'] ?? 1,
          audioUrl: stepData['audio_url'],
          colorHex: stepData['color_hex'],
          createdAt: DateTime.parse(stepData['created_at']),
          updatedAt: DateTime.parse(stepData['updated_at']),
        );

        // Update local database
        _updateLocalStep(step, payload.eventType);
        
        // Notify listeners
        _stepUpdatesController.add(step);
      }
      
      if (payload.eventType == PostgresChangeEvent.delete) {
        // Handle deletion
        final stepId = payload.oldRecord['id'];
        _deleteLocalStep(stepId);
      }
      
    } catch (e) {
      debugPrint('Error handling step change: $e');
    }
  }

  /// Handle progress changes from real-time subscription
  static void _handleProgressChange(PostgresChangePayload payload) {
    try {
      debugPrint('Progress change detected: ${payload.eventType}');
      
      // Process the record
      {
        final progressData = payload.newRecord;
        
        // Update local database
        _updateLocalProgress(progressData, payload.eventType);
        
        // Notify listeners
        _progressUpdatesController.add(progressData);
      }
      
    } catch (e) {
      debugPrint('Error handling progress change: $e');
    }
  }

  /// Handle settings changes from real-time subscription
  static void _handleSettingsChange(PostgresChangePayload payload) {
    try {
      debugPrint('Settings change detected: ${payload.eventType}');
      
      // Process the record
      {
        final settingsData = payload.newRecord;
        
        // Update local database
        _updateLocalSettings(settingsData, payload.eventType);
        
        // Notify listeners
        _settingsUpdatesController.add(settingsData);
      }
      
    } catch (e) {
      debugPrint('Error handling settings change: $e');
    }
  }

  /// Update local routine in SQLite
  static Future<void> _updateLocalRoutine(AthkarRoutine routine, PostgresChangeEvent eventType) async {
    try {
      if (eventType == PostgresChangeEvent.insert || eventType == PostgresChangeEvent.update) {
        await _dbHelper.insertOrUpdateRoutine(routine);
      }
    } catch (e) {
      debugPrint('Error updating local routine: $e');
    }
  }

  /// Update local step in SQLite
  static Future<void> _updateLocalStep(AthkarStep step, PostgresChangeEvent eventType) async {
    try {
      if (eventType == PostgresChangeEvent.insert || eventType == PostgresChangeEvent.update) {
        await _dbHelper.insertOrUpdateStep(step);
      }
    } catch (e) {
      debugPrint('Error updating local step: $e');
    }
  }

  /// Update local progress in SQLite
  static Future<void> _updateLocalProgress(Map<String, dynamic> progressData, PostgresChangeEvent eventType) async {
    try {
      if (eventType == PostgresChangeEvent.insert || eventType == PostgresChangeEvent.update) {
        await _dbHelper.insertOrUpdateProgress(progressData);
      }
    } catch (e) {
      debugPrint('Error updating local progress: $e');
    }
  }

  /// Update local settings in SQLite
  static Future<void> _updateLocalSettings(Map<String, dynamic> settingsData, PostgresChangeEvent eventType) async {
    try {
      if (eventType == PostgresChangeEvent.insert || eventType == PostgresChangeEvent.update) {
        await _dbHelper.insertOrUpdateSetting(settingsData);
      }
    } catch (e) {
      debugPrint('Error updating local settings: $e');
    }
  }

  /// Delete local routine
  static Future<void> _deleteLocalRoutine(String routineId) async {
    try {
      await _dbHelper.deleteRoutine(routineId);
    } catch (e) {
      debugPrint('Error deleting local routine: $e');
    }
  }

  /// Delete local step
  static Future<void> _deleteLocalStep(String stepId) async {
    try {
      await _dbHelper.deleteStep(stepId);
    } catch (e) {
      debugPrint('Error deleting local step: $e');
    }
  }

  /// Perform bidirectional sync
  static Future<void> performBidirectionalSync() async {
    if (_isSyncing) {
      debugPrint('Sync already in progress');
      return;
    }

    _isSyncing = true;
    
    try {
      debugPrint('Starting bidirectional sync...');
      
      // Sync local changes to Supabase
      await _syncLocalToSupabase();
      
      // Sync Supabase changes to local
      await _syncSupabaseToLocal();
      
      debugPrint('Bidirectional sync completed successfully');
      
    } catch (e) {
      debugPrint('Error during bidirectional sync: $e');
      rethrow;
    } finally {
      _isSyncing = false;
    }
  }

  /// Sync local changes to Supabase
  static Future<void> _syncLocalToSupabase() async {
    try {
      // Get all local routines and sync them
      final localRoutines = await _dbHelper.getAllRoutines();
      for (final routineData in localRoutines) {
        // Convert to AthkarRoutine and sync
        final routine = _convertToAthkarRoutine(routineData);
        await NewSupabaseService.syncAthkarRoutine(routine);
      }
      
    } catch (e) {
      debugPrint('Error syncing local to Supabase: $e');
    }
  }

  /// Sync Supabase changes to local
  static Future<void> _syncSupabaseToLocal() async {
    try {
      // Get all Supabase routines and sync them locally
      final supabaseRoutines = await NewSupabaseService.fetchUserRoutines();
      for (final routine in supabaseRoutines) {
        await _dbHelper.insertOrUpdateRoutine(routine);
      }
      
    } catch (e) {
      debugPrint('Error syncing Supabase to local: $e');
    }
  }

  /// Convert routine data to AthkarRoutine object
  static AthkarRoutine _convertToAthkarRoutine(Map<String, dynamic> data) {
    return AthkarRoutine(
      id: data['id'],
      title: data['title'],
      description: data['description'],
      categoryId: data['categoryId'],
      estimatedDuration: data['estimatedDuration'],
      colorHex: data['colorHex'],
      createdAt: DateTime.parse(data['createdAt']),
      updatedAt: DateTime.parse(data['updatedAt']),
    );
  }

  /// Cleanup subscriptions
  static Future<void> dispose() async {
    await _routinesChannel?.unsubscribe();
    await _stepsChannel?.unsubscribe();
    await _progressChannel?.unsubscribe();
    await _settingsChannel?.unsubscribe();
    
    _routineUpdatesController.close();
    _stepUpdatesController.close();
    _progressUpdatesController.close();
    _settingsUpdatesController.close();
    
    _isInitialized = false;
    debugPrint('Realtime sync service disposed');
  }

  /// Check if service is initialized
  static bool get isInitialized => _isInitialized;

  /// Check if sync is in progress
  static bool get isSyncing => _isSyncing;
}
