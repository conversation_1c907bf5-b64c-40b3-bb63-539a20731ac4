import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../providers/hadith_provider.dart';
import '../services/language_service.dart';
import '../l10n/app_localizations.dart';
import '../theme/app_theme.dart';
import '../models/hadith_models.dart';

class HadithDetailScreen extends StatefulWidget {
  final HadithData hadith;
  final HadithCollection collection;
  final HadithBook book;

  const HadithDetailScreen({
    super.key,
    required this.hadith,
    required this.collection,
    required this.book,
  });

  @override
  State<HadithDetailScreen> createState() => _HadithDetailScreenState();
}

class _HadithDetailScreenState extends State<HadithDetailScreen> {
  bool _showTranslation = true;

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    final localizations = AppLocalizations.of(context)!;

    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            '${languageService.isArabic ? "حديث رقم" : "Hadith"} ${widget.hadith.hadithNumber}',
          ),
          backgroundColor: AppTheme.primaryGreen,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: Icon(languageService.backIcon),
            onPressed: () => Navigator.pop(context),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.copy),
              onPressed: _copyHadith,
              tooltip: languageService.isArabic ? 'نسخ' : 'Copy',
            ),
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: _shareHadith,
              tooltip: languageService.isArabic ? 'مشاركة' : 'Share',
            ),
            Consumer<HadithProvider>(
              builder: (context, hadithProvider, child) {
                return IconButton(
                  icon: Icon(
                    hadithProvider.isInFavorites(
                      widget.hadith.collectionId,
                      widget.hadith.bookNumber,
                      widget.hadith.hadithNumber,
                    )
                        ? Icons.favorite
                        : Icons.favorite_border,
                  ),
                  onPressed: _toggleFavorite,
                  tooltip: languageService.isArabic ? 'المفضلة' : 'Favorite',
                );
              },
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Collection and Book Info
              Card(
                color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppTheme.primaryGreen,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Center(
                              child: Text(
                                '${widget.hadith.hadithNumber}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  languageService.isArabic 
                                      ? widget.collection.arabicName 
                                      : widget.collection.englishName,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryGreen,
                                  ),
                                ),
                                Text(
                                  languageService.isArabic 
                                      ? widget.book.arabicName 
                                      : widget.book.englishName,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getGradeColor(widget.hadith.grade).withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              languageService.isArabic ? widget.hadith.arabicGrade : widget.hadith.grade,
                              style: TextStyle(
                                color: _getGradeColor(widget.hadith.grade),
                                fontWeight: FontWeight.w500,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Arabic Text
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.format_quote,
                            color: AppTheme.primaryGreen,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            languageService.isArabic ? 'النص العربي' : 'Arabic Text',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryGreen,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      SelectableText(
                        widget.hadith.arabicText,
                        style: const TextStyle(
                          fontSize: 20,
                          height: 2.0,
                          fontWeight: FontWeight.w500,
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // English Translation (if available and enabled)
              if (widget.hadith.englishText.isNotEmpty) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.translate,
                              color: AppTheme.primaryGreen,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              languageService.isArabic ? 'الترجمة الإنجليزية' : 'English Translation',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.primaryGreen,
                              ),
                            ),
                            const Spacer(),
                            Switch(
                              value: _showTranslation,
                              onChanged: (value) {
                                setState(() => _showTranslation = value);
                              },
                              activeColor: AppTheme.primaryGreen,
                            ),
                          ],
                        ),
                        if (_showTranslation) ...[
                          const SizedBox(height: 16),
                          SelectableText(
                            widget.hadith.englishText,
                            style: TextStyle(
                              fontSize: 16,
                              height: 1.6,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Narrator Information
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.person,
                            color: AppTheme.primaryGreen,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            languageService.isArabic ? 'الراوي' : 'Narrator',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryGreen,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        languageService.isArabic ? widget.hadith.arabicNarrator : widget.hadith.narrator,
                        style: const TextStyle(
                          fontSize: 15,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Reference Information
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.bookmark,
                            color: AppTheme.primaryGreen,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            languageService.isArabic ? 'المرجع' : 'Reference',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryGreen,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        widget.hadith.reference,
                        style: const TextStyle(
                          fontSize: 14,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Notes (if available)
              if (widget.hadith.notes != null || widget.hadith.arabicNotes != null) ...[
                const SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.note,
                              color: AppTheme.primaryGreen,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              languageService.isArabic ? 'ملاحظات' : 'Notes',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.primaryGreen,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          languageService.isArabic 
                              ? (widget.hadith.arabicNotes ?? widget.hadith.notes ?? '')
                              : (widget.hadith.notes ?? widget.hadith.arabicNotes ?? ''),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[700],
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],

              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Color _getGradeColor(String grade) {
    switch (grade.toLowerCase()) {
      case 'sahih':
      case 'صحيح':
        return Colors.green;
      case 'hasan':
      case 'حسن':
        return Colors.orange;
      case 'daif':
      case 'ضعيف':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _copyHadith() {
    final languageService = Provider.of<LanguageService>(context, listen: false);
    
    final text = '''
${widget.hadith.arabicText}

${widget.hadith.englishText.isNotEmpty ? '${widget.hadith.englishText}\n\n' : ''}${languageService.isArabic ? 'الراوي' : 'Narrator'}: ${languageService.isArabic ? widget.hadith.arabicNarrator : widget.hadith.narrator}

${languageService.isArabic ? 'المرجع' : 'Reference'}: ${widget.hadith.reference}
${languageService.isArabic ? 'الدرجة' : 'Grade'}: ${languageService.isArabic ? widget.hadith.arabicGrade : widget.hadith.grade}
''';

    Clipboard.setData(ClipboardData(text: text));
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(languageService.isArabic ? 'تم نسخ الحديث' : 'Hadith copied'),
        backgroundColor: AppTheme.primaryGreen,
      ),
    );
  }

  void _shareHadith() {
    final languageService = Provider.of<LanguageService>(context, listen: false);
    
    final text = '''
${widget.hadith.arabicText}

${widget.hadith.englishText.isNotEmpty ? '${widget.hadith.englishText}\n\n' : ''}${languageService.isArabic ? 'الراوي' : 'Narrator'}: ${languageService.isArabic ? widget.hadith.arabicNarrator : widget.hadith.narrator}

${languageService.isArabic ? 'المرجع' : 'Reference'}: ${widget.hadith.reference}
${languageService.isArabic ? 'الدرجة' : 'Grade'}: ${languageService.isArabic ? widget.hadith.arabicGrade : widget.hadith.grade}

${languageService.isArabic ? 'مشارك من تطبيق الأذكار الإسلامية' : 'Shared from Islamic Athkar App'}
''';

    Share.share(text);
  }

  void _toggleFavorite() async {
    final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
    final languageService = Provider.of<LanguageService>(context, listen: false);
    
    if (hadithProvider.isInFavorites(
      widget.hadith.collectionId,
      widget.hadith.bookNumber,
      widget.hadith.hadithNumber,
    )) {
      final favoriteId = '${widget.hadith.collectionId}_${widget.hadith.bookNumber}_${widget.hadith.hadithNumber}';
      await hadithProvider.removeFromFavorites(favoriteId);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageService.isArabic ? 'تم إزالة الحديث من المفضلة' : 'Hadith removed from favorites'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      await hadithProvider.addToFavorites(
        widget.hadith,
        languageService.isArabic ? 'حديث مفضل' : 'Favorite Hadith',
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageService.isArabic ? 'تم إضافة الحديث للمفضلة' : 'Hadith added to favorites'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    }
  }
}
