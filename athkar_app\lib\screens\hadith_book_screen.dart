import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/hadith_provider.dart';
import '../services/language_service.dart';
import '../l10n/app_localizations.dart';
import '../theme/app_theme.dart';
import '../models/hadith_models.dart';
import 'hadith_detail_screen.dart';

class HadithBookScreen extends StatefulWidget {
  final HadithCollection collection;
  final HadithBook book;

  const HadithBookScreen({
    super.key,
    required this.collection,
    required this.book,
  });

  @override
  State<HadithBookScreen> createState() => _HadithBookScreenState();
}

class _HadithBookScreenState extends State<HadithBookScreen> {
  List<HadithData> _hadiths = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadHadiths();
  }

  Future<void> _loadHadiths() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
      final hadiths = await hadithProvider.getBookHadiths(
        widget.collection.id,
        widget.book.bookNumber,
      );
      setState(() {
        _hadiths = hadiths;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'خطأ في تحميل الأحاديث: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    final localizations = AppLocalizations.of(context)!;

    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            languageService.isArabic ? widget.book.arabicName : widget.book.englishName,
          ),
          backgroundColor: AppTheme.primaryGreen,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: Icon(languageService.backIcon),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Column(
          children: [
            // Book Info Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                border: Border(
                  bottom: BorderSide(color: Colors.grey[300]!),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryGreen,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Center(
                          child: Text(
                            '${widget.book.bookNumber}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              languageService.isArabic 
                                  ? widget.collection.arabicName 
                                  : widget.collection.englishName,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.primaryGreen,
                              ),
                            ),
                            Text(
                              languageService.isArabic 
                                  ? widget.collection.arabicAuthor 
                                  : widget.collection.author,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (widget.book.description != null || widget.book.arabicDescription != null) ...[
                    const SizedBox(height: 12),
                    Text(
                      languageService.isArabic 
                          ? (widget.book.arabicDescription ?? widget.book.description ?? '')
                          : (widget.book.description ?? widget.book.arabicDescription ?? ''),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                        height: 1.4,
                      ),
                    ),
                  ],
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.article, size: 16, color: AppTheme.primaryGreen),
                        const SizedBox(width: 4),
                        Text(
                          '${widget.book.totalHadiths} ${languageService.isArabic ? "حديث" : "hadiths"}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppTheme.primaryGreen,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Hadiths List
            Expanded(
              child: _buildHadithsContent(languageService),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHadithsContent(LanguageService languageService) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppTheme.primaryGreen),
            SizedBox(height: 16),
            Text('جاري تحميل الأحاديث...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              languageService.isArabic ? 'خطأ في تحميل الأحاديث' : 'Error loading hadiths',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadHadiths,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
              ),
              child: Text(languageService.isArabic ? 'إعادة المحاولة' : 'Retry'),
            ),
          ],
        ),
      );
    }

    if (_hadiths.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.article_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              languageService.isArabic ? 'لا توجد أحاديث متاحة' : 'No hadiths available',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _hadiths.length,
      itemBuilder: (context, index) {
        final hadith = _hadiths[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          elevation: 2,
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => HadithDetailScreen(
                    hadith: hadith,
                    collection: widget.collection,
                    book: widget.book,
                  ),
                ),
              );
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Hadith Number and Grade
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryGreen,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${hadith.hadithNumber}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getGradeColor(hadith.grade).withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          languageService.isArabic ? hadith.arabicGrade : hadith.grade,
                          style: TextStyle(
                            color: _getGradeColor(hadith.grade),
                            fontWeight: FontWeight.w500,
                            fontSize: 11,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Consumer<HadithProvider>(
                        builder: (context, hadithProvider, child) {
                          return IconButton(
                            icon: Icon(
                              hadithProvider.isInFavorites(
                                hadith.collectionId,
                                hadith.bookNumber,
                                hadith.hadithNumber,
                              )
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              color: AppTheme.primaryGreen,
                              size: 20,
                            ),
                            onPressed: () => _toggleFavorite(hadith),
                          );
                        },
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Arabic Text (truncated)
                  Text(
                    hadith.arabicText.length > 150 
                        ? '${hadith.arabicText.substring(0, 150)}...'
                        : hadith.arabicText,
                    style: const TextStyle(
                      fontSize: 16,
                      height: 1.6,
                      fontWeight: FontWeight.w500,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Narrator
                  Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          languageService.isArabic ? hadith.arabicNarrator : hadith.narrator,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontStyle: FontStyle.italic,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getGradeColor(String grade) {
    switch (grade.toLowerCase()) {
      case 'sahih':
      case 'صحيح':
        return Colors.green;
      case 'hasan':
      case 'حسن':
        return Colors.orange;
      case 'daif':
      case 'ضعيف':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _toggleFavorite(HadithData hadith) async {
    final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
    final languageService = Provider.of<LanguageService>(context, listen: false);
    
    if (hadithProvider.isInFavorites(hadith.collectionId, hadith.bookNumber, hadith.hadithNumber)) {
      final favoriteId = '${hadith.collectionId}_${hadith.bookNumber}_${hadith.hadithNumber}';
      await hadithProvider.removeFromFavorites(favoriteId);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageService.isArabic ? 'تم إزالة الحديث من المفضلة' : 'Hadith removed from favorites'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      await hadithProvider.addToFavorites(
        hadith,
        languageService.isArabic ? 'حديث مفضل' : 'Favorite Hadith',
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(languageService.isArabic ? 'تم إضافة الحديث للمفضلة' : 'Hadith added to favorites'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    }
  }
}
