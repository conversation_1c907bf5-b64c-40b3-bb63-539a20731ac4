import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/athkar_provider.dart';
import '../models/athkar_models.dart';
import '../widgets/athkar_card.dart';
import '../theme/app_theme.dart';
import 'create_routine_screen.dart';
import 'practice_athkar_screen.dart';

class AthkarScreen extends StatefulWidget {
  const AthkarScreen({super.key});

  @override
  State<AthkarScreen> createState() => _AthkarScreenState();
}

class _AthkarScreenState extends State<AthkarScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedCategoryId = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AthkarProvider>().loadCategories();
      context.read<AthkarProvider>().loadRoutines();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Athkar'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'My Routines'),
            Tab(text: 'Categories'),
            Tab(text: 'Pre-built Athkar'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildRoutinesTab(),
          _buildCategoriesTab(),
          _buildPreBuiltAthkarTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: "athkar_fab",
        onPressed: () {
          _showCreateRoutineDialog(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildRoutinesTab() {
    return Consumer<AthkarProvider>(
      builder: (context, athkarProvider, child) {
        if (athkarProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final routines = _selectedCategoryId == 'all'
            ? athkarProvider.routines
            : athkarProvider.getRoutinesByCategory(_selectedCategoryId);

        return Column(
          children: [
            _buildCategoryFilter(athkarProvider),
            Expanded(
              child: routines.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: routines.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: AthkarCard(
                            routine: routines[index],
                            onTap: () => _openRoutine(routines[index]),
                            onEdit: () => _editRoutine(routines[index]),
                            onDelete: () => _deleteRoutine(routines[index]),
                            onToggleFavorite: () => _toggleFavorite(routines[index]),
                          ),
                        );
                      },
                    ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCategoriesTab() {
    return Consumer<AthkarProvider>(
      builder: (context, athkarProvider, child) {
        if (athkarProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final categories = athkarProvider.categories;

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            final routinesCount = athkarProvider.getRoutinesByCategory(category.id).length;

            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              child: ListTile(
                leading: Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppTheme.getCategoryColor(category.id),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getCategoryIcon(category.icon),
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                title: Text(
                  category.name,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                subtitle: Text(
                  category.description ?? '',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '$routinesCount',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      'routines',
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                onTap: () {
                  setState(() {
                    _selectedCategoryId = category.id;
                    _tabController.animateTo(0);
                  });
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildCategoryFilter(AthkarProvider athkarProvider) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildFilterChip('All', 'all', athkarProvider),
          ...athkarProvider.categories.map(
            (category) => _buildFilterChip(category.name, category.id, athkarProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String categoryId, AthkarProvider athkarProvider) {
    final isSelected = _selectedCategoryId == categoryId;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedCategoryId = categoryId;
          });
        },
        backgroundColor: Colors.grey[200],
        selectedColor: AppTheme.primaryGreen.withValues(alpha: 0.2),
        checkmarkColor: AppTheme.primaryGreen,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            MdiIcons.bookOpenPageVariantOutline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Athkar Routines Yet',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first athkar routine to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showCreateRoutineDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('Create Routine'),
          ),
        ],
      ),
    );
  }

  void _openRoutine(AthkarRoutine routine) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PracticeAthkarScreen(routine: routine),
      ),
    );
  }

  void _editRoutine(AthkarRoutine routine) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateRoutineScreen(routine: routine),
      ),
    ).then((result) {
      if (result == true && mounted) {
        // Refresh the list
        context.read<AthkarProvider>().loadRoutines();
      }
    });
  }

  void _deleteRoutine(AthkarRoutine routine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Routine'),
        content: Text('Are you sure you want to delete "${routine.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              context.read<AthkarProvider>().deleteRoutine(routine.id);
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _toggleFavorite(AthkarRoutine routine) {
    context.read<AthkarProvider>().toggleFavorite(routine.id);
  }

  void _showCreateRoutineDialog(BuildContext context) {
    final provider = context.read<AthkarProvider>();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateRoutineScreen(),
      ),
    ).then((result) {
      if (result == true && mounted) {
        // Refresh the list
        provider.loadRoutines();
      }
    });
  }

  IconData _getCategoryIcon(String? iconName) {
    switch (iconName) {
      case 'sunrise':
        return MdiIcons.weatherSunny;
      case 'sunset':
        return MdiIcons.weatherSunset;
      case 'prayer':
        return MdiIcons.mosque;
      case 'bedtime':
        return MdiIcons.sleep;
      case 'dhikr':
        return MdiIcons.counter;
      case 'dua':
        return MdiIcons.handsPray;
      default:
        return MdiIcons.bookOpenPageVariant;
    }
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => SearchDialog(
        onSearch: (query) {
          // Filter routines based on search query
          final provider = Provider.of<AthkarProvider>(context, listen: false);
          provider.searchRoutines(query);
        },
      ),
    );
  }
}

class SearchDialog extends StatefulWidget {
  final Function(String) onSearch;

  const SearchDialog({super.key, required this.onSearch});

  @override
  State<SearchDialog> createState() => _SearchDialogState();
}

class _SearchDialogState extends State<SearchDialog> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Auto-focus the search field
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Search Athkar'),
      content: TextField(
        controller: _searchController,
        focusNode: _focusNode,
        decoration: const InputDecoration(
          hintText: 'Enter search terms...',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
        ),
        onSubmitted: (value) {
          if (value.trim().isNotEmpty) {
            widget.onSearch(value.trim());
            Navigator.pop(context);
          }
        },
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final query = _searchController.text.trim();
            if (query.isNotEmpty) {
              widget.onSearch(query);
              Navigator.pop(context);
            }
          },
          child: const Text('Search'),
        ),
      ],
    );
  }

  Widget _buildPreBuiltAthkarTab() {
    return Consumer<AthkarProvider>(
      builder: (context, athkarProvider, child) {
        final preBuiltAthkar = _getPreBuiltAthkar();

        return ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: preBuiltAthkar.length,
          itemBuilder: (context, index) {
            final athkar = preBuiltAthkar[index];
            return Card(
              margin: const EdgeInsets.symmetric(vertical: 8.0),
              elevation: 2,
              child: ListTile(
                leading: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Icon(
                    athkar['icon'] as IconData,
                    color: AppTheme.primaryGreen,
                    size: 24,
                  ),
                ),
                title: Text(
                  athkar['title'] as String,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      athkar['arabic'] as String,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        height: 1.5,
                      ),
                      textDirection: TextDirection.rtl,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      athkar['translation'] as String,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        height: 1.3,
                      ),
                    ),
                  ],
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.add_circle_outline),
                      color: AppTheme.primaryGreen,
                      onPressed: () => _addToRoutine(athkar),
                      tooltip: 'Add to Routine',
                    ),
                    IconButton(
                      icon: const Icon(Icons.play_circle_outline),
                      color: Colors.blue,
                      onPressed: () => _practiceAthkar(athkar),
                      tooltip: 'Practice Now',
                    ),
                  ],
                ),
                isThreeLine: true,
              ),
            );
          },
        );
      },
    );
  }

  List<Map<String, dynamic>> _getPreBuiltAthkar() {
    return [
      {
        'title': 'Subhan Allah',
        'arabic': 'سُبْحَانَ اللَّهِ',
        'translation': 'Glory be to Allah',
        'icon': Icons.star,
        'count': 33,
        'category': 'tasbih',
      },
      {
        'title': 'Alhamdulillah',
        'arabic': 'الْحَمْدُ لِلَّهِ',
        'translation': 'Praise be to Allah',
        'icon': Icons.favorite,
        'count': 33,
        'category': 'tasbih',
      },
      {
        'title': 'Allahu Akbar',
        'arabic': 'اللَّهُ أَكْبَرُ',
        'translation': 'Allah is the Greatest',
        'icon': Icons.keyboard_arrow_up,
        'count': 34,
        'category': 'tasbih',
      },
      {
        'title': 'La ilaha illa Allah',
        'arabic': 'لَا إِلَٰهَ إِلَّا اللَّهُ',
        'translation': 'There is no god but Allah',
        'icon': Icons.circle,
        'count': 100,
        'category': 'tahlil',
      },
      {
        'title': 'Istighfar',
        'arabic': 'أَسْتَغْفِرُ اللَّهَ',
        'translation': 'I seek forgiveness from Allah',
        'icon': Icons.healing,
        'count': 100,
        'category': 'istighfar',
      },
      {
        'title': 'Salawat on Prophet',
        'arabic': 'اللَّهُمَّ صَلِّ عَلَى مُحَمَّدٍ',
        'translation': 'O Allah, send blessings upon Muhammad',
        'icon': Icons.mosque,
        'count': 10,
        'category': 'salawat',
      },
    ];
  }

  void _addToRoutine(Map<String, dynamic> athkar) {
    // Show dialog to add to routine
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add to Routine'),
        content: Text('Add "${athkar['title']}" to a new routine?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to routine creation
              Navigator.pushNamed(context, '/create-routine');
            },
            child: const Text('Create Routine'),
          ),
        ],
      ),
    );
  }

  void _practiceAthkar(Map<String, dynamic> athkar) {
    // Show practice dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(athkar['title'] as String),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              athkar['arabic'] as String,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                height: 1.5,
              ),
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 8),
            Text(
              athkar['translation'] as String,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            Text('Recommended count: ${athkar['count']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to tasbeeh screen for practice
              Navigator.pushNamed(context, '/tasbeeh-dua');
            },
            child: const Text('Practice'),
          ),
        ],
      ),
    );
  }
}
