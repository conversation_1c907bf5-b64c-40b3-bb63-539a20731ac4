import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'dart:io';

class PermissionsService {
  static bool _isInitialized = false;

  /// Initialize permissions service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _checkInitialPermissions();
      _isInitialized = true;
      debugPrint('Permissions service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing permissions service: $e');
    }
  }

  /// Check initial permissions status
  static Future<void> _checkInitialPermissions() async {
    final permissions = _getRequiredPermissions();

    for (final permission in permissions) {
      final status = await permission.status;
      debugPrint('Permission ${permission.toString()}: ${status.toString()}');
    }
  }

  /// Get list of required permissions based on platform
  static List<Permission> _getRequiredPermissions() {
    final permissions = <Permission>[
      Permission.notification,
    ];

    if (Platform.isAndroid) {
      permissions.addAll([
        Permission.systemAlertWindow,
        Permission.storage,
        Permission.location,
        Permission.locationWhenInUse,
        Permission.scheduleExactAlarm,
      ]);
    } else if (Platform.isIOS) {
      permissions.addAll([
        Permission.locationWhenInUse,
        Permission.storage,
      ]);
    }

    return permissions;
  }

  /// Request all required permissions
  static Future<Map<Permission, PermissionStatus>> requestAllPermissions() async {
    final permissions = _getRequiredPermissions();
    final results = <Permission, PermissionStatus>{};

    for (final permission in permissions) {
      try {
        final status = await permission.request();
        results[permission] = status;
        debugPrint('Requested ${permission.toString()}: ${status.toString()}');
      } catch (e) {
        debugPrint('Error requesting ${permission.toString()}: $e');
        results[permission] = PermissionStatus.denied;
      }
    }

    return results;
  }

  /// Request notification permission
  static Future<bool> requestNotificationPermission() async {
    try {
      final status = await Permission.notification.request();
      debugPrint('Notification permission: ${status.toString()}');
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');
      return false;
    }
  }

  /// Request system alert window permission (Android overlay)
  static Future<bool> requestOverlayPermission() async {
    if (!Platform.isAndroid) return true;

    try {
      final status = await Permission.systemAlertWindow.request();
      debugPrint('Overlay permission: ${status.toString()}');
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting overlay permission: $e');
      return false;
    }
  }

  /// Request location permission
  static Future<bool> requestLocationPermission() async {
    try {
      final status = await Permission.locationWhenInUse.request();
      debugPrint('Location permission: ${status.toString()}');
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting location permission: $e');
      return false;
    }
  }

  /// Request storage permission
  static Future<bool> requestStoragePermission() async {
    try {
      final status = await Permission.storage.request();
      debugPrint('Storage permission: ${status.toString()}');
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting storage permission: $e');
      return false;
    }
  }

  /// Request exact alarm permission (Android 12+)
  static Future<bool> requestExactAlarmPermission() async {
    if (!Platform.isAndroid) return true;

    try {
      final status = await Permission.scheduleExactAlarm.request();
      debugPrint('Exact alarm permission: ${status.toString()}');
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting exact alarm permission: $e');
      return false;
    }
  }

  /// Check if notification permission is granted
  static Future<bool> isNotificationPermissionGranted() async {
    try {
      return await Permission.notification.isGranted;
    } catch (e) {
      debugPrint('Error checking notification permission: $e');
      return false;
    }
  }

  /// Check if overlay permission is granted
  static Future<bool> isOverlayPermissionGranted() async {
    if (!Platform.isAndroid) return true;

    try {
      return await Permission.systemAlertWindow.isGranted;
    } catch (e) {
      debugPrint('Error checking overlay permission: $e');
      return false;
    }
  }

  /// Check if location permission is granted
  static Future<bool> isLocationPermissionGranted() async {
    try {
      return await Permission.locationWhenInUse.isGranted;
    } catch (e) {
      debugPrint('Error checking location permission: $e');
      return false;
    }
  }

  /// Check if storage permission is granted
  static Future<bool> isStoragePermissionGranted() async {
    try {
      return await Permission.storage.isGranted;
    } catch (e) {
      debugPrint('Error checking storage permission: $e');
      return false;
    }
  }

  /// Show permission rationale dialog
  static Future<bool> showPermissionRationale(
    BuildContext context,
    String title,
    String message,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('موافق'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// Show settings dialog for permanently denied permissions
  static Future<void> showSettingsDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
            child: const Text('فتح الإعدادات'),
          ),
        ],
      ),
    );
  }

  /// Handle permission request with proper user guidance
  static Future<bool> requestPermissionWithGuidance(
    BuildContext context,
    Permission permission,
    String title,
    String rationale,
    String settingsMessage,
  ) async {
    // Check current status
    final status = await permission.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isDenied) {
      // Show rationale and request permission
      final shouldRequest = await showPermissionRationale(
        context,
        title,
        rationale,
      );

      if (shouldRequest) {
        final newStatus = await permission.request();
        return newStatus.isGranted;
      }
    }

    if (status.isPermanentlyDenied) {
      // Show settings dialog
      await showSettingsDialog(
        context,
        title,
        settingsMessage,
      );
    }

    return false;
  }

  /// Request notification permission with guidance
  static Future<bool> requestNotificationPermissionWithGuidance(
    BuildContext context,
  ) async {
    return await requestPermissionWithGuidance(
      context,
      Permission.notification,
      'إذن الإشعارات',
      'يحتاج التطبيق إلى إذن الإشعارات لإرسال تذكيرات الأذكار وأوقات الصلاة.',
      'يرجى تفعيل إذن الإشعارات من إعدادات التطبيق لتلقي التذكيرات.',
    );
  }

  /// Request overlay permission with guidance
  static Future<bool> requestOverlayPermissionWithGuidance(
    BuildContext context,
  ) async {
    if (!Platform.isAndroid) return true;

    return await requestPermissionWithGuidance(
      context,
      Permission.systemAlertWindow,
      'إذن النوافذ العائمة',
      'يحتاج التطبيق إلى إذن النوافذ العائمة لعرض عدادات الذكر خارج التطبيق.',
      'يرجى تفعيل إذن النوافذ العائمة من إعدادات التطبيق لاستخدام العدادات العائمة.',
    );
  }

  /// Request location permission with guidance
  static Future<bool> requestLocationPermissionWithGuidance(
    BuildContext context,
  ) async {
    return await requestPermissionWithGuidance(
      context,
      Permission.locationWhenInUse,
      'إذن الموقع',
      'يحتاج التطبيق إلى إذن الموقع لتحديد أوقات الصلاة واتجاه القبلة بدقة.',
      'يرجى تفعيل إذن الموقع من إعدادات التطبيق لاستخدام ميزات الموقع.',
    );
  }

  /// Get permissions status summary
  static Future<Map<String, bool>> getPermissionsStatus() async {
    return {
      'notifications': await isNotificationPermissionGranted(),
      'overlay': await isOverlayPermissionGranted(),
      'location': await isLocationPermissionGranted(),
      'storage': await isStoragePermissionGranted(),
    };
  }

  /// Check if all critical permissions are granted
  static Future<bool> areAllCriticalPermissionsGranted() async {
    try {
      // Check notification permission
      final notificationGranted = await isNotificationPermissionGranted();
      if (!notificationGranted) return false;

      // Check location permission
      final locationGranted = await isLocationPermissionGranted();
      if (!locationGranted) return false;

      return true;
    } catch (e) {
      debugPrint('Error checking critical permissions: $e');
      return false;
    }
  }


}
