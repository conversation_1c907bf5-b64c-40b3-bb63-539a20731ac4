import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../theme/app_theme.dart';

class AdvancedSettingsTab extends StatefulWidget {
  const AdvancedSettingsTab({super.key});

  @override
  State<AdvancedSettingsTab> createState() => _AdvancedSettingsTabState();
}

class _AdvancedSettingsTabState extends State<AdvancedSettingsTab> {
  bool _enableFloatingWindow = false;
  bool _enableAutoBackup = true;
  bool _enableAnalytics = false;
  bool _enableCrashReporting = true;
  String _backupFrequency = 'daily';
  String _dataUsage = 'wifi_only';

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Floating Window Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'النافذة العائمة' : 'Floating Window',
            Icons.picture_in_picture,
          ),
          const SizedBox(height: 12),
          
          Card(
            elevation: 2,
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.open_in_new, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'تفعيل النافذة العائمة' : 'Enable Floating Window'),
                  subtitle: Text(languageService.isArabic ? 'عداد أذكار عائم فوق التطبيقات' : 'Floating athkar counter over apps'),
                  value: _enableFloatingWindow,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _enableFloatingWindow = value;
                    });
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.settings_applications, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'إعدادات النافذة' : 'Window Settings'),
                  subtitle: Text(languageService.isArabic ? 'تخصيص مظهر وسلوك النافذة' : 'Customize window appearance and behavior'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showFloatingWindowSettings(context, languageService),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.security, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'أذونات النظام' : 'System Permissions'),
                  subtitle: Text(languageService.isArabic ? 'إدارة أذونات النافذة العائمة' : 'Manage floating window permissions'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showPermissionsDialog(context, languageService),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Backup & Restore Section
          _buildSectionHeader(
            languageService.isArabic ? 'النسخ الاحتياطي والاستعادة' : 'Backup & Restore',
            Icons.backup,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.cloud_upload, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'النسخ الاحتياطي التلقائي' : 'Auto Backup'),
                  subtitle: Text(languageService.isArabic ? 'نسخ احتياطي تلقائي للبيانات' : 'Automatic data backup'),
                  value: _enableAutoBackup,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _enableAutoBackup = value;
                    });
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.schedule, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'تكرار النسخ الاحتياطي' : 'Backup Frequency'),
                  subtitle: Text(_getBackupFrequencyName(_backupFrequency, languageService)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showBackupFrequencyDialog(context, languageService),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.file_upload, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'إنشاء نسخة احتياطية' : 'Create Backup'),
                  subtitle: Text(languageService.isArabic ? 'إنشاء نسخة احتياطية فورية' : 'Create immediate backup'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _createBackup(context, languageService),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.file_download, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'استعادة من نسخة احتياطية' : 'Restore from Backup'),
                  subtitle: Text(languageService.isArabic ? 'استعادة البيانات من نسخة احتياطية' : 'Restore data from backup'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _restoreBackup(context, languageService),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Data & Storage Section
          _buildSectionHeader(
            languageService.isArabic ? 'البيانات والتخزين' : 'Data & Storage',
            Icons.storage,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.wifi, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'استخدام البيانات' : 'Data Usage'),
                  subtitle: Text(_getDataUsageName(_dataUsage, languageService)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showDataUsageDialog(context, languageService),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.cleaning_services, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'مسح البيانات المؤقتة' : 'Clear Cache'),
                  subtitle: Text(languageService.isArabic ? 'حذف الملفات المؤقتة لتوفير المساحة' : 'Delete temporary files to save space'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _clearCache(context, languageService),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.folder, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'إدارة التخزين' : 'Storage Management'),
                  subtitle: Text(languageService.isArabic ? 'عرض وإدارة مساحة التخزين' : 'View and manage storage space'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showStorageInfo(context, languageService),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Privacy & Analytics Section
          _buildSectionHeader(
            languageService.isArabic ? 'الخصوصية والتحليلات' : 'Privacy & Analytics',
            Icons.privacy_tip,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.analytics, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'تحليلات الاستخدام' : 'Usage Analytics'),
                  subtitle: Text(languageService.isArabic ? 'مشاركة بيانات الاستخدام لتحسين التطبيق' : 'Share usage data to improve the app'),
                  value: _enableAnalytics,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _enableAnalytics = value;
                    });
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.bug_report, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'تقارير الأخطاء' : 'Crash Reporting'),
                  subtitle: Text(languageService.isArabic ? 'إرسال تقارير الأخطاء تلقائياً' : 'Automatically send crash reports'),
                  value: _enableCrashReporting,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _enableCrashReporting = value;
                    });
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.policy, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'سياسة الخصوصية' : 'Privacy Policy'),
                  subtitle: Text(languageService.isArabic ? 'عرض سياسة الخصوصية' : 'View privacy policy'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showPrivacyPolicy(context, languageService),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Testing & Development Section
          _buildSectionHeader(
            languageService.isArabic ? 'الاختبار والتطوير' : 'Testing & Development',
            Icons.developer_mode,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.quiz, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'اختبار الميزات' : 'Feature Testing'),
                  subtitle: Text(languageService.isArabic ? 'اختبار جميع ميزات التطبيق' : 'Test all app features'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showFeatureTests(context, languageService),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.speed, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'اختبار الأداء' : 'Performance Test'),
                  subtitle: Text(languageService.isArabic ? 'قياس أداء التطبيق' : 'Measure app performance'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _runPerformanceTest(context, languageService),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.info, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'معلومات التطبيق' : 'App Information'),
                  subtitle: Text(languageService.isArabic ? 'الإصدار ومعلومات النظام' : 'Version and system information'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showAppInfo(context, languageService),
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: AppTheme.primaryGreen, size: 24),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
      ],
    );
  }

  String _getBackupFrequencyName(String frequency, LanguageService languageService) {
    switch (frequency) {
      case 'daily':
        return languageService.isArabic ? 'يومياً' : 'Daily';
      case 'weekly':
        return languageService.isArabic ? 'أسبوعياً' : 'Weekly';
      case 'monthly':
        return languageService.isArabic ? 'شهرياً' : 'Monthly';
      case 'manual':
        return languageService.isArabic ? 'يدوياً' : 'Manual';
      default:
        return languageService.isArabic ? 'يومياً' : 'Daily';
    }
  }

  String _getDataUsageName(String usage, LanguageService languageService) {
    switch (usage) {
      case 'wifi_only':
        return languageService.isArabic ? 'Wi-Fi فقط' : 'Wi-Fi Only';
      case 'mobile_data':
        return languageService.isArabic ? 'بيانات الجوال' : 'Mobile Data';
      case 'both':
        return languageService.isArabic ? 'كلاهما' : 'Both';
      default:
        return languageService.isArabic ? 'Wi-Fi فقط' : 'Wi-Fi Only';
    }
  }

  void _showFloatingWindowSettings(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'إعدادات النافذة العائمة' : 'Floating Window Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: Text(languageService.isArabic ? 'فتح في وضع ملء الشاشة' : 'Open in fullscreen'),
              value: false,
              onChanged: (value) {
                // Implement fullscreen toggle
              },
            ),
            SwitchListTile(
              title: Text(languageService.isArabic ? 'البقاء في المقدمة' : 'Always on top'),
              value: false,
              onChanged: (value) {
                // Implement always on top toggle
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }

  void _showPermissionsDialog(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'أذونات النظام' : 'System Permissions'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.location_on),
              title: Text(languageService.isArabic ? 'إذن الموقع' : 'Location Permission'),
              trailing: const Icon(Icons.check_circle, color: Colors.green),
            ),
            ListTile(
              leading: const Icon(Icons.notifications),
              title: Text(languageService.isArabic ? 'إذن الإشعارات' : 'Notification Permission'),
              trailing: const Icon(Icons.check_circle, color: Colors.green),
            ),
            ListTile(
              leading: const Icon(Icons.storage),
              title: Text(languageService.isArabic ? 'إذن التخزين' : 'Storage Permission'),
              trailing: const Icon(Icons.check_circle, color: Colors.green),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }

  void _showBackupFrequencyDialog(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'اختر تكرار النسخ الاحتياطي' : 'Choose Backup Frequency'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildBackupFrequencyOption('daily', languageService),
            _buildBackupFrequencyOption('weekly', languageService),
            _buildBackupFrequencyOption('monthly', languageService),
            _buildBackupFrequencyOption('manual', languageService),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildBackupFrequencyOption(String frequency, LanguageService languageService) {
    return RadioListTile<String>(
      title: Text(_getBackupFrequencyName(frequency, languageService)),
      value: frequency,
      groupValue: _backupFrequency,
      activeColor: AppTheme.primaryGreen,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _backupFrequency = value;
          });
          Navigator.pop(context);
        }
      },
    );
  }

  void _showDataUsageDialog(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'اختر استخدام البيانات' : 'Choose Data Usage'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDataUsageOption('wifi_only', languageService),
            _buildDataUsageOption('mobile_data', languageService),
            _buildDataUsageOption('both', languageService),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildDataUsageOption(String usage, LanguageService languageService) {
    return RadioListTile<String>(
      title: Text(_getDataUsageName(usage, languageService)),
      value: usage,
      groupValue: _dataUsage,
      activeColor: AppTheme.primaryGreen,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _dataUsage = value;
          });
          Navigator.pop(context);
        }
      },
    );
  }

  void _createBackup(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'إنشاء نسخة احتياطية' : 'Create Backup'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.backup),
              title: Text(languageService.isArabic ? 'نسخ احتياطي للأذكار' : 'Backup Athkar'),
              onTap: () {
                // Implement athkar backup
              },
            ),
            ListTile(
              leading: const Icon(Icons.book),
              title: Text(languageService.isArabic ? 'نسخ احتياطي للقرآن' : 'Backup Quran'),
              onTap: () {
                // Implement Quran backup
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: Text(languageService.isArabic ? 'نسخ احتياطي للإعدادات' : 'Backup Settings'),
              onTap: () {
                // Implement settings backup
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }

  void _restoreBackup(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'استعادة من نسخة احتياطية' : 'Restore from Backup'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.restore),
              title: Text(languageService.isArabic ? 'استعادة الأذكار' : 'Restore Athkar'),
              onTap: () {
                // Implement athkar restore
              },
            ),
            ListTile(
              leading: const Icon(Icons.book_outlined),
              title: Text(languageService.isArabic ? 'استعادة القرآن' : 'Restore Quran'),
              onTap: () {
                // Implement Quran restore
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings_backup_restore),
              title: Text(languageService.isArabic ? 'استعادة الإعدادات' : 'Restore Settings'),
              onTap: () {
                // Implement settings restore
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }

  void _clearCache(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'مسح البيانات المؤقتة' : 'Clear Cache'),
        content: Text(languageService.isArabic ? 'هل تريد حذف جميع البيانات المؤقتة؟' : 'Do you want to delete all temporary data?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(languageService.isArabic ? 'تم مسح البيانات المؤقتة' : 'Cache cleared successfully'),
                  backgroundColor: AppTheme.primaryGreen,
                ),
              );
            },
            child: Text(languageService.isArabic ? 'مسح' : 'Clear'),
          ),
        ],
      ),
    );
  }

  void _showStorageInfo(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'معلومات التخزين' : 'Storage Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.storage),
              title: Text(languageService.isArabic ? 'المساحة المستخدمة' : 'Used Space'),
              subtitle: Text(languageService.isArabic ? '45.2 ميجابايت' : '45.2 MB'),
            ),
            ListTile(
              leading: const Icon(Icons.folder),
              title: Text(languageService.isArabic ? 'ملفات الأذكار' : 'Athkar Files'),
              subtitle: Text(languageService.isArabic ? '12.5 ميجابايت' : '12.5 MB'),
            ),
            ListTile(
              leading: const Icon(Icons.book),
              title: Text(languageService.isArabic ? 'ملفات القرآن' : 'Quran Files'),
              subtitle: Text(languageService.isArabic ? '28.7 ميجابايت' : '28.7 MB'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'سياسة الخصوصية' : 'Privacy Policy'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                languageService.isArabic ? 'سياسة الخصوصية' : 'Privacy Policy',
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Text(
                languageService.isArabic
                  ? 'نحن نحترم خصوصيتك ونلتزم بحماية بياناتك الشخصية. جميع البيانات تُحفظ محلياً على جهازك ولا تُرسل إلى خوادم خارجية إلا بموافقتك الصريحة.'
                  : 'We respect your privacy and are committed to protecting your personal data. All data is stored locally on your device and is not sent to external servers without your explicit consent.',
              ),
              const SizedBox(height: 12),
              Text(
                languageService.isArabic
                  ? 'البيانات المحفوظة محلياً تشمل: الأذكار المخصصة، إعدادات التطبيق، وسجل القراءة.'
                  : 'Locally stored data includes: custom athkar, app settings, and reading history.',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }

  void _showFeatureTests(BuildContext context, LanguageService languageService) {
    Navigator.pushNamed(context, '/feature-tests');
  }

  void _runPerformanceTest(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'اختبار الأداء' : 'Performance Test'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.speed),
              title: Text(languageService.isArabic ? 'سرعة التطبيق' : 'App Speed'),
              subtitle: Text(languageService.isArabic ? 'ممتاز' : 'Excellent'),
              trailing: const Icon(Icons.check_circle, color: Colors.green),
            ),
            ListTile(
              leading: const Icon(Icons.memory),
              title: Text(languageService.isArabic ? 'استخدام الذاكرة' : 'Memory Usage'),
              subtitle: Text(languageService.isArabic ? '45 ميجابايت' : '45 MB'),
              trailing: const Icon(Icons.check_circle, color: Colors.green),
            ),
            ListTile(
              leading: const Icon(Icons.battery_full),
              title: Text(languageService.isArabic ? 'استهلاك البطارية' : 'Battery Usage'),
              subtitle: Text(languageService.isArabic ? 'منخفض' : 'Low'),
              trailing: const Icon(Icons.check_circle, color: Colors.green),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }

  void _showAppInfo(BuildContext context, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'معلومات التطبيق' : 'App Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${languageService.isArabic ? 'الإصدار' : 'Version'}: 1.0.0'),
            const SizedBox(height: 8),
            Text('${languageService.isArabic ? 'رقم البناء' : 'Build'}: 1'),
            const SizedBox(height: 8),
            Text('${languageService.isArabic ? 'النظام' : 'Platform'}: Flutter'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'موافق' : 'OK'),
          ),
        ],
      ),
    );
  }
}
