{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["d:\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\12july\\athkar\\athkar_app\\build\\.cxx\\RelWithDebInfo\\4g17w134\\x86_64", "clean"]], "buildTargetsCommandComponents": ["d:\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\12july\\athkar\\athkar_app\\build\\.cxx\\RelWithDebInfo\\4g17w134\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}