import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/widget_manager.dart';
import '../../services/language_service.dart';
import '../../theme/app_theme.dart';

class WidgetCreationDialog extends StatefulWidget {
  final WidgetManager.WidgetType widgetType;
  final Function(WidgetManager.WidgetConfig) onWidgetCreated;

  const WidgetCreationDialog({
    super.key,
    required this.widgetType,
    required this.onWidgetCreated,
  });

  @override
  State<WidgetCreationDialog> createState() => _WidgetCreationDialogState();
}

class _WidgetCreationDialogState extends State<WidgetCreationDialog> {
  final WidgetManager _widgetManager = WidgetManager();
  
  WidgetManager.WidgetSize _selectedSize = WidgetManager.WidgetSize.medium2x1;
  final Map<String, dynamic> _settings = {};
  bool _isCreating = false;

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getWidgetIcon(widget.widgetType),
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      languageService.isArabic ? 'إنشاء ودجت جديد' : 'Create New Widget',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            
            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Widget type info
                    _buildWidgetTypeInfo(languageService),
                    
                    const SizedBox(height: 24),
                    
                    // Size selection
                    _buildSizeSelection(languageService),
                    
                    const SizedBox(height: 24),
                    
                    // Widget-specific settings
                    _buildWidgetSettings(languageService),
                    
                    const SizedBox(height: 24),
                    
                    // Preview
                    _buildPreview(languageService),
                  ],
                ),
              ),
            ),
            
            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isCreating ? null : () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.grey[600],
                        side: BorderSide(color: Colors.grey[400]!),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: Text(
                        languageService.isArabic ? 'إلغاء' : 'Cancel',
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _isCreating ? null : _createWidget,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryGreen,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: _isCreating
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              languageService.isArabic ? 'إنشاء الودجت' : 'Create Widget',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWidgetTypeInfo(LanguageService languageService) {
    final info = _getWidgetTypeInfo(widget.widgetType, languageService);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryGreen.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryGreen.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.primaryGreen.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              info['icon'] as IconData,
              color: AppTheme.primaryGreen,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  info['title'] as String,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryGreen,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  info['description'] as String,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSizeSelection(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'حجم الودجت' : 'Widget Size',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: WidgetManager.WidgetSize.values.map((size) {
            final isSelected = _selectedSize == size;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedSize = size;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.primaryGreen : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? AppTheme.primaryGreen : Colors.grey[300]!,
                  ),
                ),
                child: Text(
                  _getSizeText(size, languageService),
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildWidgetSettings(LanguageService languageService) {
    switch (widget.widgetType) {
      case WidgetManager.WidgetType.routineCounter:
        return _buildRoutineCounterSettings(languageService);
      case WidgetManager.WidgetType.ayahOfDay:
        return _buildAyahOfDaySettings(languageService);
      case WidgetManager.WidgetType.athkarReminder:
        return _buildAthkarReminderSettings(languageService);
      case WidgetManager.WidgetType.quranProgress:
        return _buildQuranProgressSettings(languageService);
      case WidgetManager.WidgetType.prayerTimes:
        return _buildPrayerTimesSettings(languageService);
      case WidgetManager.WidgetType.islamicCalendar:
        return _buildIslamicCalendarSettings(languageService);
      case WidgetManager.WidgetType.dhikrCounter:
        return _buildDhikrCounterSettings(languageService);
      case WidgetManager.WidgetType.statistics:
        return _buildStatisticsSettings(languageService);
    }
  }

  Widget _buildRoutineCounterSettings(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'إعدادات العداد' : 'Counter Settings',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 12),
        SwitchListTile(
          title: Text(
            languageService.isArabic ? 'عرض النسبة المئوية' : 'Show Percentage',
          ),
          value: _settings['showPercentage'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showPercentage'] = value;
            });
          },
          activeColor: AppTheme.primaryGreen,
        ),
        SwitchListTile(
          title: Text(
            languageService.isArabic ? 'تحديث تلقائي' : 'Auto Refresh',
          ),
          value: _settings['autoRefresh'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['autoRefresh'] = value;
            });
          },
          activeColor: AppTheme.primaryGreen,
        ),
      ],
    );
  }

  Widget _buildAyahOfDaySettings(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'إعدادات آية اليوم' : 'Ayah of Day Settings',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 12),
        SwitchListTile(
          title: Text(
            languageService.isArabic ? 'عرض الترجمة' : 'Show Translation',
          ),
          value: _settings['showTranslation'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showTranslation'] = value;
            });
          },
          activeColor: AppTheme.primaryGreen,
        ),
        SwitchListTile(
          title: Text(
            languageService.isArabic ? 'تغيير يومي' : 'Daily Change',
          ),
          value: _settings['dailyChange'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['dailyChange'] = value;
            });
          },
          activeColor: AppTheme.primaryGreen,
        ),
      ],
    );
  }

  Widget _buildAthkarReminderSettings(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'إعدادات التذكير' : 'Reminder Settings',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 12),
        SwitchListTile(
          title: Text(
            languageService.isArabic ? 'عرض الوقت المتبقي' : 'Show Time Remaining',
          ),
          value: _settings['showTimeRemaining'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showTimeRemaining'] = value;
            });
          },
          activeColor: AppTheme.primaryGreen,
        ),
      ],
    );
  }

  Widget _buildQuranProgressSettings(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'إعدادات التقدم' : 'Progress Settings',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 12),
        SwitchListTile(
          title: Text(
            languageService.isArabic ? 'عرض الهدف اليومي' : 'Show Daily Goal',
          ),
          value: _settings['showDailyGoal'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showDailyGoal'] = value;
            });
          },
          activeColor: AppTheme.primaryGreen,
        ),
      ],
    );
  }

  Widget _buildPrayerTimesSettings(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'إعدادات أوقات الصلاة' : 'Prayer Times Settings',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 12),
        SwitchListTile(
          title: Text(
            languageService.isArabic ? 'عرض جميع الأوقات' : 'Show All Times',
          ),
          value: _settings['showAllTimes'] ?? false,
          onChanged: (value) {
            setState(() {
              _settings['showAllTimes'] = value;
            });
          },
          activeColor: AppTheme.primaryGreen,
        ),
      ],
    );
  }

  Widget _buildIslamicCalendarSettings(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'إعدادات التقويم' : 'Calendar Settings',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 12),
        SwitchListTile(
          title: Text(
            languageService.isArabic ? 'عرض الأحداث' : 'Show Events',
          ),
          value: _settings['showEvents'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showEvents'] = value;
            });
          },
          activeColor: AppTheme.primaryGreen,
        ),
      ],
    );
  }

  Widget _buildDhikrCounterSettings(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'إعدادات العداد' : 'Counter Settings',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 12),
        SwitchListTile(
          title: Text(
            languageService.isArabic ? 'عرض الهدف' : 'Show Target',
          ),
          value: _settings['showTarget'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showTarget'] = value;
            });
          },
          activeColor: AppTheme.primaryGreen,
        ),
      ],
    );
  }

  Widget _buildStatisticsSettings(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'إعدادات الإحصائيات' : 'Statistics Settings',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 12),
        SwitchListTile(
          title: Text(
            languageService.isArabic ? 'عرض الإنجازات' : 'Show Achievements',
          ),
          value: _settings['showAchievements'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showAchievements'] = value;
            });
          },
          activeColor: AppTheme.primaryGreen,
        ),
      ],
    );
  }

  Widget _buildPreview(LanguageService languageService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'معاينة الودجت' : 'Widget Preview',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            children: [
              Text(
                languageService.isArabic ? 'معاينة الودجت' : 'Widget Preview',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 60,
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Icon(
                    _getWidgetIcon(widget.widgetType),
                    color: AppTheme.primaryGreen,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _createWidget() async {
    setState(() {
      _isCreating = true;
    });

    try {
      final success = await _widgetManager.createWidget(
        widget.widgetType,
        _selectedSize,
        _settings,
      );

      if (success) {
        // Create a mock config for callback
        final config = WidgetManager.WidgetConfig(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: widget.widgetType,
          size: _selectedSize,
          settings: _settings,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        widget.onWidgetCreated(config);
        if (mounted) {
          Navigator.pop(context);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                Provider.of<LanguageService>(context, listen: false).isArabic
                    ? 'فشل في إنشاء الودجت'
                    : 'Failed to create widget',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LanguageService>(context, listen: false).isArabic
                  ? 'حدث خطأ أثناء إنشاء الودجت'
                  : 'Error creating widget',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }

  IconData _getWidgetIcon(WidgetManager.WidgetType type) {
    switch (type) {
      case WidgetManager.WidgetType.routineCounter:
        return Icons.track_changes;
      case WidgetManager.WidgetType.ayahOfDay:
        return Icons.menu_book;
      case WidgetManager.WidgetType.athkarReminder:
        return Icons.notifications_active;
      case WidgetManager.WidgetType.quranProgress:
        return Icons.auto_stories;
      case WidgetManager.WidgetType.prayerTimes:
        return Icons.access_time;
      case WidgetManager.WidgetType.islamicCalendar:
        return Icons.calendar_today;
      case WidgetManager.WidgetType.dhikrCounter:
        return Icons.add_circle;
      case WidgetManager.WidgetType.statistics:
        return Icons.bar_chart;
    }
  }

  Map<String, dynamic> _getWidgetTypeInfo(
    WidgetManager.WidgetType type,
    LanguageService languageService,
  ) {
    // Same implementation as in widget_management_screen.dart
    if (languageService.isArabic) {
      switch (type) {
        case WidgetManager.WidgetType.routineCounter:
          return {
            'title': 'عداد الروتين',
            'description': 'تتبع تقدم الأذكار اليومية',
            'icon': Icons.track_changes,
          };
        case WidgetManager.WidgetType.ayahOfDay:
          return {
            'title': 'آية اليوم',
            'description': 'آية قرآنية يومية',
            'icon': Icons.menu_book,
          };
        // Add other cases...
        default:
          return {
            'title': 'ودجت',
            'description': 'وصف الودجت',
            'icon': Icons.widgets,
          };
      }
    } else {
      switch (type) {
        case WidgetManager.WidgetType.routineCounter:
          return {
            'title': 'Routine Counter',
            'description': 'Track daily Athkar progress',
            'icon': Icons.track_changes,
          };
        case WidgetManager.WidgetType.ayahOfDay:
          return {
            'title': 'Ayah of Day',
            'description': 'Daily Quranic verse',
            'icon': Icons.menu_book,
          };
        // Add other cases...
        default:
          return {
            'title': 'Widget',
            'description': 'Widget description',
            'icon': Icons.widgets,
          };
      }
    }
  }

  String _getSizeText(WidgetManager.WidgetSize size, LanguageService languageService) {
    switch (size) {
      case WidgetManager.WidgetSize.small1x1:
        return languageService.isArabic ? 'صغير (1×1)' : 'Small (1×1)';
      case WidgetManager.WidgetSize.medium2x1:
        return languageService.isArabic ? 'متوسط (2×1)' : 'Medium (2×1)';
      case WidgetManager.WidgetSize.large2x2:
        return languageService.isArabic ? 'كبير (2×2)' : 'Large (2×2)';
      case WidgetManager.WidgetSize.extraLarge4x2:
        return languageService.isArabic ? 'كبير جداً (4×2)' : 'Extra Large (4×2)';
    }
  }
}
