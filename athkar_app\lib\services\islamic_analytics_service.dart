import 'package:flutter/material.dart';

/// Comprehensive Islamic analytics service with spiritual progress tracking
/// Provides insights into worship patterns, dhikr habits, and spiritual growth
class IslamicAnalyticsService {
  static final IslamicAnalyticsService _instance = IslamicAnalyticsService._internal();
  factory IslamicAnalyticsService() => _instance;
  IslamicAnalyticsService._internal();

  // Analytics data storage (temporarily unused)
  // final Map<String, dynamic> _analyticsData = {};
  final List<SpiritualActivity> _activities = [];
  final Map<String, int> _dhikrCounts = {};
  final Map<String, Duration> _sessionDurations = {};
  final List<IslamicGoal> _goals = [];
  
  bool _isInitialized = false;

  /// Initialize the analytics service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadAnalyticsData();
      await _initializeDefaultGoals();
      
      _isInitialized = true;
      debugPrint('Islamic Analytics Service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Islamic Analytics Service: $e');
      rethrow;
    }
  }

  /// Record a spiritual activity
  Future<void> recordActivity(SpiritualActivity activity) async {
    if (!_isInitialized) await initialize();
    
    _activities.add(activity);
    
    // Update counters
    _dhikrCounts[activity.type.name] = (_dhikrCounts[activity.type.name] ?? 0) + activity.count;
    
    // Update session duration
    if (activity.duration != null) {
      final currentDuration = _sessionDurations[activity.type.name] ?? Duration.zero;
      _sessionDurations[activity.type.name] = currentDuration + activity.duration!;
    }
    
    // Check goal progress
    await _updateGoalProgress(activity);
    
    // Save data
    await _saveAnalyticsData();
  }

  /// Get comprehensive dashboard data
  Future<IslamicDashboard> getDashboardData() async {
    if (!_isInitialized) await initialize();
    
    final today = DateTime.now();
    final thisWeek = today.subtract(const Duration(days: 7));
    final thisMonth = DateTime(today.year, today.month, 1);
    
    return IslamicDashboard(
      totalDhikrCount: _getTotalDhikrCount(),
      todayActivities: _getActivitiesForPeriod(DateTime(today.year, today.month, today.day)),
      weeklyProgress: _getWeeklyProgress(thisWeek),
      monthlyProgress: _getMonthlyProgress(thisMonth),
      spiritualStreak: _calculateSpiritualStreak(),
      completedGoals: _getCompletedGoals(),
      activeGoals: _getActiveGoals(),
      favoriteAthkar: _getFavoriteAthkar(),
      prayerTimeAdherence: _calculatePrayerTimeAdherence(),
      quranReadingProgress: _getQuranReadingProgress(),
      dhikrDistribution: _getDhikrDistribution(),
      spiritualLevel: _calculateSpiritualLevel(),
      recommendations: _generateRecommendations(),
    );
  }

  /// Get detailed statistics for a specific period
  Future<PeriodStatistics> getStatisticsForPeriod(DateTime start, DateTime end) async {
    final activities = _getActivitiesForPeriod(start, end);
    
    return PeriodStatistics(
      period: DateRange(start: start, end: end),
      totalActivities: activities.length,
      totalDhikrCount: activities.fold(0, (sum, activity) => sum + activity.count),
      totalDuration: activities.fold(Duration.zero, (sum, activity) => sum + (activity.duration ?? Duration.zero)),
      averageDailyActivities: activities.length / (end.difference(start).inDays + 1),
      mostActiveDay: _getMostActiveDay(activities),
      activityBreakdown: _getActivityBreakdown(activities),
      progressTrend: _calculateProgressTrend(activities),
    );
  }

  /// Set a new spiritual goal
  Future<void> setGoal(IslamicGoal goal) async {
    _goals.add(goal);
    await _saveAnalyticsData();
  }

  /// Update goal progress
  Future<void> _updateGoalProgress(SpiritualActivity activity) async {
    for (final goal in _goals) {
      if (goal.isCompleted) continue;
      
      if (goal.type == activity.type) {
        goal.currentProgress += activity.count;
        
        if (goal.currentProgress >= goal.targetCount) {
          goal.isCompleted = true;
          goal.completedAt = DateTime.now();
        }
      }
    }
  }

  /// Calculate spiritual streak (consecutive days of activity)
  int _calculateSpiritualStreak() {
    if (_activities.isEmpty) return 0;
    
    final sortedActivities = _activities.toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    int streak = 0;
    DateTime? lastDate;
    
    for (final activity in sortedActivities) {
      final activityDate = DateTime(
        activity.timestamp.year,
        activity.timestamp.month,
        activity.timestamp.day,
      );
      
      if (lastDate == null) {
        lastDate = activityDate;
        streak = 1;
      } else {
        final difference = lastDate.difference(activityDate).inDays;
        
        if (difference == 1) {
          streak++;
          lastDate = activityDate;
        } else if (difference > 1) {
          break;
        }
      }
    }
    
    return streak;
  }

  /// Calculate spiritual level based on activities
  SpiritualLevel _calculateSpiritualLevel() {
    final totalDhikr = _getTotalDhikrCount();
    final streak = _calculateSpiritualStreak();
    final completedGoals = _getCompletedGoals().length;
    
    final score = (totalDhikr * 0.1) + (streak * 10) + (completedGoals * 50);
    
    if (score >= 1000) return SpiritualLevel.enlightened;
    if (score >= 500) return SpiritualLevel.devoted;
    if (score >= 200) return SpiritualLevel.committed;
    if (score >= 50) return SpiritualLevel.practicing;
    return SpiritualLevel.beginner;
  }

  /// Generate personalized recommendations
  List<String> _generateRecommendations() {
    final recommendations = <String>[];
    
    // Based on activity patterns
    final recentActivities = _getActivitiesForPeriod(DateTime.now().subtract(const Duration(days: 7)));
    
    if (recentActivities.isEmpty) {
      recommendations.add('ابدأ رحلتك الروحية بأذكار الصباح والمساء');
    } else {
      final morningCount = recentActivities.where((a) => a.type == SpiritualActivityType.morningAthkar).length;
      final eveningCount = recentActivities.where((a) => a.type == SpiritualActivityType.eveningAthkar).length;
      
      if (morningCount < 3) {
        recommendations.add('حاول المحافظة على أذكار الصباح يومياً');
      }
      
      if (eveningCount < 3) {
        recommendations.add('لا تنس أذكار المساء للحماية والبركة');
      }
    }
    
    // Based on goals
    final activeGoals = _getActiveGoals();
    if (activeGoals.isEmpty) {
      recommendations.add('ضع هدفاً روحياً جديداً لتحفيز نفسك');
    }
    
    // Based on spiritual level
    final level = _calculateSpiritualLevel();
    switch (level) {
      case SpiritualLevel.beginner:
        recommendations.add('ابدأ بالتسبيح البسيط: سبحان الله، الحمد لله، الله أكبر');
        break;
      case SpiritualLevel.practicing:
        recommendations.add('أضف قراءة القرآن إلى روتينك اليومي');
        break;
      case SpiritualLevel.committed:
        recommendations.add('جرب الأدعية النبوية لتعميق تجربتك الروحية');
        break;
      case SpiritualLevel.devoted:
        recommendations.add('شارك تجربتك مع الآخرين وكن قدوة');
        break;
      case SpiritualLevel.enlightened:
        recommendations.add('استمر في هذا المستوى الرائع وعلم الآخرين');
        break;
    }
    
    return recommendations;
  }

  /// Get activities for a specific period
  List<SpiritualActivity> _getActivitiesForPeriod(DateTime start, [DateTime? end]) {
    end ??= DateTime.now();
    
    return _activities.where((activity) {
      return activity.timestamp.isAfter(start) && activity.timestamp.isBefore(end!.add(const Duration(days: 1)));
    }).toList();
  }

  /// Get total dhikr count
  int _getTotalDhikrCount() {
    return _dhikrCounts.values.fold(0, (sum, count) => sum + count);
  }

  /// Get weekly progress
  Map<String, int> _getWeeklyProgress(DateTime startOfWeek) {
    final weeklyProgress = <String, int>{};
    
    for (int i = 0; i < 7; i++) {
      final day = startOfWeek.add(Duration(days: i));
      final dayName = _getDayName(day.weekday);
      final dayActivities = _getActivitiesForPeriod(day);
      weeklyProgress[dayName] = dayActivities.fold(0, (sum, activity) => sum + activity.count);
    }
    
    return weeklyProgress;
  }

  /// Get monthly progress
  Map<String, int> _getMonthlyProgress(DateTime startOfMonth) {
    final monthlyProgress = <String, int>{};
    final daysInMonth = DateTime(startOfMonth.year, startOfMonth.month + 1, 0).day;
    
    for (int i = 1; i <= daysInMonth; i++) {
      final day = DateTime(startOfMonth.year, startOfMonth.month, i);
      final dayActivities = _getActivitiesForPeriod(day);
      monthlyProgress[i.toString()] = dayActivities.fold(0, (sum, activity) => sum + activity.count);
    }
    
    return monthlyProgress;
  }

  /// Get completed goals
  List<IslamicGoal> _getCompletedGoals() {
    return _goals.where((goal) => goal.isCompleted).toList();
  }

  /// Get active goals
  List<IslamicGoal> _getActiveGoals() {
    return _goals.where((goal) => !goal.isCompleted).toList();
  }

  /// Get favorite athkar based on usage
  List<String> _getFavoriteAthkar() {
    final sortedDhikr = _dhikrCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedDhikr.take(5).map((e) => e.key).toList();
  }

  /// Calculate prayer time adherence (mock implementation)
  double _calculatePrayerTimeAdherence() {
    // This would integrate with prayer time tracking
    return 0.85; // 85% adherence
  }

  /// Get Quran reading progress (mock implementation)
  double _getQuranReadingProgress() {
    // This would integrate with Quran reading tracking
    return 0.15; // 15% of Quran completed
  }

  /// Get dhikr distribution by type
  Map<String, double> _getDhikrDistribution() {
    final total = _getTotalDhikrCount();
    if (total == 0) return {};
    
    final distribution = <String, double>{};
    for (final entry in _dhikrCounts.entries) {
      distribution[entry.key] = entry.value / total;
    }
    
    return distribution;
  }

  /// Get most active day from activities
  String _getMostActiveDay(List<SpiritualActivity> activities) {
    final dayCount = <int, int>{};
    
    for (final activity in activities) {
      final weekday = activity.timestamp.weekday;
      dayCount[weekday] = (dayCount[weekday] ?? 0) + activity.count;
    }
    
    if (dayCount.isEmpty) return 'لا توجد بيانات';
    
    final mostActiveWeekday = dayCount.entries.reduce((a, b) => a.value > b.value ? a : b).key;
    return _getDayName(mostActiveWeekday);
  }

  /// Get activity breakdown by type
  Map<String, int> _getActivityBreakdown(List<SpiritualActivity> activities) {
    final breakdown = <String, int>{};
    
    for (final activity in activities) {
      breakdown[activity.type.name] = (breakdown[activity.type.name] ?? 0) + activity.count;
    }
    
    return breakdown;
  }

  /// Calculate progress trend
  double _calculateProgressTrend(List<SpiritualActivity> activities) {
    if (activities.length < 2) return 0.0;
    
    // Simple trend calculation - compare first half with second half
    final midPoint = activities.length ~/ 2;
    final firstHalf = activities.take(midPoint).fold(0, (sum, activity) => sum + activity.count);
    final secondHalf = activities.skip(midPoint).fold(0, (sum, activity) => sum + activity.count);
    
    if (firstHalf == 0) return secondHalf > 0 ? 1.0 : 0.0;
    
    return (secondHalf - firstHalf) / firstHalf;
  }

  /// Get day name in Arabic
  String _getDayName(int weekday) {
    const dayNames = [
      'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
    ];
    return dayNames[weekday - 1];
  }

  /// Initialize default goals
  Future<void> _initializeDefaultGoals() async {
    if (_goals.isEmpty) {
      _goals.addAll([
        IslamicGoal(
          id: 'daily_dhikr',
          title: 'الذكر اليومي',
          description: 'اذكر الله 100 مرة يومياً',
          type: SpiritualActivityType.generalDhikr,
          targetCount: 100,
          deadline: DateTime.now().add(const Duration(days: 30)),
        ),
        IslamicGoal(
          id: 'morning_athkar',
          title: 'أذكار الصباح',
          description: 'حافظ على أذكار الصباح لمدة أسبوع',
          type: SpiritualActivityType.morningAthkar,
          targetCount: 7,
          deadline: DateTime.now().add(const Duration(days: 7)),
        ),
      ]);
    }
  }

  /// Load analytics data from storage
  Future<void> _loadAnalyticsData() async {
    // In a real implementation, this would load from persistent storage
    debugPrint('Loading analytics data...');
  }

  /// Save analytics data to storage
  Future<void> _saveAnalyticsData() async {
    // In a real implementation, this would save to persistent storage
    debugPrint('Saving analytics data...');
  }
}

/// Represents a spiritual activity
class SpiritualActivity {
  final String id;
  final SpiritualActivityType type;
  final int count;
  final Duration? duration;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  SpiritualActivity({
    required this.id,
    required this.type,
    required this.count,
    this.duration,
    required this.timestamp,
    this.metadata,
  });
}

/// Types of spiritual activities
enum SpiritualActivityType {
  morningAthkar,
  eveningAthkar,
  generalDhikr,
  quranReading,
  dua,
  tasbeeh,
  prayer,
  istighfar,
}

/// Represents an Islamic goal
class IslamicGoal {
  final String id;
  final String title;
  final String description;
  final SpiritualActivityType type;
  final int targetCount;
  int currentProgress;
  final DateTime deadline;
  bool isCompleted;
  DateTime? completedAt;

  IslamicGoal({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.targetCount,
    this.currentProgress = 0,
    required this.deadline,
    this.isCompleted = false,
    this.completedAt,
  });

  double get progressPercentage => currentProgress / targetCount;
}

/// Comprehensive dashboard data
class IslamicDashboard {
  final int totalDhikrCount;
  final List<SpiritualActivity> todayActivities;
  final Map<String, int> weeklyProgress;
  final Map<String, int> monthlyProgress;
  final int spiritualStreak;
  final List<IslamicGoal> completedGoals;
  final List<IslamicGoal> activeGoals;
  final List<String> favoriteAthkar;
  final double prayerTimeAdherence;
  final double quranReadingProgress;
  final Map<String, double> dhikrDistribution;
  final SpiritualLevel spiritualLevel;
  final List<String> recommendations;

  IslamicDashboard({
    required this.totalDhikrCount,
    required this.todayActivities,
    required this.weeklyProgress,
    required this.monthlyProgress,
    required this.spiritualStreak,
    required this.completedGoals,
    required this.activeGoals,
    required this.favoriteAthkar,
    required this.prayerTimeAdherence,
    required this.quranReadingProgress,
    required this.dhikrDistribution,
    required this.spiritualLevel,
    required this.recommendations,
  });
}

/// Period statistics
class PeriodStatistics {
  final DateRange period;
  final int totalActivities;
  final int totalDhikrCount;
  final Duration totalDuration;
  final double averageDailyActivities;
  final String mostActiveDay;
  final Map<String, int> activityBreakdown;
  final double progressTrend;

  PeriodStatistics({
    required this.period,
    required this.totalActivities,
    required this.totalDhikrCount,
    required this.totalDuration,
    required this.averageDailyActivities,
    required this.mostActiveDay,
    required this.activityBreakdown,
    required this.progressTrend,
  });
}

/// Date range
class DateRange {
  final DateTime start;
  final DateTime end;

  DateRange({required this.start, required this.end});
}

/// Spiritual levels
enum SpiritualLevel {
  beginner,
  practicing,
  committed,
  devoted,
  enlightened,
}

/// Extension for spiritual level display
extension SpiritualLevelExtension on SpiritualLevel {
  String get displayName {
    switch (this) {
      case SpiritualLevel.beginner:
        return 'مبتدئ';
      case SpiritualLevel.practicing:
        return 'ممارس';
      case SpiritualLevel.committed:
        return 'ملتزم';
      case SpiritualLevel.devoted:
        return 'متفان';
      case SpiritualLevel.enlightened:
        return 'مستنير';
    }
  }

  Color get color {
    switch (this) {
      case SpiritualLevel.beginner:
        return Colors.grey;
      case SpiritualLevel.practicing:
        return Colors.blue;
      case SpiritualLevel.committed:
        return Colors.green;
      case SpiritualLevel.devoted:
        return Colors.orange;
      case SpiritualLevel.enlightened:
        return Colors.purple;
    }
  }
}
