import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quran_khatma_provider.dart';
import '../services/language_service.dart';
import '../theme/app_theme.dart';
import '../models/quran_khatma_models.dart';

class CreateKhatmaScreen extends StatefulWidget {
  const CreateKhatmaScreen({super.key});

  @override
  State<CreateKhatmaScreen> createState() => _CreateKhatmaScreenState();
}

class _CreateKhatmaScreenState extends State<CreateKhatmaScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _arabicNameController = TextEditingController();
  final _customDaysController = TextEditingController();
  
  KhatmaType _selectedType = KhatmaType.thirtyDays;
  bool _enableReminders = true;
  final List<int> _reminderTimes = [9, 15, 21];
  bool _enableProgress = true;
  bool _enableCertificate = true;
  bool _isCreating = false;

  @override
  void dispose() {
    _nameController.dispose();
    _arabicNameController.dispose();
    _customDaysController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(languageService.isArabic ? 'ختمة جديدة' : 'New Khatma'),
          backgroundColor: AppTheme.primaryGreen,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: Icon(languageService.backIcon),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Text(
                  languageService.isArabic ? 'إعداد ختمة القرآن الكريم' : 'Setup Quran Khatma',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryGreen,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  languageService.isArabic 
                      ? 'اختر نوع الختمة والإعدادات المناسبة لك'
                      : 'Choose the type of Khatma and settings that suit you',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 32),

                // Khatma Name
                Text(
                  languageService.isArabic ? 'اسم الختمة' : 'Khatma Name',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    hintText: languageService.isArabic ? 'مثال: ختمة رمضان 2024' : 'Example: Ramadan 2024 Khatma',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: AppTheme.primaryGreen, width: 2),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return languageService.isArabic ? 'يرجى إدخال اسم الختمة' : 'Please enter Khatma name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Arabic Name
                Text(
                  languageService.isArabic ? 'الاسم بالعربية' : 'Arabic Name',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _arabicNameController,
                  decoration: InputDecoration(
                    hintText: languageService.isArabic ? 'ختمة رمضان ١٤٤٥' : 'ختمة رمضان ١٤٤٥',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: AppTheme.primaryGreen, width: 2),
                    ),
                  ),
                  textDirection: TextDirection.rtl,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return languageService.isArabic ? 'يرجى إدخال الاسم بالعربية' : 'Please enter Arabic name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Khatma Type
                Text(
                  languageService.isArabic ? 'نوع الختمة' : 'Khatma Type',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                
                ...KhatmaType.values.map((type) {
                  return RadioListTile<KhatmaType>(
                    title: Text(_getKhatmaTypeText(type, languageService)),
                    subtitle: Text(_getKhatmaTypeDescription(type, languageService)),
                    value: type,
                    groupValue: _selectedType,
                    activeColor: AppTheme.primaryGreen,
                    onChanged: (value) {
                      setState(() {
                        _selectedType = value!;
                        if (type == KhatmaType.custom) {
                          _customDaysController.text = '30';
                        }
                      });
                    },
                  );
                }),

                // Custom days input
                if (_selectedType == KhatmaType.custom) ...[
                  const SizedBox(height: 16),
                  Text(
                    languageService.isArabic ? 'عدد الأيام' : 'Number of Days',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _customDaysController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: languageService.isArabic ? 'أدخل عدد الأيام' : 'Enter number of days',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: AppTheme.primaryGreen, width: 2),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return languageService.isArabic ? 'يرجى إدخال عدد الأيام' : 'Please enter number of days';
                      }
                      final days = int.tryParse(value);
                      if (days == null || days < 1 || days > 365) {
                        return languageService.isArabic ? 'يرجى إدخال رقم صحيح بين 1 و 365' : 'Please enter a valid number between 1 and 365';
                      }
                      return null;
                    },
                  ),
                ],

                const SizedBox(height: 32),

                // Settings
                Text(
                  languageService.isArabic ? 'الإعدادات' : 'Settings',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryGreen,
                  ),
                ),
                const SizedBox(height: 16),

                // Enable Reminders
                SwitchListTile(
                  title: Text(languageService.isArabic ? 'تفعيل التذكيرات' : 'Enable Reminders'),
                  subtitle: Text(languageService.isArabic ? 'تذكيرات يومية لقراءة الورد' : 'Daily reminders for reading'),
                  value: _enableReminders,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _enableReminders = value;
                    });
                  },
                ),

                // Reminder Times
                if (_enableReminders) ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          languageService.isArabic ? 'أوقات التذكير' : 'Reminder Times',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          children: [9, 12, 15, 18, 21].map((hour) {
                            final isSelected = _reminderTimes.contains(hour);
                            return FilterChip(
                              label: Text('$hour:00'),
                              selected: isSelected,
                              selectedColor: AppTheme.primaryGreen.withValues(alpha: 0.3),
                              onSelected: (selected) {
                                setState(() {
                                  if (selected) {
                                    _reminderTimes.add(hour);
                                  } else {
                                    _reminderTimes.remove(hour);
                                  }
                                });
                              },
                            );
                          }).toList(),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Enable Progress Tracking
                SwitchListTile(
                  title: Text(languageService.isArabic ? 'تتبع التقدم' : 'Progress Tracking'),
                  subtitle: Text(languageService.isArabic ? 'عرض إحصائيات التقدم' : 'Show progress statistics'),
                  value: _enableProgress,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _enableProgress = value;
                    });
                  },
                ),

                // Enable Certificate
                SwitchListTile(
                  title: Text(languageService.isArabic ? 'شهادة الإنجاز' : 'Completion Certificate'),
                  subtitle: Text(languageService.isArabic ? 'الحصول على شهادة عند الانتهاء' : 'Get certificate upon completion'),
                  value: _enableCertificate,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _enableCertificate = value;
                    });
                  },
                ),

                const SizedBox(height: 32),

                // Create Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isCreating ? null : _createKhatma,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryGreen,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isCreating
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : Text(
                            languageService.isArabic ? 'إنشاء الختمة' : 'Create Khatma',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getKhatmaTypeText(KhatmaType type, LanguageService languageService) {
    switch (type) {
      case KhatmaType.thirtyDays:
        return languageService.isArabic ? 'ختمة 30 يوم' : '30-Day Khatma';
      case KhatmaType.sixtyDays:
        return languageService.isArabic ? 'ختمة 60 يوم' : '60-Day Khatma';
      case KhatmaType.ramadan:
        return languageService.isArabic ? 'ختمة رمضان' : 'Ramadan Khatma';
      case KhatmaType.weekly:
        return languageService.isArabic ? 'ختمة أسبوعية' : 'Weekly Khatma';
      case KhatmaType.custom:
        return languageService.isArabic ? 'ختمة مخصصة' : 'Custom Khatma';
    }
  }

  String _getKhatmaTypeDescription(KhatmaType type, LanguageService languageService) {
    switch (type) {
      case KhatmaType.thirtyDays:
        return languageService.isArabic ? 'ختم القرآن في شهر واحد' : 'Complete Quran in one month';
      case KhatmaType.sixtyDays:
        return languageService.isArabic ? 'ختم القرآن في شهرين' : 'Complete Quran in two months';
      case KhatmaType.ramadan:
        return languageService.isArabic ? 'ختم القرآن خلال شهر رمضان' : 'Complete Quran during Ramadan';
      case KhatmaType.weekly:
        return languageService.isArabic ? 'ختم القرآن في أسبوع واحد' : 'Complete Quran in one week';
      case KhatmaType.custom:
        return languageService.isArabic ? 'اختر عدد الأيام بنفسك' : 'Choose your own number of days';
    }
  }

  int _getKhatmaDays(KhatmaType type) {
    switch (type) {
      case KhatmaType.thirtyDays:
        return 30;
      case KhatmaType.sixtyDays:
        return 60;
      case KhatmaType.ramadan:
        return 30;
      case KhatmaType.weekly:
        return 7;
      case KhatmaType.custom:
        return int.tryParse(_customDaysController.text) ?? 30;
    }
  }

  Future<void> _createKhatma() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isCreating = true;
    });

    try {
      final khatmaProvider = Provider.of<QuranKhatmaProvider>(context, listen: false);
      final languageService = Provider.of<LanguageService>(context, listen: false);

      final settings = KhatmaSettings(
        enableReminders: _enableReminders,
        reminderTimes: _reminderTimes,
        enableProgress: _enableProgress,
        enableCertificate: _enableCertificate,
      );

      await khatmaProvider.createKhatma(
        name: _nameController.text.trim(),
        arabicName: _arabicNameController.text.trim(),
        type: _selectedType,
        totalDays: _getKhatmaDays(_selectedType),
        settings: settings,
      );

      if (mounted) {
        final messenger = ScaffoldMessenger.of(context);
        final navigator = Navigator.of(context);
        messenger.showSnackBar(
          SnackBar(
            content: Text(
              languageService.isArabic ? 'تم إنشاء الختمة بنجاح' : 'Khatma created successfully',
            ),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
        navigator.pop();
      }
    } catch (e) {
      if (mounted) {
        final messenger = ScaffoldMessenger.of(context);
        messenger.showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في إنشاء الختمة: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }
}
