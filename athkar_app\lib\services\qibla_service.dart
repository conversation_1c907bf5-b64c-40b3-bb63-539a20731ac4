import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sensors_plus/sensors_plus.dart';
import 'dart:async';

class QiblaService {
  static const double _kaabaLatitude = 21.4225;
  static const double _kaabaLongitude = 39.8262;
  
  static StreamController<QiblaData>? _qiblaController;
  static StreamSubscription<MagnetometerEvent>? _magnetometerSubscription;
  static StreamSubscription<AccelerometerEvent>? _accelerometerSubscription;
  static Position? _currentPosition;
  
  static Stream<QiblaData> get qiblaStream {
    _qiblaController ??= StreamController<QiblaData>.broadcast();
    return _qiblaController!.stream;
  }

  static Future<bool> initialize() async {
    try {
      // Request location permission
      final locationPermission = await Permission.location.request();
      if (locationPermission != PermissionStatus.granted) {
        throw Exception('Location permission denied');
      }

      // Get current location
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Start sensor streams
      await _startSensorStreams();
      
      return true;
    } catch (e) {
      debugPrint('Error initializing Qibla service: $e');
      return false;
    }
  }

  static Future<void> _startSensorStreams() async {
    double? magnetometerX, magnetometerY, magnetometerZ;
    double? accelerometerX, accelerometerY, accelerometerZ;

    // Listen to magnetometer
    _magnetometerSubscription = magnetometerEventStream().listen((event) {
      magnetometerX = event.x;
      magnetometerY = event.y;
      magnetometerZ = event.z;
      _updateQiblaDirection(
        magnetometerX, magnetometerY, magnetometerZ,
        accelerometerX, accelerometerY, accelerometerZ,
      );
    });

    // Listen to accelerometer
    _accelerometerSubscription = accelerometerEventStream().listen((event) {
      accelerometerX = event.x;
      accelerometerY = event.y;
      accelerometerZ = event.z;
      _updateQiblaDirection(
        magnetometerX, magnetometerY, magnetometerZ,
        accelerometerX, accelerometerY, accelerometerZ,
      );
    });
  }

  static void _updateQiblaDirection(
    double? magX, double? magY, double? magZ,
    double? accX, double? accY, double? accZ,
  ) {
    if (_currentPosition == null || 
        magX == null || magY == null || magZ == null ||
        accX == null || accY == null || accZ == null) {
      return;
    }

    // Calculate qibla direction
    final qiblaDirection = calculateQiblaDirection(
      _currentPosition!.latitude,
      _currentPosition!.longitude,
    );

    // Calculate device orientation
    final deviceOrientation = _calculateDeviceOrientation(
      magX, magY, magZ, accX, accY, accZ,
    );

    // Calculate relative qibla direction
    final relativeQiblaDirection = qiblaDirection - deviceOrientation;

    final qiblaData = QiblaData(
      qiblaDirection: qiblaDirection,
      deviceOrientation: deviceOrientation,
      relativeQiblaDirection: relativeQiblaDirection,
      distance: calculateDistanceToKaaba(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
      ),
      accuracy: _currentPosition!.accuracy,
      isCalibrated: _isDeviceCalibrated(magX, magY, magZ),
      latitude: _currentPosition!.latitude,
      longitude: _currentPosition!.longitude,
      magneticHeading: deviceOrientation,
    );

    _qiblaController?.add(qiblaData);
  }

  static double calculateQiblaDirection(double latitude, double longitude) {
    final lat1 = _degreesToRadians(latitude);
    final lon1 = _degreesToRadians(longitude);
    final lat2 = _degreesToRadians(_kaabaLatitude);
    final lon2 = _degreesToRadians(_kaabaLongitude);

    final deltaLon = lon2 - lon1;

    final y = math.sin(deltaLon) * math.cos(lat2);
    final x = math.cos(lat1) * math.sin(lat2) - 
              math.sin(lat1) * math.cos(lat2) * math.cos(deltaLon);

    final bearing = math.atan2(y, x);
    return _radiansToDegrees(bearing);
  }

  static double calculateDistanceToKaaba(double latitude, double longitude) {
    return Geolocator.distanceBetween(
      latitude, longitude,
      _kaabaLatitude, _kaabaLongitude,
    ) / 1000; // Convert to kilometers
  }

  static double _calculateDeviceOrientation(
    double magX, double magY, double magZ,
    double accX, double accY, double accZ,
  ) {
    // Normalize accelerometer values
    final norm = math.sqrt(accX * accX + accY * accY + accZ * accZ);
    accX /= norm;
    accY /= norm;
    accZ /= norm;

    // Calculate rotation matrix
    final ex = math.sqrt(accY * accY + accZ * accZ);
    final ey = -accX * accY / ex;
    final ez = -accX * accZ / ex;

    // Calculate magnetic field components
    final hx = magX * ex + magY * ey + magZ * ez;
    final hy = magY * ex - magX * ey;

    // Calculate azimuth
    final azimuth = math.atan2(hy, hx);
    return _radiansToDegrees(azimuth);
  }

  static bool _isDeviceCalibrated(double magX, double magY, double magZ) {
    final magnitude = math.sqrt(magX * magX + magY * magY + magZ * magZ);
    // Check if magnetic field strength is within expected range
    return magnitude > 20 && magnitude < 100;
  }

  static double _degreesToRadians(double degrees) {
    return degrees * math.pi / 180;
  }

  static double _radiansToDegrees(double radians) {
    return radians * 180 / math.pi;
  }

  static Future<void> updateLocation() async {
    try {
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } catch (e) {
      debugPrint('Error updating location: $e');
    }
  }

  static void dispose() {
    _magnetometerSubscription?.cancel();
    _accelerometerSubscription?.cancel();
    _qiblaController?.close();
    _qiblaController = null;
  }

  // Get prayer times for current location
  static Future<PrayerTimes?> getPrayerTimes([DateTime? date]) async {
    if (_currentPosition == null) return null;
    
    final prayerDate = date ?? DateTime.now();
    return PrayerTimesCalculator.calculate(
      _currentPosition!.latitude,
      _currentPosition!.longitude,
      prayerDate,
    );
  }

  // Get Islamic date
  static IslamicDate getIslamicDate([DateTime? date]) {
    final gregorianDate = date ?? DateTime.now();
    return IslamicDateConverter.fromGregorian(gregorianDate);
  }
}

class QiblaData {
  final double qiblaDirection;
  final double deviceOrientation;
  final double relativeQiblaDirection;
  final double distance;
  final double accuracy;
  final bool isCalibrated;
  final double latitude;
  final double longitude;
  final double magneticHeading;

  QiblaData({
    required this.qiblaDirection,
    required this.deviceOrientation,
    required this.relativeQiblaDirection,
    required this.distance,
    required this.accuracy,
    required this.isCalibrated,
    required this.latitude,
    required this.longitude,
    required this.magneticHeading,
  });

  // Convenience getters for backward compatibility
  double get direction => qiblaDirection;
}

class PrayerTimes {
  final DateTime fajr;
  final DateTime sunrise;
  final DateTime dhuhr;
  final DateTime asr;
  final DateTime maghrib;
  final DateTime isha;

  PrayerTimes({
    required this.fajr,
    required this.sunrise,
    required this.dhuhr,
    required this.asr,
    required this.maghrib,
    required this.isha,
  });
}

class PrayerTimesCalculator {
  static const double _fajrAngle = 18.0;
  static const double _ishaAngle = 17.0;
  static const double _asrShadowFactor = 1.0;

  static PrayerTimes calculate(double latitude, double longitude, DateTime date) {
    final julianDay = _getJulianDay(date);
    final timeZone = _getTimeZone(longitude);

    // Calculate equation of time and solar declination
    final eqTime = _getEquationOfTime(julianDay);
    final solarDec = _getSolarDeclination(julianDay);

    // Calculate prayer times
    final dhuhr = _calculateDhuhr(longitude, timeZone, eqTime);
    final sunrise = _calculateSunrise(latitude, solarDec, longitude, timeZone, eqTime);
    final sunset = _calculateSunset(latitude, solarDec, longitude, timeZone, eqTime);
    final fajr = _calculateFajr(latitude, solarDec, longitude, timeZone, eqTime);
    final isha = _calculateIsha(latitude, solarDec, longitude, timeZone, eqTime);
    final asr = _calculateAsr(latitude, solarDec, longitude, timeZone, eqTime);

    return PrayerTimes(
      fajr: _timeToDateTime(date, fajr),
      sunrise: _timeToDateTime(date, sunrise),
      dhuhr: _timeToDateTime(date, dhuhr),
      asr: _timeToDateTime(date, asr),
      maghrib: _timeToDateTime(date, sunset),
      isha: _timeToDateTime(date, isha),
    );
  }

  static double _getJulianDay(DateTime date) {
    final a = (14 - date.month) ~/ 12;
    final y = date.year - a;
    final m = date.month + 12 * a - 3;
    return date.day + (153 * m + 2) ~/ 5 + 365 * y + y ~/ 4 - y ~/ 100 + y ~/ 400 + 1721119.5;
  }

  static double _getTimeZone(double longitude) {
    return longitude / 15.0;
  }

  static double _getEquationOfTime(double julianDay) {
    final n = julianDay - 2451545.0;
    final l = (280.460 + 0.9856474 * n) % 360;
    final g = math.pi / 180 * ((357.528 + 0.9856003 * n) % 360);
    final lambda = math.pi / 180 * (l + 1.915 * math.sin(g) + 0.020 * math.sin(2 * g));
    final alpha = math.atan2(math.cos(23.439 * math.pi / 180) * math.sin(lambda), math.cos(lambda));
    return 4 * (l * math.pi / 180 - alpha) * 180 / math.pi;
  }

  static double _getSolarDeclination(double julianDay) {
    final n = julianDay - 2451545.0;
    final l = (280.460 + 0.9856474 * n) % 360;
    final g = math.pi / 180 * ((357.528 + 0.9856003 * n) % 360);
    final lambda = math.pi / 180 * (l + 1.915 * math.sin(g) + 0.020 * math.sin(2 * g));
    return math.asin(math.sin(23.439 * math.pi / 180) * math.sin(lambda)) * 180 / math.pi;
  }

  static double _calculateDhuhr(double longitude, double timeZone, double eqTime) {
    return 12 - timeZone - eqTime / 60;
  }

  static double _calculateSunrise(double latitude, double solarDec, double longitude, double timeZone, double eqTime) {
    final hourAngle = math.acos(-math.tan(latitude * math.pi / 180) * math.tan(solarDec * math.pi / 180)) * 180 / math.pi;
    return 12 - hourAngle / 15 - timeZone - eqTime / 60;
  }

  static double _calculateSunset(double latitude, double solarDec, double longitude, double timeZone, double eqTime) {
    final hourAngle = math.acos(-math.tan(latitude * math.pi / 180) * math.tan(solarDec * math.pi / 180)) * 180 / math.pi;
    return 12 + hourAngle / 15 - timeZone - eqTime / 60;
  }

  static double _calculateFajr(double latitude, double solarDec, double longitude, double timeZone, double eqTime) {
    final hourAngle = math.acos((-math.sin(_fajrAngle * math.pi / 180) - math.sin(latitude * math.pi / 180) * math.sin(solarDec * math.pi / 180)) / (math.cos(latitude * math.pi / 180) * math.cos(solarDec * math.pi / 180))) * 180 / math.pi;
    return 12 - hourAngle / 15 - timeZone - eqTime / 60;
  }

  static double _calculateIsha(double latitude, double solarDec, double longitude, double timeZone, double eqTime) {
    final hourAngle = math.acos((-math.sin(_ishaAngle * math.pi / 180) - math.sin(latitude * math.pi / 180) * math.sin(solarDec * math.pi / 180)) / (math.cos(latitude * math.pi / 180) * math.cos(solarDec * math.pi / 180))) * 180 / math.pi;
    return 12 + hourAngle / 15 - timeZone - eqTime / 60;
  }

  static double _calculateAsr(double latitude, double solarDec, double longitude, double timeZone, double eqTime) {
    final shadowLength = _asrShadowFactor + math.tan((latitude - solarDec).abs() * math.pi / 180);
    final hourAngle = math.acos((math.sin(math.atan(1 / shadowLength)) - math.sin(latitude * math.pi / 180) * math.sin(solarDec * math.pi / 180)) / (math.cos(latitude * math.pi / 180) * math.cos(solarDec * math.pi / 180))) * 180 / math.pi;
    return 12 + hourAngle / 15 - timeZone - eqTime / 60;
  }

  static DateTime _timeToDateTime(DateTime date, double time) {
    final hours = time.floor();
    final minutes = ((time - hours) * 60).round();
    return DateTime(date.year, date.month, date.day, hours, minutes);
  }
}

class IslamicDate {
  final int year;
  final int month;
  final int day;
  final String monthName;

  IslamicDate({
    required this.year,
    required this.month,
    required this.day,
    required this.monthName,
  });

  @override
  String toString() => '$day $monthName $year AH';
}

class IslamicDateConverter {
  static final List<String> _monthNames = [
    'Muharram', 'Safar', 'Rabi\' al-awwal', 'Rabi\' al-thani',
    'Jumada al-awwal', 'Jumada al-thani', 'Rajab', 'Sha\'ban',
    'Ramadan', 'Shawwal', 'Dhu al-Qi\'dah', 'Dhu al-Hijjah'
  ];

  // Islamic epoch: July 16, 622 CE (Julian calendar)
  static const int _islamicEpochJulian = 1948439;

  static IslamicDate fromGregorian(DateTime gregorianDate) {
    // Convert Gregorian to Julian Day Number
    final julianDay = _gregorianToJulian(gregorianDate);

    // Convert Julian Day to Islamic date using accurate algorithm
    return _julianToIslamic(julianDay);
  }

  static DateTime toGregorian(IslamicDate islamicDate) {
    // Convert Islamic date to Julian Day Number
    final julianDay = _islamicToJulian(islamicDate);

    // Convert Julian Day to Gregorian
    return _julianToGregorian(julianDay);
  }

  static int _gregorianToJulian(DateTime date) {
    final year = date.year;
    final month = date.month;
    final day = date.day;

    final a = (14 - month) ~/ 12;
    final y = year - a;
    final m = month + 12 * a - 3;

    return day + (153 * m + 2) ~/ 5 + 365 * y + y ~/ 4 - y ~/ 100 + y ~/ 400 + 1721119;
  }

  static DateTime _julianToGregorian(int julianDay) {
    final a = julianDay + 32044;
    final b = (4 * a + 3) ~/ 146097;
    final c = a - (146097 * b) ~/ 4;
    final d = (4 * c + 3) ~/ 1461;
    final e = c - (1461 * d) ~/ 4;
    final m = (5 * e + 2) ~/ 153;

    final day = e - (153 * m + 2) ~/ 5 + 1;
    final month = m + 3 - 12 * (m ~/ 10);
    final year = 100 * b + d - 4800 + m ~/ 10;

    return DateTime(year, month, day);
  }

  static IslamicDate _julianToIslamic(int julianDay) {
    // Days since Islamic epoch
    final daysSinceEpoch = julianDay - _islamicEpochJulian;

    // Approximate Islamic year (354.367 days per year)
    final approxYear = (daysSinceEpoch * 30) ~/ 10631 + 1;

    // Calculate exact year by iteration
    int year = approxYear;
    int yearStart = _islamicYearStart(year);

    while (yearStart > julianDay) {
      year--;
      yearStart = _islamicYearStart(year);
    }

    while (_islamicYearStart(year + 1) <= julianDay) {
      year++;
      yearStart = _islamicYearStart(year);
    }

    // Calculate month and day
    final dayOfYear = julianDay - yearStart + 1;
    int month = 1;
    int dayOfMonth = dayOfYear;

    for (int m = 1; m <= 12; m++) {
      final monthLength = _islamicMonthLength(year, m);
      if (dayOfMonth <= monthLength) {
        month = m;
        break;
      }
      dayOfMonth -= monthLength;
    }

    return IslamicDate(
      year: year,
      month: month,
      day: dayOfMonth,
      monthName: _monthNames[month - 1],
    );
  }

  static int _islamicToJulian(IslamicDate islamicDate) {
    final yearStart = _islamicYearStart(islamicDate.year);
    int dayOfYear = 0;

    for (int m = 1; m < islamicDate.month; m++) {
      dayOfYear += _islamicMonthLength(islamicDate.year, m);
    }

    dayOfYear += islamicDate.day - 1;
    return yearStart + dayOfYear;
  }

  static int _islamicYearStart(int year) {
    // Calculate Julian day of 1 Muharram for given Islamic year
    // Using Kuwaiti algorithm
    final cycleYear = (year - 1) % 30;
    final cycle = (year - 1) ~/ 30;

    // Days in complete 30-year cycles
    final cycleDays = cycle * 10631;

    // Days in current cycle
    int yearDays = 0;
    for (int y = 1; y <= cycleYear; y++) {
      yearDays += _islamicYearLength(y);
    }

    return _islamicEpochJulian + cycleDays + yearDays;
  }

  static int _islamicYearLength(int year) {
    // Leap years in 30-year cycle: 2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29
    final leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
    final cycleYear = year % 30;
    return leapYears.contains(cycleYear) ? 355 : 354;
  }

  static int _islamicMonthLength(int year, int month) {
    // Odd months have 30 days, even months have 29 days
    // Exception: Dhu al-Hijjah has 30 days in leap years
    if (month % 2 == 1) {
      return 30;
    } else if (month == 12 && _islamicYearLength(year) == 355) {
      return 30;
    } else {
      return 29;
    }
  }
}
