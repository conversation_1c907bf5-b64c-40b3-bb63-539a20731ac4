import 'package:flutter/material.dart';

class AppTheme {
  // Islamic-inspired color palette
  static const Color primaryGreen = Color(0xFF2E7D32); // Deep Islamic green
  static const Color lightGreen = Color(0xFF4CAF50);
  static const Color accentGold = Color(0xFFFFB300); // Islamic gold
  static const Color lightGold = Color(0xFFFFC107);
  static const Color darkBlue = Color(0xFF1565C0); // Night prayer blue
  static const Color lightBlue = Color(0xFF42A5F5);
  static const Color warmBeige = Color(0xFFF5F5DC);
  static const Color softGray = Color(0xFFF5F5F5);
  static const Color darkGray = Color(0xFF424242);
  static const Color textDark = Color(0xFF212121);
  static const Color textLight = Color(0xFF757575);

  // Gradient colors for different times of day
  static const List<Color> morningGradient = [
    Color(0xFFFFE082), // Light yellow
    Color(0xFFFFB74D), // Orange
  ];

  static const List<Color> eveningGradient = [
    Color(0xFFFF8A65), // Light orange
    Color(0xFFFF7043), // Deep orange
  ];

  static const List<Color> nightGradient = [
    Color(0xFF5C6BC0), // Indigo
    Color(0xFF3F51B5), // Deep indigo
  ];

  static const List<Color> generalGradient = [
    Color(0xFF81C784), // Light green
    Color(0xFF66BB6A), // Green
  ];

  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryGreen,
        brightness: Brightness.light,
        primary: primaryGreen,
        secondary: accentGold,
        surface: Colors.white,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: textDark,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Colors.white,
        selectedItemColor: primaryGreen,
        unselectedItemColor: textLight,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryGreen,
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: accentGold,
        foregroundColor: Colors.white,
        elevation: 4,
      ),
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: textDark,
        ),
        headlineMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.w600,
          color: textDark,
        ),
        headlineSmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: textDark,
        ),
        titleLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: textDark,
        ),
        titleMedium: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: textDark,
        ),
        titleSmall: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: textDark,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: textDark,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: textDark,
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          color: textLight,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: textLight),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: primaryGreen, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryGreen,
        brightness: Brightness.dark,
        primary: lightGreen,
        secondary: lightGold,
        surface: const Color(0xFF1E1E1E),
        onPrimary: Colors.black,
        onSecondary: Colors.black,
        onSurface: Colors.white,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF1E1E1E),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Color(0xFF1E1E1E),
        selectedItemColor: lightGreen,
        unselectedItemColor: Colors.grey,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: const Color(0xFF2D2D2D),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: lightGreen,
          foregroundColor: Colors.black,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: lightGold,
        foregroundColor: Colors.black,
        elevation: 4,
      ),
    );
  }

  // Helper method to get gradient for different athkar types
  static LinearGradient getAthkarGradient(String categoryId) {
    switch (categoryId) {
      case 'morning-athkar':
        return const LinearGradient(
          colors: morningGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'evening-athkar':
        return const LinearGradient(
          colors: eveningGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'sleep-athkar':
        return const LinearGradient(
          colors: nightGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return const LinearGradient(
          colors: generalGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  // Helper method to get icon color for categories
  static Color getCategoryColor(String categoryId) {
    switch (categoryId) {
      case 'morning-athkar':
        return const Color(0xFFFFB74D);
      case 'evening-athkar':
        return const Color(0xFFFF8A65);
      case 'prayer-athkar':
        return const Color(0xFF81C784);
      case 'sleep-athkar':
        return const Color(0xFF9575CD);
      case 'general-dhikr':
        return const Color(0xFF64B5F6);
      case 'dua-collection':
        return const Color(0xFFF06292);
      default:
        return primaryGreen;
    }
  }
}
