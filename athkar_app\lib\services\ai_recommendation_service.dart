import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import '../models/recommendation_models.dart';
import '../database/database_helper.dart';

class AIRecommendationService {
  static final DatabaseHelper _dbHelper = DatabaseHelper();

  
  // Machine learning models (simplified implementations)
  static final Map<String, double> _userPreferenceWeights = {};

  static final Map<String, List<String>> _userInteractions = {};

  static Future<void> initialize() async {
    try {
      await _loadUserPreferences();
      await _loadItemFeatures();
      await _loadUserInteractions();
      await _trainModels();
    } catch (e) {
      debugPrint('Error initializing AI recommendation service: $e');
    }
  }

  // Get personalized athkar recommendations
  static Future<List<AthkarRecommendation>> getAthkarRecommendations(
    String userId, {
    int limit = 5,
    RecommendationType type = RecommendationType.personalized,
  }) async {
    try {
      switch (type) {
        case RecommendationType.personalized:
          return await _getPersonalizedRecommendations(userId, limit);
        case RecommendationType.trending:
          return await _getTrendingRecommendations(limit);
        case RecommendationType.similar:
          return await _getSimilarUserRecommendations(userId, limit);
        case RecommendationType.contextual:
          return await _getContextualRecommendations(userId, limit);
      }
    } catch (e) {
      debugPrint('Error getting athkar recommendations: $e');
      return [];
    }
  }

  static Future<List<AthkarRecommendation>> _getPersonalizedRecommendations(
    String userId, 
    int limit,
  ) async {
    final userProfile = await _getUserProfile(userId);
    final userHistory = await _getUserHistory(userId);
    
    // Calculate user preference vector
    final preferenceVector = _calculateUserPreferences(userProfile, userHistory);
    
    // Get all available athkar
    final allAthkar = await _getAllAthkar();
    
    // Calculate similarity scores
    final recommendations = <AthkarRecommendation>[];
    
    for (final athkar in allAthkar) {
      if (_hasUserInteracted(userId, athkar.id)) continue;
      
      final score = _calculateRecommendationScore(
        preferenceVector,
        athkar,
        userHistory,
      );
      
      if (score > 0.3) { // Threshold for recommendations
        recommendations.add(AthkarRecommendation(
          athkarId: athkar.id,
          title: athkar.title,
          description: athkar.description ?? '',
          score: score,
          reason: _generateRecommendationReason(athkar, userProfile),
          type: RecommendationType.personalized,
          metadata: {
            'category': athkar.categoryId ?? '',
            'difficulty': _calculateDifficulty(athkar),
            'duration': athkar.estimatedDuration?.toString() ?? '0',
          },
        ));
      }
    }
    
    // Sort by score and return top recommendations
    recommendations.sort((a, b) => b.score.compareTo(a.score));
    return recommendations.take(limit).toList();
  }

  static Future<List<AthkarRecommendation>> _getTrendingRecommendations(int limit) async {
    // Get most popular athkar based on recent user interactions
    final trendingQuery = '''
      SELECT athkar_id, COUNT(*) as interaction_count
      FROM user_interactions 
      WHERE created_at > ? 
      GROUP BY athkar_id 
      ORDER BY interaction_count DESC 
      LIMIT ?
    ''';
    
    final weekAgo = DateTime.now().subtract(const Duration(days: 7));
    final results = await _dbHelper.rawQuery(
      trendingQuery,
      [weekAgo.millisecondsSinceEpoch, limit],
    );
    
    final recommendations = <AthkarRecommendation>[];
    
    for (final result in results) {
      final athkarId = result['athkar_id'] as String;
      final athkar = await _getAthkarById(athkarId);
      
      if (athkar != null) {
        recommendations.add(AthkarRecommendation(
          athkarId: athkar.id,
          title: athkar.title,
          description: athkar.description ?? '',
          score: 0.9, // High score for trending items
          reason: 'Trending this week',
          type: RecommendationType.trending,
          metadata: {
            'interaction_count': result['interaction_count'].toString(),
          },
        ));
      }
    }
    
    return recommendations;
  }

  static Future<List<AthkarRecommendation>> _getSimilarUserRecommendations(
    String userId, 
    int limit,
  ) async {
    // Find users with similar preferences
    final similarUsers = await _findSimilarUsers(userId);
    final recommendations = <AthkarRecommendation>[];
    
    for (final similarUserId in similarUsers.take(5)) {
      final similarUserHistory = await _getUserHistory(similarUserId);
      
      for (final interaction in similarUserHistory) {
        if (_hasUserInteracted(userId, interaction.athkarId)) continue;
        
        final athkar = await _getAthkarById(interaction.athkarId);
        if (athkar == null) continue;
        
        final score = _calculateSimilarityScore(userId, similarUserId, athkar);
        
        recommendations.add(AthkarRecommendation(
          athkarId: athkar.id,
          title: athkar.title,
          description: athkar.description ?? '',
          score: score,
          reason: 'Users like you also practice this',
          type: RecommendationType.similar,
          metadata: {
            'similar_user_count': similarUsers.length.toString(),
          },
        ));
      }
    }
    
    // Remove duplicates and sort
    final uniqueRecommendations = <String, AthkarRecommendation>{};
    for (final rec in recommendations) {
      if (!uniqueRecommendations.containsKey(rec.athkarId) ||
          uniqueRecommendations[rec.athkarId]!.score < rec.score) {
        uniqueRecommendations[rec.athkarId] = rec;
      }
    }
    
    final sortedRecommendations = uniqueRecommendations.values.toList()
      ..sort((a, b) => b.score.compareTo(a.score));
    
    return sortedRecommendations.take(limit).toList();
  }

  static Future<List<AthkarRecommendation>> _getContextualRecommendations(
    String userId, 
    int limit,
  ) async {
    final now = DateTime.now();
    final timeOfDay = _getTimeOfDay(now);
    final dayOfWeek = now.weekday;
    final isRamadan = _isRamadan(now);
    
    final contextualAthkar = await _getContextualAthkar(timeOfDay, dayOfWeek, isRamadan);
    final recommendations = <AthkarRecommendation>[];
    
    for (final athkar in contextualAthkar.take(limit)) {
      final score = _calculateContextualScore(athkar, timeOfDay, isRamadan);
      
      recommendations.add(AthkarRecommendation(
        athkarId: athkar.id,
        title: athkar.title,
        description: athkar.description ?? '',
        score: score,
        reason: _generateContextualReason(timeOfDay, isRamadan),
        type: RecommendationType.contextual,
        metadata: {
          'time_of_day': timeOfDay.toString(),
          'is_ramadan': isRamadan.toString(),
        },
      ));
    }
    
    return recommendations;
  }

  // Smart scheduling recommendations
  static Future<List<ScheduleRecommendation>> getScheduleRecommendations(
    String userId,
  ) async {
    final userHistory = await _getUserHistory(userId);

    // Analyze user's prayer and dhikr patterns
    final patterns = _analyzeUserPatterns(userHistory);
    
    final recommendations = <ScheduleRecommendation>[];
    
    // Morning recommendations
    if (patterns.morningActivity > 0.3) {
      recommendations.add(ScheduleRecommendation(
        id: 'morning_athkar',
        title: 'Morning Athkar',
        description: 'Start your day with morning remembrance',
        recommendedTime: TimeOfDay.morning,
        duration: const Duration(minutes: 10),
        confidence: patterns.morningActivity,
        reason: 'Based on your morning activity patterns',
      ));
    }
    
    // Evening recommendations
    if (patterns.eveningActivity > 0.3) {
      recommendations.add(ScheduleRecommendation(
        id: 'evening_athkar',
        title: 'Evening Athkar',
        description: 'End your day with evening remembrance',
        recommendedTime: TimeOfDay.evening,
        duration: const Duration(minutes: 15),
        confidence: patterns.eveningActivity,
        reason: 'Based on your evening activity patterns',
      ));
    }
    
    return recommendations;
  }

  // Content recommendations based on user mood/state
  static Future<List<ContentRecommendation>> getContentRecommendations(
    String userId, {
    UserMood? mood,
    UserState? state,
  }) async {
    final recommendations = <ContentRecommendation>[];
    
    if (mood != null) {
      switch (mood) {
        case UserMood.stressed:
          recommendations.addAll(await _getStressReliefContent());
          break;
        case UserMood.grateful:
          recommendations.addAll(await _getGratitudeContent());
          break;
        case UserMood.seekingGuidance:
          recommendations.addAll(await _getGuidanceContent());
          break;
        case UserMood.repentant:
          recommendations.addAll(await _getRepentanceContent());
          break;
        case UserMood.peaceful:
          recommendations.addAll(await _getPeacefulContent());
          break;
        case UserMood.anxious:
          recommendations.addAll(await _getAnxietyReliefContent());
          break;
        default:
          recommendations.addAll(await _getGeneralContent());
          break;
      }
    }
    
    if (state != null) {
      switch (state) {
        case UserState.traveling:
          recommendations.addAll(await _getTravelContent());
          break;
        case UserState.sick:
          recommendations.addAll(await _getHealingContent());
          break;
        case UserState.studying:
          recommendations.addAll(await _getStudyContent());
          break;
        default:
          recommendations.addAll(await _getGeneralContent());
          break;
      }
    }
    
    return recommendations;
  }

  // Helper methods
  static Future<UserProfile> _getUserProfile(String userId) async {
    try {
      // Load user preferences from database
      final preferences = await _dbHelper.query(
        'user_preferences',
        where: 'user_id = ?',
        whereArgs: [userId],
      );

      final prefMap = <String, dynamic>{};
      for (final pref in preferences) {
        prefMap[pref['preference_key']] = pref['preference_value'];
      }

      // Load user sessions for behavior analysis
      final sessions = await _dbHelper.query(
        'user_sessions',
        where: 'user_id = ?',
        whereArgs: [userId],
        orderBy: 'start_time DESC',
        limit: 50,
      );

      final behaviorPatterns = <String, dynamic>{
        'session_count': sessions.length,
        'avg_session_duration': sessions.isNotEmpty
            ? sessions.map((s) => s['duration'] ?? 0).reduce((a, b) => a + b) / sessions.length
            : 0,
        'most_active_time': _getMostActiveTime(sessions),
        'preferred_categories': await _getPreferredCategories(userId),
      };

      return UserProfile(
        userId: userId,
        preferences: prefMap.cast<String, double>(),
        demographics: {},
        behaviorPatterns: behaviorPatterns.cast<String, double>(),
      );
    } catch (e) {
      debugPrint('Error loading user profile: $e');
      return UserProfile(
        userId: userId,
        preferences: {},
        demographics: {},
        behaviorPatterns: {},
      );
    }
  }

  static Future<List<UserInteraction>> _getUserHistory(String userId) async {
    final results = await _dbHelper.query(
      'user_interactions',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
      limit: 100,
    );
    
    return results.map((json) => UserInteraction.fromJson(json)).toList();
  }

  static Future<List<dynamic>> _getAllAthkar() async {
    try {
      final routines = await _dbHelper.query('athkar_routines');
      final tasbeehItems = await _dbHelper.query('tasbeeh_items');
      final duaItems = await _dbHelper.query('dua_items');

      return [...routines, ...tasbeehItems, ...duaItems];
    } catch (e) {
      debugPrint('Error loading all athkar: $e');
      return [];
    }
  }

  static Future<dynamic> _getAthkarById(String athkarId) async {
    try {
      // Try to find in routines first
      var results = await _dbHelper.query(
        'athkar_routines',
        where: 'id = ?',
        whereArgs: [athkarId],
      );
      if (results.isNotEmpty) return results.first;

      // Try tasbeeh items
      results = await _dbHelper.query(
        'tasbeeh_items',
        where: 'id = ?',
        whereArgs: [athkarId],
      );
      if (results.isNotEmpty) return results.first;

      // Try dua items
      results = await _dbHelper.query(
        'dua_items',
        where: 'id = ?',
        whereArgs: [athkarId],
      );
      if (results.isNotEmpty) return results.first;

      return null;
    } catch (e) {
      debugPrint('Error loading athkar by ID: $e');
      return null;
    }
  }

  static Map<String, double> _calculateUserPreferences(
    UserProfile profile,
    List<UserInteraction> history,
  ) {
    final preferences = <String, double>{};
    
    // Analyze interaction patterns
    for (final interaction in history) {
      final category = interaction.metadata['category'] as String? ?? 'general';
      preferences[category] = (preferences[category] ?? 0.0) + 1.0;
    }
    
    // Normalize preferences
    final total = preferences.values.fold(0.0, (sum, value) => sum + value);
    if (total > 0) {
      preferences.updateAll((key, value) => value / total);
    }
    
    return preferences;
  }

  static double _calculateRecommendationScore(
    Map<String, double> userPreferences,
    dynamic athkar,
    List<UserInteraction> history,
  ) {
    // Simplified scoring algorithm
    double score = 0.0;
    
    // Category preference score
    final category = athkar.categoryId ?? 'general';
    score += userPreferences[category] ?? 0.0;
    
    // Popularity score
    score += _calculatePopularityScore(athkar.id);
    
    // Recency boost
    score += _calculateRecencyBoost(athkar.id);
    
    return math.min(1.0, score);
  }

  static double _calculatePopularityScore(String athkarId) {
    try {
      // Calculate popularity based on usage count
      final usageCount = _userInteractions.values
          .expand((interactions) => interactions)
          .where((id) => id == athkarId)
          .length;

      // Normalize to 0-1 scale (assuming max 100 interactions is very popular)
      return (usageCount / 100.0).clamp(0.0, 1.0);
    } catch (e) {
      debugPrint('Error calculating popularity score: $e');
      return 0.5;
    }
  }

  static double _calculateRecencyBoost(String athkarId) {
    try {
      // Check if athkar was used recently (within last 7 days)
      final recentWeight = _userPreferenceWeights[athkarId] ?? 0.0;
      return (recentWeight * 0.1).clamp(0.0, 0.3);
    } catch (e) {
      debugPrint('Error calculating recency boost: $e');
      return 0.1;
    }
  }

  static String _generateRecommendationReason(dynamic athkar, UserProfile profile) {
    try {
      final category = athkar['category'] ?? 'general';

      // Generate reason based on user behavior patterns
      final behaviorPatterns = profile.behaviorPatterns;
      final mostActiveTime = behaviorPatterns['most_active_time'] ?? 'morning';

      if (category == 'stress_relief') {
        return 'Recommended for stress relief based on your activity patterns';
      } else if (category == 'gratitude') {
        return 'Perfect for expressing gratitude during your $mostActiveTime routine';
      } else if (category == 'guidance') {
        return 'Recommended for seeking guidance and clarity';
      } else {
        return 'Recommended based on your preferences and usage patterns';
      }
    } catch (e) {
      debugPrint('Error generating recommendation reason: $e');
      return 'Recommended for you';
    }
  }

  static bool _hasUserInteracted(String userId, String athkarId) {
    try {
      final userInteractions = _userInteractions[userId] ?? [];
      return userInteractions.contains(athkarId);
    } catch (e) {
      debugPrint('Error checking user interaction: $e');
      return false;
    }
  }

  static double _calculateDifficulty(dynamic athkar) {
    try {
      final content = athkar['arabic_text'] ?? athkar['content'] ?? '';
      final words = content.split(' ').length;

      // Simple difficulty calculation based on content length
      if (words <= 5) return 0.2; // Easy
      if (words <= 15) return 0.5; // Medium
      if (words <= 30) return 0.8; // Hard
      return 1.0; // Very Hard
    } catch (e) {
      debugPrint('Error calculating difficulty: $e');
      return 0.5;
    }
  }

  static Future<List<String>> _findSimilarUsers(String userId) async {
    // Implement collaborative filtering to find similar users
    try {
      final userHistory = await _getUserHistory(userId);
      final allUsers = await _getAllUserIds();

      final similarities = <String, double>{};

      for (final otherUserId in allUsers) {
        if (otherUserId != userId) {
          final otherHistory = await _getUserHistory(otherUserId);
          final similarity = _calculateUserSimilarity(userHistory, otherHistory);
          if (similarity > 0.5) {
            similarities[otherUserId] = similarity;
          }
        }
      }

      // Return top 10 most similar users
      final sortedUsers = similarities.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      return sortedUsers.take(10).map((e) => e.key).toList();
    } catch (e) {
      return [];
    }
  }

  static Future<List<String>> _getAllUserIds() async {
    // Get all user IDs from database
    try {
      final result = await _dbHelper.query('user_profiles');
      return result.map((row) => row['user_id'] as String).toList();
    } catch (e) {
      return [];
    }
  }

  static double _calculateUserSimilarity(List<UserInteraction> userHistory1, List<UserInteraction> userHistory2) {
    // Calculate similarity between two user histories using cosine similarity
    try {
      // Convert interaction lists to category frequency maps
      final categories1 = <String, double>{};
      final categories2 = <String, double>{};

      for (final interaction in userHistory1) {
        final category = interaction.type.toString();
        categories1[category] = (categories1[category] ?? 0.0) + 1.0;
      }

      for (final interaction in userHistory2) {
        final category = interaction.type.toString();
        categories2[category] = (categories2[category] ?? 0.0) + 1.0;
      }

      final allCategories = {...categories1.keys, ...categories2.keys};
      if (allCategories.isEmpty) return 0.0;

      double dotProduct = 0.0;
      double norm1 = 0.0;
      double norm2 = 0.0;

      for (final category in allCategories) {
        final value1 = categories1[category] ?? 0.0;
        final value2 = categories2[category] ?? 0.0;

        dotProduct += value1 * value2;
        norm1 += value1 * value1;
        norm2 += value2 * value2;
      }

      if (norm1 == 0.0 || norm2 == 0.0) return 0.0;

      return dotProduct / (math.sqrt(norm1) * math.sqrt(norm2));
    } catch (e) {
      return 0.0;
    }
  }

  static double _calculateSimilarityScore(String userId, String similarUserId, dynamic athkar) {
    // Calculate similarity score between users for specific athkar
    try {
      final athkarId = athkar['id'] as String? ?? '';
      final athkarCategory = athkar['category'] as String? ?? 'general';

      // Base similarity score
      double score = 0.5;

      // Increase score if users have similar interaction patterns with this athkar
      if (athkarId.isNotEmpty) {
        // This would typically involve checking interaction history
        // For now, return a reasonable default based on category
        switch (athkarCategory) {
          case 'morning':
          case 'evening':
            score = 0.8;
            break;
          case 'prayer':
            score = 0.9;
            break;
          default:
            score = 0.7;
        }
      }

      return score;
    } catch (e) {
      return 0.7;
    }
  }

  static TimeOfDay _getTimeOfDay(DateTime dateTime) {
    final hour = dateTime.hour;
    if (hour >= 5 && hour < 12) return TimeOfDay.morning;
    if (hour >= 12 && hour < 17) return TimeOfDay.afternoon;
    if (hour >= 17 && hour < 21) return TimeOfDay.evening;
    return TimeOfDay.night;
  }

  static bool _isRamadan(DateTime dateTime) {
    // Implement proper Ramadan detection based on Islamic calendar
    // This is a simplified implementation - in production, use a proper Islamic calendar library
    final year = dateTime.year;

    // Approximate Ramadan dates (these would need to be updated annually)
    // In a real app, use a proper Islamic calendar calculation
    final Map<int, List<DateTime>> ramadanDates = {
      2024: [DateTime(2024, 3, 10), DateTime(2024, 4, 9)],
      2025: [DateTime(2025, 2, 28), DateTime(2025, 3, 30)],
      2026: [DateTime(2026, 2, 17), DateTime(2026, 3, 19)],
    };

    final dates = ramadanDates[year];
    if (dates != null && dates.length == 2) {
      return dateTime.isAfter(dates[0]) && dateTime.isBefore(dates[1]);
    }

    return false;
  }

  static Future<List<dynamic>> _getContextualAthkar(
    TimeOfDay timeOfDay,
    int dayOfWeek,
    bool isRamadan,
  ) async {
    // Get athkar appropriate for current context
    try {
      final contextualAthkar = <Map<String, dynamic>>[];

      // Morning athkar
      if (timeOfDay == TimeOfDay.morning) {
        contextualAthkar.addAll([
          {'id': 'morning_1', 'title': 'Morning Dhikr', 'category': 'morning', 'priority': 0.9},
          {'id': 'morning_2', 'title': 'Ayat al-Kursi', 'category': 'morning', 'priority': 0.8},
        ]);
      }

      // Evening athkar
      if (timeOfDay == TimeOfDay.evening) {
        contextualAthkar.addAll([
          {'id': 'evening_1', 'title': 'Evening Dhikr', 'category': 'evening', 'priority': 0.9},
          {'id': 'evening_2', 'title': 'Seeking Protection', 'category': 'evening', 'priority': 0.8},
        ]);
      }

      // Friday special athkar
      if (dayOfWeek == DateTime.friday) {
        contextualAthkar.add({
          'id': 'friday_1', 'title': 'Friday Dhikr', 'category': 'friday', 'priority': 0.95
        });
      }

      // Ramadan special athkar
      if (isRamadan) {
        contextualAthkar.addAll([
          {'id': 'ramadan_1', 'title': 'Ramadan Dhikr', 'category': 'ramadan', 'priority': 1.0},
          {'id': 'ramadan_2', 'title': 'Iftar Dua', 'category': 'ramadan', 'priority': 0.9},
        ]);
      }

      return contextualAthkar;
    } catch (e) {
      return [];
    }
  }

  static double _calculateContextualScore(dynamic athkar, TimeOfDay timeOfDay, bool isRamadan) {
    // Calculate contextual relevance score
    try {
      double score = 0.5; // Base score

      final category = athkar['category'] as String? ?? 'general';
      final priority = (athkar['priority'] as num?)?.toDouble() ?? 0.5;

      // Increase score based on time relevance
      switch (timeOfDay) {
        case TimeOfDay.morning:
          if (category == 'morning') score += 0.4;
          break;
        case TimeOfDay.evening:
          if (category == 'evening') score += 0.4;
          break;
        case TimeOfDay.afternoon:
          if (category == 'prayer') score += 0.3;
          break;
        case TimeOfDay.night:
          if (category == 'night') score += 0.3;
          break;
      }

      // Ramadan bonus
      if (isRamadan && category == 'ramadan') {
        score += 0.3;
      }

      // Apply priority multiplier
      score *= priority;

      // Ensure score is between 0 and 1
      return math.min(1.0, math.max(0.0, score));
    } catch (e) {
      return 0.8;
    }
  }

  static String _generateContextualReason(TimeOfDay timeOfDay, bool isRamadan) {
    if (isRamadan) return 'Perfect for Ramadan';
    
    switch (timeOfDay) {
      case TimeOfDay.morning:
        return 'Great way to start your morning';
      case TimeOfDay.afternoon:
        return 'Perfect for midday reflection';
      case TimeOfDay.evening:
        return 'Ideal for evening remembrance';
      case TimeOfDay.night:
        return 'Peaceful night dhikr';
    }
  }

  static UserPatterns _analyzeUserPatterns(List<UserInteraction> history) {
    double morningActivity = 0.0;
    double afternoonActivity = 0.0;
    double eveningActivity = 0.0;
    double nightActivity = 0.0;
    
    for (final interaction in history) {
      final hour = interaction.timestamp.hour;
      if (hour >= 5 && hour < 12) {
        morningActivity += 1.0;
      } else if (hour >= 12 && hour < 17) {
        afternoonActivity += 1.0;
      } else if (hour >= 17 && hour < 21) {
        eveningActivity += 1.0;
      } else {
        nightActivity += 1.0;
      }
    }
    
    final total = history.length.toDouble();
    if (total > 0) {
      morningActivity /= total;
      afternoonActivity /= total;
      eveningActivity /= total;
      nightActivity /= total;
    }
    
    return UserPatterns(
      morningActivity: morningActivity,
      afternoonActivity: afternoonActivity,
      eveningActivity: eveningActivity,
      nightActivity: nightActivity,
    );
  }





  static Future<void> _loadUserPreferences() async {
    try {
      final preferences = await _dbHelper.query('user_preferences');
      for (final pref in preferences) {
        final key = pref['preference_key'] as String;
        final value = double.tryParse(pref['preference_value'] as String) ?? 0.0;

        if (!_userPreferenceWeights.containsKey(key)) {
          _userPreferenceWeights[key] = value;
        }
      }
    } catch (e) {
      debugPrint('Error loading user preferences: $e');
    }
  }

  static Future<void> _loadItemFeatures() async {
    try {
      // Load athkar features for recommendation algorithm
      final routines = await _dbHelper.query('athkar_routines');
      final tasbeehItems = await _dbHelper.query('tasbeeh_items');
      final duaItems = await _dbHelper.query('dua_items');

      // Process and store item features for ML algorithms
      // This is a simplified implementation
      debugPrint('Loaded ${routines.length + tasbeehItems.length + duaItems.length} items for feature extraction');
    } catch (e) {
      debugPrint('Error loading item features: $e');
    }
  }

  static Future<void> _loadUserInteractions() async {
    try {
      final interactions = await _dbHelper.query('user_actions');
      for (final interaction in interactions) {
        final userId = interaction['user_id'] as String;
        final actionName = interaction['action_name'] as String;

        if (!_userInteractions.containsKey(userId)) {
          _userInteractions[userId] = [];
        }
        _userInteractions[userId]!.add(actionName);
      }
    } catch (e) {
      debugPrint('Error loading user interactions: $e');
    }
  }

  static Future<void> _trainModels() async {
    try {
      // Simplified model training - in production, this would use ML libraries
      await _loadUserPreferences();
      await _loadItemFeatures();
      await _loadUserInteractions();

      debugPrint('Recommendation models trained successfully');
    } catch (e) {
      debugPrint('Error training models: $e');
    }
  }

  // Track user interactions for improving recommendations
  static Future<void> trackInteraction(
    String userId,
    String athkarId,
    InteractionType type, {
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final interaction = UserInteraction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        athkarId: athkarId,
        type: type,
        timestamp: DateTime.now(),
        metadata: metadata ?? {},
      );
      
      await _dbHelper.insert('user_interactions', interaction.toJson());
      
      // Update recommendation models
      await _updateModels(interaction);
    } catch (e) {
      debugPrint('Error tracking interaction: $e');
    }
  }

  static Future<void> _updateModels(UserInteraction interaction) async {
    // Update user interaction history
    if (!_userInteractions.containsKey(interaction.userId)) {
      _userInteractions[interaction.userId] = [];
    }
    _userInteractions[interaction.userId]!.add(interaction.athkarId);

    // Update user preference weights based on interaction type
    final weight = _getInteractionWeight(interaction.type.toString());
    _userPreferenceWeights[interaction.athkarId] =
        (_userPreferenceWeights[interaction.athkarId] ?? 0.0) + weight;
  }

  // Helper methods for user profile analysis
  static String _getMostActiveTime(List<Map<String, dynamic>> sessions) {
    if (sessions.isEmpty) return 'morning';

    final hourCounts = <int, int>{};
    for (final session in sessions) {
      final startTime = session['start_time'] as int?;
      if (startTime != null) {
        final hour = DateTime.fromMillisecondsSinceEpoch(startTime).hour;
        hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
      }
    }

    if (hourCounts.isEmpty) return 'morning';

    final mostActiveHour = hourCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;

    if (mostActiveHour >= 5 && mostActiveHour < 12) return 'morning';
    if (mostActiveHour >= 12 && mostActiveHour < 17) return 'afternoon';
    if (mostActiveHour >= 17 && mostActiveHour < 21) return 'evening';
    return 'night';
  }

  static Future<List<String>> _getPreferredCategories(String userId) async {
    try {
      final results = await _dbHelper.rawQuery('''
        SELECT ar.category_id, COUNT(*) as usage_count
        FROM user_progress up
        JOIN athkar_routines ar ON up.routine_id = ar.id
        WHERE up.user_id = ?
        GROUP BY ar.category_id
        ORDER BY usage_count DESC
        LIMIT 5
      ''', [userId]);

      return results.map((r) => r['category_id'] as String).toList();
    } catch (e) {
      debugPrint('Error getting preferred categories: $e');
      return [];
    }
  }

  static double _getInteractionWeight(String interactionType) {
    switch (interactionType) {
      case 'completed':
        return 1.0;
      case 'liked':
        return 0.8;
      case 'shared':
        return 0.6;
      case 'viewed':
        return 0.3;
      default:
        return 0.1;
    }
  }

  // Content recommendation methods
  static Future<List<ContentRecommendation>> _getStressReliefContent() async {
    return [
      ContentRecommendation(
        id: 'stress_relief_1',
        type: 'dhikr',
        title: 'Stress Relief Dhikr',
        content: 'اللَّهُمَّ أَعِنِّي عَلَى ذِكْرِكَ وَشُكْرِكَ وَحُسْنِ عِبَادَتِكَ',
        relevanceScore: 0.9,
        reason: 'Recommended for stress relief',
        metadata: {
          'category': 'stress_relief',
          'difficulty': 'easy',
          'estimatedDuration': 5,
        },
      ),
    ];
  }

  static Future<List<ContentRecommendation>> _getGratitudeContent() async {
    return [
      ContentRecommendation(
        id: 'gratitude_1',
        type: 'dhikr',
        title: 'Gratitude Dhikr',
        content: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        relevanceScore: 0.9,
        reason: 'Recommended for expressing gratitude',
        metadata: {
          'category': 'gratitude',
          'difficulty': 'easy',
          'estimatedDuration': 3,
        },
      ),
    ];
  }

  static Future<List<ContentRecommendation>> _getGuidanceContent() async {
    return [
      ContentRecommendation(
        id: 'guidance_1',
        type: 'dua',
        title: 'Seeking Guidance',
        content: 'اللَّهُمَّ اهْدِنِي فِيمَنْ هَدَيْتَ',
        relevanceScore: 0.9,
        reason: 'Recommended for seeking guidance',
        metadata: {
          'category': 'guidance',
          'difficulty': 'medium',
          'estimatedDuration': 5,
        },
      ),
    ];
  }

  static Future<List<ContentRecommendation>> _getRepentanceContent() async {
    return [
      ContentRecommendation(
        id: 'repentance_1',
        type: 'dhikr',
        title: 'Seeking Forgiveness',
        content: 'أَسْتَغْفِرُ اللَّهَ الْعَظِيمَ الَّذِي لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ وَأَتُوبُ إِلَيْهِ',
        relevanceScore: 0.9,
        reason: 'Recommended for seeking forgiveness',
        metadata: {
          'category': 'repentance',
          'difficulty': 'medium',
          'estimatedDuration': 5,
        },
      ),
    ];
  }

  static Future<List<ContentRecommendation>> _getPeacefulContent() async {
    return [
      ContentRecommendation(
        id: 'peaceful_1',
        type: 'dhikr',
        title: 'Peace and Tranquility',
        content: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ سُبْحَانَ اللَّهِ الْعَظِيمِ',
        relevanceScore: 0.9,
        reason: 'Recommended for peace and tranquility',
        metadata: {'category': 'peace', 'difficulty': 'easy', 'estimatedDuration': 3},
      ),
    ];
  }

  static Future<List<ContentRecommendation>> _getAnxietyReliefContent() async {
    return [
      ContentRecommendation(
        id: 'anxiety_1',
        type: 'dhikr',
        title: 'Anxiety Relief',
        content: 'لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ',
        relevanceScore: 0.9,
        reason: 'Recommended for anxiety relief',
        metadata: {'category': 'anxiety_relief', 'difficulty': 'easy', 'estimatedDuration': 3},
      ),
    ];
  }

  static Future<List<ContentRecommendation>> _getGeneralContent() async {
    return [
      ContentRecommendation(
        id: 'general_1',
        type: 'dhikr',
        title: 'General Dhikr',
        content: 'لَا إِلَهَ إِلَّا اللَّهُ',
        relevanceScore: 0.7,
        reason: 'General recommendation',
        metadata: {'category': 'general', 'difficulty': 'easy', 'estimatedDuration': 2},
      ),
    ];
  }

  static Future<List<ContentRecommendation>> _getTravelContent() async {
    return [
      ContentRecommendation(
        id: 'travel_1',
        type: 'dua',
        title: 'Travel Dua',
        content: 'سُبْحَانَ الَّذِي سَخَّرَ لَنَا هَذَا وَمَا كُنَّا لَهُ مُقْرِنِينَ',
        relevanceScore: 0.9,
        reason: 'Recommended for travel',
        metadata: {'category': 'travel', 'difficulty': 'medium', 'estimatedDuration': 5},
      ),
    ];
  }

  static Future<List<ContentRecommendation>> _getHealingContent() async {
    return [
      ContentRecommendation(
        id: 'healing_1',
        type: 'dua',
        title: 'Healing Dua',
        content: 'اللَّهُمَّ رَبَّ النَّاسِ أَذْهِبِ الْبَأْسَ',
        relevanceScore: 0.9,
        reason: 'Recommended for healing',
        metadata: {'category': 'healing', 'difficulty': 'medium', 'estimatedDuration': 5},
      ),
    ];
  }

  static Future<List<ContentRecommendation>> _getStudyContent() async {
    return [
      ContentRecommendation(
        id: 'study_1',
        type: 'dua',
        title: 'Study Dua',
        content: 'رَبِّ اشْرَحْ لِي صَدْرِي وَيَسِّرْ لِي أَمْرِي',
        relevanceScore: 0.9,
        reason: 'Recommended for studying',
        metadata: {'category': 'study', 'difficulty': 'medium', 'estimatedDuration': 5},
      ),
    ];
  }
}
