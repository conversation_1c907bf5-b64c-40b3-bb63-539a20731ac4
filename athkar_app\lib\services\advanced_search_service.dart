import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../database/database_helper.dart';
import '../models/search_models.dart';

/// Advanced search service for Islamic content
/// Provides comprehensive search functionality across Hadith, Quran, and Athkar
class AdvancedSearchService {
  static final DatabaseHelper _dbHelper = DatabaseHelper();
  
  // Search preferences
  static bool _searchInArabic = true;
  static bool _searchInTranslation = true;
  static bool _searchInNarrator = true;
  static bool _searchInTafseer = true;
  
  /// Initialize search service with user preferences
  static Future<void> initialize() async {
    await _loadSearchPreferences();
  }
  
  /// Load search preferences from SharedPreferences
  static Future<void> _loadSearchPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    
    _searchInArabic = prefs.getBool('hadith_search_arabic') ?? true;
    _searchInTranslation = prefs.getBool('hadith_search_translation') ?? true;
    _searchInNarrator = prefs.getBool('hadith_search_narrator') ?? true;
    _searchInTafseer = prefs.getBool('quran_search_tafseer') ?? true;
  }
  
  /// Search Hadith with advanced filters
  static Future<List<HadithSearchResult>> searchHadith({
    required String query,
    List<String>? collections,
    String? narrator,
    String? grade,
    int limit = 50,
  }) async {
    if (query.trim().isEmpty) return [];
    
    await _loadSearchPreferences();
    
    final results = <HadithSearchResult>[];
    // Prepare search terms for better matching
    _prepareSearchTerms(query);
    
    try {
      // Build search query based on preferences
      String whereClause = '';
      List<String> conditions = [];
      
      if (_searchInArabic) {
        conditions.add('arabic_text LIKE ?');
      }
      
      if (_searchInTranslation) {
        conditions.add('translation LIKE ?');
      }
      
      if (_searchInNarrator) {
        conditions.add('narrator LIKE ?');
      }
      
      if (collections != null && collections.isNotEmpty) {
        conditions.add('collection_id IN (${collections.map((_) => '?').join(',')})');
      }
      
      if (narrator != null && narrator.isNotEmpty) {
        conditions.add('narrator LIKE ?');
      }
      
      if (grade != null) {
        conditions.add('grade = ?');
      }
      
      whereClause = conditions.join(' OR ');
      
      // Execute search query
      final db = await _dbHelper.database;
      final queryParams = <String>[];
      
      // Add search term parameters
      for (int i = 0; i < conditions.length; i++) {
        if (conditions[i].contains('LIKE')) {
          queryParams.add('%${query.trim()}%');
        }
      }
      
      // Add filter parameters
      if (collections != null && collections.isNotEmpty) {
        queryParams.addAll(collections);
      }
      
      if (narrator != null && narrator.isNotEmpty) {
        queryParams.add('%$narrator%');
      }
      
      if (grade != null) {
        queryParams.add(grade.toString());
      }
      
      final rawResults = await db.query(
        'hadith',
        where: whereClause,
        whereArgs: queryParams,
        limit: limit,
        orderBy: 'id DESC',
      );
      
      // Convert to HadithSearchResult objects
      for (final row in rawResults) {
        results.add(HadithSearchResult.fromMap(row, query));
      }
      
      // Sort by relevance
      results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
      
    } catch (e) {
      debugPrint('Error searching Hadith: $e');
    }
    
    return results;
  }
  
  /// Search Quran with advanced filters
  static Future<List<QuranSearchResult>> searchQuran({
    required String query,
    List<int>? surahs,
    bool includeTranslation = true,
    bool includeTafseer = true,
    int limit = 50,
  }) async {
    if (query.trim().isEmpty) return [];
    
    final results = <QuranSearchResult>[];
    
    try {
      final db = await _dbHelper.database;
      final conditions = <String>[];
      final queryParams = <String>[];
      
      // Search in Arabic text
      conditions.add('arabic_text LIKE ?');
      queryParams.add('%${query.trim()}%');
      
      // Search in translation if enabled
      if (includeTranslation) {
        conditions.add('translation LIKE ?');
        queryParams.add('%${query.trim()}%');
      }
      
      // Search in Tafseer if enabled
      if (includeTafseer && _searchInTafseer) {
        conditions.add('tafseer LIKE ?');
        queryParams.add('%${query.trim()}%');
      }
      
      // Filter by specific Surahs
      String whereClause = '(${conditions.join(' OR ')})';
      if (surahs != null && surahs.isNotEmpty) {
        whereClause += ' AND surah_number IN (${surahs.map((_) => '?').join(',')})';
        queryParams.addAll(surahs.map((s) => s.toString()));
      }
      
      final rawResults = await db.query(
        'quran_verses',
        where: whereClause,
        whereArgs: queryParams,
        limit: limit,
        orderBy: 'surah_number, ayah_number',
      );
      
      // Convert to QuranSearchResult objects
      for (final row in rawResults) {
        results.add(QuranSearchResult.fromMap(row, query));
      }
      
    } catch (e) {
      debugPrint('Error searching Quran: $e');
    }
    
    return results;
  }
  
  /// Search Athkar with filters
  static Future<List<AthkarSearchResult>> searchAthkar({
    required String query,
    List<String>? categories,
    int limit = 50,
  }) async {
    if (query.trim().isEmpty) return [];
    
    final results = <AthkarSearchResult>[];
    
    try {
      final db = await _dbHelper.database;
      final conditions = <String>[];
      final queryParams = <String>[];
      
      // Search in Arabic text and translation
      conditions.addAll([
        'arabic_text LIKE ?',
        'translation LIKE ?',
        'title LIKE ?',
        'description LIKE ?',
      ]);
      
      queryParams.addAll([
        '%${query.trim()}%',
        '%${query.trim()}%',
        '%${query.trim()}%',
        '%${query.trim()}%',
      ]);
      
      String whereClause = '(${conditions.join(' OR ')})';
      
      // Filter by categories
      if (categories != null && categories.isNotEmpty) {
        whereClause += ' AND category_id IN (${categories.map((_) => '?').join(',')})';
        queryParams.addAll(categories);
      }
      
      final rawResults = await db.query(
        'athkar_steps',
        where: whereClause,
        whereArgs: queryParams,
        limit: limit,
        orderBy: 'id DESC',
      );
      
      // Convert to AthkarSearchResult objects
      for (final row in rawResults) {
        results.add(AthkarSearchResult.fromMap(row, query));
      }
      
    } catch (e) {
      debugPrint('Error searching Athkar: $e');
    }
    
    return results;
  }
  
  /// Prepare search terms for better matching
  static List<String> _prepareSearchTerms(String query) {
    final terms = query.trim().split(RegExp(r'\s+'));
    final prepared = <String>[];
    
    for (final term in terms) {
      if (term.length >= 2) {
        prepared.add(term);
        // Add variations for Arabic text
        if (_isArabicText(term)) {
          prepared.addAll(_getArabicVariations(term));
        }
      }
    }
    
    return prepared;
  }
  
  /// Check if text contains Arabic characters
  static bool _isArabicText(String text) {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }
  
  /// Get Arabic text variations for better search
  static List<String> _getArabicVariations(String arabicText) {
    final variations = <String>[];
    
    // Remove diacritics for broader search
    String withoutDiacritics = arabicText.replaceAll(RegExp(r'[\u064B-\u0652\u0670\u0640]'), '');
    if (withoutDiacritics != arabicText) {
      variations.add(withoutDiacritics);
    }
    
    // Add common letter substitutions
    final substitutions = {
      'ة': 'ه',
      'ه': 'ة',
      'ي': 'ى',
      'ى': 'ي',
      'أ': 'ا',
      'إ': 'ا',
      'آ': 'ا',
    };
    
    String withSubstitutions = arabicText;
    substitutions.forEach((from, to) {
      withSubstitutions = withSubstitutions.replaceAll(from, to);
    });
    
    if (withSubstitutions != arabicText) {
      variations.add(withSubstitutions);
    }
    
    return variations;
  }
  
  /// Get search suggestions based on query
  static Future<List<String>> getSearchSuggestions(String query) async {
    if (query.trim().length < 2) return [];
    
    final suggestions = <String>[];
    
    try {
      final db = await _dbHelper.database;
      
      // Get suggestions from recent searches
      final recentSearches = await db.query(
        'search_history',
        where: 'query LIKE ?',
        whereArgs: ['%${query.trim()}%'],
        orderBy: 'search_count DESC, last_searched DESC',
        limit: 5,
      );
      
      for (final row in recentSearches) {
        suggestions.add(row['query'] as String);
      }
      
      // Get suggestions from popular terms
      if (suggestions.length < 5) {
        final popularTerms = await _getPopularSearchTerms(query);
        suggestions.addAll(popularTerms);
      }
      
    } catch (e) {
      debugPrint('Error getting search suggestions: $e');
    }
    
    return suggestions.take(5).toList();
  }
  
  /// Get popular search terms
  static Future<List<String>> _getPopularSearchTerms(String query) async {
    // This would typically come from analytics or predefined popular terms
    final popularTerms = [
      'الصلاة',
      'الوضوء',
      'الذكر',
      'الدعاء',
      'التسبيح',
      'الاستغفار',
      'البسملة',
      'الحمد',
      'التوبة',
      'الشكر',
    ];
    
    return popularTerms
        .where((term) => term.contains(query.trim()))
        .toList();
  }
  
  /// Save search query to history
  static Future<void> saveSearchHistory(String query) async {
    if (query.trim().isEmpty) return;
    
    try {
      final db = await _dbHelper.database;
      
      // Check if query already exists
      final existing = await db.query(
        'search_history',
        where: 'query = ?',
        whereArgs: [query.trim()],
      );
      
      if (existing.isNotEmpty) {
        // Update existing record
        await db.update(
          'search_history',
          {
            'search_count': (existing.first['search_count'] as int) + 1,
            'last_searched': DateTime.now().toIso8601String(),
          },
          where: 'query = ?',
          whereArgs: [query.trim()],
        );
      } else {
        // Insert new record
        await db.insert('search_history', {
          'query': query.trim(),
          'search_count': 1,
          'last_searched': DateTime.now().toIso8601String(),
        });
      }
      
    } catch (e) {
      debugPrint('Error saving search history: $e');
    }
  }
}
