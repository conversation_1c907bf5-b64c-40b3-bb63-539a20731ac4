import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/hadith_provider.dart';
import '../services/language_service.dart';
import '../l10n/app_localizations.dart';
import '../theme/app_theme.dart';
import '../models/hadith_models.dart';
import 'hadith_detail_screen.dart';

class HadithFavoritesScreen extends StatefulWidget {
  const HadithFavoritesScreen({super.key});

  @override
  State<HadithFavoritesScreen> createState() => _HadithFavoritesScreenState();
}

class _HadithFavoritesScreenState extends State<HadithFavoritesScreen> {
  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    final localizations = AppLocalizations.of(context)!;

    return Consumer<HadithProvider>(
      builder: (context, hadithProvider, child) {
        if (hadithProvider.favorites.isEmpty) {
          return _buildEmptyState(languageService);
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: hadithProvider.favorites.length,
          itemBuilder: (context, index) {
            final favorite = hadithProvider.favorites[index];
            return _buildFavoriteCard(favorite, hadithProvider, languageService);
          },
        );
      },
    );
  }

  Widget _buildEmptyState(LanguageService languageService) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            languageService.isArabic ? 'لا توجد أحاديث مفضلة' : 'No favorite hadiths',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            languageService.isArabic 
                ? 'اضغط على أيقونة القلب في أي حديث لإضافته للمفضلة'
                : 'Tap the heart icon on any hadith to add it to favorites',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey[600],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteCard(
    HadithFavorite favorite,
    HadithProvider hadithProvider,
    LanguageService languageService,
  ) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      elevation: 2,
      child: InkWell(
        onTap: () => _openHadithDetail(favorite, hadithProvider),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with collection and date
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getCollectionName(favorite.collectionId, languageService),
                      style: const TextStyle(
                        fontSize: 11,
                        color: AppTheme.primaryGreen,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _formatDate(favorite.dateAdded, languageService),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 8),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'delete') {
                        _confirmDelete(favorite, hadithProvider, languageService);
                      } else if (value == 'edit') {
                        _editFavorite(favorite, hadithProvider, languageService);
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            const Icon(Icons.edit, size: 16),
                            const SizedBox(width: 8),
                            Text(languageService.isArabic ? 'تعديل' : 'Edit'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            const Icon(Icons.delete, size: 16, color: Colors.red),
                            const SizedBox(width: 8),
                            Text(
                              languageService.isArabic ? 'حذف' : 'Delete',
                              style: const TextStyle(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Title
              Text(
                favorite.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 8),
              
              // Notes (if available)
              if (favorite.notes.isNotEmpty) ...[
                Text(
                  favorite.notes,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                    height: 1.4,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],
              
              // Reference info
              Row(
                children: [
                  Icon(
                    Icons.bookmark,
                    size: 14,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${favorite.collectionId}:${favorite.bookNumber}:${favorite.hadithNumber}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontFamily: 'monospace',
                    ),
                  ),
                ],
              ),
              
              // Tags (if available)
              if (favorite.tags.isNotEmpty) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children: favorite.tags.map((tag) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        tag,
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.blue,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _getCollectionName(String collectionId, LanguageService languageService) {
    final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
    final collection = hadithProvider.collections.firstWhere(
      (c) => c.id == collectionId,
      orElse: () => HadithCollection(
        id: collectionId,
        name: collectionId,
        arabicName: collectionId,
        englishName: collectionId,
        description: '',
        arabicDescription: '',
        totalBooks: 0,
        totalHadiths: 0,
        author: '',
        arabicAuthor: '',
      ),
    );
    
    return languageService.isArabic ? collection.arabicName : collection.englishName;
  }

  String _formatDate(DateTime date, LanguageService languageService) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return languageService.isArabic ? 'اليوم' : 'Today';
    } else if (difference.inDays == 1) {
      return languageService.isArabic ? 'أمس' : 'Yesterday';
    } else if (difference.inDays < 7) {
      return languageService.isArabic 
          ? 'منذ ${difference.inDays} أيام'
          : '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Future<void> _openHadithDetail(HadithFavorite favorite, HadithProvider hadithProvider) async {
    try {
      final hadith = await hadithProvider.getHadith(
        favorite.collectionId,
        favorite.bookNumber,
        favorite.hadithNumber,
      );
      
      if (hadith != null && mounted) {
        final collection = hadithProvider.collections.firstWhere(
          (c) => c.id == favorite.collectionId,
        );
        
        final books = await hadithProvider.getCollectionBooks(favorite.collectionId);
        final book = books.firstWhere(
          (b) => b.bookNumber == favorite.bookNumber,
        );
        
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => HadithDetailScreen(
              hadith: hadith,
              collection: collection,
              book: book,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح الحديث: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _confirmDelete(
    HadithFavorite favorite,
    HadithProvider hadithProvider,
    LanguageService languageService,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'حذف من المفضلة' : 'Remove from Favorites'),
        content: Text(
          languageService.isArabic 
              ? 'هل تريد حذف هذا الحديث من المفضلة؟'
              : 'Do you want to remove this hadith from favorites?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await hadithProvider.removeFromFavorites(favorite.id);
              if (mounted) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(languageService.isArabic ? 'تم حذف الحديث من المفضلة' : 'Hadith removed from favorites'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(languageService.isArabic ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );
  }

  void _editFavorite(
    HadithFavorite favorite,
    HadithProvider hadithProvider,
    LanguageService languageService,
  ) {
    final titleController = TextEditingController(text: favorite.title);
    final notesController = TextEditingController(text: favorite.notes);
    List<String> tags = List.from(favorite.tags);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(languageService.isArabic ? 'تعديل المفضلة' : 'Edit Favorite'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  decoration: InputDecoration(
                    labelText: languageService.isArabic ? 'العنوان' : 'Title',
                    border: const OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: notesController,
                  decoration: InputDecoration(
                    labelText: languageService.isArabic ? 'الملاحظات' : 'Notes',
                    border: const OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                // Tags would be implemented here
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                // Update favorite logic would be implemented here
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(languageService.isArabic ? 'تم تحديث المفضلة' : 'Favorite updated'),
                    backgroundColor: AppTheme.primaryGreen,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
              ),
              child: Text(languageService.isArabic ? 'حفظ' : 'Save'),
            ),
          ],
        ),
      ),
    );
  }
}
