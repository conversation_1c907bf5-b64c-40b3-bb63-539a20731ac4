#!/usr/bin/env python3
"""
Generate app icons for the Islamic Athkar application
Requires: pip install Pillow
"""

from PIL import Image, ImageDraw, ImageFont
import math
import os

def create_icon(size):
    """Create an app icon of the specified size"""
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Colors
    green_dark = (46, 125, 50)      # #2E7D32
    green_light = (76, 175, 80)     # #4CAF50
    gold = (255, 179, 0)            # #FFB300
    gold_light = (255, 215, 0)      # #FFD700
    white = (255, 255, 255)
    
    # Calculate dimensions
    center = size // 2
    radius = int(size * 0.45)
    
    # Draw background circle with gradient effect
    for i in range(radius, 0, -1):
        # Create gradient from light green to dark green
        ratio = i / radius
        r = int(green_light[0] * ratio + green_dark[0] * (1 - ratio))
        g = int(green_light[1] * ratio + green_dark[1] * (1 - ratio))
        b = int(green_light[2] * ratio + green_dark[2] * (1 - ratio))
        
        draw.ellipse([center - i, center - i, center + i, center + i], 
                    fill=(r, g, b, 255))
    
    # Draw outer ring
    ring_width = max(2, size // 64)
    draw.ellipse([center - radius + ring_width, center - radius + ring_width, 
                 center + radius - ring_width, center + radius - ring_width], 
                outline=gold, width=ring_width)
    
    # Draw 8-pointed star (Islamic star)
    star_radius = int(radius * 0.4)
    star_points = []
    
    for i in range(16):  # 8 points with intermediate points
        angle = i * math.pi / 8
        if i % 2 == 0:  # Main points
            r = star_radius
        else:  # Intermediate points
            r = star_radius * 0.4
        
        x = center + int(r * math.cos(angle - math.pi / 2))
        y = center + int(r * math.sin(angle - math.pi / 2))
        star_points.append((x, y))
    
    # Draw star
    draw.polygon(star_points, fill=gold, outline=gold_light)
    
    # Draw inner star
    inner_star_radius = int(star_radius * 0.6)
    inner_star_points = []
    
    for i in range(16):
        angle = i * math.pi / 8
        if i % 2 == 0:
            r = inner_star_radius
        else:
            r = inner_star_radius * 0.4
        
        x = center + int(r * math.cos(angle - math.pi / 2))
        y = center + int(r * math.sin(angle - math.pi / 2))
        inner_star_points.append((x, y))
    
    draw.polygon(inner_star_points, fill=white, outline=gold)
    
    # Draw crescent moon at top
    moon_y = center - int(radius * 0.6)
    moon_size = int(radius * 0.25)
    
    # Outer circle for crescent
    draw.ellipse([center - moon_size, moon_y - moon_size, 
                 center + moon_size, moon_y + moon_size], fill=white)
    
    # Inner circle to create crescent shape
    inner_offset = int(moon_size * 0.3)
    draw.ellipse([center - moon_size + inner_offset, moon_y - moon_size, 
                 center + moon_size + inner_offset, moon_y + moon_size], 
                fill=green_dark)
    
    # Draw prayer beads at bottom
    beads_y = center + int(radius * 0.6)
    bead_size = max(2, size // 32)
    
    # Central bead
    draw.ellipse([center - bead_size, beads_y - bead_size, 
                 center + bead_size, beads_y + bead_size], fill=gold)
    
    # Side beads
    bead_spacing = int(bead_size * 2.5)
    for i in [-2, -1, 1, 2]:
        x = center + i * bead_spacing
        bead_radius = bead_size if abs(i) == 1 else int(bead_size * 0.7)
        draw.ellipse([x - bead_radius, beads_y - bead_radius, 
                     x + bead_radius, beads_y + bead_radius], 
                    fill=white, outline=gold)
    
    # Draw connecting line for beads
    line_y = beads_y
    draw.line([center - 2 * bead_spacing, line_y, center + 2 * bead_spacing, line_y], 
              fill=gold, width=max(1, size // 128))
    
    return img

def main():
    """Generate icons in multiple sizes"""
    # Icon sizes for Android
    sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192
    }
    
    # Create high-res version
    high_res = create_icon(512)
    high_res.save('assets/icons/ic_launcher_512.png')
    print("Created high-res icon: assets/icons/ic_launcher_512.png")
    
    # Create Android icons
    for folder, size in sizes.items():
        icon = create_icon(size)
        
        # Create directory if it doesn't exist
        dir_path = f'android/app/src/main/res/{folder}'
        os.makedirs(dir_path, exist_ok=True)
        
        # Save icon
        icon_path = f'{dir_path}/ic_launcher.png'
        icon.save(icon_path)
        print(f"Created {size}x{size} icon: {icon_path}")
    
    print("\nAll icons generated successfully!")
    print("Don't forget to update AndroidManifest.xml to use the new icon.")

if __name__ == "__main__":
    main()
