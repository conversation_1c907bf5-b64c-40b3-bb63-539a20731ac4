<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":video_player_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\video_player_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":vibration" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\vibration\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":sqflite_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\sqflite_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":sign_in_with_apple" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\sign_in_with_apple\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\permission_handler_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\path_provider_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":in_app_purchase_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\in_app_purchase_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":google_sign_in_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\google_sign_in_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":flutter_secure_storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\flutter_secure_storage\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\flutter_plugin_android_lifecycle\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":flutter_overlay_window" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\flutter_overlay_window\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":flutter_native_splash" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\flutter_native_splash\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":flutter_keyboard_visibility" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\flutter_keyboard_visibility\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":firebase_core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":firebase_messaging" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":firebase_crashlytics" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":connectivity_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\connectivity_plus\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":app_links" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\app_links\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":local_auth_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\local_auth_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":geolocator_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":flutter_local_notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\flutter_local_notifications\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":file_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":audioplayers_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\audioplayers_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":workmanager_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\workmanager_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":webview_flutter_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\webview_flutter_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\shared_preferences_android\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":share_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":sensors_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\sensors_plus\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\package_info_plus\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":wakelock_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\wakelock_plus\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":network_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\network_info_plus\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":firebase_analytics" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":device_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\device_info_plus\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":camera_android_camerax" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":battery_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\battery_plus\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":app_settings" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\app_settings\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\android\app\src\main\assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\android\app\src\release\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\projects\12july\athkar\athkar_app\build\app\intermediates\shader_assets\release\compileReleaseShaders\out"/></dataSet></merger>