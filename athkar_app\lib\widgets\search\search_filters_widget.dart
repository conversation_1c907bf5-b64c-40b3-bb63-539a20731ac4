import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../models/search_models.dart';
import '../../theme/app_theme.dart';

class SearchFiltersWidget extends StatefulWidget {
  final SearchFilters filters;
  final Function(SearchFilters) onFiltersChanged;

  const SearchFiltersWidget({
    super.key,
    required this.filters,
    required this.onFiltersChanged,
  });

  @override
  State<SearchFiltersWidget> createState() => _SearchFiltersWidgetState();
}

class _SearchFiltersWidgetState extends State<SearchFiltersWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  // Filter state
  List<String> _selectedCollections = [];
  List<int> _selectedSurahs = [];
  List<String> _selectedCategories = [];
  String _selectedNarrator = '';
  String _selectedGrade = '';
  bool _includeArabic = true;
  bool _includeTranslation = true;
  bool _includeTafseer = true;
  bool _includeNarrator = true;

  // Available options
  final List<Map<String, String>> _hadithCollections = [
    {'id': 'bukhari', 'name_ar': 'صحيح البخاري', 'name_en': 'Sahih Bukhari'},
    {'id': 'muslim', 'name_ar': 'صحيح مسلم', 'name_en': 'Sahih Muslim'},
    {'id': 'abudawud', 'name_ar': 'سنن أبي داود', 'name_en': 'Sunan Abu Dawud'},
    {'id': 'tirmidhi', 'name_ar': 'سنن الترمذي', 'name_en': 'Sunan Tirmidhi'},
    {'id': 'nasai', 'name_ar': 'سنن النسائي', 'name_en': 'Sunan Nasai'},
    {'id': 'ibnmajah', 'name_ar': 'سنن ابن ماجه', 'name_en': 'Sunan Ibn Majah'},
  ];

  final List<Map<String, dynamic>> _quranSurahs = [
    {'number': 1, 'name_ar': 'الفاتحة', 'name_en': 'Al-Fatiha'},
    {'number': 2, 'name_ar': 'البقرة', 'name_en': 'Al-Baqarah'},
    {'number': 3, 'name_ar': 'آل عمران', 'name_en': 'Ali Imran'},
    {'number': 4, 'name_ar': 'النساء', 'name_en': 'An-Nisa'},
    {'number': 5, 'name_ar': 'المائدة', 'name_en': 'Al-Maidah'},
    // Add more surahs as needed
  ];

  final List<Map<String, String>> _athkarCategories = [
    {'id': 'morning', 'name_ar': 'أذكار الصباح', 'name_en': 'Morning Athkar'},
    {'id': 'evening', 'name_ar': 'أذكار المساء', 'name_en': 'Evening Athkar'},
    {'id': 'prayer', 'name_ar': 'أذكار الصلاة', 'name_en': 'Prayer Athkar'},
    {'id': 'sleep', 'name_ar': 'أذكار النوم', 'name_en': 'Sleep Athkar'},
    {'id': 'general', 'name_ar': 'أذكار عامة', 'name_en': 'General Athkar'},
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _initializeFilters();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeFilters() {
    _selectedCollections = widget.filters.collections ?? [];
    _selectedSurahs = widget.filters.surahs ?? [];
    _selectedCategories = widget.filters.categories ?? [];
    _selectedNarrator = widget.filters.narrator ?? '';
    _selectedGrade = widget.filters.grade ?? '';
    _includeArabic = widget.filters.includeArabic;
    _includeTranslation = widget.filters.includeTranslation;
    _includeTafseer = widget.filters.includeTafseer;
    _includeNarrator = widget.filters.includeNarrator;
  }

  void _applyFilters() {
    final newFilters = SearchFilters(
      collections: _selectedCollections.isEmpty ? null : _selectedCollections,
      surahs: _selectedSurahs.isEmpty ? null : _selectedSurahs,
      categories: _selectedCategories.isEmpty ? null : _selectedCategories,
      narrator: _selectedNarrator.isEmpty ? null : _selectedNarrator,
      grade: _selectedGrade.isEmpty ? null : _selectedGrade,
      includeArabic: _includeArabic,
      includeTranslation: _includeTranslation,
      includeTafseer: _includeTafseer,
      includeNarrator: _includeNarrator,
    );

    widget.onFiltersChanged(newFilters);
  }

  void _clearFilters() {
    setState(() {
      _selectedCollections.clear();
      _selectedSurahs.clear();
      _selectedCategories.clear();
      _selectedNarrator = '';
      _selectedGrade = '';
      _includeArabic = true;
      _includeTranslation = true;
      _includeTafseer = true;
      _includeNarrator = true;
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, -1),
        end: Offset.zero,
      ).animate(_slideAnimation),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.05),
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey[300]!,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.filter_list,
                    color: AppTheme.primaryGreen,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    languageService.isArabic ? 'مرشحات البحث' : 'Search Filters',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryGreen,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: _clearFilters,
                    child: Text(
                      languageService.isArabic ? 'مسح الكل' : 'Clear All',
                      style: const TextStyle(
                        color: AppTheme.primaryGreen,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Filters Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Search In Section
                  _buildSectionTitle(
                    languageService.isArabic ? 'البحث في:' : 'Search in:',
                    Icons.search,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildToggleChip(
                        languageService.isArabic ? 'النص العربي' : 'Arabic Text',
                        _includeArabic,
                        (value) => setState(() => _includeArabic = value),
                      ),
                      _buildToggleChip(
                        languageService.isArabic ? 'الترجمة' : 'Translation',
                        _includeTranslation,
                        (value) => setState(() => _includeTranslation = value),
                      ),
                      _buildToggleChip(
                        languageService.isArabic ? 'التفسير' : 'Tafseer',
                        _includeTafseer,
                        (value) => setState(() => _includeTafseer = value),
                      ),
                      _buildToggleChip(
                        languageService.isArabic ? 'الراوي' : 'Narrator',
                        _includeNarrator,
                        (value) => setState(() => _includeNarrator = value),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Hadith Collections
                  _buildSectionTitle(
                    languageService.isArabic ? 'مجموعات الأحاديث:' : 'Hadith Collections:',
                    Icons.book,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _hadithCollections.map((collection) {
                      final isSelected = _selectedCollections.contains(collection['id']);
                      return _buildSelectableChip(
                        languageService.isArabic ? collection['name_ar']! : collection['name_en']!,
                        isSelected,
                        () {
                          setState(() {
                            if (isSelected) {
                              _selectedCollections.remove(collection['id']);
                            } else {
                              _selectedCollections.add(collection['id']!);
                            }
                          });
                        },
                      );
                    }).toList(),
                  ),

                  const SizedBox(height: 24),

                  // Quran Surahs (showing first few)
                  _buildSectionTitle(
                    languageService.isArabic ? 'سور القرآن:' : 'Quran Surahs:',
                    Icons.menu_book,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _quranSurahs.take(5).map((surah) {
                      final isSelected = _selectedSurahs.contains(surah['number']);
                      return _buildSelectableChip(
                        languageService.isArabic ? surah['name_ar']! : surah['name_en']!,
                        isSelected,
                        () {
                          setState(() {
                            if (isSelected) {
                              _selectedSurahs.remove(surah['number']);
                            } else {
                              _selectedSurahs.add(surah['number'] as int);
                            }
                          });
                        },
                      );
                    }).toList(),
                  ),

                  const SizedBox(height: 24),

                  // Athkar Categories
                  _buildSectionTitle(
                    languageService.isArabic ? 'فئات الأذكار:' : 'Athkar Categories:',
                    Icons.favorite,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _athkarCategories.map((category) {
                      final isSelected = _selectedCategories.contains(category['id']);
                      return _buildSelectableChip(
                        languageService.isArabic ? category['name_ar']! : category['name_en']!,
                        isSelected,
                        () {
                          setState(() {
                            if (isSelected) {
                              _selectedCategories.remove(category['id']);
                            } else {
                              _selectedCategories.add(category['id']!);
                            }
                          });
                        },
                      );
                    }).toList(),
                  ),

                  const SizedBox(height: 24),

                  // Apply Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _applyFilters,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryGreen,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        languageService.isArabic ? 'تطبيق المرشحات' : 'Apply Filters',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 18,
          color: AppTheme.primaryGreen,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryGreen,
          ),
        ),
      ],
    );
  }

  Widget _buildToggleChip(String label, bool isSelected, Function(bool) onChanged) {
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          color: isSelected ? Colors.white : AppTheme.primaryGreen,
          fontWeight: FontWeight.w500,
        ),
      ),
      selected: isSelected,
      onSelected: onChanged,
      selectedColor: AppTheme.primaryGreen,
      backgroundColor: Colors.white,
      side: BorderSide(
        color: AppTheme.primaryGreen,
        width: 1,
      ),
      showCheckmark: false,
    );
  }

  Widget _buildSelectableChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryGreen : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppTheme.primaryGreen,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isSelected ? Colors.white : AppTheme.primaryGreen,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
