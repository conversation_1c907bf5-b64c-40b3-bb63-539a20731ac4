import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/language_service.dart';
import '../providers/theme_provider.dart';
import '../theme/app_theme.dart';

class ThemeCustomizationWidget extends StatefulWidget {
  const ThemeCustomizationWidget({super.key});

  @override
  State<ThemeCustomizationWidget> createState() => _ThemeCustomizationWidgetState();
}

class _ThemeCustomizationWidgetState extends State<ThemeCustomizationWidget> {
  Color _selectedPrimaryColor = AppTheme.primaryGreen;
  Color _selectedAccentColor = const Color(0xFFFFB300);
  String _selectedPattern = 'geometric';
  double _patternOpacity = 0.1;

  final List<Color> _islamicColors = [
    const Color(0xFF2E7D32), // Traditional Green
    const Color(0xFF1565C0), // Masjid Blue
    const Color(0xFFFF8F00), // Sunset Gold
    const Color(0xFF4A148C), // Royal Purple
    const Color(0xFF8D6E63), // Earth Brown
    const Color(0xFF37474F), // Night Gray
    const Color(0xFF00695C), // Teal
    const Color(0xFFD32F2F), // Deep Red
  ];

  final List<String> _islamicPatterns = [
    'geometric',
    'arabesque',
    'calligraphy',
    'stars',
    'none',
  ];

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(languageService.isArabic ? 'تخصيص المظهر' : 'Theme Customization'),
        backgroundColor: _selectedPrimaryColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: Icon(languageService.backIcon),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () => _saveTheme(themeProvider, languageService),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Preview Section
            _buildPreviewSection(languageService),
            
            const SizedBox(height: 24),

            // Primary Color Section
            _buildSectionHeader(
              languageService.isArabic ? 'اللون الأساسي' : 'Primary Color',
              Icons.palette,
            ),
            const SizedBox(height: 12),
            _buildColorPicker(_islamicColors, _selectedPrimaryColor, (color) {
              setState(() {
                _selectedPrimaryColor = color;
              });
            }),

            const SizedBox(height: 24),

            // Accent Color Section
            _buildSectionHeader(
              languageService.isArabic ? 'اللون المساعد' : 'Accent Color',
              Icons.color_lens,
            ),
            const SizedBox(height: 12),
            _buildColorPicker(_islamicColors, _selectedAccentColor, (color) {
              setState(() {
                _selectedAccentColor = color;
              });
            }),

            const SizedBox(height: 24),

            // Background Pattern Section
            _buildSectionHeader(
              languageService.isArabic ? 'نمط الخلفية' : 'Background Pattern',
              Icons.texture,
            ),
            const SizedBox(height: 12),
            _buildPatternSelector(languageService),

            const SizedBox(height: 24),

            // Pattern Opacity Section
            _buildSectionHeader(
              languageService.isArabic ? 'شفافية النمط' : 'Pattern Opacity',
              Icons.opacity,
            ),
            const SizedBox(height: 12),
            _buildOpacitySlider(languageService),

            const SizedBox(height: 32),

            // Preset Themes Section
            _buildSectionHeader(
              languageService.isArabic ? 'المظاهر المحددة مسبقاً' : 'Preset Themes',
              Icons.style,
            ),
            const SizedBox(height: 12),
            _buildPresetThemes(languageService),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewSection(LanguageService languageService) {
    return Card(
      elevation: 4,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              _selectedPrimaryColor.withValues(alpha: 0.1),
              _selectedAccentColor.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              languageService.isArabic ? 'معاينة المظهر' : 'Theme Preview',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: _selectedPrimaryColor,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _selectedPrimaryColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.mosque, color: Colors.white),
                  const SizedBox(width: 8),
                  Text(
                    languageService.isArabic ? 'أذكار المسلم' : 'Muslim Athkar',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _selectedAccentColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: _selectedAccentColor),
                    ),
                    child: Text(
                      languageService.isArabic ? 'سُبْحَانَ اللَّهِ' : 'Subhan Allah',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: _selectedAccentColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _selectedPrimaryColor,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: const Icon(Icons.add, color: Colors.white),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: _selectedPrimaryColor, size: 24),
        const SizedBox(width: 12),
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: _selectedPrimaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildColorPicker(List<Color> colors, Color selectedColor, Function(Color) onColorSelected) {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: colors.map((color) {
        final isSelected = color == selectedColor;
        return GestureDetector(
          onTap: () => onColorSelected(color),
          child: Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: isSelected ? Colors.black : Colors.grey.shade300,
                width: isSelected ? 3 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: isSelected
                ? const Icon(Icons.check, color: Colors.white, size: 24)
                : null,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPatternSelector(LanguageService languageService) {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: _islamicPatterns.map((pattern) {
        final isSelected = pattern == _selectedPattern;
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedPattern = pattern;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected ? _selectedPrimaryColor : Colors.grey.shade200,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected ? _selectedPrimaryColor : Colors.grey.shade400,
              ),
            ),
            child: Text(
              _getPatternName(pattern, languageService),
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black87,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildOpacitySlider(LanguageService languageService) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(languageService.isArabic ? 'شفاف' : 'Transparent'),
                Text('${(_patternOpacity * 100).round()}%'),
                Text(languageService.isArabic ? 'معتم' : 'Opaque'),
              ],
            ),
            Slider(
              value: _patternOpacity,
              min: 0.0,
              max: 0.5,
              divisions: 10,
              activeColor: _selectedPrimaryColor,
              onChanged: (value) {
                setState(() {
                  _patternOpacity = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPresetThemes(LanguageService languageService) {
    final presets = [
      {
        'name': languageService.isArabic ? 'الأخضر التقليدي' : 'Traditional Green',
        'primary': const Color(0xFF2E7D32),
        'accent': const Color(0xFFFFB300),
        'pattern': 'geometric',
      },
      {
        'name': languageService.isArabic ? 'أزرق المسجد' : 'Masjid Blue',
        'primary': const Color(0xFF1565C0),
        'accent': const Color(0xFF00BCD4),
        'pattern': 'arabesque',
      },
      {
        'name': languageService.isArabic ? 'ذهبي الغروب' : 'Sunset Gold',
        'primary': const Color(0xFFFF8F00),
        'accent': const Color(0xFFD32F2F),
        'pattern': 'stars',
      },
      {
        'name': languageService.isArabic ? 'الوضع الليلي' : 'Night Mode',
        'primary': const Color(0xFF37474F),
        'accent': const Color(0xFF00695C),
        'pattern': 'none',
      },
    ];

    return Column(
      children: presets.map((preset) {
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    preset['primary'] as Color,
                    preset['accent'] as Color,
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            title: Text(preset['name'] as String),
            subtitle: Text(_getPatternName(preset['pattern'] as String, languageService)),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              setState(() {
                _selectedPrimaryColor = preset['primary'] as Color;
                _selectedAccentColor = preset['accent'] as Color;
                _selectedPattern = preset['pattern'] as String;
              });
            },
          ),
        );
      }).toList(),
    );
  }

  String _getPatternName(String pattern, LanguageService languageService) {
    switch (pattern) {
      case 'geometric':
        return languageService.isArabic ? 'هندسي' : 'Geometric';
      case 'arabesque':
        return languageService.isArabic ? 'أرابيسك' : 'Arabesque';
      case 'calligraphy':
        return languageService.isArabic ? 'خط عربي' : 'Calligraphy';
      case 'stars':
        return languageService.isArabic ? 'نجوم' : 'Stars';
      case 'none':
        return languageService.isArabic ? 'بدون نمط' : 'None';
      default:
        return pattern;
    }
  }

  void _saveTheme(ThemeProvider themeProvider, LanguageService languageService) {
    // Save the custom theme
    themeProvider.setCustomTheme(
      primaryColor: _selectedPrimaryColor,
      accentColor: _selectedAccentColor,
      backgroundPattern: _selectedPattern,
      patternOpacity: _patternOpacity,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          languageService.isArabic ? 'تم حفظ المظهر بنجاح' : 'Theme saved successfully',
        ),
        backgroundColor: _selectedPrimaryColor,
      ),
    );

    Navigator.pop(context);
  }
}
