import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'dart:math';

/// Smart notification service with prayer time-based scheduling and intelligent reminders
/// Provides contextual Islamic notifications based on user behavior and prayer times
class SmartNotificationService {
  static final SmartNotificationService _instance = SmartNotificationService._internal();
  factory SmartNotificationService() => _instance;
  SmartNotificationService._internal();

  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  
  // Notification scheduling data
  final List<SmartNotification> _scheduledNotifications = [];
  final Map<String, NotificationPreference> _userPreferences = {};
  final List<NotificationHistory> _notificationHistory = [];
  
  bool _isInitialized = false;

  /// Initialize the smart notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _initializeNotifications();
      await _loadUserPreferences();
      await _scheduleIntelligentReminders();
      
      _isInitialized = true;
      debugPrint('Smart Notification Service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Smart Notification Service: $e');
      rethrow;
    }
  }

  /// Schedule prayer time-based notifications
  Future<void> schedulePrayerBasedNotifications({
    required List<PrayerTime> prayerTimes,
    bool enablePreReminders = true,
    bool enablePostReminders = true,
  }) async {
    if (!_isInitialized) await initialize();

    for (final prayerTime in prayerTimes) {
      // Main prayer notification
      await _scheduleNotification(SmartNotification(
        id: 'prayer_${prayerTime.name}_${prayerTime.time.millisecondsSinceEpoch}',
        title: 'حان وقت ${prayerTime.displayName}',
        body: 'الله أكبر، الله أكبر، أشهد أن لا إله إلا الله',
        scheduledTime: prayerTime.time,
        type: NotificationType.prayerTime,
        priority: NotificationPriority.high,
        sound: 'athan_${prayerTime.name}.mp3',
        metadata: {'prayer': prayerTime.name},
      ));

      // Pre-prayer reminder (15 minutes before)
      if (enablePreReminders) {
        await _scheduleNotification(SmartNotification(
          id: 'pre_prayer_${prayerTime.name}_${prayerTime.time.millisecondsSinceEpoch}',
          title: 'تذكير: ${prayerTime.displayName} خلال 15 دقيقة',
          body: 'استعد للصلاة وتوضأ',
          scheduledTime: prayerTime.time.subtract(const Duration(minutes: 15)),
          type: NotificationType.prayerReminder,
          priority: NotificationPriority.medium,
          metadata: {'prayer': prayerTime.name, 'type': 'pre'},
        ));
      }

      // Post-prayer athkar reminder (5 minutes after)
      if (enablePostReminders) {
        await _scheduleNotification(SmartNotification(
          id: 'post_prayer_${prayerTime.name}_${prayerTime.time.millisecondsSinceEpoch}',
          title: 'أذكار ما بعد الصلاة',
          body: 'لا تنس أذكار ما بعد ${prayerTime.displayName}',
          scheduledTime: prayerTime.time.add(const Duration(minutes: 5)),
          type: NotificationType.athkarReminder,
          priority: NotificationPriority.medium,
          metadata: {'prayer': prayerTime.name, 'type': 'post'},
        ));
      }
    }
  }

  /// Schedule intelligent athkar reminders based on user behavior
  Future<void> scheduleIntelligentAthkarReminders() async {
    final preferences = _userPreferences['athkar'] ?? NotificationPreference.defaultPreference();
    
    if (!preferences.enabled) return;

    // Morning athkar - intelligent timing based on user's wake-up pattern
    final morningTime = await _calculateOptimalMorningTime();
    await _scheduleNotification(SmartNotification(
      id: 'morning_athkar_${DateTime.now().millisecondsSinceEpoch}',
      title: 'أذكار الصباح',
      body: 'ابدأ يومك بذكر الله والدعاء',
      scheduledTime: morningTime,
      type: NotificationType.athkarReminder,
      priority: NotificationPriority.medium,
      repeatInterval: const Duration(days: 1),
      metadata: {'category': 'morning'},
    ));

    // Evening athkar - intelligent timing based on sunset
    final eveningTime = await _calculateOptimalEveningTime();
    await _scheduleNotification(SmartNotification(
      id: 'evening_athkar_${DateTime.now().millisecondsSinceEpoch}',
      title: 'أذكار المساء',
      body: 'اختتم يومك بالذكر والاستغفار',
      scheduledTime: eveningTime,
      type: NotificationType.athkarReminder,
      priority: NotificationPriority.medium,
      repeatInterval: const Duration(days: 1),
      metadata: {'category': 'evening'},
    ));
  }

  /// Schedule contextual notifications based on user activity
  Future<void> scheduleContextualNotifications({
    required UserActivityPattern activityPattern,
  }) async {
    // Dhikr break reminders during work hours
    if (activityPattern.workHours != null) {
      for (int hour = activityPattern.workHours!.start; 
           hour <= activityPattern.workHours!.end; 
           hour += 2) {
        await _scheduleNotification(SmartNotification(
          id: 'work_dhikr_$hour',
          title: 'استراحة ذكر',
          body: 'خذ دقيقة لذكر الله: سبحان الله وبحمده',
          scheduledTime: DateTime.now().copyWith(hour: hour, minute: 0),
          type: NotificationType.dhikrBreak,
          priority: NotificationPriority.low,
          repeatInterval: const Duration(days: 1),
        ));
      }
    }

    // Sleep time dua reminder
    if (activityPattern.sleepTime != null) {
      await _scheduleNotification(SmartNotification(
        id: 'sleep_dua',
        title: 'دعاء النوم',
        body: 'باسمك اللهم أموت وأحيا',
        scheduledTime: activityPattern.sleepTime!.subtract(const Duration(minutes: 10)),
        type: NotificationType.sleepDua,
        priority: NotificationPriority.medium,
        repeatInterval: const Duration(days: 1),
      ));
    }
  }

  /// Schedule motivational notifications based on spiritual progress
  Future<void> scheduleMotivationalNotifications({
    required SpiritualProgress progress,
  }) async {
    // Streak celebration
    if (progress.currentStreak > 0 && progress.currentStreak % 7 == 0) {
      await _scheduleNotification(SmartNotification(
        id: 'streak_celebration_${progress.currentStreak}',
        title: 'مبارك! ${progress.currentStreak} أيام متتالية',
        body: 'استمر في هذا الإنجاز الرائع',
        scheduledTime: DateTime.now().add(const Duration(minutes: 1)),
        type: NotificationType.achievement,
        priority: NotificationPriority.high,
      ));
    }

    // Goal completion
    for (final goal in progress.nearCompletionGoals) {
      await _scheduleNotification(SmartNotification(
        id: 'goal_reminder_${goal.id}',
        title: 'أوشكت على إنجاز هدفك!',
        body: 'باقي ${goal.remainingCount} لإكمال "${goal.title}"',
        scheduledTime: DateTime.now().add(const Duration(hours: 2)),
        type: NotificationType.goalReminder,
        priority: NotificationPriority.medium,
        metadata: {'goal_id': goal.id},
      ));
    }

    // Encouragement for low activity
    if (progress.recentActivityLevel < 0.3) {
      await _scheduleNotification(SmartNotification(
        id: 'encouragement_${DateTime.now().millisecondsSinceEpoch}',
        title: 'لا تنس ذكر الله',
        body: 'الذكر يطمئن القلب ويجلب السكينة',
        scheduledTime: DateTime.now().add(const Duration(hours: 4)),
        type: NotificationType.encouragement,
        priority: NotificationPriority.low,
      ));
    }
  }

  /// Update notification preferences
  Future<void> updatePreferences(String category, NotificationPreference preference) async {
    _userPreferences[category] = preference;
    await _saveUserPreferences();
    
    // Reschedule notifications based on new preferences
    await _rescheduleNotifications(category);
  }

  /// Get notification analytics
  NotificationAnalytics getNotificationAnalytics() {
    final totalSent = _notificationHistory.length;
    final totalOpened = _notificationHistory.where((n) => n.wasOpened).length;
    final openRate = totalSent > 0 ? totalOpened / totalSent : 0.0;
    
    final typeBreakdown = <NotificationType, int>{};
    for (final notification in _notificationHistory) {
      typeBreakdown[notification.notification.type] = (typeBreakdown[notification.notification.type] ?? 0) + 1;
    }
    
    final bestTimes = _calculateBestNotificationTimes();
    
    return NotificationAnalytics(
      totalSent: totalSent,
      totalOpened: totalOpened,
      openRate: openRate,
      typeBreakdown: typeBreakdown,
      bestTimes: bestTimes,
      averageResponseTime: _calculateAverageResponseTime(),
    );
  }

  /// Calculate optimal morning time based on user behavior
  Future<DateTime> _calculateOptimalMorningTime() async {
    // Analyze user's app usage patterns to determine best morning time
    // Default to 7:00 AM if no data available
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, 7, 0);
  }

  /// Calculate optimal evening time based on sunset and user behavior
  Future<DateTime> _calculateOptimalEveningTime() async {
    // This would integrate with prayer time service to get Maghrib time
    // Default to 6:00 PM if no data available
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, 18, 0);
  }

  /// Schedule a smart notification
  Future<void> _scheduleNotification(SmartNotification notification) async {
    _scheduledNotifications.add(notification);
    
    // Schedule with the system
    await _notifications.zonedSchedule(
      notification.id.hashCode,
      notification.title,
      notification.body,
      _convertToTZDateTime(notification.scheduledTime),
      _createNotificationDetails(notification),
      androidAllowWhileIdle: true,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      payload: notification.id,
    );
  }

  /// Initialize notifications
  Future<void> _initializeNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    
    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    final notificationId = response.payload;
    if (notificationId != null) {
      _recordNotificationInteraction(notificationId, true);
    }
  }

  /// Record notification interaction for analytics
  void _recordNotificationInteraction(String notificationId, bool wasOpened) {
    final notification = _scheduledNotifications.firstWhere(
      (n) => n.id == notificationId,
      orElse: () => SmartNotification(
        id: notificationId,
        title: '',
        body: '',
        scheduledTime: DateTime.now(),
        type: NotificationType.general,
        priority: NotificationPriority.low,
      ),
    );
    
    _notificationHistory.add(NotificationHistory(
      notification: notification,
      sentAt: DateTime.now(),
      wasOpened: wasOpened,
      openedAt: wasOpened ? DateTime.now() : null,
    ));
  }

  /// Create notification details based on notification type
  NotificationDetails _createNotificationDetails(SmartNotification notification) {
    final androidDetails = AndroidNotificationDetails(
      'athkar_${notification.type.name}',
      notification.type.displayName,
      channelDescription: notification.type.description,
      importance: _mapPriorityToImportance(notification.priority),
      priority: _mapPriorityToAndroidPriority(notification.priority),
      sound: notification.sound != null 
          ? RawResourceAndroidNotificationSound(notification.sound!.split('.').first)
          : null,
      enableVibration: true,
      playSound: true,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    return NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
  }

  /// Map priority to Android importance
  Importance _mapPriorityToImportance(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Importance.low;
      case NotificationPriority.medium:
        return Importance.defaultImportance;
      case NotificationPriority.high:
        return Importance.high;
    }
  }

  /// Map priority to Android priority
  Priority _mapPriorityToAndroidPriority(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Priority.low;
      case NotificationPriority.medium:
        return Priority.defaultPriority;
      case NotificationPriority.high:
        return Priority.high;
    }
  }

  /// Convert DateTime to TZDateTime (mock implementation)
  dynamic _convertToTZDateTime(DateTime dateTime) {
    // In a real implementation, this would use timezone package
    return dateTime;
  }

  /// Schedule intelligent reminders
  Future<void> _scheduleIntelligentReminders() async {
    // This would analyze user patterns and schedule optimal reminders
    debugPrint('Scheduling intelligent reminders...');
  }

  /// Reschedule notifications for a category
  Future<void> _rescheduleNotifications(String category) async {
    // Cancel existing notifications for this category
    // Reschedule based on new preferences
    debugPrint('Rescheduling notifications for category: $category');
  }

  /// Calculate best notification times based on user interaction
  List<TimeOfDay> _calculateBestNotificationTimes() {
    // Analyze when users are most likely to interact with notifications
    return [
      const TimeOfDay(hour: 7, minute: 0),
      const TimeOfDay(hour: 12, minute: 0),
      const TimeOfDay(hour: 18, minute: 0),
    ];
  }

  /// Calculate average response time to notifications
  Duration _calculateAverageResponseTime() {
    final responseTimes = _notificationHistory
        .where((n) => n.wasOpened && n.openedAt != null)
        .map((n) => n.openedAt!.difference(n.sentAt))
        .toList();
    
    if (responseTimes.isEmpty) return Duration.zero;
    
    final totalMilliseconds = responseTimes
        .map((d) => d.inMilliseconds)
        .reduce((a, b) => a + b);
    
    return Duration(milliseconds: totalMilliseconds ~/ responseTimes.length);
  }

  /// Load user preferences
  Future<void> _loadUserPreferences() async {
    // Load from persistent storage
    debugPrint('Loading notification preferences...');
  }

  /// Save user preferences
  Future<void> _saveUserPreferences() async {
    // Save to persistent storage
    debugPrint('Saving notification preferences...');
  }
}

/// Represents a smart notification
class SmartNotification {
  final String id;
  final String title;
  final String body;
  final DateTime scheduledTime;
  final NotificationType type;
  final NotificationPriority priority;
  final Duration? repeatInterval;
  final String? sound;
  final Map<String, dynamic>? metadata;

  SmartNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.scheduledTime,
    required this.type,
    required this.priority,
    this.repeatInterval,
    this.sound,
    this.metadata,
  });
}

/// Types of notifications
enum NotificationType {
  prayerTime,
  prayerReminder,
  athkarReminder,
  dhikrBreak,
  sleepDua,
  achievement,
  goalReminder,
  encouragement,
  general,
}

/// Notification priorities
enum NotificationPriority { low, medium, high }

/// Notification preferences
class NotificationPreference {
  final bool enabled;
  final List<TimeOfDay> allowedTimes;
  final List<int> allowedDays; // 1-7 for Monday-Sunday
  final bool soundEnabled;
  final bool vibrationEnabled;

  NotificationPreference({
    required this.enabled,
    required this.allowedTimes,
    required this.allowedDays,
    required this.soundEnabled,
    required this.vibrationEnabled,
  });

  static NotificationPreference defaultPreference() {
    return NotificationPreference(
      enabled: true,
      allowedTimes: [
        const TimeOfDay(hour: 7, minute: 0),
        const TimeOfDay(hour: 18, minute: 0),
      ],
      allowedDays: [1, 2, 3, 4, 5, 6, 7], // All days
      soundEnabled: true,
      vibrationEnabled: true,
    );
  }
}

/// Prayer time data
class PrayerTime {
  final String name;
  final String displayName;
  final DateTime time;

  PrayerTime({
    required this.name,
    required this.displayName,
    required this.time,
  });
}

/// User activity pattern
class UserActivityPattern {
  final TimeRange? workHours;
  final DateTime? sleepTime;
  final DateTime? wakeTime;
  final List<TimeOfDay> mostActiveHours;

  UserActivityPattern({
    this.workHours,
    this.sleepTime,
    this.wakeTime,
    required this.mostActiveHours,
  });
}

/// Time range
class TimeRange {
  final int start;
  final int end;

  TimeRange({required this.start, required this.end});
}

/// Spiritual progress data
class SpiritualProgress {
  final int currentStreak;
  final double recentActivityLevel;
  final List<Goal> nearCompletionGoals;

  SpiritualProgress({
    required this.currentStreak,
    required this.recentActivityLevel,
    required this.nearCompletionGoals,
  });
}

/// Goal data
class Goal {
  final String id;
  final String title;
  final int remainingCount;

  Goal({
    required this.id,
    required this.title,
    required this.remainingCount,
  });
}

/// Notification history
class NotificationHistory {
  final SmartNotification notification;
  final DateTime sentAt;
  final bool wasOpened;
  final DateTime? openedAt;

  NotificationHistory({
    required this.notification,
    required this.sentAt,
    required this.wasOpened,
    this.openedAt,
  });
}

/// Notification analytics
class NotificationAnalytics {
  final int totalSent;
  final int totalOpened;
  final double openRate;
  final Map<NotificationType, int> typeBreakdown;
  final List<TimeOfDay> bestTimes;
  final Duration averageResponseTime;

  NotificationAnalytics({
    required this.totalSent,
    required this.totalOpened,
    required this.openRate,
    required this.typeBreakdown,
    required this.bestTimes,
    required this.averageResponseTime,
  });
}

/// Extension for notification type display
extension NotificationTypeExtension on NotificationType {
  String get displayName {
    switch (this) {
      case NotificationType.prayerTime:
        return 'أوقات الصلاة';
      case NotificationType.prayerReminder:
        return 'تذكير الصلاة';
      case NotificationType.athkarReminder:
        return 'تذكير الأذكار';
      case NotificationType.dhikrBreak:
        return 'استراحة ذكر';
      case NotificationType.sleepDua:
        return 'دعاء النوم';
      case NotificationType.achievement:
        return 'إنجاز';
      case NotificationType.goalReminder:
        return 'تذكير الهدف';
      case NotificationType.encouragement:
        return 'تشجيع';
      case NotificationType.general:
        return 'عام';
    }
  }

  String get description {
    switch (this) {
      case NotificationType.prayerTime:
        return 'إشعارات أوقات الصلاة والأذان';
      case NotificationType.prayerReminder:
        return 'تذكيرات ما قبل وبعد الصلاة';
      case NotificationType.athkarReminder:
        return 'تذكيرات الأذكار اليومية';
      case NotificationType.dhikrBreak:
        return 'استراحات الذكر أثناء العمل';
      case NotificationType.sleepDua:
        return 'أدعية النوم والاستيقاظ';
      case NotificationType.achievement:
        return 'إشعارات الإنجازات والمكافآت';
      case NotificationType.goalReminder:
        return 'تذكيرات الأهداف الروحية';
      case NotificationType.encouragement:
        return 'رسائل التشجيع والتحفيز';
      case NotificationType.general:
        return 'إشعارات عامة';
    }
  }
}
