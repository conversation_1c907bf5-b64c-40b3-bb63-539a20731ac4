import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  // App Title
  String get appTitle => locale.languageCode == 'ar' ? 'أذكار المسلم' : 'Muslim Athkar';
  String get appSubtitle => locale.languageCode == 'ar' ? 'تطبيق شامل للأذكار والأدعية' : 'Comprehensive Athkar and Dua App';

  // Navigation
  String get home => locale.languageCode == 'ar' ? 'الرئيسية' : 'Home';
  String get morningAthkar => locale.languageCode == 'ar' ? 'أذكار الصباح' : 'Morning Athkar';
  String get eveningAthkar => locale.languageCode == 'ar' ? 'أذكار المساء' : 'Evening Athkar';
  String get generalAthkar => locale.languageCode == 'ar' ? 'أذكار عامة' : 'General Athkar';
  String get tasbeeh => locale.languageCode == 'ar' ? 'التسبيح' : 'Tasbeeh';
  String get duas => locale.languageCode == 'ar' ? 'الأدعية' : 'Duas';
  String get quran => locale.languageCode == 'ar' ? 'القرآن الكريم' : 'Holy Quran';
  String get prayerTimes => locale.languageCode == 'ar' ? 'أوقات الصلاة' : 'Prayer Times';
  String get qiblaDirection => locale.languageCode == 'ar' ? 'اتجاه القبلة' : 'Qibla Direction';
  String get settings => locale.languageCode == 'ar' ? 'الإعدادات' : 'Settings';
  String get profile => locale.languageCode == 'ar' ? 'الملف الشخصي' : 'Profile';

  // Sections
  String get athkarAndDuas => locale.languageCode == 'ar' ? 'الأذكار والأدعية' : 'Athkar & Duas';
  String get advancedFeatures => locale.languageCode == 'ar' ? 'الميزات المتقدمة' : 'Advanced Features';
  String get settingsSection => locale.languageCode == 'ar' ? 'الإعدادات' : 'Settings';

  // Advanced Features
  String get floatingWindows => locale.languageCode == 'ar' ? 'النوافذ العائمة' : 'Floating Windows';
  String get backup => locale.languageCode == 'ar' ? 'النسخ الاحتياطي' : 'Backup';
  String get featureTesting => locale.languageCode == 'ar' ? 'اختبار الميزات' : 'Feature Testing';
  String get notifications => locale.languageCode == 'ar' ? 'الإشعارات' : 'Notifications';
  String get audioSettings => locale.languageCode == 'ar' ? 'إعدادات الصوت' : 'Audio Settings';

  // Authentication
  String get signIn => locale.languageCode == 'ar' ? 'تسجيل الدخول' : 'Sign In';
  String get signUp => locale.languageCode == 'ar' ? 'إنشاء حساب' : 'Sign Up';
  String get email => locale.languageCode == 'ar' ? 'البريد الإلكتروني' : 'Email';
  String get password => locale.languageCode == 'ar' ? 'كلمة المرور' : 'Password';
  String get forgotPassword => locale.languageCode == 'ar' ? 'نسيت كلمة المرور؟' : 'Forgot Password?';
  String get createAccount => locale.languageCode == 'ar' ? 'إنشاء حساب جديد' : 'Create New Account';
  String get alreadyHaveAccount => locale.languageCode == 'ar' ? 'لديك حساب بالفعل؟' : 'Already have an account?';

  // Common Actions
  String get save => locale.languageCode == 'ar' ? 'حفظ' : 'Save';
  String get cancel => locale.languageCode == 'ar' ? 'إلغاء' : 'Cancel';
  String get delete => locale.languageCode == 'ar' ? 'حذف' : 'Delete';
  String get edit => locale.languageCode == 'ar' ? 'تعديل' : 'Edit';
  String get add => locale.languageCode == 'ar' ? 'إضافة' : 'Add';
  String get ok => locale.languageCode == 'ar' ? 'موافق' : 'OK';
  String get yes => locale.languageCode == 'ar' ? 'نعم' : 'Yes';
  String get no => locale.languageCode == 'ar' ? 'لا' : 'No';
  String get close => locale.languageCode == 'ar' ? 'إغلاق' : 'Close';
  String get back => locale.languageCode == 'ar' ? 'رجوع' : 'Back';
  String get next => locale.languageCode == 'ar' ? 'التالي' : 'Next';
  String get previous => locale.languageCode == 'ar' ? 'السابق' : 'Previous';

  // Counter & Progress
  String get counter => locale.languageCode == 'ar' ? 'العداد' : 'Counter';
  String get target => locale.languageCode == 'ar' ? 'الهدف' : 'Target';
  String get current => locale.languageCode == 'ar' ? 'الحالي' : 'Current';
  String get completed => locale.languageCode == 'ar' ? 'مكتمل' : 'Completed';
  String get progress => locale.languageCode == 'ar' ? 'التقدم' : 'Progress';
  String get reset => locale.languageCode == 'ar' ? 'إعادة تعيين' : 'Reset';

  // Prayer Times
  String get fajr => locale.languageCode == 'ar' ? 'الفجر' : 'Fajr';
  String get sunrise => locale.languageCode == 'ar' ? 'الشروق' : 'Sunrise';
  String get dhuhr => locale.languageCode == 'ar' ? 'الظهر' : 'Dhuhr';
  String get asr => locale.languageCode == 'ar' ? 'العصر' : 'Asr';
  String get maghrib => locale.languageCode == 'ar' ? 'المغرب' : 'Maghrib';
  String get isha => locale.languageCode == 'ar' ? 'العشاء' : 'Isha';

  // Settings Categories
  String get generalSettings => locale.languageCode == 'ar' ? 'الإعدادات العامة' : 'General Settings';
  String get notificationSettings => locale.languageCode == 'ar' ? 'إعدادات الإشعارات' : 'Notification Settings';
  String get languageSettings => locale.languageCode == 'ar' ? 'إعدادات اللغة' : 'Language Settings';
  String get themeSettings => locale.languageCode == 'ar' ? 'إعدادات المظهر' : 'Theme Settings';
  String get permissionSettings => locale.languageCode == 'ar' ? 'إعدادات الصلاحيات' : 'Permission Settings';

  // Language
  String get language => locale.languageCode == 'ar' ? 'اللغة' : 'Language';
  String get arabic => locale.languageCode == 'ar' ? 'العربية' : 'Arabic';
  String get english => locale.languageCode == 'ar' ? 'الإنجليزية' : 'English';
  String get rtlSupport => locale.languageCode == 'ar' ? 'دعم الكتابة من اليمين لليسار' : 'RTL Support';

  // Permissions
  String get permissions => locale.languageCode == 'ar' ? 'الصلاحيات' : 'Permissions';
  String get notificationPermission => locale.languageCode == 'ar' ? 'إذن الإشعارات' : 'Notification Permission';
  String get locationPermission => locale.languageCode == 'ar' ? 'إذن الموقع' : 'Location Permission';
  String get storagePermission => locale.languageCode == 'ar' ? 'إذن التخزين' : 'Storage Permission';
  String get overlayPermission => locale.languageCode == 'ar' ? 'إذن النوافذ العائمة' : 'Overlay Permission';

  // Error Messages
  String get error => locale.languageCode == 'ar' ? 'خطأ' : 'Error';
  String get success => locale.languageCode == 'ar' ? 'نجح' : 'Success';
  String get warning => locale.languageCode == 'ar' ? 'تحذير' : 'Warning';
  String get info => locale.languageCode == 'ar' ? 'معلومات' : 'Info';
  String get noInternetConnection => locale.languageCode == 'ar' ? 'لا يوجد اتصال بالإنترنت' : 'No Internet Connection';
  String get somethingWentWrong => locale.languageCode == 'ar' ? 'حدث خطأ ما' : 'Something went wrong';

  // Quran
  String get surah => locale.languageCode == 'ar' ? 'السورة' : 'Surah';
  String get ayah => locale.languageCode == 'ar' ? 'الآية' : 'Ayah';
  String get juz => locale.languageCode == 'ar' ? 'الجزء' : 'Juz';
  String get page => locale.languageCode == 'ar' ? 'الصفحة' : 'Page';
  String get bookmark => locale.languageCode == 'ar' ? 'إشارة مرجعية' : 'Bookmark';
  String get bookmarks => locale.languageCode == 'ar' ? 'الإشارات المرجعية' : 'Bookmarks';
  String get search => locale.languageCode == 'ar' ? 'بحث' : 'Search';
  String get khatma => locale.languageCode == 'ar' ? 'ختمة' : 'Khatma';
  String get memorization => locale.languageCode == 'ar' ? 'الحفظ' : 'Memorization';

  // Time
  String get today => locale.languageCode == 'ar' ? 'اليوم' : 'Today';
  String get yesterday => locale.languageCode == 'ar' ? 'أمس' : 'Yesterday';
  String get tomorrow => locale.languageCode == 'ar' ? 'غداً' : 'Tomorrow';
  String get morning => locale.languageCode == 'ar' ? 'الصباح' : 'Morning';
  String get evening => locale.languageCode == 'ar' ? 'المساء' : 'Evening';

  // Status Messages
  String get loading => locale.languageCode == 'ar' ? 'جاري التحميل...' : 'Loading...';
  String get noData => locale.languageCode == 'ar' ? 'لا توجد بيانات' : 'No Data';
  String get offline => locale.languageCode == 'ar' ? 'غير متصل' : 'Offline';
  String get online => locale.languageCode == 'ar' ? 'متصل' : 'Online';
  String get syncing => locale.languageCode == 'ar' ? 'جاري المزامنة...' : 'Syncing...';
  String get syncComplete => locale.languageCode == 'ar' ? 'تمت المزامنة' : 'Sync Complete';

  // Validation Messages
  String get fieldRequired => locale.languageCode == 'ar' ? 'هذا الحقل مطلوب' : 'This field is required';
  String get invalidEmail => locale.languageCode == 'ar' ? 'بريد إلكتروني غير صحيح' : 'Invalid email';
  String get passwordTooShort => locale.languageCode == 'ar' ? 'كلمة المرور قصيرة جداً' : 'Password too short';
  String get passwordsDoNotMatch => locale.languageCode == 'ar' ? 'كلمات المرور غير متطابقة' : 'Passwords do not match';

  // Prebuilt Content
  String get prebuiltContent => locale.languageCode == 'ar' ? 'المحتوى المبني مسبقاً' : 'Prebuilt Content';
  String get addToRoutine => locale.languageCode == 'ar' ? 'إضافة للروتين' : 'Add to Routine';
  String get runNow => locale.languageCode == 'ar' ? 'تشغيل الآن' : 'Run Now';
  String get addPrebuilt => locale.languageCode == 'ar' ? 'إضافة محتوى مبني مسبقاً' : 'Add Prebuilt Content';

  // Testing
  String get testNotifications => locale.languageCode == 'ar' ? 'اختبار الإشعارات' : 'Test Notifications';
  String get testFloatingWindows => locale.languageCode == 'ar' ? 'اختبار النوافذ العائمة' : 'Test Floating Windows';
  String get testPrayerTimes => locale.languageCode == 'ar' ? 'اختبار أوقات الصلاة' : 'Test Prayer Times';
  String get testReminders => locale.languageCode == 'ar' ? 'اختبار التذكيرات' : 'Test Reminders';
  String get runTest => locale.languageCode == 'ar' ? 'تشغيل الاختبار' : 'Run Test';
  String get testResults => locale.languageCode == 'ar' ? 'نتائج الاختبار' : 'Test Results';
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['ar', 'en'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
