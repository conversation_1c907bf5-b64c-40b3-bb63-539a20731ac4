import 'package:json_annotation/json_annotation.dart';

part 'prebuilt_content_models.g.dart';

enum ContentCategory {
  @JsonValue('athkar')
  athkar,
  @JsonValue('dua')
  dua,
  @JsonValue('tasbeeh')
  tasbeeh,
  @JsonValue('routine')
  routine,
}

enum DifficultyLevel {
  @JsonValue('beginner')
  beginner,
  @JsonValue('intermediate')
  intermediate,
  @JsonValue('advanced')
  advanced,
}

@JsonSerializable()
class PrebuiltContent {
  final String id;
  final String title;
  final String arabicText;
  final String? transliteration;
  final String? translation;
  final String? description;
  final ContentCategory category;
  final String? subcategory;
  final DifficultyLevel difficulty;
  final int? targetCount;
  final String? source; // Quran, Hadith, etc.
  final String? reference; // Specific verse or hadith reference
  final List<String>? tags;
  final String? audioUrl;
  final String? colorHex;
  final bool isPopular;
  final bool isFavorite;
  final int? usageCount;
  final List<PrebuiltStep>? steps; // For routines
  final DateTime createdAt;
  final DateTime updatedAt;

  PrebuiltContent({
    required this.id,
    required this.title,
    required this.arabicText,
    this.transliteration,
    this.translation,
    this.description,
    required this.category,
    this.subcategory,
    this.difficulty = DifficultyLevel.beginner,
    this.targetCount,
    this.source,
    this.reference,
    this.tags,
    this.audioUrl,
    this.colorHex,
    this.isPopular = false,
    this.isFavorite = false,
    this.usageCount,
    this.steps,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PrebuiltContent.fromJson(Map<String, dynamic> json) =>
      _$PrebuiltContentFromJson(json);
  Map<String, dynamic> toJson() => _$PrebuiltContentToJson(this);

  PrebuiltContent copyWith({
    String? id,
    String? title,
    String? arabicText,
    String? transliteration,
    String? translation,
    String? description,
    ContentCategory? category,
    String? subcategory,
    DifficultyLevel? difficulty,
    int? targetCount,
    String? source,
    String? reference,
    List<String>? tags,
    String? audioUrl,
    String? colorHex,
    bool? isPopular,
    bool? isFavorite,
    int? usageCount,
    List<PrebuiltStep>? steps,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PrebuiltContent(
      id: id ?? this.id,
      title: title ?? this.title,
      arabicText: arabicText ?? this.arabicText,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
      description: description ?? this.description,
      category: category ?? this.category,
      subcategory: subcategory ?? this.subcategory,
      difficulty: difficulty ?? this.difficulty,
      targetCount: targetCount ?? this.targetCount,
      source: source ?? this.source,
      reference: reference ?? this.reference,
      tags: tags ?? this.tags,
      audioUrl: audioUrl ?? this.audioUrl,
      colorHex: colorHex ?? this.colorHex,
      isPopular: isPopular ?? this.isPopular,
      isFavorite: isFavorite ?? this.isFavorite,
      usageCount: usageCount ?? this.usageCount,
      steps: steps ?? this.steps,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class PrebuiltStep {
  final int stepOrder;
  final String arabicText;
  final String? transliteration;
  final String? translation;
  final int targetCount;
  final String? colorHex;

  PrebuiltStep({
    required this.stepOrder,
    required this.arabicText,
    this.transliteration,
    this.translation,
    this.targetCount = 1,
    this.colorHex,
  });

  factory PrebuiltStep.fromJson(Map<String, dynamic> json) =>
      _$PrebuiltStepFromJson(json);
  Map<String, dynamic> toJson() => _$PrebuiltStepToJson(this);
}

// Content categories with their subcategories
class ContentCategories {
  static const Map<ContentCategory, List<String>> subcategories = {
    ContentCategory.athkar: [
      'morning',
      'evening',
      'prayer',
      'sleeping',
      'travel',
      'eating',
      'general',
    ],
    ContentCategory.dua: [
      'daily',
      'prayer',
      'forgiveness',
      'protection',
      'guidance',
      'health',
      'family',
      'travel',
      'special_occasions',
    ],
    ContentCategory.tasbeeh: [
      'subhan_allah',
      'alhamdulillah',
      'allahu_akbar',
      'la_ilaha_illa_allah',
      'istighfar',
      'salawat',
      'general',
    ],
    ContentCategory.routine: [
      'morning_routine',
      'evening_routine',
      'prayer_routine',
      'weekly_routine',
      'ramadan_routine',
      'custom_routine',
    ],
  };

  static List<String> getSubcategories(ContentCategory category) {
    return subcategories[category] ?? [];
  }

  static String getCategoryDisplayName(ContentCategory category) {
    switch (category) {
      case ContentCategory.athkar:
        return 'Athkar';
      case ContentCategory.dua:
        return 'Duas';
      case ContentCategory.tasbeeh:
        return 'Tasbeeh';
      case ContentCategory.routine:
        return 'Routines';
    }
  }

  static String getSubcategoryDisplayName(String subcategory) {
    final displayNames = {
      'morning': 'Morning',
      'evening': 'Evening',
      'prayer': 'Prayer',
      'sleeping': 'Before Sleep',
      'travel': 'Travel',
      'eating': 'Eating',
      'general': 'General',
      'daily': 'Daily',
      'forgiveness': 'Forgiveness',
      'protection': 'Protection',
      'guidance': 'Guidance',
      'health': 'Health',
      'family': 'Family',
      'special_occasions': 'Special Occasions',
      'subhan_allah': 'Subhan Allah',
      'alhamdulillah': 'Alhamdulillah',
      'allahu_akbar': 'Allahu Akbar',
      'la_ilaha_illa_allah': 'La ilaha illa Allah',
      'istighfar': 'Istighfar',
      'salawat': 'Salawat',
      'morning_routine': 'Morning Routine',
      'evening_routine': 'Evening Routine',
      'prayer_routine': 'Prayer Routine',
      'weekly_routine': 'Weekly Routine',
      'ramadan_routine': 'Ramadan Routine',
      'custom_routine': 'Custom Routine',
    };
    return displayNames[subcategory] ?? subcategory;
  }

  static String getDifficultyDisplayName(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return 'Beginner';
      case DifficultyLevel.intermediate:
        return 'Intermediate';
      case DifficultyLevel.advanced:
        return 'Advanced';
    }
  }
}

// Common tags for content
class ContentTags {
  static const List<String> commonTags = [
    'daily',
    'morning',
    'evening',
    'prayer',
    'protection',
    'forgiveness',
    'gratitude',
    'guidance',
    'peace',
    'blessing',
    'remembrance',
    'worship',
    'meditation',
    'spiritual',
    'healing',
    'strength',
    'patience',
    'wisdom',
    'mercy',
    'love',
  ];
}
