import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/view_configuration_provider.dart';
import '../models/view_models.dart';
import '../services/language_service.dart';
import '../theme/app_theme.dart';
import '../widgets/customization/color_picker_widget.dart';
import '../widgets/customization/font_selector_widget.dart';
import '../widgets/customization/layout_options_widget.dart';

class ViewCustomizationScreen extends StatefulWidget {
  const ViewCustomizationScreen({super.key});

  @override
  State<ViewCustomizationScreen> createState() => _ViewCustomizationScreenState();
}

class _ViewCustomizationScreenState extends State<ViewCustomizationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    final viewProvider = Provider.of<ViewConfigurationProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          languageService.isArabic ? 'تخصيص العرض' : 'View Customization',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: viewProvider.primaryColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _showResetDialog(context, viewProvider, languageService),
            tooltip: languageService.isArabic ? 'إعادة تعيين' : 'Reset',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              text: languageService.isArabic ? 'العرض' : 'Display',
              icon: const Icon(Icons.view_module, size: 20),
            ),
            Tab(
              text: languageService.isArabic ? 'الألوان' : 'Colors',
              icon: const Icon(Icons.palette, size: 20),
            ),
            Tab(
              text: languageService.isArabic ? 'الخطوط' : 'Fonts',
              icon: const Icon(Icons.text_fields, size: 20),
            ),
            Tab(
              text: languageService.isArabic ? 'التخطيط' : 'Layout',
              icon: const Icon(Icons.dashboard, size: 20),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Display Settings Tab
          _buildDisplaySettingsTab(viewProvider, languageService),
          
          // Color Settings Tab
          _buildColorSettingsTab(viewProvider, languageService),
          
          // Font Settings Tab
          _buildFontSettingsTab(viewProvider, languageService),
          
          // Layout Settings Tab
          _buildLayoutSettingsTab(viewProvider, languageService),
        ],
      ),
    );
  }

  Widget _buildDisplaySettingsTab(ViewConfigurationProvider viewProvider, LanguageService languageService) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // View Mode Selection
          _buildSectionTitle(
            languageService.isArabic ? 'نمط العرض' : 'View Mode',
            Icons.view_module,
          ),
          const SizedBox(height: 12),
          _buildViewModeSelector(viewProvider, languageService),
          
          const SizedBox(height: 24),
          
          // Layout Density
          _buildSectionTitle(
            languageService.isArabic ? 'كثافة التخطيط' : 'Layout Density',
            Icons.density_medium,
          ),
          const SizedBox(height: 12),
          _buildLayoutDensitySelector(viewProvider, languageService),
          
          const SizedBox(height: 24),
          
          // Content Display Options
          _buildSectionTitle(
            languageService.isArabic ? 'خيارات العرض' : 'Display Options',
            Icons.visibility,
          ),
          const SizedBox(height: 12),
          _buildDisplayOptions(viewProvider, languageService),
          
          const SizedBox(height: 24),
          
          // Animation Settings
          _buildSectionTitle(
            languageService.isArabic ? 'إعدادات الحركة' : 'Animation Settings',
            Icons.animation,
          ),
          const SizedBox(height: 12),
          _buildAnimationSettings(viewProvider, languageService),
        ],
      ),
    );
  }

  Widget _buildColorSettingsTab(ViewConfigurationProvider viewProvider, LanguageService languageService) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Primary Color
          _buildSectionTitle(
            languageService.isArabic ? 'اللون الأساسي' : 'Primary Color',
            Icons.color_lens,
          ),
          const SizedBox(height: 12),
          ColorPickerWidget(
            currentColor: viewProvider.primaryColor,
            onColorChanged: viewProvider.updatePrimaryColor,
            title: languageService.isArabic ? 'اختر اللون الأساسي' : 'Choose Primary Color',
          ),
          
          const SizedBox(height: 24),
          
          // Secondary Color
          _buildSectionTitle(
            languageService.isArabic ? 'اللون الثانوي' : 'Secondary Color',
            Icons.color_lens_outlined,
          ),
          const SizedBox(height: 12),
          ColorPickerWidget(
            currentColor: viewProvider.secondaryColor,
            onColorChanged: viewProvider.updateSecondaryColor,
            title: languageService.isArabic ? 'اختر اللون الثانوي' : 'Choose Secondary Color',
          ),
          
          const SizedBox(height: 24),
          
          // Background Color
          _buildSectionTitle(
            languageService.isArabic ? 'لون الخلفية' : 'Background Color',
            Icons.format_color_fill,
          ),
          const SizedBox(height: 12),
          ColorPickerWidget(
            currentColor: viewProvider.backgroundColor,
            onColorChanged: viewProvider.updateBackgroundColor,
            title: languageService.isArabic ? 'اختر لون الخلفية' : 'Choose Background Color',
          ),
          
          const SizedBox(height: 24),
          
          // Text Color
          _buildSectionTitle(
            languageService.isArabic ? 'لون النص' : 'Text Color',
            Icons.text_format,
          ),
          const SizedBox(height: 12),
          ColorPickerWidget(
            currentColor: viewProvider.textColor,
            onColorChanged: viewProvider.updateTextColor,
            title: languageService.isArabic ? 'اختر لون النص' : 'Choose Text Color',
          ),
        ],
      ),
    );
  }

  Widget _buildFontSettingsTab(ViewConfigurationProvider viewProvider, LanguageService languageService) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Font Family
          _buildSectionTitle(
            languageService.isArabic ? 'نوع الخط' : 'Font Family',
            Icons.font_download,
          ),
          const SizedBox(height: 12),
          FontSelectorWidget(
            currentFont: viewProvider.fontFamily,
            onFontChanged: viewProvider.updateFontFamily,
          ),
          
          const SizedBox(height: 24),
          
          // Font Size
          _buildSectionTitle(
            languageService.isArabic ? 'حجم الخط' : 'Font Size',
            Icons.format_size,
          ),
          const SizedBox(height: 12),
          _buildFontSizeSlider(viewProvider, languageService),
          
          const SizedBox(height: 24),
          
          // Quran-specific font settings
          _buildSectionTitle(
            languageService.isArabic ? 'إعدادات خط القرآن' : 'Quran Font Settings',
            Icons.menu_book,
          ),
          const SizedBox(height: 12),
          _buildQuranFontSettings(viewProvider, languageService),
        ],
      ),
    );
  }

  Widget _buildLayoutSettingsTab(ViewConfigurationProvider viewProvider, LanguageService languageService) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LayoutOptionsWidget(
            configuration: viewProvider.configuration,
            onConfigurationChanged: viewProvider.updateConfiguration,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: AppTheme.primaryGreen,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
      ],
    );
  }

  Widget _buildViewModeSelector(ViewConfigurationProvider viewProvider, LanguageService languageService) {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: ViewMode.values.map((mode) {
        final isSelected = viewProvider.viewMode == mode;
        return GestureDetector(
          onTap: () => viewProvider.updateViewMode(mode),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected ? AppTheme.primaryGreen : Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? AppTheme.primaryGreen : Colors.grey[400]!,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  mode.icon,
                  color: isSelected ? Colors.white : Colors.grey[700],
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  mode.displayName,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey[700],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildLayoutDensitySelector(ViewConfigurationProvider viewProvider, LanguageService languageService) {
    return Row(
      children: LayoutDensity.values.map((density) {
        final isSelected = viewProvider.layoutDensity == density;
        return Expanded(
          child: GestureDetector(
            onTap: () => viewProvider.updateLayoutDensity(density),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.primaryGreen : Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _getDensityName(density, languageService.isArabic),
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDisplayOptions(ViewConfigurationProvider viewProvider, LanguageService languageService) {
    return Column(
      children: [
        SwitchListTile(
          title: Text(languageService.isArabic ? 'عرض النص العربي' : 'Show Arabic Text'),
          value: viewProvider.showArabic,
          onChanged: (_) => viewProvider.toggleArabicDisplay(),
          activeColor: AppTheme.primaryGreen,
        ),
        SwitchListTile(
          title: Text(languageService.isArabic ? 'عرض الترجمة' : 'Show Translation'),
          value: viewProvider.showTranslation,
          onChanged: (_) => viewProvider.toggleTranslationDisplay(),
          activeColor: AppTheme.primaryGreen,
        ),
      ],
    );
  }

  Widget _buildAnimationSettings(ViewConfigurationProvider viewProvider, LanguageService languageService) {
    return Column(
      children: [
        SwitchListTile(
          title: Text(languageService.isArabic ? 'تفعيل الحركات' : 'Enable Animations'),
          value: viewProvider.enableAnimations,
          onChanged: (_) => viewProvider.toggleAnimations(),
          activeColor: AppTheme.primaryGreen,
        ),
        if (viewProvider.enableAnimations) ...[
          const SizedBox(height: 16),
          Text(
            languageService.isArabic ? 'سرعة الحركة' : 'Animation Speed',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Slider(
            value: viewProvider.configuration.animationSpeed,
            min: 0.5,
            max: 2.0,
            divisions: 6,
            label: '${viewProvider.configuration.animationSpeed.toStringAsFixed(1)}x',
            onChanged: viewProvider.updateAnimationSpeed,
            activeColor: AppTheme.primaryGreen,
          ),
        ],
      ],
    );
  }

  Widget _buildFontSizeSlider(ViewConfigurationProvider viewProvider, LanguageService languageService) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              languageService.isArabic ? 'صغير' : 'Small',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              '${viewProvider.fontSize.toInt()}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryGreen,
              ),
            ),
            Text(
              languageService.isArabic ? 'كبير' : 'Large',
              style: const TextStyle(fontSize: 18),
            ),
          ],
        ),
        Slider(
          value: viewProvider.fontSize,
          min: 12.0,
          max: 24.0,
          divisions: 12,
          onChanged: viewProvider.updateFontSize,
          activeColor: AppTheme.primaryGreen,
        ),
        // Preview text
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            languageService.isArabic 
                ? 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ'
                : 'In the name of Allah, the Most Gracious, the Most Merciful',
            style: viewProvider.getTextStyle(),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildQuranFontSettings(ViewConfigurationProvider viewProvider, LanguageService languageService) {
    return Column(
      children: [
        // Arabic font size for Quran
        Text(
          languageService.isArabic ? 'حجم الخط العربي' : 'Arabic Font Size',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        Slider(
          value: viewProvider.quranConfiguration.arabicFontSize,
          min: 14.0,
          max: 28.0,
          divisions: 14,
          label: '${viewProvider.quranConfiguration.arabicFontSize.toInt()}',
          onChanged: viewProvider.updateQuranArabicFontSize,
          activeColor: AppTheme.primaryGreen,
        ),
        
        const SizedBox(height: 16),
        
        // Translation font size for Quran
        Text(
          languageService.isArabic ? 'حجم خط الترجمة' : 'Translation Font Size',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        Slider(
          value: viewProvider.quranConfiguration.translationFontSize,
          min: 10.0,
          max: 20.0,
          divisions: 10,
          label: '${viewProvider.quranConfiguration.translationFontSize.toInt()}',
          onChanged: viewProvider.updateQuranTranslationFontSize,
          activeColor: AppTheme.primaryGreen,
        ),
      ],
    );
  }

  String _getDensityName(LayoutDensity density, bool isArabic) {
    switch (density) {
      case LayoutDensity.comfortable:
        return isArabic ? 'مريح' : 'Comfortable';
      case LayoutDensity.compact:
        return isArabic ? 'مضغوط' : 'Compact';
      case LayoutDensity.spacious:
        return isArabic ? 'واسع' : 'Spacious';
    }
  }

  void _showResetDialog(BuildContext context, ViewConfigurationProvider viewProvider, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'إعادة تعيين الإعدادات' : 'Reset Settings'),
        content: Text(
          languageService.isArabic 
              ? 'هل تريد إعادة تعيين جميع إعدادات العرض إلى القيم الافتراضية؟'
              : 'Do you want to reset all view settings to default values?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              viewProvider.resetToDefaults();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    languageService.isArabic 
                        ? 'تم إعادة تعيين الإعدادات بنجاح'
                        : 'Settings reset successfully',
                  ),
                  backgroundColor: AppTheme.primaryGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              foregroundColor: Colors.white,
            ),
            child: Text(languageService.isArabic ? 'إعادة تعيين' : 'Reset'),
          ),
        ],
      ),
    );
  }
}
