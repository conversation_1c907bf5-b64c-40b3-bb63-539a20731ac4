import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/app_theme.dart';
import '../services/notification_service.dart';
import '../services/audio_service.dart';
// import '../widgets/time_picker_widget.dart'; // Not needed

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  bool _notificationsEnabled = true;
  bool _morningAthkarEnabled = true;
  bool _eveningAthkarEnabled = true;
  bool _prayerTimesEnabled = true;
  bool _dhikrRemindersEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  
  TimeOfDay _morningTime = const TimeOfDay(hour: 7, minute: 0);
  TimeOfDay _eveningTime = const TimeOfDay(hour: 18, minute: 0);
  
  String _selectedSoundTheme = 'default';
  String? _customNotificationSound;
  
  List<String> _customReminderTimes = [];
  
  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      setState(() {
        _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
        _morningAthkarEnabled = prefs.getBool('morning_athkar_enabled') ?? true;
        _eveningAthkarEnabled = prefs.getBool('evening_athkar_enabled') ?? true;
        _prayerTimesEnabled = prefs.getBool('prayer_times_enabled') ?? true;
        _dhikrRemindersEnabled = prefs.getBool('dhikr_reminders_enabled') ?? true;
        _soundEnabled = prefs.getBool('sound_enabled') ?? true;
        _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
        _selectedSoundTheme = prefs.getString('sound_theme') ?? 'default';
        _customNotificationSound = prefs.getString('custom_notification_sound');
        
        // Load times
        final morningTimeString = prefs.getString('morning_athkar_time') ?? '07:00';
        final eveningTimeString = prefs.getString('evening_athkar_time') ?? '18:00';
        
        final morningParts = morningTimeString.split(':');
        final eveningParts = eveningTimeString.split(':');
        
        _morningTime = TimeOfDay(
          hour: int.parse(morningParts[0]),
          minute: int.parse(morningParts[1]),
        );
        
        _eveningTime = TimeOfDay(
          hour: int.parse(eveningParts[0]),
          minute: int.parse(eveningParts[1]),
        );
        
        // Load custom reminder times
        _customReminderTimes = prefs.getStringList('custom_reminder_times') ?? [];
      });
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setBool('notifications_enabled', _notificationsEnabled);
      await prefs.setBool('morning_athkar_enabled', _morningAthkarEnabled);
      await prefs.setBool('evening_athkar_enabled', _eveningAthkarEnabled);
      await prefs.setBool('prayer_times_enabled', _prayerTimesEnabled);
      await prefs.setBool('dhikr_reminders_enabled', _dhikrRemindersEnabled);
      await prefs.setBool('sound_enabled', _soundEnabled);
      await prefs.setBool('vibration_enabled', _vibrationEnabled);
      await prefs.setString('sound_theme', _selectedSoundTheme);
      
      if (_customNotificationSound != null) {
        await prefs.setString('custom_notification_sound', _customNotificationSound!);
      }
      
      // Save times
      await prefs.setString('morning_athkar_time', 
          '${_morningTime.hour.toString().padLeft(2, '0')}:${_morningTime.minute.toString().padLeft(2, '0')}');
      await prefs.setString('evening_athkar_time', 
          '${_eveningTime.hour.toString().padLeft(2, '0')}:${_eveningTime.minute.toString().padLeft(2, '0')}');
      
      await prefs.setStringList('custom_reminder_times', _customReminderTimes);
      
      // Apply settings
      await AudioService.setSoundEnabled(_soundEnabled);
      await AudioService.setSoundTheme(_selectedSoundTheme);
      
      if (_notificationsEnabled) {
        await NotificationService.scheduleDailyAthkarReminders();
      } else {
        await NotificationService.cancelAllNotifications();
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الإعدادات بنجاح'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error saving notification settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خطأ في حفظ الإعدادات'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectTime(BuildContext context, bool isMorning) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isMorning ? _morningTime : _eveningTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppTheme.primaryGreen,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isMorning) {
          _morningTime = picked;
        } else {
          _eveningTime = picked;
        }
      });
    }
  }

  Future<void> _addCustomReminderTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppTheme.primaryGreen,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      final timeString = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      if (!_customReminderTimes.contains(timeString)) {
        setState(() {
          _customReminderTimes.add(timeString);
        });
      }
    }
  }

  void _removeCustomReminderTime(String time) {
    setState(() {
      _customReminderTimes.remove(time);
    });
  }

  Future<void> _uploadCustomSound() async {
    final soundPath = await AudioService.uploadCustomSound();
    if (soundPath != null) {
      setState(() {
        _customNotificationSound = soundPath;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم رفع الصوت المخصص بنجاح'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الإشعارات'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Main notification toggle
          Card(
            child: SwitchListTile(
              title: const Text('تفعيل الإشعارات'),
              subtitle: const Text('تفعيل أو إلغاء جميع الإشعارات'),
              value: _notificationsEnabled,
              activeColor: AppTheme.primaryGreen,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
              },
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Athkar reminders section
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text(
                    'تذكيرات الأذكار',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SwitchListTile(
                  title: const Text('أذكار الصباح'),
                  subtitle: Text('الوقت: ${_morningTime.format(context)}'),
                  value: _morningAthkarEnabled && _notificationsEnabled,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: _notificationsEnabled ? (value) {
                    setState(() {
                      _morningAthkarEnabled = value;
                    });
                  } : null,
                ),
                ListTile(
                  title: const Text('تغيير وقت أذكار الصباح'),
                  trailing: const Icon(Icons.access_time),
                  enabled: _morningAthkarEnabled && _notificationsEnabled,
                  onTap: () => _selectTime(context, true),
                ),
                const Divider(),
                SwitchListTile(
                  title: const Text('أذكار المساء'),
                  subtitle: Text('الوقت: ${_eveningTime.format(context)}'),
                  value: _eveningAthkarEnabled && _notificationsEnabled,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: _notificationsEnabled ? (value) {
                    setState(() {
                      _eveningAthkarEnabled = value;
                    });
                  } : null,
                ),
                ListTile(
                  title: const Text('تغيير وقت أذكار المساء'),
                  trailing: const Icon(Icons.access_time),
                  enabled: _eveningAthkarEnabled && _notificationsEnabled,
                  onTap: () => _selectTime(context, false),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Prayer times section
          Card(
            child: SwitchListTile(
              title: const Text('تذكيرات أوقات الصلاة'),
              subtitle: const Text('إشعارات قبل وقت الصلاة بـ 5 دقائق'),
              value: _prayerTimesEnabled && _notificationsEnabled,
              activeColor: AppTheme.primaryGreen,
              onChanged: _notificationsEnabled ? (value) {
                setState(() {
                  _prayerTimesEnabled = value;
                });
              } : null,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Custom reminder times
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text(
                    'أوقات تذكير مخصصة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                ..._customReminderTimes.map((time) => ListTile(
                  title: Text(time),
                  trailing: IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () => _removeCustomReminderTime(time),
                  ),
                )),
                ListTile(
                  title: const Text('إضافة وقت تذكير جديد'),
                  leading: const Icon(Icons.add, color: AppTheme.primaryGreen),
                  enabled: _notificationsEnabled,
                  onTap: _addCustomReminderTime,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Sound settings
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text(
                    'إعدادات الصوت',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SwitchListTile(
                  title: const Text('تفعيل الأصوات'),
                  value: _soundEnabled,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _soundEnabled = value;
                    });
                  },
                ),
                SwitchListTile(
                  title: const Text('تفعيل الاهتزاز'),
                  value: _vibrationEnabled,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _vibrationEnabled = value;
                    });
                  },
                ),
                ListTile(
                  title: const Text('نمط الصوت'),
                  subtitle: Text(_selectedSoundTheme),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  enabled: _soundEnabled,
                  onTap: () => _showSoundThemeDialog(),
                ),
                ListTile(
                  title: const Text('رفع صوت مخصص'),
                  subtitle: _customNotificationSound != null 
                      ? const Text('تم رفع صوت مخصص')
                      : const Text('لم يتم رفع صوت مخصص'),
                  trailing: const Icon(Icons.upload_file),
                  enabled: _soundEnabled,
                  onTap: _uploadCustomSound,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSoundThemeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر نمط الصوت'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AudioService.getAvailableSoundThemes().map((theme) => 
            RadioListTile<String>(
              title: Text(theme),
              value: theme,
              groupValue: _selectedSoundTheme,
              activeColor: AppTheme.primaryGreen,
              onChanged: (value) {
                setState(() {
                  _selectedSoundTheme = value!;
                });
                AudioService.testSoundTheme(value!);
                Navigator.pop(context);
              },
            ),
          ).toList(),
        ),
      ),
    );
  }
}
