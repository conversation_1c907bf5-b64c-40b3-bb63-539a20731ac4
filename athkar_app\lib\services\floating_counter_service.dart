import 'package:flutter/foundation.dart';
import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FloatingCounterService {
  static bool _isOverlayActive = false;
  static int _currentCount = 0;
  static String _currentDhikr = 'سُبْحَانَ اللهِ';

  static bool get isOverlayActive => _isOverlayActive;
  static int get currentCount => _currentCount;
  static String get currentDhikr => _currentDhikr;

  // Request overlay permission
  static Future<bool> requestOverlayPermission() async {
    try {
      if (defaultTargetPlatform != TargetPlatform.android) {
        debugPrint('Floating overlay is only supported on Android');
        return false;
      }

      // Check if permission is already granted
      bool hasPermission = await FlutterOverlayWindow.isPermissionGranted();

      if (!hasPermission) {
        // Request permission
        hasPermission = await FlutterOverlayWindow.requestPermission() ?? false;
      }

      return hasPermission;
    } catch (e) {
      debugPrint('Error requesting overlay permission: $e');
      return false;
    }
  }

  // Start floating counter
  static Future<bool> startFloatingCounter({
    String dhikr = 'سُبْحَانَ اللهِ',
    int initialCount = 0,
  }) async {
    try {
      if (_isOverlayActive) {
        await stopFloatingCounter();
      }

      // Request permission first
      bool hasPermission = await requestOverlayPermission();
      if (!hasPermission) {
        debugPrint('Overlay permission not granted');
        return false;
      }

      _currentDhikr = dhikr;
      _currentCount = initialCount;

      // Start overlay
      await FlutterOverlayWindow.showOverlay(
        enableDrag: true,
        overlayTitle: "Dhikr Counter",
        overlayContent: 'Counting: $dhikr',
        flag: OverlayFlag.defaultFlag,
        visibility: NotificationVisibility.visibilityPublic,
        positionGravity: PositionGravity.auto,
        width: 200,
        height: 300,
      );

      _isOverlayActive = true;
      await _saveCounterState();
      
      return true;
    } catch (e) {
      debugPrint('Error starting floating counter: $e');
      return false;
    }
  }

  // Stop floating counter
  static Future<void> stopFloatingCounter() async {
    try {
      if (_isOverlayActive) {
        await FlutterOverlayWindow.closeOverlay();
        _isOverlayActive = false;
        await _saveCounterState();
      }
    } catch (e) {
      debugPrint('Error stopping floating counter: $e');
    }
  }

  // Increment counter
  static Future<void> incrementCounter() async {
    try {
      _currentCount++;
      await _saveCounterState();
      
      if (_isOverlayActive) {
        // Note: updateOverlayContent may not be available in all versions
        // The overlay will need to be restarted to show updated content
        debugPrint('Counter updated: $_currentCount');
      }
    } catch (e) {
      debugPrint('Error incrementing counter: $e');
    }
  }

  // Reset counter
  static Future<void> resetCounter() async {
    try {
      _currentCount = 0;
      await _saveCounterState();
      
      if (_isOverlayActive) {
        debugPrint('Counter reset: $_currentCount');
      }
    } catch (e) {
      debugPrint('Error resetting counter: $e');
    }
  }

  // Set dhikr text
  static Future<void> setDhikr(String dhikr) async {
    try {
      _currentDhikr = dhikr;
      await _saveCounterState();
      
      if (_isOverlayActive) {
        debugPrint('Dhikr updated: $_currentDhikr');
      }
    } catch (e) {
      debugPrint('Error setting dhikr: $e');
    }
  }

  // Save counter state to preferences
  static Future<void> _saveCounterState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('floating_counter_count', _currentCount);
      await prefs.setString('floating_counter_dhikr', _currentDhikr);
      await prefs.setBool('floating_counter_active', _isOverlayActive);
    } catch (e) {
      debugPrint('Error saving counter state: $e');
    }
  }

  // Load counter state from preferences
  static Future<void> loadCounterState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentCount = prefs.getInt('floating_counter_count') ?? 0;
      _currentDhikr = prefs.getString('floating_counter_dhikr') ?? 'سُبْحَانَ اللهِ';
      _isOverlayActive = prefs.getBool('floating_counter_active') ?? false;
      
      // If overlay was active, restart it
      if (_isOverlayActive) {
        _isOverlayActive = false; // Reset to avoid conflicts
        await startFloatingCounter(
          dhikr: _currentDhikr,
          initialCount: _currentCount,
        );
      }
    } catch (e) {
      debugPrint('Error loading counter state: $e');
    }
  }

  // Get counter statistics
  static Future<Map<String, dynamic>> getCounterStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final totalCount = prefs.getInt('total_dhikr_count') ?? 0;
      final sessionsCount = prefs.getInt('dhikr_sessions_count') ?? 0;
      final lastSessionDate = prefs.getString('last_dhikr_session');
      
      return {
        'currentCount': _currentCount,
        'totalCount': totalCount,
        'sessionsCount': sessionsCount,
        'lastSessionDate': lastSessionDate,
        'currentDhikr': _currentDhikr,
        'isActive': _isOverlayActive,
      };
    } catch (e) {
      debugPrint('Error getting counter stats: $e');
      return {};
    }
  }

  // Save session when counter is completed
  static Future<void> completeSession() async {
    try {
      if (_currentCount > 0) {
        final prefs = await SharedPreferences.getInstance();
        final totalCount = prefs.getInt('total_dhikr_count') ?? 0;
        final sessionsCount = prefs.getInt('dhikr_sessions_count') ?? 0;
        
        await prefs.setInt('total_dhikr_count', totalCount + _currentCount);
        await prefs.setInt('dhikr_sessions_count', sessionsCount + 1);
        await prefs.setString('last_dhikr_session', DateTime.now().toIso8601String());
        
        // Reset current count
        await resetCounter();
      }
    } catch (e) {
      debugPrint('Error completing session: $e');
    }
  }

  // Check if overlay is supported
  static bool isOverlaySupported() {
    return defaultTargetPlatform == TargetPlatform.android;
  }

  // Get overlay status
  static Future<bool> isOverlayPermissionGranted() async {
    try {
      if (!isOverlaySupported()) return false;
      return await FlutterOverlayWindow.isPermissionGranted();
    } catch (e) {
      debugPrint('Error checking overlay permission: $e');
      return false;
    }
  }
}
