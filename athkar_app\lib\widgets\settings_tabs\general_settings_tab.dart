import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import '../../services/language_service.dart';
import '../../services/notification_service.dart';
import '../../providers/theme_provider.dart';
import '../../providers/language_provider.dart';
import '../../theme/app_theme.dart';


class GeneralSettingsTab extends StatefulWidget {
  const GeneralSettingsTab({super.key});

  @override
  State<GeneralSettingsTab> createState() => _GeneralSettingsTabState();
}

class _GeneralSettingsTabState extends State<GeneralSettingsTab> {
  bool _notificationsEnabled = true;
  bool _vibrationEnabled = true;
  bool _soundEnabled = true;
  bool _autoRefreshEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      _notificationsEnabled = prefs.getBool('general_notifications_enabled') ?? true;
      _vibrationEnabled = prefs.getBool('general_vibration_enabled') ?? true;
      _soundEnabled = prefs.getBool('general_sound_enabled') ?? true;
      _autoRefreshEnabled = prefs.getBool('general_auto_refresh_enabled') ?? true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final languageProvider = Provider.of<LanguageProvider>(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Language Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات اللغة' : 'Language Settings',
            Icons.language,
          ),
          const SizedBox(height: 12),
          
          Card(
            elevation: 2,
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.language, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'اللغة' : 'Language'),
                  subtitle: Text(languageService.isArabic ? 'العربية' : 'English'),
                  trailing: Switch(
                    value: languageService.isArabic,
                    activeColor: AppTheme.primaryGreen,
                    onChanged: (value) {
                      languageProvider.toggleLanguage();
                    },
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.text_fields, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'اتجاه النص' : 'Text Direction'),
                  subtitle: Text(languageService.isArabic ? 'من اليمين إلى اليسار' : 'Left to Right'),
                  trailing: Icon(
                    languageService.isArabic ? Icons.format_textdirection_r_to_l : Icons.format_textdirection_l_to_r,
                    color: AppTheme.primaryGreen,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Theme Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات المظهر' : 'Theme Settings',
            Icons.palette,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.dark_mode, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'الوضع الليلي' : 'Dark Mode'),
                  subtitle: Text(
                    themeProvider.isDarkMode 
                        ? (languageService.isArabic ? 'مفعل' : 'Enabled')
                        : (languageService.isArabic ? 'معطل' : 'Disabled'),
                  ),
                  trailing: Switch(
                    value: themeProvider.isDarkMode,
                    activeColor: AppTheme.primaryGreen,
                    onChanged: (value) {
                      themeProvider.toggleDarkMode();
                    },
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.color_lens, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'نظام الألوان' : 'Color Scheme'),
                  subtitle: Text(_getColorSchemeName(themeProvider.currentColorScheme, languageService)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showColorSchemeDialog(context, themeProvider, languageService),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.format_size, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'حجم الخط' : 'Font Size'),
                  subtitle: Text(_getFontSizeName(themeProvider.fontSize, languageService)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showFontSizeDialog(context, themeProvider, languageService),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Notification Settings Section
          _buildSectionHeader(
            languageService.isArabic ? 'إعدادات الإشعارات' : 'Notification Settings',
            Icons.notifications,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.notifications_active, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'تفعيل الإشعارات' : 'Enable Notifications'),
                  subtitle: Text(languageService.isArabic ? 'إشعارات الأذكار والصلاة' : 'Athkar and Prayer notifications'),
                  value: _notificationsEnabled,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final messenger = ScaffoldMessenger.of(context);
                    final isArabic = languageService.isArabic;

                    setState(() {
                      _notificationsEnabled = value;
                    });

                    // Save to SharedPreferences
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('general_notifications_enabled', value);

                    // Update notification service
                    if (value) {
                      await NotificationService.initialize();
                    } else {
                      await NotificationService.cancelAllNotifications();
                    }

                    if (mounted) {
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            isArabic
                                ? (value ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات')
                                : (value ? 'Notifications enabled' : 'Notifications disabled'),
                          ),
                          backgroundColor: AppTheme.primaryGreen,
                        ),
                      );
                    }
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.vibration, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'الاهتزاز' : 'Vibration'),
                  subtitle: Text(languageService.isArabic ? 'اهتزاز عند الإشعارات' : 'Vibrate on notifications'),
                  value: _vibrationEnabled,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final messenger = ScaffoldMessenger.of(context);
                    final isArabic = languageService.isArabic;

                    setState(() {
                      _vibrationEnabled = value;
                    });

                    // Save to SharedPreferences
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('general_vibration_enabled', value);

                    // Test vibration if enabled
                    if (value) {
                      HapticFeedback.mediumImpact();
                    }

                    if (mounted) {
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            isArabic
                                ? (value ? 'تم تفعيل الاهتزاز' : 'تم إيقاف الاهتزاز')
                                : (value ? 'Vibration enabled' : 'Vibration disabled'),
                          ),
                          backgroundColor: AppTheme.primaryGreen,
                        ),
                      );
                    }
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.volume_up, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'الصوت' : 'Sound'),
                  subtitle: Text(languageService.isArabic ? 'صوت الإشعارات' : 'Notification sound'),
                  value: _soundEnabled,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final messenger = ScaffoldMessenger.of(context);
                    final isArabic = languageService.isArabic;

                    setState(() {
                      _soundEnabled = value;
                    });

                    // Save to SharedPreferences
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('general_sound_enabled', value);

                    // Play test sound if enabled
                    if (value) {
                      try {
                        // Play system notification sound
                        SystemSound.play(SystemSoundType.alert);
                      } catch (e) {
                        debugPrint('Error playing notification sound: $e');
                      }
                    }

                    if (mounted) {
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            isArabic
                                ? (value ? 'تم تفعيل الصوت' : 'تم إيقاف الصوت')
                                : (value ? 'Sound enabled' : 'Sound disabled'),
                          ),
                          backgroundColor: AppTheme.primaryGreen,
                        ),
                      );
                    }
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // App Behavior Section
          _buildSectionHeader(
            languageService.isArabic ? 'سلوك التطبيق' : 'App Behavior',
            Icons.settings_applications,
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 2,
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.autorenew, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'التحديث التلقائي' : 'Auto Refresh'),
                  subtitle: Text(languageService.isArabic ? 'تحديث البيانات تلقائياً' : 'Automatically refresh data'),
                  value: _autoRefreshEnabled,
                  activeColor: AppTheme.primaryGreen,
                  onChanged: (value) async {
                    final messenger = ScaffoldMessenger.of(context);
                    final isArabic = languageService.isArabic;

                    setState(() {
                      _autoRefreshEnabled = value;
                    });

                    // Save to SharedPreferences
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setBool('general_auto_refresh_enabled', value);

                    if (mounted) {
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            isArabic
                                ? (value ? 'تم تفعيل التحديث التلقائي' : 'تم إيقاف التحديث التلقائي')
                                : (value ? 'Auto refresh enabled' : 'Auto refresh disabled'),
                          ),
                          backgroundColor: AppTheme.primaryGreen,
                        ),
                      );
                    }
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.offline_bolt, color: AppTheme.primaryGreen),
                  title: Text(languageService.isArabic ? 'الوضع غير المتصل' : 'Offline Mode'),
                  subtitle: Text(languageService.isArabic ? 'العمل بدون اتصال بالإنترنت' : 'Work without internet connection'),
                  value: true, // Always enabled for Islamic app
                  activeColor: AppTheme.primaryGreen,
                  onChanged: null, // Disabled - always offline capable
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: AppTheme.primaryGreen, size: 24),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
      ],
    );
  }

  String _getColorSchemeName(String scheme, LanguageService languageService) {
    switch (scheme) {
      case 'traditional_green':
        return languageService.isArabic ? 'الأخضر التقليدي' : 'Traditional Green';
      case 'masjid_blue':
        return languageService.isArabic ? 'أزرق المسجد' : 'Masjid Blue';
      case 'sunset_gold':
        return languageService.isArabic ? 'ذهبي الغروب' : 'Sunset Gold';
      case 'night_mode':
        return languageService.isArabic ? 'الوضع الليلي' : 'Night Mode';
      default:
        return languageService.isArabic ? 'الأخضر التقليدي' : 'Traditional Green';
    }
  }

  String _getFontSizeName(double fontSize, LanguageService languageService) {
    if (fontSize <= 14) {
      return languageService.isArabic ? 'صغير' : 'Small';
    } else if (fontSize <= 16) {
      return languageService.isArabic ? 'متوسط' : 'Medium';
    } else if (fontSize <= 18) {
      return languageService.isArabic ? 'كبير' : 'Large';
    } else {
      return languageService.isArabic ? 'كبير جداً' : 'Extra Large';
    }
  }

  void _showColorSchemeDialog(BuildContext context, ThemeProvider themeProvider, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'اختر نظام الألوان' : 'Choose Color Scheme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildColorSchemeOption('traditional_green', languageService.isArabic ? 'الأخضر التقليدي' : 'Traditional Green', Colors.green, themeProvider),
            _buildColorSchemeOption('masjid_blue', languageService.isArabic ? 'أزرق المسجد' : 'Masjid Blue', Colors.blue, themeProvider),
            _buildColorSchemeOption('sunset_gold', languageService.isArabic ? 'ذهبي الغروب' : 'Sunset Gold', Colors.orange, themeProvider),
            _buildColorSchemeOption('night_mode', languageService.isArabic ? 'الوضع الليلي' : 'Night Mode', Colors.grey, themeProvider),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildColorSchemeOption(String scheme, String name, Color color, ThemeProvider themeProvider) {
    return RadioListTile<String>(
      title: Text(name),
      value: scheme,
      groupValue: themeProvider.currentColorScheme,
      activeColor: color,
      onChanged: (value) {
        if (value != null) {
          themeProvider.setColorScheme(value);
          Navigator.pop(context);
        }
      },
    );
  }

  void _showFontSizeDialog(BuildContext context, ThemeProvider themeProvider, LanguageService languageService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageService.isArabic ? 'اختر حجم الخط' : 'Choose Font Size'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildFontSizeOption(12.0, languageService.isArabic ? 'صغير' : 'Small', themeProvider),
            _buildFontSizeOption(14.0, languageService.isArabic ? 'متوسط' : 'Medium', themeProvider),
            _buildFontSizeOption(16.0, languageService.isArabic ? 'كبير' : 'Large', themeProvider),
            _buildFontSizeOption(18.0, languageService.isArabic ? 'كبير جداً' : 'Extra Large', themeProvider),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageService.isArabic ? 'إلغاء' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildFontSizeOption(double size, String name, ThemeProvider themeProvider) {
    return RadioListTile<double>(
      title: Text(name, style: TextStyle(fontSize: size)),
      value: size,
      groupValue: themeProvider.fontSize,
      activeColor: AppTheme.primaryGreen,
      onChanged: (value) {
        if (value != null) {
          themeProvider.setFontSize(value);
          Navigator.pop(context);
        }
      },
    );
  }
}
