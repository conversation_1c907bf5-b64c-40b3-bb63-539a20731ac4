{"logs": [{"outputFile": "com.islamicapps.athkar.athkar_app-mergeDebugResources-65:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\84addddb59162e1cea52976d5f2c6cc1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "407", "startColumns": "4", "startOffsets": "23412", "endColumns": "49", "endOffsets": "23457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\aa55b2079cbc673a6a445c1850daa153\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "404", "startColumns": "4", "startOffsets": "23255", "endColumns": "42", "endOffsets": "23293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28f988f0d4c2cc22199e4c3cefdd595e\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "35,2189,2899,2905", "startColumns": "4,4,4,4", "startOffsets": "1213,142972,166751,166962", "endLines": "35,2191,2904,2988", "endColumns": "60,12,24,24", "endOffsets": "1269,143112,166957,171473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9afb31c75cdd321eb1d7dc2fef0c4e5a\\transformed\\work-runtime-2.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "97,98,99,100", "startColumns": "4,4,4,4", "startOffsets": "3383,3448,3518,3582", "endColumns": "64,69,63,60", "endOffsets": "3443,3513,3577,3638"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cceca150324ee75eadce8003b387b1fb\\transformed\\biometric-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,114,277,470,473,488,489,490,491,492,493,494,495,496,497", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,244,327,438,573,4623,15788,28826,29008,30068,30157,30256,30364,30461,30549,30649,30719,30816,30926", "endLines": "5,7,10,14,33,114,277,470,473,488,489,490,491,492,493,494,495,496,497", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "239,322,433,568,1149,4674,15836,28897,29063,30152,30251,30359,30456,30544,30644,30714,30811,30921,31010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\85879f220671a879b538e8ef16ed1744\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "444", "startColumns": "4", "startOffsets": "25899", "endColumns": "82", "endOffsets": "25977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66c3f8d759689e7c8bf8d566a47d4905\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "474,475,476,477,478,479,480,481,482", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "29068,29138,29200,29265,29329,29406,29471,29561,29645", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "29133,29195,29260,29324,29401,29466,29556,29640,29709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "61,108,109,128,129,161,162,265,266,267,268,269,270,271,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,369,370,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,416,445,446,447,448,449,450,451,503,2028,2029,2034,2037,2042,2187,2188,2844,2861,3031,3064,3094,3127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2034,4144,4216,5614,5679,7813,7882,14964,15034,15102,15174,15244,15305,15379,16769,16830,16891,16953,17017,17079,17140,17208,17308,17368,17434,17507,17576,17633,17685,18845,18917,18993,19058,19117,19176,19236,19296,19356,19416,19476,19536,19596,19656,19716,19776,19835,19895,19955,20015,20075,20135,20195,20255,20315,20375,20435,20494,20554,20614,20673,20732,20791,20850,20909,21477,21512,22098,22153,22216,22271,22329,22387,22448,22511,22568,22619,22669,22730,22787,22853,22887,22922,23963,25982,26049,26121,26190,26259,26333,26405,31252,130398,130515,130782,131075,131342,142833,142905,164489,165093,172928,174659,175659,176341", "endLines": "61,108,109,128,129,161,162,265,266,267,268,269,270,271,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,369,370,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,416,445,446,447,448,449,450,451,503,2028,2032,2034,2040,2042,2187,2188,2849,2870,3063,3084,3126,3132", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "2089,4211,4299,5674,5740,7877,7940,15029,15097,15169,15239,15300,15374,15447,16825,16886,16948,17012,17074,17135,17203,17303,17363,17429,17502,17571,17628,17680,17742,18912,18988,19053,19112,19171,19231,19291,19351,19411,19471,19531,19591,19651,19711,19771,19830,19890,19950,20010,20070,20130,20190,20250,20310,20370,20430,20489,20549,20609,20668,20727,20786,20845,20904,20963,21507,21542,22148,22211,22266,22324,22382,22443,22506,22563,22614,22664,22725,22782,22848,22882,22917,22952,24028,26044,26116,26185,26254,26328,26400,26488,31318,130510,130711,130887,131271,131466,142900,142967,164687,165389,174654,175335,176336,176503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "122,123,124,125,263,264,472,484,485,486", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "5250,5308,5374,5437,14821,14892,28940,29771,29838,29917", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "5303,5369,5432,5494,14887,14959,29003,29833,29912,29981"}}, {"source": "D:\\projects\\12july\\athkar\\athkar_app\\build\\firebase_crashlytics\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "60", "endOffsets": "111"}, "to": {"startLines": "95", "startColumns": "4", "startOffsets": "3249", "endColumns": "60", "endOffsets": "3305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "96,164,305,306,307,308,309,310,311,372,373,374,414,415,471,483,498,499,504,505,506,1582,1766,1769,1775,1781,1784,1790,1794,1797,1804,1810,1813,1819,1824,1829,1836,1838,1844,1850,1858,1863,1870,1875,1881,1885,1892,1896,1902,1908,1911,1915,1916,2835,2850,2989,3027,3169,3357,3375,3439,3449,3459,3466,3472,3576,3745,3762", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3310,8034,17747,17811,17866,17934,18001,18066,18123,21590,21638,21686,23837,23900,28902,29714,31015,31059,31323,31462,31512,99835,113573,113678,113923,114261,114407,114747,114959,115122,115529,115867,115990,116329,116568,116825,117196,117256,117594,117880,118329,118621,119009,119314,119658,119903,120233,120440,120708,120981,121125,121326,121373,164169,164692,171478,172779,177721,183631,184259,186184,186466,186771,187033,187293,190809,197104,197634", "endLines": "96,164,305,306,307,308,309,310,311,372,373,374,414,415,471,483,498,501,504,505,506,1598,1768,1774,1780,1783,1789,1793,1796,1803,1809,1812,1818,1823,1828,1835,1837,1843,1849,1857,1862,1869,1874,1880,1884,1891,1895,1901,1907,1910,1914,1915,1916,2839,2860,3008,3030,3178,3364,3438,3448,3458,3465,3471,3514,3588,3761,3778", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "3378,8098,17806,17861,17929,17996,18061,18118,18175,21633,21681,21742,23895,23958,28935,29766,31054,31194,31457,31507,31555,101268,113673,113918,114256,114402,114742,114954,115117,115524,115862,115985,116324,116563,116820,117191,117251,117589,117875,118324,118616,119004,119309,119653,119898,120228,120435,120703,120976,121120,121321,121368,121424,164349,165088,172202,172923,178048,183874,186179,186461,186766,187028,187288,188711,191256,197629,198197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c8746a36ac065afed39d95b2852a559\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "364,380,408,3085,3090", "startColumns": "4,4,4,4,4", "startOffsets": "21233,21987,23462,175340,175510", "endLines": "364,380,408,3089,3093", "endColumns": "56,64,63,24,24", "endOffsets": "21285,22047,23521,175505,175654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\34d42fe4c975f0d33f3375454392120e\\transformed\\jetified-firebase-messaging-24.1.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "487", "startColumns": "4", "startOffsets": "29986", "endColumns": "81", "endOffsets": "30063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e1f6d2e0b1aa38467964f5b59b4f29f9\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "130,131,132,133,134,135,136,137,452,453,454,455,456,457,458,459,461,462,463,464,465,466,467,468,469,3179,3589", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5745,5835,5915,6005,6095,6175,6256,6336,26493,26598,26779,26904,27011,27191,27314,27430,27700,27888,27993,28174,28299,28474,28622,28685,28747,178053,191261", "endLines": "130,131,132,133,134,135,136,137,452,453,454,455,456,457,458,459,461,462,463,464,465,466,467,468,469,3191,3607", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "5830,5910,6000,6090,6170,6251,6331,6411,26593,26774,26899,27006,27186,27309,27425,27528,27883,27988,28169,28294,28469,28617,28680,28742,28821,178363,191673"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,219,220,224,228,232,237,243,250,254,258,263,267,271,275,279,283,287,293,297,303,307,313,317,322,326,329,333,339,343,349,353,359,362,366,370,374,378,382,383,384,385,388,391,394,397,401,402,403,404,405,408,410,412,414,419,420,424,430,434,435,437,448,449,453,459,463,464,465,469,496,500,501,505,533,703,729,899,925,956,964,970,984,1006,1011,1016,1026,1035,1044,1048,1055,1063,1070,1071,1080,1083,1086,1090,1094,1098,1101,1102,1107,1112,1122,1127,1134,1140,1141,1144,1148,1153,1155,1157,1160,1163,1165,1169,1172,1179,1182,1185,1189,1191,1195,1197,1199,1201,1205,1213,1221,1233,1239,1248,1251,1262,1265,1266,1271,1272,1277,1346,1416,1417,1427,1436,1437,1439,1443,1446,1449,1452,1455,1458,1461,1464,1468,1471,1474,1477,1481,1484,1488,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1516,1517,1518,1519,1520,1521,1522,1523,1525,1526,1528,1529,1531,1533,1534,1536,1537,1538,1539,1540,1541,1543,1544,1545,1546,1547,1548,1550,1552,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1568,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,535,605,666,741,817,894,972,1057,1139,1215,1291,1368,1446,1552,1658,1737,1817,1874,1932,2006,2081,2146,2212,2272,2333,2405,2478,2545,2613,2672,2731,2790,2849,2908,2962,3016,3069,3123,3177,3231,3285,3359,3438,3511,3656,3728,3800,3873,3930,4061,4135,4209,4284,4356,4429,4499,4570,4630,4691,4760,4829,4899,4973,5049,5113,5190,5266,5343,5408,5477,5554,5629,5698,5766,5843,5909,5970,6067,6132,6201,6300,6371,6430,6488,6545,6604,6668,6739,6811,6883,6955,7027,7094,7162,7230,7289,7352,7416,7506,7597,7657,7723,7790,7856,7926,7990,8043,8110,8171,8238,8351,8409,8472,8537,8602,8677,8750,8822,8871,8932,8993,9054,9116,9180,9244,9308,9373,9436,9496,9557,9623,9682,9742,9804,9875,9935,10003,10089,10176,10266,10353,10441,10523,10606,10696,10787,10839,10897,10942,11008,11072,11129,11186,11240,11297,11345,11394,11445,11479,11526,11575,11621,11653,11717,11839,11896,11970,12040,12118,12172,12242,12327,12375,12421,12482,12545,12611,12675,12746,12809,12874,12938,12999,13060,13112,13185,13259,13328,13403,13477,13551,13692,13762,13815,13893,13983,14071,14167,14257,14839,14928,15175,15456,15708,15993,16386,16863,17085,17307,17583,17810,18040,18270,18500,18730,18957,19376,19602,20027,20257,20685,20904,21187,21395,21526,21753,22179,22404,22831,23052,23477,23597,23873,24174,24498,24789,25103,25240,25371,25476,25718,25885,26089,26297,26568,26680,26792,26897,27014,27228,27374,27514,27600,27948,28036,28282,28700,28949,29031,29129,29746,29846,30098,30522,30777,30871,30960,31197,33249,33491,33593,33846,36030,47063,48579,59710,61238,62995,63621,64041,65102,66367,66623,66859,67406,67900,68505,68703,69283,69847,70222,70340,70878,71035,71231,71504,71760,71930,72071,72135,72500,72867,73543,73807,74145,74498,74592,74778,75084,75346,75471,75598,75837,76048,76167,76360,76537,76992,77173,77295,77554,77667,77854,77956,78063,78192,78467,78975,79471,80348,80642,81212,81361,82093,82265,82349,82685,82777,83055,88464,94016,94078,94708,95322,95413,95526,95755,95915,96067,96238,96404,96573,96740,96903,97146,97316,97489,97660,97934,98133,98338,98668,98752,98848,98944,99042,99142,99244,99346,99448,99550,99652,99752,99848,99960,100089,100212,100343,100474,100572,100686,100780,100920,101054,101150,101262,101362,101478,101574,101686,101786,101926,102062,102226,102356,102514,102664,102805,102949,103084,103196,103346,103474,103602,103738,103870,104000,104130,104242,104382,104528,104672,104810,104876,104966,105042,105146,105236,105338,105446,105554,105654,105734,105826,105924,106034,106086,106164,106270,106362,106466,106576,106698,106861,107018,107098,107198,107288,107398,107488,107729,107823,107929,108021,108121,108233,108347,108463,108579,108673,108787,108899,109001,109121,109243,109325,109429,109549,109675,109773,109867,109955,110067,110183,110305,110417,110592,110708,110794,110886,110998,111122,111189,111315,111383,111511,111655,111783,111852,111947,112062,112175,112274,112383,112494,112605,112706,112811,112911,113041,113132,113255,113349,113461,113547,113651,113747,113835,113953,114057,114161,114287,114375,114483,114583,114673,114783,114867,114969,115053,115107,115171,115277,115363,115473,115557,115677,120821,120939,121054,121186,121901,122593,123110,124709,126242,126630,131365,151627,151887,153397,154430,156443,156705,157061,157891,164673,165807,166101,166324,166651,168701,169349,173200,174402,178481,179696,181105", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,702,728,898,924,955,963,969,983,1005,1010,1015,1025,1034,1043,1047,1054,1062,1069,1070,1079,1082,1085,1089,1093,1097,1100,1101,1106,1111,1121,1126,1133,1139,1140,1143,1147,1152,1154,1156,1159,1162,1164,1168,1171,1178,1181,1184,1188,1190,1194,1196,1198,1200,1204,1212,1220,1232,1238,1247,1250,1261,1264,1265,1270,1271,1276,1345,1415,1416,1426,1435,1436,1438,1442,1445,1448,1451,1454,1457,1460,1463,1467,1470,1473,1476,1480,1483,1487,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1513,1515,1516,1517,1518,1519,1520,1521,1522,1524,1525,1527,1528,1530,1532,1533,1535,1536,1537,1538,1539,1540,1542,1543,1544,1545,1546,1547,1549,1551,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1567,1568,1569,1570,1571,1572,1573,1575,1579,1583,1584,1585,1586,1587,1588,1592,1593,1594,1595,1597,1599,1601,1603,1605,1606,1607,1608,1610,1612,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1628,1629,1630,1631,1633,1635,1636,1638,1639,1641,1643,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1658,1659,1660,1661,1663,1664,1665,1666,1667,1669,1671,1673,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1773,1776,1779,1782,1796,1807,1817,1847,1874,1883,1958,2355,2360,2388,2406,2442,2448,2454,2477,2618,2638,2644,2648,2654,2691,2703,2769,2793,2862,2881,2907,2916", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,530,600,661,736,812,889,967,1052,1134,1210,1286,1363,1441,1547,1653,1732,1812,1869,1927,2001,2076,2141,2207,2267,2328,2400,2473,2540,2608,2667,2726,2785,2844,2903,2957,3011,3064,3118,3172,3226,3280,3354,3433,3506,3580,3723,3795,3868,3925,3983,4130,4204,4279,4351,4424,4494,4565,4625,4686,4755,4824,4894,4968,5044,5108,5185,5261,5338,5403,5472,5549,5624,5693,5761,5838,5904,5965,6062,6127,6196,6295,6366,6425,6483,6540,6599,6663,6734,6806,6878,6950,7022,7089,7157,7225,7284,7347,7411,7501,7592,7652,7718,7785,7851,7921,7985,8038,8105,8166,8233,8346,8404,8467,8532,8597,8672,8745,8817,8866,8927,8988,9049,9111,9175,9239,9303,9368,9431,9491,9552,9618,9677,9737,9799,9870,9930,9998,10084,10171,10261,10348,10436,10518,10601,10691,10782,10834,10892,10937,11003,11067,11124,11181,11235,11292,11340,11389,11440,11474,11521,11570,11616,11648,11712,11774,11891,11965,12035,12113,12167,12237,12322,12370,12416,12477,12540,12606,12670,12741,12804,12869,12933,12994,13055,13107,13180,13254,13323,13398,13472,13546,13687,13757,13810,13888,13978,14066,14162,14252,14834,14923,15170,15451,15703,15988,16381,16858,17080,17302,17578,17805,18035,18265,18495,18725,18952,19371,19597,20022,20252,20680,20899,21182,21390,21521,21748,22174,22399,22826,23047,23472,23592,23868,24169,24493,24784,25098,25235,25366,25471,25713,25880,26084,26292,26563,26675,26787,26892,27009,27223,27369,27509,27595,27943,28031,28277,28695,28944,29026,29124,29741,29841,30093,30517,30772,30866,30955,31192,33244,33486,33588,33841,36025,47058,48574,59705,61233,62990,63616,64036,65097,66362,66618,66854,67401,67895,68500,68698,69278,69842,70217,70335,70873,71030,71226,71499,71755,71925,72066,72130,72495,72862,73538,73802,74140,74493,74587,74773,75079,75341,75466,75593,75832,76043,76162,76355,76532,76987,77168,77290,77549,77662,77849,77951,78058,78187,78462,78970,79466,80343,80637,81207,81356,82088,82260,82344,82680,82772,83050,88459,94011,94073,94703,95317,95408,95521,95750,95910,96062,96233,96399,96568,96735,96898,97141,97311,97484,97655,97929,98128,98333,98663,98747,98843,98939,99037,99137,99239,99341,99443,99545,99647,99747,99843,99955,100084,100207,100338,100469,100567,100681,100775,100915,101049,101145,101257,101357,101473,101569,101681,101781,101921,102057,102221,102351,102509,102659,102800,102944,103079,103191,103341,103469,103597,103733,103865,103995,104125,104237,104377,104523,104667,104805,104871,104961,105037,105141,105231,105333,105441,105549,105649,105729,105821,105919,106029,106081,106159,106265,106357,106461,106571,106693,106856,107013,107093,107193,107283,107393,107483,107724,107818,107924,108016,108116,108228,108342,108458,108574,108668,108782,108894,108996,109116,109238,109320,109424,109544,109670,109768,109862,109950,110062,110178,110300,110412,110587,110703,110789,110881,110993,111117,111184,111310,111378,111506,111650,111778,111847,111942,112057,112170,112269,112378,112489,112600,112701,112806,112906,113036,113127,113250,113344,113456,113542,113646,113742,113830,113948,114052,114156,114282,114370,114478,114578,114668,114778,114862,114964,115048,115102,115166,115272,115358,115468,115552,115672,120816,120934,121049,121181,121896,122588,123105,124704,126237,126625,131360,151622,151882,153392,154425,156438,156700,157056,157886,164668,165802,166096,166319,166646,168696,169344,173195,174397,178476,179691,181100,181574"}, "to": {"startLines": "36,59,60,91,92,93,94,101,102,103,104,105,106,107,110,111,112,113,116,117,118,119,120,121,126,127,138,139,140,141,142,143,144,145,147,148,149,150,151,152,153,154,155,156,157,158,159,160,165,166,167,168,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,272,273,278,279,280,281,282,283,284,316,317,318,319,320,321,322,323,359,360,361,362,367,375,376,381,403,409,410,412,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,502,507,508,515,516,517,518,526,527,531,535,539,544,550,557,561,565,570,574,578,582,586,590,594,600,604,610,614,620,624,629,633,636,640,646,650,656,660,666,669,673,677,681,685,689,690,691,692,695,698,701,704,708,709,710,711,712,715,717,719,721,726,727,731,737,741,742,744,755,756,760,766,770,771,772,776,803,807,808,812,840,1009,1035,1204,1230,1261,1269,1275,1289,1311,1316,1321,1331,1340,1349,1353,1360,1368,1375,1376,1385,1388,1391,1395,1399,1403,1406,1407,1412,1417,1427,1432,1439,1445,1446,1449,1453,1458,1460,1462,1465,1468,1470,1474,1477,1484,1487,1490,1494,1496,1500,1502,1504,1506,1510,1518,1526,1538,1544,1553,1556,1567,1570,1571,1576,1577,1606,1675,1745,1746,1756,1765,1917,1919,1923,1926,1929,1932,1935,1938,1941,1944,1948,1951,1954,1957,1961,1964,1968,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1994,1996,1997,1998,1999,2000,2001,2002,2003,2005,2006,2008,2009,2011,2013,2014,2016,2017,2018,2019,2020,2021,2023,2024,2025,2026,2027,2044,2046,2048,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2064,2065,2066,2067,2068,2069,2070,2072,2076,2080,2081,2082,2083,2084,2085,2089,2090,2091,2092,2094,2096,2098,2100,2102,2103,2104,2105,2107,2109,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2125,2126,2127,2128,2130,2132,2133,2135,2136,2138,2140,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2155,2156,2157,2158,2160,2161,2162,2163,2164,2166,2168,2170,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2192,2267,2270,2273,2276,2290,2307,2349,2378,2405,2414,2476,2840,2871,3009,3133,3157,3163,3192,3213,3337,3365,3371,3515,3541,3608,3679,3779,3799,3854,3866,3892", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1274,1940,1985,3032,3073,3128,3187,3643,3707,3777,3838,3913,3989,4066,4304,4389,4471,4547,4724,4801,4879,4985,5091,5170,5499,5556,6416,6490,6565,6630,6696,6756,6817,6889,7006,7073,7141,7200,7259,7318,7377,7436,7490,7544,7597,7651,7705,7759,8103,8177,8256,8329,8474,8546,8618,8691,8748,8879,8953,9027,9102,9174,9247,9317,9388,9448,9509,9578,9647,9717,9791,9867,9931,10008,10084,10161,10226,10295,10372,10447,10516,10584,10661,10727,10788,10885,10950,11019,11118,11189,11248,11306,11363,11422,11486,11557,11629,11701,11773,11845,11912,11980,12048,12107,12170,12234,12324,12415,12475,12541,12608,12674,12744,12808,12861,12928,12989,13056,13169,13227,13290,13355,13420,13495,13568,13640,13689,13750,13811,13872,13934,13998,14062,14126,14191,14254,14314,14375,14441,14500,14560,14622,14693,14753,15452,15538,15841,15931,16018,16106,16188,16271,16361,18392,18444,18502,18547,18613,18677,18734,18791,20968,21025,21073,21122,21377,21747,21794,22052,23223,23526,23590,23712,24033,24107,24177,24255,24309,24379,24464,24512,24558,24619,24682,24748,24812,24883,24946,25011,25075,25136,25197,25249,25322,25396,25465,25540,25614,25688,25829,31199,31560,31638,32028,32116,32212,32302,32884,32973,33220,33501,33753,34038,34431,34908,35130,35352,35628,35855,36085,36315,36545,36775,37002,37421,37647,38072,38302,38730,38949,39232,39440,39571,39798,40224,40449,40876,41097,41522,41642,41918,42219,42543,42834,43148,43285,43416,43521,43763,43930,44134,44342,44613,44725,44837,44942,45059,45273,45419,45559,45645,45993,46081,46327,46745,46994,47076,47174,47766,47866,48118,48542,48797,48891,48980,49217,51241,51483,51585,51838,53994,64435,65951,76490,78018,79775,80401,80821,81882,83147,83403,83639,84186,84680,85285,85483,86063,86627,87002,87120,87658,87815,88011,88284,88540,88710,88851,88915,89280,89647,90323,90587,90925,91278,91372,91558,91864,92126,92251,92378,92617,92828,92947,93140,93317,93772,93953,94075,94334,94447,94634,94736,94843,94972,95247,95755,96251,97128,97422,97992,98141,98873,99045,99129,99465,99557,101623,106869,112258,112320,112898,113482,121429,121542,121771,121931,122083,122254,122420,122589,122756,122919,123162,123332,123505,123676,123950,124149,124354,124684,124768,124864,124960,125058,125158,125260,125362,125464,125566,125668,125768,125864,125976,126105,126228,126359,126490,126588,126702,126796,126936,127070,127166,127278,127378,127494,127590,127702,127802,127942,128078,128242,128372,128530,128680,128821,128965,129100,129212,129362,129490,129618,129754,129886,130016,130146,130258,131538,131684,131828,131966,132032,132122,132198,132302,132392,132494,132602,132710,132810,132890,132982,133080,133190,133242,133320,133426,133518,133622,133732,133854,134017,134174,134254,134354,134444,134554,134644,134885,134979,135085,135177,135277,135389,135503,135619,135735,135829,135943,136055,136157,136277,136399,136481,136585,136705,136831,136929,137023,137111,137223,137339,137461,137573,137748,137864,137950,138042,138154,138278,138345,138471,138539,138667,138811,138939,139008,139103,139218,139331,139430,139539,139650,139761,139862,139967,140067,140197,140288,140411,140505,140617,140703,140807,140903,140991,141109,141213,141317,141443,141531,141639,141739,141829,141939,142023,142125,142209,142263,142327,142433,142519,142629,142713,143117,145733,145851,145966,146046,146407,146993,148397,149741,151102,151490,154265,164354,165394,172207,176508,177259,177521,178368,178747,183025,183879,184108,188716,189726,191678,194078,198202,198946,201077,201417,202728", "endLines": "36,59,60,91,92,93,94,101,102,103,104,105,106,107,110,111,112,113,116,117,118,119,120,121,126,127,138,139,140,141,142,143,144,145,147,148,149,150,151,152,153,154,155,156,157,158,159,160,165,166,167,168,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,272,273,278,279,280,281,282,283,284,316,317,318,319,320,321,322,323,359,360,361,362,367,375,376,381,403,409,410,412,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,502,507,508,515,516,517,525,526,530,534,538,543,549,556,560,564,569,573,577,581,585,589,593,599,603,609,613,619,623,628,632,635,639,645,649,655,659,665,668,672,676,680,684,688,689,690,691,694,697,700,703,707,708,709,710,711,714,716,718,720,725,726,730,736,740,741,743,754,755,759,765,769,770,771,775,802,806,807,811,839,1008,1034,1203,1229,1260,1268,1274,1288,1310,1315,1320,1330,1339,1348,1352,1359,1367,1374,1375,1384,1387,1390,1394,1398,1402,1405,1406,1411,1416,1426,1431,1438,1444,1445,1448,1452,1457,1459,1461,1464,1467,1469,1473,1476,1483,1486,1489,1493,1495,1499,1501,1503,1505,1509,1517,1525,1537,1543,1552,1555,1566,1569,1570,1575,1576,1581,1674,1744,1745,1755,1764,1765,1918,1922,1925,1928,1931,1934,1937,1940,1943,1947,1950,1953,1956,1960,1963,1967,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1993,1995,1996,1997,1998,1999,2000,2001,2002,2004,2005,2007,2008,2010,2012,2013,2015,2016,2017,2018,2019,2020,2022,2023,2024,2025,2026,2027,2045,2047,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2063,2064,2065,2066,2067,2068,2069,2071,2075,2079,2080,2081,2082,2083,2084,2088,2089,2090,2091,2093,2095,2097,2099,2101,2102,2103,2104,2106,2108,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2124,2125,2126,2127,2129,2131,2132,2134,2135,2137,2139,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2154,2155,2156,2157,2159,2160,2161,2162,2163,2165,2167,2169,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2266,2269,2272,2275,2289,2295,2316,2377,2404,2413,2475,2834,2843,2898,3026,3156,3162,3168,3212,3336,3356,3370,3374,3520,3575,3619,3744,3798,3853,3865,3891,3898", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1324,1980,2029,3068,3123,3182,3244,3702,3772,3833,3908,3984,4061,4139,4384,4466,4542,4618,4796,4874,4980,5086,5165,5245,5551,5609,6485,6560,6625,6691,6751,6812,6884,6957,7068,7136,7195,7254,7313,7372,7431,7485,7539,7592,7646,7700,7754,7808,8172,8251,8324,8398,8541,8613,8686,8743,8801,8948,9022,9097,9169,9242,9312,9383,9443,9504,9573,9642,9712,9786,9862,9926,10003,10079,10156,10221,10290,10367,10442,10511,10579,10656,10722,10783,10880,10945,11014,11113,11184,11243,11301,11358,11417,11481,11552,11624,11696,11768,11840,11907,11975,12043,12102,12165,12229,12319,12410,12470,12536,12603,12669,12739,12803,12856,12923,12984,13051,13164,13222,13285,13350,13415,13490,13563,13635,13684,13745,13806,13867,13929,13993,14057,14121,14186,14249,14309,14370,14436,14495,14555,14617,14688,14748,14816,15533,15620,15926,16013,16101,16183,16266,16356,16447,18439,18497,18542,18608,18672,18729,18786,18840,21020,21068,21117,21168,21406,21789,21838,22093,23250,23585,23647,23764,24102,24172,24250,24304,24374,24459,24507,24553,24614,24677,24743,24807,24878,24941,25006,25070,25131,25192,25244,25317,25391,25460,25535,25609,25683,25824,25894,31247,31633,31723,32111,32207,32297,32879,32968,33215,33496,33748,34033,34426,34903,35125,35347,35623,35850,36080,36310,36540,36770,36997,37416,37642,38067,38297,38725,38944,39227,39435,39566,39793,40219,40444,40871,41092,41517,41637,41913,42214,42538,42829,43143,43280,43411,43516,43758,43925,44129,44337,44608,44720,44832,44937,45054,45268,45414,45554,45640,45988,46076,46322,46740,46989,47071,47169,47761,47861,48113,48537,48792,48886,48975,49212,51236,51478,51580,51833,53989,64430,65946,76485,78013,79770,80396,80816,81877,83142,83398,83634,84181,84675,85280,85478,86058,86622,86997,87115,87653,87810,88006,88279,88535,88705,88846,88910,89275,89642,90318,90582,90920,91273,91367,91553,91859,92121,92246,92373,92612,92823,92942,93135,93312,93767,93948,94070,94329,94442,94629,94731,94838,94967,95242,95750,96246,97123,97417,97987,98136,98868,99040,99124,99460,99552,99830,106864,112253,112315,112893,113477,113568,121537,121766,121926,122078,122249,122415,122584,122751,122914,123157,123327,123500,123671,123945,124144,124349,124679,124763,124859,124955,125053,125153,125255,125357,125459,125561,125663,125763,125859,125971,126100,126223,126354,126485,126583,126697,126791,126931,127065,127161,127273,127373,127489,127585,127697,127797,127937,128073,128237,128367,128525,128675,128816,128960,129095,129207,129357,129485,129613,129749,129881,130011,130141,130253,130393,131679,131823,131961,132027,132117,132193,132297,132387,132489,132597,132705,132805,132885,132977,133075,133185,133237,133315,133421,133513,133617,133727,133849,134012,134169,134249,134349,134439,134549,134639,134880,134974,135080,135172,135272,135384,135498,135614,135730,135824,135938,136050,136152,136272,136394,136476,136580,136700,136826,136924,137018,137106,137218,137334,137456,137568,137743,137859,137945,138037,138149,138273,138340,138466,138534,138662,138806,138934,139003,139098,139213,139326,139425,139534,139645,139756,139857,139962,140062,140192,140283,140406,140500,140612,140698,140802,140898,140986,141104,141208,141312,141438,141526,141634,141734,141824,141934,142018,142120,142204,142258,142322,142428,142514,142624,142708,142828,145728,145846,145961,146041,146402,146635,147505,149736,151097,151485,154260,164164,164484,166746,172774,177254,177516,177716,178742,183020,183626,184103,184254,188926,190804,191985,197099,198941,201072,201412,202723,202926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f87704cc6ac259b753f491455f413615\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "365,366,371,378,379,398,399,400,401,402", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "21290,21330,21547,21885,21940,22957,23011,23063,23112,23173", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "21325,21372,21585,21935,21982,23006,23058,23107,23168,23218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\93eeca70efd8419049cd49df8af72af1\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "377,405", "startColumns": "4,4", "startOffsets": "21843,23298", "endColumns": "41,59", "endOffsets": "21880,23353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f84db7003533a22de0405c5251ecb704\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "163,169,175,312,313,314,315,411,2033,2035,2036,2041,2043", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7945,8403,8806,18180,18233,18286,18339,23652,130716,130892,131014,131276,131471", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "8029,8469,8874,18228,18281,18334,18387,23707,130777,131009,131070,131337,131533"}}, {"source": "D:\\projects\\12july\\athkar\\athkar_app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1599,1603", "startColumns": "4,4", "startOffsets": "101273,101454", "endLines": "1602,1605", "endColumns": "12,12", "endOffsets": "101449,101618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\79275990ee9dddfd68bc7c9d7157e0cd\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "274,275,276,286,287,288,368,3521", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "15625,15684,15732,16498,16573,16649,21411,188931", "endLines": "274,275,276,286,287,288,368,3540", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "15679,15727,15783,16568,16644,16716,21472,189721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f734b899c9b5bcf473e5c8a79b68b93\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "406", "startColumns": "4", "startOffsets": "23358", "endColumns": "53", "endOffsets": "23407"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7b33c4ac072486c90a47d13cee761d9b\\transformed\\jetified-play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "413,460", "startColumns": "4,4", "startOffsets": "23769,27533", "endColumns": "67,166", "endOffsets": "23832,27695"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\185f2479ab24942c0bba65b9ff947d79\\transformed\\jetified-appcompat-resources-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2317,2333,2339,3659,3675", "startColumns": "4,4,4,4,4", "startOffsets": "147510,147935,148113,193540,193951", "endLines": "2332,2338,2348,3674,3678", "endColumns": "24,24,24,24,24", "endOffsets": "147930,148108,148392,193946,194073"}}, {"source": "D:\\projects\\12july\\athkar\\athkar_app\\build\\local_auth_android\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,100,144,190,238", "endLines": "2,3,4,5,11", "endColumns": "44,43,45,47,10", "endOffsets": "95,139,185,233,533"}, "to": {"startLines": "115,146,285,289,509", "startColumns": "4,4,4,4,4", "startOffsets": "4679,6962,16452,16721,31728", "endLines": "115,146,285,289,514", "endColumns": "44,43,45,47,10", "endOffsets": "4719,7001,16493,16764,32023"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\197f12b192a3f06912c946d4cbd2dd7d\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "34,37,43,51,62,74,80,86,87,88,89,90,363,2296,2302,3620,3628,3643", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1154,1329,1502,1721,2094,2408,2596,2783,2836,2896,2948,2993,21173,146640,146835,191990,192272,192886", "endLines": "34,42,50,58,73,79,85,86,87,88,89,90,363,2301,2306,3627,3642,3658", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "1208,1497,1716,1935,2403,2591,2778,2831,2891,2943,2988,3027,21228,146830,146988,192267,192881,193535"}}]}]}