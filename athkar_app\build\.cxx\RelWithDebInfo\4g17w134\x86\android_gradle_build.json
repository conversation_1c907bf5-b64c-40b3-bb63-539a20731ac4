{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["d:\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\12july\\athkar\\athkar_app\\build\\.cxx\\RelWithDebInfo\\4g17w134\\x86", "clean"]], "buildTargetsCommandComponents": ["d:\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\12july\\athkar\\athkar_app\\build\\.cxx\\RelWithDebInfo\\4g17w134\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}