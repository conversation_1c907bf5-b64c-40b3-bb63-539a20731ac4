// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('Basic widget test', (WidgetTester tester) async {
    // Build a simple widget for testing
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          appBar: AppBar(title: const Text('Test')),
          body: const Center(
            child: Text('Hello World'),
          ),
        ),
      ),
    );

    // Verify that the widget loads successfully
    expect(find.text('Hello World'), findsOneWidget);
    expect(find.text('Test'), findsOneWidget);
  });

  testWidgets('Material app test', (WidgetTester tester) async {
    // Test basic Material app functionality
    await tester.pumpWidget(
      const MaterialApp(
        home: Text('Islamic App Test'),
      ),
    );

    expect(find.text('Islamic App Test'), findsOneWidget);
  });
}
