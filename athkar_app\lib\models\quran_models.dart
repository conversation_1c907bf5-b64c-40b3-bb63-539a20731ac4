import 'package:json_annotation/json_annotation.dart';

part 'quran_models.g.dart';

@JsonSerializable()
class Surah {
  final int id;
  final String name;
  @<PERSON>son<PERSON><PERSON>(name: 'name_arabic')
  final String arabicName;
  @J<PERSON><PERSON><PERSON>(name: 'name_simple')
  final String englishName;
  @J<PERSON><PERSON><PERSON>(name: 'verses_count')
  final int numberOfAyahs;
  @<PERSON>son<PERSON>ey(name: 'revelation_place')
  final String revelationPlace;

  Surah({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.englishName,
    required this.numberOfAyahs,
    required this.revelationPlace,
  });

  // Convenience getters for backward compatibility
  int get number => id;
  String get englishNameTranslation => englishName;
  String get revelationType => revelationPlace == 'makkah' ? 'Meccan' : 'Medinan';

  factory Surah.fromJson(Map<String, dynamic> json) => _$SurahFromJson(json);
  Map<String, dynamic> toJson() => _$SurahToJson(this);
}

@JsonSerializable()
class Ayah {
  final int id;
  @Json<PERSON>ey(name: 'verse_number')
  final int verseNumber;
  @JsonKey(name: 'text_uthmani')
  final String textUthmani;
  @JsonKey(name: 'text_simple')
  final String textSimple;
  final String? translation;
  final String? transliteration;
  @JsonKey(name: 'chapter_id')
  final int? chapterId;

  Ayah({
    required this.id,
    required this.verseNumber,
    required this.textUthmani,
    required this.textSimple,
    this.translation,
    this.transliteration,
    this.chapterId,
  });

  factory Ayah.fromJson(Map<String, dynamic> json) => _$AyahFromJson(json);
  Map<String, dynamic> toJson() => _$AyahToJson(this);
}

@JsonSerializable()
class Translation {
  final int id;
  final String name;
  @JsonKey(name: 'author_name')
  final String authorName;
  @JsonKey(name: 'language_name')
  final String languageName;
  final String slug;

  Translation({
    required this.id,
    required this.name,
    required this.authorName,
    required this.languageName,
    required this.slug,
  });

  factory Translation.fromJson(Map<String, dynamic> json) => _$TranslationFromJson(json);
  Map<String, dynamic> toJson() => _$TranslationToJson(this);
}

@JsonSerializable()
class VerseOfTheDay {
  final Ayah ayah;
  final Surah? surah;
  final DateTime date;

  VerseOfTheDay({
    required this.ayah,
    this.surah,
    required this.date,
  });

  factory VerseOfTheDay.fromJson(Map<String, dynamic> json) => _$VerseOfTheDayFromJson(json);
  Map<String, dynamic> toJson() => _$VerseOfTheDayToJson(this);
}

@JsonSerializable()
class SearchResult {
  final int id;
  @JsonKey(name: 'verse_number')
  final int verseNumber;
  @JsonKey(name: 'chapter_id')
  final int chapterId;
  final String text;
  final String? translation;
  final List<String> highlights;

  SearchResult({
    required this.id,
    required this.verseNumber,
    required this.chapterId,
    required this.text,
    this.translation,
    required this.highlights,
  });

  factory SearchResult.fromJson(Map<String, dynamic> json) => _$SearchResultFromJson(json);
  Map<String, dynamic> toJson() => _$SearchResultToJson(this);
}

@JsonSerializable()
class Tafsir {
  final int id;
  final String name;
  @JsonKey(name: 'author_name')
  final String authorName;
  final String text;
  @JsonKey(name: 'language_name')
  final String languageName;

  Tafsir({
    required this.id,
    required this.name,
    required this.authorName,
    required this.text,
    required this.languageName,
  });

  factory Tafsir.fromJson(Map<String, dynamic> json) => _$TafsirFromJson(json);
  Map<String, dynamic> toJson() => _$TafsirToJson(this);
}

@JsonSerializable()
class BookmarkedVerse {
  final int surahNumber;
  final int ayahNumber;
  final String note;
  final DateTime createdAt;
  final String? tags;

  BookmarkedVerse({
    required this.surahNumber,
    required this.ayahNumber,
    required this.note,
    required this.createdAt,
    this.tags,
  });

  factory BookmarkedVerse.fromJson(Map<String, dynamic> json) => _$BookmarkedVerseFromJson(json);
  Map<String, dynamic> toJson() => _$BookmarkedVerseToJson(this);
}

@JsonSerializable()
class ReadingProgress {
  final List<int> completedSurahs;
  final int currentSurah;
  final int currentAyah;
  final int totalAyahsRead;
  final DateTime lastReadDate;
  final Duration totalReadingTime;

  ReadingProgress({
    required this.completedSurahs,
    required this.currentSurah,
    required this.currentAyah,
    required this.totalAyahsRead,
    required this.lastReadDate,
    this.totalReadingTime = Duration.zero,
  });

  factory ReadingProgress.fromJson(Map<String, dynamic> json) => _$ReadingProgressFromJson(json);
  Map<String, dynamic> toJson() => _$ReadingProgressToJson(this);

  double get progressPercentage {
    // Total ayahs in Quran: 6236
    return (totalAyahsRead / 6236) * 100;
  }
}

@JsonSerializable()
class Reciter {
  final int id;
  final String name;
  @JsonKey(name: 'arabic_name')
  final String arabicName;
  final String style;
  @JsonKey(name: 'file_formats')
  final List<String> fileFormats;

  Reciter({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.style,
    required this.fileFormats,
  });

  factory Reciter.fromJson(Map<String, dynamic> json) => _$ReciterFromJson(json);
  Map<String, dynamic> toJson() => _$ReciterToJson(this);
}

@JsonSerializable()
class QuranSettings {
  final String preferredTranslation;
  final String preferredReciter;
  final double fontSize;
  final bool showTransliteration;
  final bool showTranslation;
  final bool autoPlay;
  final double playbackSpeed;
  final bool nightMode;

  QuranSettings({
    this.preferredTranslation = 'en.sahih',
    this.preferredReciter = 'ar.alafasy',
    this.fontSize = 18.0,
    this.showTransliteration = true,
    this.showTranslation = true,
    this.autoPlay = false,
    this.playbackSpeed = 1.0,
    this.nightMode = false,
  });

  factory QuranSettings.fromJson(Map<String, dynamic> json) => _$QuranSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$QuranSettingsToJson(this);

  QuranSettings copyWith({
    String? preferredTranslation,
    String? preferredReciter,
    double? fontSize,
    bool? showTransliteration,
    bool? showTranslation,
    bool? autoPlay,
    double? playbackSpeed,
    bool? nightMode,
  }) {
    return QuranSettings(
      preferredTranslation: preferredTranslation ?? this.preferredTranslation,
      preferredReciter: preferredReciter ?? this.preferredReciter,
      fontSize: fontSize ?? this.fontSize,
      showTransliteration: showTransliteration ?? this.showTransliteration,
      showTranslation: showTranslation ?? this.showTranslation,
      autoPlay: autoPlay ?? this.autoPlay,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
      nightMode: nightMode ?? this.nightMode,
    );
  }
}

@JsonSerializable()
class QuranStudySession {
  final String id;
  final DateTime startTime;
  final DateTime? endTime;
  final int surahNumber;
  final int startAyah;
  final int? endAyah;
  final Duration duration;
  final List<String> notes;
  final Map<String, dynamic> metadata;

  QuranStudySession({
    required this.id,
    required this.startTime,
    this.endTime,
    required this.surahNumber,
    required this.startAyah,
    this.endAyah,
    this.duration = Duration.zero,
    this.notes = const [],
    this.metadata = const {},
  });

  factory QuranStudySession.fromJson(Map<String, dynamic> json) => _$QuranStudySessionFromJson(json);
  Map<String, dynamic> toJson() => _$QuranStudySessionToJson(this);
}
