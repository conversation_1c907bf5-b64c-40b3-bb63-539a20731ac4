// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Security_EnterpriseData_H
#define WINRT_Windows_Security_EnterpriseData_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.210806.1"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.210806.1"
#include "winrt/impl/Windows.Security.EnterpriseData.2.h"
namespace winrt::impl
{
}
WINRT_EXPORT namespace winrt::Windows::Security::EnterpriseData
{
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
#endif
}
#endif
