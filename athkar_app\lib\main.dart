import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'config/supabase_config.dart';
import 'theme/app_theme.dart';
import 'screens/main_navigation.dart';
import 'screens/permissions_request_screen.dart';
import 'screens/splash_screen.dart';
import 'screens/create_routine_screen.dart';
import 'screens/athkar_screen.dart';
import 'screens/prayer_times_screen.dart';
import 'screens/qibla_screen.dart';
import 'screens/quran_screen.dart';
import 'screens/islamic_calendar_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/statistics_screen.dart';
import 'providers/auth_provider.dart';
import 'providers/athkar_provider.dart';
import 'providers/tasbeeh_dua_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/language_provider.dart';
import 'services/notification_service.dart';
import 'services/connectivity_service.dart';
import 'services/offline_queue_service.dart';
import 'services/supabase_setup_service.dart';
import 'services/realtime_sync_service.dart';
import 'services/auth_service.dart';
import 'services/audio_service.dart';
import 'services/permissions_service.dart';
import 'services/floating_window_service.dart';
import 'services/language_service.dart';
import 'providers/quran_provider.dart';
import 'l10n/app_localizations.dart';
import 'database/database_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize database factory for desktop platforms
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }

  // Initialize timezone data
  tz.initializeTimeZones();

  // Initialize Supabase
  await SupabaseConfig.initialize();

  // Initialize database
  final dbHelper = DatabaseHelper();
  await dbHelper.database; // This will initialize the database

  // Initialize services
  await NotificationService.initialize();
  await ConnectivityService.initialize();
  await OfflineQueueService.initialize();
  await AuthService.initialize();
  await SupabaseSetupService.setupFromScratch();
  await RealtimeSyncService.initialize();

  // Initialize notification and audio services
  await PermissionsService.initialize();
  await AudioService.initialize();
  await FloatingWindowService.initialize();

  // Initialize language service
  await LanguageService().initialize();

  runApp(const AthkarApp());
}

class AthkarApp extends StatelessWidget {
  const AthkarApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => AuthProvider()..initialize()),
        ChangeNotifierProvider(create: (_) => AthkarProvider()),
        ChangeNotifierProvider(create: (_) => TasbeehDuaProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()..initialize()),
        ChangeNotifierProvider(create: (_) => LanguageService()),
        ChangeNotifierProvider<QuranService>(create: (_) => QuranService()),
      ],
      child: Consumer3<ThemeProvider, LanguageProvider, LanguageService>(
        builder: (context, themeProvider, languageProvider, languageService, child) {
          return MaterialApp(
            title: 'Athkar - Islamic Remembrance',
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.materialThemeMode,
            locale: languageService.currentLocale,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],

            home: const SplashScreen(),
            debugShowCheckedModeBanner: false,
            routes: {
              '/permissions': (context) => const PermissionsRequestScreen(),
              '/home': (context) => const MainNavigation(),
              '/create-routine': (context) => const CreateRoutineScreen(),
              '/athkar': (context) => const AthkarScreen(),
              '/prayer-times': (context) => const PrayerTimesScreen(),
              '/qibla': (context) => const QiblaScreen(),
              '/quran': (context) => const QuranScreen(),
              '/islamic-calendar': (context) => const IslamicCalendarScreen(),
              '/settings': (context) => const SettingsScreen(),
              '/statistics': (context) => const StatisticsScreen(),
            },
          );
        },
      ),
    );
  }
}
