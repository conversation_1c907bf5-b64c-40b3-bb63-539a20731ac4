import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'config/supabase_config.dart';
import 'theme/app_theme.dart';
import 'screens/main_navigation.dart';
import 'screens/create_routine_screen.dart';
import 'screens/athkar_screen.dart';
import 'screens/prayer_times_screen.dart';
import 'screens/qibla_screen.dart';
import 'screens/quran_screen.dart';
import 'screens/islamic_calendar_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/statistics_screen.dart';
import 'providers/auth_provider.dart';
import 'providers/athkar_provider.dart';
import 'providers/tasbeeh_dua_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/language_provider.dart';
import 'services/notification_service.dart';
import 'services/connectivity_service.dart';
import 'services/offline_queue_service.dart';
import 'services/supabase_sync_service.dart';
import 'services/auth_service.dart';
import 'database/database_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize database factory for desktop platforms
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }

  // Initialize timezone data
  tz.initializeTimeZones();

  // Initialize Supabase
  await SupabaseConfig.initialize();

  // Initialize database
  final dbHelper = DatabaseHelper();
  await dbHelper.database; // This will initialize the database

  // Initialize services
  await NotificationService.initialize();
  await ConnectivityService.initialize();
  await OfflineQueueService.initialize();
  await AuthService.initialize();
  await SupabaseSyncService.initialize();

  runApp(const AthkarApp());
}

class AthkarApp extends StatelessWidget {
  const AthkarApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => AuthProvider()..initialize()),
        ChangeNotifierProvider(create: (_) => AthkarProvider()),
        ChangeNotifierProvider(create: (_) => TasbeehDuaProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()..initialize()),
      ],
      child: Consumer2<ThemeProvider, LanguageProvider>(
        builder: (context, themeProvider, languageProvider, child) {
          return MaterialApp(
            title: 'Athkar - Islamic Remembrance',
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.materialThemeMode,
            locale: languageProvider.currentLocale,
            supportedLocales: const [
              Locale('ar', ''), // Arabic
              Locale('en', ''), // English
            ],
            localizationsDelegates: [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            home: const MainNavigation(),
            debugShowCheckedModeBanner: false,
            routes: {
              '/create-routine': (context) => const CreateRoutineScreen(),
              '/athkar': (context) => const AthkarScreen(),
              '/prayer-times': (context) => const PrayerTimesScreen(),
              '/qibla': (context) => const QiblaScreen(),
              '/quran': (context) => const QuranScreen(),
              '/islamic-calendar': (context) => const IslamicCalendarScreen(),
              '/settings': (context) => const SettingsScreen(),
              '/statistics': (context) => const StatisticsScreen(),
            },
          );
        },
      ),
    );
  }
}
