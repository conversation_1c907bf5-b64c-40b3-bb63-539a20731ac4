import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

/// Service for managing authentic Islamic content including duas, athkar, and tasbeeh
/// All content is verified from authentic Islamic sources
class AuthenticIslamicContentService {
  static final AuthenticIslamicContentService _instance = AuthenticIslamicContentService._internal();
  factory AuthenticIslamicContentService() => _instance;
  AuthenticIslamicContentService._internal();

  List<IslamicContent> _morningAthkar = [];
  List<IslamicContent> _eveningAthkar = [];
  List<IslamicContent> _sleepDuas = [];
  List<IslamicContent> _foodDuas = [];
  List<IslamicContent> _travelDuas = [];
  List<IslamicContent> _generalDuas = [];
  List<IslamicContent> _tasbeehPhrases = [];
  List<IslamicContent> _quranDuas = [];
  List<IslamicContent> _propheticDuas = [];
  
  bool _isInitialized = false;

  /// Initialize the service with authentic Islamic content
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadMorningAthkar();
      await _loadEveningAthkar();
      await _loadSleepDuas();
      await _loadFoodDuas();
      await _loadTravelDuas();
      await _loadGeneralDuas();
      await _loadTasbeehPhrases();
      await _loadQuranDuas();
      await _loadPropheticDuas();
      
      _isInitialized = true;
      debugPrint('Authentic Islamic Content Service initialized with ${getTotalContentCount()} items');
    } catch (e) {
      debugPrint('Error initializing Authentic Islamic Content Service: $e');
      rethrow;
    }
  }

  /// Get total count of all Islamic content
  int getTotalContentCount() {
    return _morningAthkar.length + 
           _eveningAthkar.length + 
           _sleepDuas.length + 
           _foodDuas.length + 
           _travelDuas.length + 
           _generalDuas.length + 
           _tasbeehPhrases.length + 
           _quranDuas.length + 
           _propheticDuas.length;
  }

  /// Get morning athkar (أذكار الصباح)
  List<IslamicContent> getMorningAthkar() => List.unmodifiable(_morningAthkar);

  /// Get evening athkar (أذكار المساء)
  List<IslamicContent> getEveningAthkar() => List.unmodifiable(_eveningAthkar);

  /// Get sleep duas (أدعية النوم)
  List<IslamicContent> getSleepDuas() => List.unmodifiable(_sleepDuas);

  /// Get food duas (أدعية الطعام)
  List<IslamicContent> getFoodDuas() => List.unmodifiable(_foodDuas);

  /// Get travel duas (أدعية السفر)
  List<IslamicContent> getTravelDuas() => List.unmodifiable(_travelDuas);

  /// Get general duas (الأدعية العامة)
  List<IslamicContent> getGeneralDuas() => List.unmodifiable(_generalDuas);

  /// Get tasbeeh phrases (عبارات التسبيح)
  List<IslamicContent> getTasbeehPhrases() => List.unmodifiable(_tasbeehPhrases);

  /// Get Quranic duas (الأدعية القرآنية)
  List<IslamicContent> getQuranDuas() => List.unmodifiable(_quranDuas);

  /// Get Prophetic duas (الأدعية النبوية)
  List<IslamicContent> getPropheticDuas() => List.unmodifiable(_propheticDuas);

  /// Get all content by category
  List<IslamicContent> getContentByCategory(IslamicContentCategory category) {
    switch (category) {
      case IslamicContentCategory.morningAthkar:
        return getMorningAthkar();
      case IslamicContentCategory.eveningAthkar:
        return getEveningAthkar();
      case IslamicContentCategory.sleepDuas:
        return getSleepDuas();
      case IslamicContentCategory.foodDuas:
        return getFoodDuas();
      case IslamicContentCategory.travelDuas:
        return getTravelDuas();
      case IslamicContentCategory.generalDuas:
        return getGeneralDuas();
      case IslamicContentCategory.tasbeeh:
        return getTasbeehPhrases();
      case IslamicContentCategory.quranDuas:
        return getQuranDuas();
      case IslamicContentCategory.propheticDuas:
        return getPropheticDuas();
    }
  }

  /// Search content across all categories
  List<IslamicContent> searchContent(String query) {
    if (query.isEmpty) return [];

    final allContent = [
      ..._morningAthkar,
      ..._eveningAthkar,
      ..._sleepDuas,
      ..._foodDuas,
      ..._travelDuas,
      ..._generalDuas,
      ..._tasbeehPhrases,
      ..._quranDuas,
      ..._propheticDuas,
    ];

    return allContent.where((content) {
      return content.arabicText.contains(query) ||
             content.transliteration.toLowerCase().contains(query.toLowerCase()) ||
             content.translation.toLowerCase().contains(query.toLowerCase()) ||
             content.benefits.any((benefit) => benefit.toLowerCase().contains(query.toLowerCase()));
    }).toList();
  }

  /// Get random content from a category
  IslamicContent? getRandomContent(IslamicContentCategory category) {
    final content = getContentByCategory(category);
    if (content.isEmpty) return null;
    
    final random = DateTime.now().millisecondsSinceEpoch % content.length;
    return content[random];
  }

  /// Load morning athkar from authentic sources
  Future<void> _loadMorningAthkar() async {
    _morningAthkar = [
      // Basic morning athkar
      IslamicContent(
        id: 'morning_001',
        arabicText: 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
        transliteration: 'A\'udhu billahi min ash-shaytani\'r-rajim',
        translation: 'I seek refuge in Allah from Satan, the accursed one',
        category: IslamicContentCategory.morningAthkar,
        source: 'القرآن الكريم',
        repetitions: 1,
        benefits: ['Protection from Satan', 'Spiritual purification'],
        audioPath: 'assets/audio/morning/001.mp3',
      ),
      IslamicContent(
        id: 'morning_002',
        arabicText: 'بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ',
        transliteration: 'Bismillahi\'r-rahmani\'r-rahim',
        translation: 'In the name of Allah, the Most Gracious, the Most Merciful',
        category: IslamicContentCategory.morningAthkar,
        source: 'القرآن الكريم',
        repetitions: 1,
        benefits: ['Blessing in all actions', 'Divine protection'],
        audioPath: 'assets/audio/morning/002.mp3',
      ),
      IslamicContent(
        id: 'morning_003',
        arabicText: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        transliteration: 'Alhamdu lillahi rabbi\'l-\'alamin',
        translation: 'All praise is due to Allah, Lord of all the worlds',
        category: IslamicContentCategory.morningAthkar,
        source: 'القرآن الكريم - سورة الفاتحة',
        repetitions: 3,
        benefits: ['Gratitude to Allah', 'Spiritual elevation'],
        audioPath: 'assets/audio/morning/003.mp3',
      ),
      IslamicContent(
        id: 'morning_004',
        arabicText: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
        transliteration: 'Subhan Allahi wa bihamdihi',
        translation: 'Glory be to Allah and praise be to Him',
        category: IslamicContentCategory.morningAthkar,
        source: 'صحيح البخاري',
        repetitions: 100,
        benefits: ['Forgiveness of sins', 'Reward equivalent to freeing a slave'],
        audioPath: 'assets/audio/morning/004.mp3',
      ),
      IslamicContent(
        id: 'morning_005',
        arabicText: 'لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
        transliteration: 'La ilaha illa Allah wahdahu la sharika lahu, lahu\'l-mulku wa lahu\'l-hamdu wa huwa \'ala kulli shay\'in qadir',
        translation: 'There is no god but Allah alone, with no partner. His is the dominion and His is the praise, and He is able to do all things',
        category: IslamicContentCategory.morningAthkar,
        source: 'صحيح البخاري ومسلم',
        repetitions: 10,
        benefits: ['Equivalent to freeing 10 slaves', 'Protection from Satan'],
        audioPath: 'assets/audio/morning/005.mp3',
      ),
      // Additional comprehensive morning athkar
      IslamicContent(
        id: 'morning_006',
        arabicText: 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ',
        transliteration: 'Asbahna wa asbaha\'l-mulku lillah, wa\'l-hamdu lillah, la ilaha illa Allah wahdahu la sharika lah',
        translation: 'We have entered the morning and the dominion belongs to Allah, and praise belongs to Allah. There is no god but Allah alone, with no partner',
        category: IslamicContentCategory.morningAthkar,
        source: 'صحيح مسلم',
        repetitions: 1,
        benefits: ['Morning protection', 'Acknowledgment of Allah\'s sovereignty'],
        audioPath: 'assets/audio/morning/006.mp3',
      ),
      IslamicContent(
        id: 'morning_007',
        arabicText: 'اللَّهُمَّ بِكَ أَصْبَحْنَا، وَبِكَ أَمْسَيْنَا، وَبِكَ نَحْيَا، وَبِكَ نَمُوتُ، وَإِلَيْكَ النُّشُورُ',
        transliteration: 'Allahumma bika asbahna, wa bika amsayna, wa bika nahya, wa bika namutu, wa ilayka\'n-nushur',
        translation: 'O Allah, by You we have entered the morning, by You we have entered the evening, by You we live, by You we die, and to You is the resurrection',
        category: IslamicContentCategory.morningAthkar,
        source: 'سنن الترمذي',
        repetitions: 1,
        benefits: ['Complete reliance on Allah', 'Remembrance of the afterlife'],
        audioPath: 'assets/audio/morning/007.mp3',
      ),
      IslamicContent(
        id: 'morning_008',
        arabicText: 'اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَهَ إِلَّا أَنْتَ، خَلَقْتَنِي وَأَنَا عَبْدُكَ',
        transliteration: 'Allahumma anta rabbi la ilaha illa ant, khalaqtani wa ana \'abduk',
        translation: 'O Allah, You are my Lord, there is no god but You. You created me and I am Your servant',
        category: IslamicContentCategory.morningAthkar,
        source: 'صحيح البخاري',
        repetitions: 1,
        benefits: ['Reaffirmation of faith', 'Recognition of servitude to Allah'],
        audioPath: 'assets/audio/morning/008.mp3',
      ),
      IslamicContent(
        id: 'morning_009',
        arabicText: 'رَضِيتُ بِاللَّهِ رَبًّا، وَبِالْإِسْلَامِ دِينًا، وَبِمُحَمَّدٍ رَسُولًا',
        transliteration: 'Raditu billahi rabban, wa bil-Islami dinan, wa bi Muhammadin rasulan',
        translation: 'I am pleased with Allah as my Lord, Islam as my religion, and Muhammad as my messenger',
        category: IslamicContentCategory.morningAthkar,
        source: 'سنن أبي داود',
        repetitions: 3,
        benefits: ['Contentment with faith', 'Allah\'s pleasure and paradise'],
        audioPath: 'assets/audio/morning/009.mp3',
      ),
      IslamicContent(
        id: 'morning_010',
        arabicText: 'اللَّهُمَّ عَافِنِي فِي بَدَنِي، اللَّهُمَّ عَافِنِي فِي سَمْعِي، اللَّهُمَّ عَافِنِي فِي بَصَرِي',
        transliteration: 'Allahumma \'afini fi badani, Allahumma \'afini fi sam\'i, Allahumma \'afini fi basari',
        translation: 'O Allah, grant me health in my body. O Allah, grant me health in my hearing. O Allah, grant me health in my sight',
        category: IslamicContentCategory.morningAthkar,
        source: 'سنن أبي داود',
        repetitions: 3,
        benefits: ['Physical health and well-being', 'Protection of senses'],
        audioPath: 'assets/audio/morning/010.mp3',
      ),
    ];
  }

  /// Load evening athkar from authentic sources
  Future<void> _loadEveningAthkar() async {
    _eveningAthkar = [
      IslamicContent(
        id: 'evening_001',
        arabicText: 'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ',
        transliteration: 'Amsayna wa amsa\'l-mulku lillah, wa\'l-hamdu lillah',
        translation: 'We have entered the evening and the dominion belongs to Allah, and praise belongs to Allah',
        category: IslamicContentCategory.eveningAthkar,
        source: 'صحيح مسلم',
        repetitions: 1,
        benefits: ['Evening protection', 'Acknowledgment of Allah\'s sovereignty'],
        audioPath: 'assets/audio/evening/001.mp3',
      ),
      IslamicContent(
        id: 'evening_002',
        arabicText: 'اللَّهُمَّ بِكَ أَمْسَيْنَا، وَبِكَ أَصْبَحْنَا، وَبِكَ نَحْيَا، وَبِكَ نَمُوتُ، وَإِلَيْكَ النُّشُورُ',
        transliteration: 'Allahumma bika amsayna, wa bika asbahna, wa bika nahya, wa bika namutu, wa ilayka\'n-nushur',
        translation: 'O Allah, by You we have entered the evening, by You we have entered the morning, by You we live, by You we die, and to You is the resurrection',
        category: IslamicContentCategory.eveningAthkar,
        source: 'سنن الترمذي',
        repetitions: 1,
        benefits: ['Complete reliance on Allah', 'Remembrance of the afterlife'],
        audioPath: 'assets/audio/evening/002.mp3',
      ),
      IslamicContent(
        id: 'evening_003',
        arabicText: 'أَعُوذُ بِكَلِمَاتِ اللَّهِ التَّامَّاتِ مِنْ شَرِّ مَا خَلَقَ',
        transliteration: 'A\'udhu bi kalimat Allah at-tammati min sharri ma khalaq',
        translation: 'I seek refuge in the perfect words of Allah from the evil of what He has created',
        category: IslamicContentCategory.eveningAthkar,
        source: 'صحيح مسلم',
        repetitions: 3,
        benefits: ['Protection from all evil', 'Safety from harmful creatures'],
        audioPath: 'assets/audio/evening/003.mp3',
      ),
      IslamicContent(
        id: 'evening_004',
        arabicText: 'اللَّهُمَّ عَالِمَ الْغَيْبِ وَالشَّهَادَةِ فَاطِرَ السَّمَاوَاتِ وَالْأَرْضِ',
        transliteration: 'Allahumma \'alim al-ghaybi wa\'sh-shahadati fatir as-samawati wa\'l-ard',
        translation: 'O Allah, Knower of the unseen and the seen, Creator of the heavens and the earth',
        category: IslamicContentCategory.eveningAthkar,
        source: 'سنن أبي داود والترمذي',
        repetitions: 1,
        benefits: ['Recognition of Allah\'s knowledge', 'Spiritual connection'],
        audioPath: 'assets/audio/evening/004.mp3',
      ),
      IslamicContent(
        id: 'evening_005',
        arabicText: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ مِنْ خَيْرِ هَذِهِ اللَّيْلَةِ وَخَيْرِ مَا فِيهَا',
        transliteration: 'Allahumma inni as\'aluka min khayri hadhihi\'l-laylati wa khayri ma fiha',
        translation: 'O Allah, I ask You for the good of this night and the good that is in it',
        category: IslamicContentCategory.eveningAthkar,
        source: 'صحيح مسلم',
        repetitions: 1,
        benefits: ['Seeking good in the night', 'Protection from night\'s evil'],
        audioPath: 'assets/audio/evening/005.mp3',
      ),
      IslamicContent(
        id: 'evening_006',
        arabicText: 'حَسْبِيَ اللَّهُ لَا إِلَهَ إِلَّا هُوَ عَلَيْهِ تَوَكَّلْتُ وَهُوَ رَبُّ الْعَرْشِ الْعَظِيمِ',
        transliteration: 'Hasbi Allah la ilaha illa huwa \'alayhi tawakkaltu wa huwa rabb al-\'arsh al-\'azim',
        translation: 'Allah is sufficient for me. There is no god but He. In Him I put my trust, and He is the Lord of the mighty throne',
        category: IslamicContentCategory.eveningAthkar,
        source: 'القرآن الكريم - سورة التوبة',
        repetitions: 7,
        benefits: ['Complete trust in Allah', 'Protection from all worries'],
        audioPath: 'assets/audio/evening/006.mp3',
      ),
      IslamicContent(
        id: 'evening_007',
        arabicText: 'اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَهَ إِلَّا أَنْتَ، عَلَيْكَ تَوَكَّلْتُ وَأَنْتَ رَبُّ الْعَرْشِ الْعَظِيمِ',
        transliteration: 'Allahumma anta rabbi la ilaha illa ant, \'alayka tawakkaltu wa anta rabb al-\'arsh al-\'azim',
        translation: 'O Allah, You are my Lord, there is no god but You. In You I put my trust, and You are the Lord of the mighty throne',
        category: IslamicContentCategory.eveningAthkar,
        source: 'سنن أبي داود',
        repetitions: 1,
        benefits: ['Strengthening of faith', 'Divine protection'],
        audioPath: 'assets/audio/evening/007.mp3',
      ),
      IslamicContent(
        id: 'evening_008',
        arabicText: 'اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنَ الْهَمِّ وَالْحَزَنِ، وَأَعُوذُ بِكَ مِنَ الْعَجْزِ وَالْكَسَلِ',
        transliteration: 'Allahumma inni a\'udhu bika min al-hammi wa\'l-hazan, wa a\'udhu bika min al-\'ajzi wa\'l-kasal',
        translation: 'O Allah, I seek refuge in You from worry and grief, and I seek refuge in You from incapacity and laziness',
        category: IslamicContentCategory.eveningAthkar,
        source: 'صحيح البخاري',
        repetitions: 1,
        benefits: ['Relief from anxiety', 'Protection from negative traits'],
        audioPath: 'assets/audio/evening/008.mp3',
      ),
    ];
  }

  /// Load sleep duas from authentic sources
  Future<void> _loadSleepDuas() async {
    _sleepDuas = [
      IslamicContent(
        id: 'sleep_001',
        arabicText: 'بِاسْمِكَ اللَّهُمَّ أَمُوتُ وَأَحْيَا',
        transliteration: 'Bismika Allahumma amutu wa ahya',
        translation: 'In Your name, O Allah, I die and I live',
        category: IslamicContentCategory.sleepDuas,
        source: 'صحيح البخاري',
        repetitions: 1,
        benefits: ['Peaceful sleep', 'Protection during sleep'],
        audioPath: 'assets/audio/sleep/001.mp3',
      ),
      IslamicContent(
        id: 'sleep_002',
        arabicText: 'اللَّهُمَّ قِنِي عَذَابَكَ يَوْمَ تَبْعَثُ عِبَادَكَ',
        transliteration: 'Allahumma qini \'adhabaka yawma tab\'athu \'ibadak',
        translation: 'O Allah, protect me from Your punishment on the day You resurrect Your servants',
        category: IslamicContentCategory.sleepDuas,
        source: 'سنن أبي داود والترمذي',
        repetitions: 3,
        benefits: ['Protection from punishment', 'Preparation for afterlife'],
        audioPath: 'assets/audio/sleep/002.mp3',
      ),
      IslamicContent(
        id: 'sleep_003',
        arabicText: 'اللَّهُمَّ أَسْلَمْتُ نَفْسِي إِلَيْكَ، وَفَوَّضْتُ أَمْرِي إِلَيْكَ',
        transliteration: 'Allahumma aslamtu nafsi ilayk, wa fawwadtu amri ilayk',
        translation: 'O Allah, I have surrendered myself to You and entrusted my affairs to You',
        category: IslamicContentCategory.sleepDuas,
        source: 'صحيح البخاري ومسلم',
        repetitions: 1,
        benefits: ['Complete trust in Allah', 'Peaceful surrender'],
        audioPath: 'assets/audio/sleep/003.mp3',
      ),
      IslamicContent(
        id: 'sleep_004',
        arabicText: 'سُبْحَانَ اللَّهِ',
        transliteration: 'Subhan Allah',
        translation: 'Glory be to Allah',
        category: IslamicContentCategory.sleepDuas,
        source: 'صحيح البخاري ومسلم',
        repetitions: 33,
        benefits: ['Spiritual purification before sleep', 'Divine protection'],
        audioPath: 'assets/audio/sleep/004.mp3',
      ),
      IslamicContent(
        id: 'sleep_005',
        arabicText: 'الْحَمْدُ لِلَّهِ',
        transliteration: 'Alhamdu lillah',
        translation: 'All praise is due to Allah',
        category: IslamicContentCategory.sleepDuas,
        source: 'صحيح البخاري ومسلم',
        repetitions: 33,
        benefits: ['Gratitude before rest', 'Blessed sleep'],
        audioPath: 'assets/audio/sleep/005.mp3',
      ),
      IslamicContent(
        id: 'sleep_006',
        arabicText: 'اللَّهُ أَكْبَرُ',
        transliteration: 'Allahu akbar',
        translation: 'Allah is the Greatest',
        category: IslamicContentCategory.sleepDuas,
        source: 'صحيح البخاري ومسلم',
        repetitions: 34,
        benefits: ['Magnification of Allah', 'Strength of faith'],
        audioPath: 'assets/audio/sleep/006.mp3',
      ),
    ];
  }

  /// Load food duas from authentic sources
  Future<void> _loadFoodDuas() async {
    _foodDuas = [
      IslamicContent(
        id: 'food_001',
        arabicText: 'بِسْمِ اللَّهِ',
        transliteration: 'Bismillah',
        translation: 'In the name of Allah',
        category: IslamicContentCategory.foodDuas,
        source: 'سنن أبي داود',
        repetitions: 1,
        benefits: ['Blessing in food', 'Protection from harm'],
        audioPath: 'assets/audio/food/001.mp3',
      ),
      IslamicContent(
        id: 'food_002',
        arabicText: 'الْحَمْدُ لِلَّهِ الَّذِي أَطْعَمَنَا وَسَقَانَا وَجَعَلَنَا مُسْلِمِينَ',
        transliteration: 'Alhamdu lillahi\'lladhi at\'amana wa saqana wa ja\'alana muslimin',
        translation: 'All praise is due to Allah who fed us and gave us drink and made us Muslims',
        category: IslamicContentCategory.foodDuas,
        source: 'سنن أبي داود والترمذي',
        repetitions: 1,
        benefits: ['Gratitude for sustenance', 'Recognition of Islamic identity'],
        audioPath: 'assets/audio/food/002.mp3',
      ),
      IslamicContent(
        id: 'food_003',
        arabicText: 'اللَّهُمَّ بَارِكْ لَنَا فِيمَا رَزَقْتَنَا وَقِنَا عَذَابَ النَّارِ',
        transliteration: 'Allahumma barik lana fima razaqtana wa qina \'adhab an-nar',
        translation: 'O Allah, bless for us what You have provided us and save us from the punishment of the Fire',
        category: IslamicContentCategory.foodDuas,
        source: 'سنن الترمذي',
        repetitions: 1,
        benefits: ['Blessing in sustenance', 'Protection from hellfire'],
        audioPath: 'assets/audio/food/003.mp3',
      ),
      IslamicContent(
        id: 'food_004',
        arabicText: 'بِسْمِ اللَّهِ وَبَرَكَةِ اللَّهِ',
        transliteration: 'Bismillahi wa barakati Allah',
        translation: 'In the name of Allah and with the blessing of Allah',
        category: IslamicContentCategory.foodDuas,
        source: 'سنن ابن ماجه',
        repetitions: 1,
        benefits: ['Divine blessing', 'Sanctification of food'],
        audioPath: 'assets/audio/food/004.mp3',
      ),
      IslamicContent(
        id: 'food_005',
        arabicText: 'اللَّهُمَّ أَطْعِمْ مَنْ أَطْعَمَنِي وَاسْقِ مَنْ سَقَانِي',
        transliteration: 'Allahumma at\'im man at\'amani wa\'sqi man saqani',
        translation: 'O Allah, feed the one who fed me and give drink to the one who gave me drink',
        category: IslamicContentCategory.foodDuas,
        source: 'صحيح مسلم',
        repetitions: 1,
        benefits: ['Reciprocal blessing', 'Gratitude to hosts'],
        audioPath: 'assets/audio/food/005.mp3',
      ),
    ];
  }

  /// Load travel duas from authentic sources
  Future<void> _loadTravelDuas() async {
    _travelDuas = [
      IslamicContent(
        id: 'travel_001',
        arabicText: 'سُبْحَانَ الَّذِي سَخَّرَ لَنَا هَذَا وَمَا كُنَّا لَهُ مُقْرِنِينَ',
        transliteration: 'Subhan alladhi sakhkhara lana hadha wa ma kunna lahu muqrinin',
        translation: 'Glory be to Him who has subjected this to us, and we could never have it (by our efforts)',
        category: IslamicContentCategory.travelDuas,
        source: 'القرآن الكريم - سورة الزخرف',
        repetitions: 1,
        benefits: ['Safe travel', 'Protection during journey'],
        audioPath: 'assets/audio/travel/001.mp3',
      ),
      IslamicContent(
        id: 'travel_002',
        arabicText: 'اللَّهُمَّ إِنَّا نَسْأَلُكَ فِي سَفَرِنَا هَذَا الْبِرَّ وَالتَّقْوَى',
        transliteration: 'Allahumma inna nas\'aluka fi safarina hadha\'l-birra wa\'t-taqwa',
        translation: 'O Allah, we ask You in this journey of ours for righteousness and piety',
        category: IslamicContentCategory.travelDuas,
        source: 'سنن الترمذي',
        repetitions: 1,
        benefits: ['Righteous journey', 'Spiritual growth during travel'],
        audioPath: 'assets/audio/travel/002.mp3',
      ),
      IslamicContent(
        id: 'travel_003',
        arabicText: 'اللَّهُمَّ اطْوِ لَنَا الْأَرْضَ وَهَوِّنْ عَلَيْنَا السَّفَرَ',
        transliteration: 'Allahumma\'twi lana\'l-arda wa hawwin \'alayna\'s-safar',
        translation: 'O Allah, make the earth easy for us to traverse and make the journey easy for us',
        category: IslamicContentCategory.travelDuas,
        source: 'سنن الترمذي',
        repetitions: 1,
        benefits: ['Easy journey', 'Shortened distances'],
        audioPath: 'assets/audio/travel/003.mp3',
      ),
      IslamicContent(
        id: 'travel_004',
        arabicText: 'اللَّهُمَّ أَنْتَ الصَّاحِبُ فِي السَّفَرِ وَالْخَلِيفَةُ فِي الْأَهْلِ',
        transliteration: 'Allahumma anta\'s-sahibu fi\'s-safari wa\'l-khalifatu fi\'l-ahl',
        translation: 'O Allah, You are the Companion in the journey and the Guardian of the family',
        category: IslamicContentCategory.travelDuas,
        source: 'صحيح مسلم',
        repetitions: 1,
        benefits: ['Divine companionship', 'Family protection'],
        audioPath: 'assets/audio/travel/004.mp3',
      ),
      IslamicContent(
        id: 'travel_005',
        arabicText: 'اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنْ وَعْثَاءِ السَّفَرِ وَكَآبَةِ الْمَنْظَرِ',
        transliteration: 'Allahumma inni a\'udhu bika min wa\'tha\'i\'s-safari wa ka\'abati\'l-manzar',
        translation: 'O Allah, I seek refuge in You from the hardships of travel and from the distressing sights',
        category: IslamicContentCategory.travelDuas,
        source: 'صحيح مسلم',
        repetitions: 1,
        benefits: ['Protection from travel hardships', 'Pleasant journey'],
        audioPath: 'assets/audio/travel/005.mp3',
      ),
    ];
  }

  /// Load general duas from authentic sources
  Future<void> _loadGeneralDuas() async {
    _generalDuas = [
      IslamicContent(
        id: 'general_001',
        arabicText: 'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ',
        transliteration: 'Rabbana atina fi\'d-dunya hasanatan wa fi\'l-akhirati hasanatan wa qina \'adhab an-nar',
        translation: 'Our Lord, give us good in this world and good in the next world, and save us from the punishment of the Fire',
        category: IslamicContentCategory.generalDuas,
        source: 'القرآن الكريم - سورة البقرة',
        repetitions: 1,
        benefits: ['Comprehensive dua for this life and the hereafter'],
        audioPath: 'assets/audio/general/001.mp3',
      ),
      IslamicContent(
        id: 'general_002',
        arabicText: 'رَبِّ اغْفِرْ لِي ذَنْبِي وَخَطَئِي وَجَهْلِي',
        transliteration: 'Rabbi\'ghfir li dhanbi wa khata\'i wa jahli',
        translation: 'My Lord, forgive me my sin, my error, and my ignorance',
        category: IslamicContentCategory.generalDuas,
        source: 'صحيح البخاري ومسلم',
        repetitions: 1,
        benefits: ['Forgiveness of sins', 'Spiritual purification'],
        audioPath: 'assets/audio/general/002.mp3',
      ),
      IslamicContent(
        id: 'general_003',
        arabicText: 'اللَّهُمَّ أَصْلِحْ لِي دِينِي الَّذِي هُوَ عِصْمَةُ أَمْرِي',
        transliteration: 'Allahumma aslih li dini\'lladhi huwa \'ismatu amri',
        translation: 'O Allah, set right for me my religion which is the safeguard of my affairs',
        category: IslamicContentCategory.generalDuas,
        source: 'صحيح مسلم',
        repetitions: 1,
        benefits: ['Improvement in religious practice', 'Spiritual guidance'],
        audioPath: 'assets/audio/general/003.mp3',
      ),
      IslamicContent(
        id: 'general_004',
        arabicText: 'اللَّهُمَّ اهْدِنِي فِيمَنْ هَدَيْتَ وَعَافِنِي فِيمَنْ عَافَيْتَ',
        transliteration: 'Allahumma\'hdini fiman hadayt wa \'afini fiman \'afayt',
        translation: 'O Allah, guide me among those You have guided and grant me health among those You have granted health',
        category: IslamicContentCategory.generalDuas,
        source: 'سنن أبي داود والترمذي',
        repetitions: 1,
        benefits: ['Divine guidance', 'Health and well-being'],
        audioPath: 'assets/audio/general/004.mp3',
      ),
      IslamicContent(
        id: 'general_005',
        arabicText: 'رَبَّنَا لَا تُزِغْ قُلُوبَنَا بَعْدَ إِذْ هَدَيْتَنَا وَهَبْ لَنَا مِنْ لَدُنْكَ رَحْمَةً',
        transliteration: 'Rabbana la tuzigh qulubana ba\'da idh hadaytana wa hab lana min ladunka rahmah',
        translation: 'Our Lord, do not let our hearts deviate after You have guided us and grant us from Yourself mercy',
        category: IslamicContentCategory.generalDuas,
        source: 'القرآن الكريم - سورة آل عمران',
        repetitions: 1,
        benefits: ['Steadfastness in faith', 'Divine mercy'],
        audioPath: 'assets/audio/general/005.mp3',
      ),
      IslamicContent(
        id: 'general_006',
        arabicText: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ الْهُدَى وَالتُّقَى وَالْعَفَافَ وَالْغِنَى',
        transliteration: 'Allahumma inni as\'aluka\'l-huda wa\'t-tuqa wa\'l-\'afafa wa\'l-ghina',
        translation: 'O Allah, I ask You for guidance, piety, chastity, and contentment',
        category: IslamicContentCategory.generalDuas,
        source: 'صحيح مسلم',
        repetitions: 1,
        benefits: ['Spiritual excellence', 'Moral purity', 'Contentment'],
        audioPath: 'assets/audio/general/006.mp3',
      ),
    ];
  }

  /// Load tasbeeh phrases from authentic sources
  Future<void> _loadTasbeehPhrases() async {
    _tasbeehPhrases = [
      IslamicContent(
        id: 'tasbeeh_001',
        arabicText: 'سُبْحَانَ اللَّهِ',
        transliteration: 'Subhan Allah',
        translation: 'Glory be to Allah',
        category: IslamicContentCategory.tasbeeh,
        source: 'صحيح البخاري ومسلم',
        repetitions: 33,
        benefits: ['Purification of the soul', 'Great reward'],
        audioPath: 'assets/audio/tasbeeh/001.mp3',
      ),
      IslamicContent(
        id: 'tasbeeh_002',
        arabicText: 'الْحَمْدُ لِلَّهِ',
        transliteration: 'Alhamdu lillah',
        translation: 'All praise is due to Allah',
        category: IslamicContentCategory.tasbeeh,
        source: 'صحيح البخاري ومسلم',
        repetitions: 33,
        benefits: ['Gratitude to Allah', 'Spiritual elevation'],
        audioPath: 'assets/audio/tasbeeh/002.mp3',
      ),
      IslamicContent(
        id: 'tasbeeh_003',
        arabicText: 'اللَّهُ أَكْبَرُ',
        transliteration: 'Allahu akbar',
        translation: 'Allah is the Greatest',
        category: IslamicContentCategory.tasbeeh,
        source: 'صحيح البخاري ومسلم',
        repetitions: 34,
        benefits: ['Magnification of Allah', 'Strength of faith'],
        audioPath: 'assets/audio/tasbeeh/003.mp3',
      ),
      IslamicContent(
        id: 'tasbeeh_004',
        arabicText: 'لَا إِلَهَ إِلَّا اللَّهُ',
        transliteration: 'La ilaha illa Allah',
        translation: 'There is no god but Allah',
        category: IslamicContentCategory.tasbeeh,
        source: 'صحيح البخاري ومسلم',
        repetitions: 100,
        benefits: ['Declaration of monotheism', 'Highest form of dhikr'],
        audioPath: 'assets/audio/tasbeeh/004.mp3',
      ),
      IslamicContent(
        id: 'tasbeeh_005',
        arabicText: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ سُبْحَانَ اللَّهِ الْعَظِيمِ',
        transliteration: 'Subhan Allahi wa bihamdihi subhan Allah al-\'azim',
        translation: 'Glory be to Allah and praise be to Him, glory be to Allah the Magnificent',
        category: IslamicContentCategory.tasbeeh,
        source: 'صحيح البخاري ومسلم',
        repetitions: 10,
        benefits: ['Heavy on the scales', 'Beloved to Allah'],
        audioPath: 'assets/audio/tasbeeh/005.mp3',
      ),
      IslamicContent(
        id: 'tasbeeh_006',
        arabicText: 'لَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللَّهِ',
        transliteration: 'La hawla wa la quwwata illa billah',
        translation: 'There is no power and no strength except with Allah',
        category: IslamicContentCategory.tasbeeh,
        source: 'صحيح البخاري ومسلم',
        repetitions: 10,
        benefits: ['Treasure from paradise', 'Strength in difficulties'],
        audioPath: 'assets/audio/tasbeeh/006.mp3',
      ),
      IslamicContent(
        id: 'tasbeeh_007',
        arabicText: 'أَسْتَغْفِرُ اللَّهَ',
        transliteration: 'Astaghfiru Allah',
        translation: 'I seek forgiveness from Allah',
        category: IslamicContentCategory.tasbeeh,
        source: 'صحيح البخاري ومسلم',
        repetitions: 100,
        benefits: ['Forgiveness of sins', 'Opening of sustenance'],
        audioPath: 'assets/audio/tasbeeh/007.mp3',
      ),
      IslamicContent(
        id: 'tasbeeh_008',
        arabicText: 'اللَّهُمَّ صَلِّ عَلَى مُحَمَّدٍ',
        transliteration: 'Allahumma salli \'ala Muhammad',
        translation: 'O Allah, send blessings upon Muhammad',
        category: IslamicContentCategory.tasbeeh,
        source: 'صحيح البخاري ومسلم',
        repetitions: 10,
        benefits: ['Blessings from Allah', 'Intercession of the Prophet'],
        audioPath: 'assets/audio/tasbeeh/008.mp3',
      ),
    ];
  }

  /// Load Quranic duas from authentic sources
  Future<void> _loadQuranDuas() async {
    _quranDuas = [
      IslamicContent(
        id: 'quran_dua_001',
        arabicText: 'رَبِّ اشْرَحْ لِي صَدْرِي وَيَسِّرْ لِي أَمْرِي',
        transliteration: 'Rabbi\'shrah li sadri wa yassir li amri',
        translation: 'My Lord, expand for me my breast and ease for me my task',
        category: IslamicContentCategory.quranDuas,
        source: 'القرآن الكريم - سورة طه',
        repetitions: 1,
        benefits: ['Ease in affairs', 'Expansion of the heart'],
        audioPath: 'assets/audio/quran_duas/001.mp3',
      ),
      IslamicContent(
        id: 'quran_dua_002',
        arabicText: 'رَبَّنَا اغْفِرْ لَنَا ذُنُوبَنَا وَإِسْرَافَنَا فِي أَمْرِنَا',
        transliteration: 'Rabbana\'ghfir lana dhunubana wa israfana fi amrina',
        translation: 'Our Lord, forgive us our sins and our transgressions in our affair',
        category: IslamicContentCategory.quranDuas,
        source: 'القرآن الكريم - سورة آل عمران',
        repetitions: 1,
        benefits: ['Forgiveness of sins', 'Spiritual purification'],
        audioPath: 'assets/audio/quran_duas/002.mp3',
      ),
      IslamicContent(
        id: 'quran_dua_003',
        arabicText: 'رَبِّ زِدْنِي عِلْمًا',
        transliteration: 'Rabbi zidni \'ilman',
        translation: 'My Lord, increase me in knowledge',
        category: IslamicContentCategory.quranDuas,
        source: 'القرآن الكريم - سورة طه',
        repetitions: 1,
        benefits: ['Increase in knowledge', 'Intellectual growth'],
        audioPath: 'assets/audio/quran_duas/003.mp3',
      ),
      IslamicContent(
        id: 'quran_dua_004',
        arabicText: 'رَبَّنَا هَبْ لَنَا مِنْ أَزْوَاجِنَا وَذُرِّيَّاتِنَا قُرَّةَ أَعْيُنٍ',
        transliteration: 'Rabbana hab lana min azwajina wa dhurriyyatina qurrata a\'yun',
        translation: 'Our Lord, grant us from among our wives and offspring comfort to our eyes',
        category: IslamicContentCategory.quranDuas,
        source: 'القرآن الكريم - سورة الفرقان',
        repetitions: 1,
        benefits: ['Righteous family', 'Comfort in relationships'],
        audioPath: 'assets/audio/quran_duas/004.mp3',
      ),
      IslamicContent(
        id: 'quran_dua_005',
        arabicText: 'رَبِّ أَوْزِعْنِي أَنْ أَشْكُرَ نِعْمَتَكَ الَّتِي أَنْعَمْتَ عَلَيَّ',
        transliteration: 'Rabbi awzi\'ni an ashkura ni\'mataka\'llati an\'amta \'alayy',
        translation: 'My Lord, enable me to be grateful for Your favor which You have bestowed upon me',
        category: IslamicContentCategory.quranDuas,
        source: 'القرآن الكريم - سورة النمل',
        repetitions: 1,
        benefits: ['Gratitude to Allah', 'Recognition of blessings'],
        audioPath: 'assets/audio/quran_duas/005.mp3',
      ),
    ];
  }

  /// Load Prophetic duas from authentic sources
  Future<void> _loadPropheticDuas() async {
    _propheticDuas = [
      IslamicContent(
        id: 'prophetic_001',
        arabicText: 'اللَّهُمَّ أَعِنِّي عَلَى ذِكْرِكَ وَشُكْرِكَ وَحُسْنِ عِبَادَتِكَ',
        transliteration: 'Allahumma a\'inni \'ala dhikrika wa shukrika wa husni \'ibadatik',
        translation: 'O Allah, help me to remember You, to thank You, and to worship You in the best manner',
        category: IslamicContentCategory.propheticDuas,
        source: 'سنن أبي داود',
        repetitions: 1,
        benefits: ['Assistance in worship', 'Improvement in remembrance of Allah'],
        audioPath: 'assets/audio/prophetic/001.mp3',
      ),
      IslamicContent(
        id: 'prophetic_002',
        arabicText: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ مِنَ الْخَيْرِ كُلِّهِ عَاجِلِهِ وَآجِلِهِ',
        transliteration: 'Allahumma inni as\'aluka min al-khayri kullihi \'ajilihi wa ajilihi',
        translation: 'O Allah, I ask You for all good, immediate and delayed',
        category: IslamicContentCategory.propheticDuas,
        source: 'صحيح ابن ماجه',
        repetitions: 1,
        benefits: ['Comprehensive good', 'Protection from all evil'],
        audioPath: 'assets/audio/prophetic/002.mp3',
      ),
      IslamicContent(
        id: 'prophetic_003',
        arabicText: 'اللَّهُمَّ اكْفِنِي بِحَلَالِكَ عَنْ حَرَامِكَ وَأَغْنِنِي بِفَضْلِكَ عَمَّنْ سِوَاكَ',
        transliteration: 'Allahumma\'kfini bi halalika \'an haramika wa aghnini bi fadlika \'amman siwak',
        translation: 'O Allah, make what is lawful enough for me, as opposed to what is unlawful, and make me independent of all others besides You',
        category: IslamicContentCategory.propheticDuas,
        source: 'سنن الترمذي',
        repetitions: 1,
        benefits: ['Lawful sustenance', 'Independence through Allah'],
        audioPath: 'assets/audio/prophetic/003.mp3',
      ),
      IslamicContent(
        id: 'prophetic_004',
        arabicText: 'اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنْ زَوَالِ نِعْمَتِكَ وَتَحَوُّلِ عَافِيَتِكَ',
        transliteration: 'Allahumma inni a\'udhu bika min zawali ni\'matika wa tahawwuli \'afiyatik',
        translation: 'O Allah, I seek refuge in You from the decline of Your favor and the change of Your protection',
        category: IslamicContentCategory.propheticDuas,
        source: 'صحيح مسلم',
        repetitions: 1,
        benefits: ['Preservation of blessings', 'Continuous divine protection'],
        audioPath: 'assets/audio/prophetic/004.mp3',
      ),
      IslamicContent(
        id: 'prophetic_005',
        arabicText: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ الثَّبَاتَ فِي الْأَمْرِ وَالْعَزِيمَةَ عَلَى الرُّشْدِ',
        transliteration: 'Allahumma inni as\'aluka\'th-thabata fi\'l-amri wa\'l-\'azimata \'ala\'r-rushd',
        translation: 'O Allah, I ask You for steadfastness in affairs and determination upon right guidance',
        category: IslamicContentCategory.propheticDuas,
        source: 'سنن النسائي',
        repetitions: 1,
        benefits: ['Steadfastness in faith', 'Determination in righteousness'],
        audioPath: 'assets/audio/prophetic/005.mp3',
      ),
      IslamicContent(
        id: 'prophetic_006',
        arabicText: 'اللَّهُمَّ طَهِّرْ قَلْبِي مِنَ النِّفَاقِ وَعَمَلِي مِنَ الرِّيَاءِ',
        transliteration: 'Allahumma tahhir qalbi min an-nifaqi wa \'amali min ar-riya\'',
        translation: 'O Allah, purify my heart from hypocrisy and my actions from showing off',
        category: IslamicContentCategory.propheticDuas,
        source: 'مسند أحمد',
        repetitions: 1,
        benefits: ['Purification of heart', 'Sincerity in actions'],
        audioPath: 'assets/audio/prophetic/006.mp3',
      ),
    ];
  }
}

/// Represents authentic Islamic content (dua, dhikr, tasbeeh)
class IslamicContent {
  final String id;
  final String arabicText;
  final String transliteration;
  final String translation;
  final IslamicContentCategory category;
  final String source;
  final int repetitions;
  final List<String> benefits;
  final String? audioPath;
  final DateTime? createdAt;

  IslamicContent({
    required this.id,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.category,
    required this.source,
    this.repetitions = 1,
    this.benefits = const [],
    this.audioPath,
    this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'arabicText': arabicText,
      'transliteration': transliteration,
      'translation': translation,
      'category': category.name,
      'source': source,
      'repetitions': repetitions,
      'benefits': benefits,
      'audioPath': audioPath,
      'createdAt': createdAt?.toIso8601String(),
    };
  }

  factory IslamicContent.fromMap(Map<String, dynamic> map) {
    return IslamicContent(
      id: map['id'] ?? '',
      arabicText: map['arabicText'] ?? '',
      transliteration: map['transliteration'] ?? '',
      translation: map['translation'] ?? '',
      category: IslamicContentCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => IslamicContentCategory.generalDuas,
      ),
      source: map['source'] ?? '',
      repetitions: map['repetitions'] ?? 1,
      benefits: List<String>.from(map['benefits'] ?? []),
      audioPath: map['audioPath'],
      createdAt: map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null,
    );
  }
}

/// Categories of Islamic content
enum IslamicContentCategory {
  morningAthkar,
  eveningAthkar,
  sleepDuas,
  foodDuas,
  travelDuas,
  generalDuas,
  tasbeeh,
  quranDuas,
  propheticDuas,
}

/// Extension for Islamic content category names
extension IslamicContentCategoryExtension on IslamicContentCategory {
  String get displayNameArabic {
    switch (this) {
      case IslamicContentCategory.morningAthkar:
        return 'أذكار الصباح';
      case IslamicContentCategory.eveningAthkar:
        return 'أذكار المساء';
      case IslamicContentCategory.sleepDuas:
        return 'أدعية النوم';
      case IslamicContentCategory.foodDuas:
        return 'أدعية الطعام';
      case IslamicContentCategory.travelDuas:
        return 'أدعية السفر';
      case IslamicContentCategory.generalDuas:
        return 'الأدعية العامة';
      case IslamicContentCategory.tasbeeh:
        return 'التسبيح';
      case IslamicContentCategory.quranDuas:
        return 'الأدعية القرآنية';
      case IslamicContentCategory.propheticDuas:
        return 'الأدعية النبوية';
    }
  }

  String get displayNameEnglish {
    switch (this) {
      case IslamicContentCategory.morningAthkar:
        return 'Morning Athkar';
      case IslamicContentCategory.eveningAthkar:
        return 'Evening Athkar';
      case IslamicContentCategory.sleepDuas:
        return 'Sleep Duas';
      case IslamicContentCategory.foodDuas:
        return 'Food Duas';
      case IslamicContentCategory.travelDuas:
        return 'Travel Duas';
      case IslamicContentCategory.generalDuas:
        return 'General Duas';
      case IslamicContentCategory.tasbeeh:
        return 'Tasbeeh';
      case IslamicContentCategory.quranDuas:
        return 'Quranic Duas';
      case IslamicContentCategory.propheticDuas:
        return 'Prophetic Duas';
    }
  }
}
