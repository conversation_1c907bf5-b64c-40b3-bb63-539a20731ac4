import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../services/prebuilt_content_service.dart';
import '../models/prebuilt_content_models.dart';
import '../providers/language_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/loading_overlay.dart';

class PrebuiltContentScreen extends StatefulWidget {
  const PrebuiltContentScreen({super.key});

  @override
  State<PrebuiltContentScreen> createState() => _PrebuiltContentScreenState();
}

class _PrebuiltContentScreenState extends State<PrebuiltContentScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<PrebuiltContent> _allContent = [];
  List<PrebuiltContent> _filteredContent = [];
  bool _isLoading = true;
  String _searchQuery = '';
  ContentCategory _selectedCategory = ContentCategory.athkar;
  String? _selectedSubcategory;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_onTabChanged);
    _loadContent();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      final categories = [
        ContentCategory.athkar,
        ContentCategory.dua,
        ContentCategory.tasbeeh,
        ContentCategory.routine,
      ];
      
      setState(() {
        _selectedCategory = categories[_tabController.index];
        _selectedSubcategory = null;
        _searchQuery = '';
      });
      
      _filterContent();
    }
  }

  Future<void> _loadContent() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final content = await PrebuiltContentService.getAllContent();
      setState(() {
        _allContent = content;
        _isLoading = false;
      });
      _filterContent();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading content: $e')),
        );
      }
    }
  }

  void _filterContent() {
    setState(() {
      _filteredContent = _allContent.where((content) {
        // Filter by category
        if (content.category != _selectedCategory) return false;
        
        // Filter by subcategory if selected
        if (_selectedSubcategory != null &&
            content.subcategory != _selectedSubcategory) {
          return false;
        }
        
        // Filter by search query
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          return content.title.toLowerCase().contains(query) ||
                 content.arabicText.toLowerCase().contains(query) ||
                 (content.transliteration?.toLowerCase().contains(query) ?? false) ||
                 (content.translation?.toLowerCase().contains(query) ?? false);
        }
        
        return true;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    
    return Scaffold(
      appBar: AppBar(
        title: Text(languageProvider.isArabic ? 'المحتوى المبني مسبقاً' : 'Prebuilt Content'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: AppTheme.accentGold,
          tabs: [
            Tab(text: languageProvider.isArabic ? 'أذكار' : 'Athkar'),
            Tab(text: languageProvider.isArabic ? 'دعاء' : 'Duas'),
            Tab(text: languageProvider.isArabic ? 'تسبيح' : 'Tasbeeh'),
            Tab(text: languageProvider.isArabic ? 'روتين' : 'Routines'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadContent,
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Column(
          children: [
            _buildSearchAndFilter(languageProvider),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildContentList(ContentCategory.athkar, languageProvider),
                  _buildContentList(ContentCategory.dua, languageProvider),
                  _buildContentList(ContentCategory.tasbeeh, languageProvider),
                  _buildContentList(ContentCategory.routine, languageProvider),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndFilter(LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            decoration: InputDecoration(
              hintText: languageProvider.isArabic ? 'البحث...' : 'Search...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _filterContent();
            },
          ),
          
          const SizedBox(height: 12),
          
          // Subcategory filter
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  label: languageProvider.isArabic ? 'الكل' : 'All',
                  isSelected: _selectedSubcategory == null,
                  onTap: () {
                    setState(() {
                      _selectedSubcategory = null;
                    });
                    _filterContent();
                  },
                ),
                const SizedBox(width: 8),
                ...ContentCategories.getSubcategories(_selectedCategory).map(
                  (subcategory) => Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: _buildFilterChip(
                      label: ContentCategories.getSubcategoryDisplayName(subcategory),
                      isSelected: _selectedSubcategory == subcategory,
                      onTap: () {
                        setState(() {
                          _selectedSubcategory = subcategory;
                        });
                        _filterContent();
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryGreen : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppTheme.primaryGreen : Colors.grey[300]!,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black87,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildContentList(ContentCategory category, LanguageProvider languageProvider) {
    if (_filteredContent.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              MdiIcons.bookOpenPageVariant,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              languageProvider.isArabic ? 'لا يوجد محتوى' : 'No content found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              languageProvider.isArabic 
                  ? 'جرب تغيير الفلتر أو البحث' 
                  : 'Try changing the filter or search',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredContent.length,
      itemBuilder: (context, index) {
        final content = _filteredContent[index];
        return _buildContentCard(content, languageProvider);
      },
    );
  }

  Widget _buildContentCard(PrebuiltContent content, LanguageProvider languageProvider) {
    final color = content.colorHex != null 
        ? Color(int.parse('FF${content.colorHex!}', radix: 16))
        : AppTheme.primaryGreen;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showContentDetails(content, languageProvider),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                color.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 8,
                    height: 40,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          content.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (content.subcategory != null)
                          Text(
                            ContentCategories.getSubcategoryDisplayName(content.subcategory!),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (content.isPopular)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppTheme.accentGold,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        languageProvider.isArabic ? 'شائع' : 'Popular',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Arabic text
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  content.arabicText,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    height: 1.8,
                  ),
                  textAlign: TextAlign.center,
                  textDirection: TextDirection.rtl,
                ),
              ),
              
              if (content.transliteration != null) ...[
                const SizedBox(height: 8),
                Text(
                  content.transliteration!,
                  style: TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                    color: Colors.grey[700],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
              
              if (content.translation != null) ...[
                const SizedBox(height: 8),
                Text(
                  content.translation!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
              
              const SizedBox(height: 12),
              
              Row(
                children: [
                  if ((content.targetCount ?? 1) > 1)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${content.targetCount}x',
                        style: TextStyle(
                          color: color,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  
                  const Spacer(),
                  
                  TextButton.icon(
                    onPressed: () => _addToRoutine(content),
                    icon: const Icon(Icons.add, size: 16),
                    label: Text(
                      languageProvider.isArabic ? 'إضافة' : 'Add',
                      style: const TextStyle(fontSize: 12),
                    ),
                    style: TextButton.styleFrom(
                      foregroundColor: color,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showContentDetails(PrebuiltContent content, LanguageProvider languageProvider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        expand: false,
        builder: (context, scrollController) {
          return Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                
                Text(
                  content.title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Arabic text
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            content.arabicText,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                              height: 2.0,
                            ),
                            textAlign: TextAlign.center,
                            textDirection: TextDirection.rtl,
                          ),
                        ),
                        
                        if (content.transliteration != null) ...[
                          const SizedBox(height: 16),
                          Text(
                            languageProvider.isArabic ? 'النطق:' : 'Transliteration:',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            content.transliteration!,
                            style: const TextStyle(
                              fontSize: 16,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                        
                        if (content.translation != null) ...[
                          const SizedBox(height: 16),
                          Text(
                            languageProvider.isArabic ? 'الترجمة:' : 'Translation:',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            content.translation!,
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                        
                        if (content.description != null) ...[
                          const SizedBox(height: 16),
                          Text(
                            languageProvider.isArabic ? 'الوصف:' : 'Description:',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            content.description!,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                        
                        if (content.source != null) ...[
                          const SizedBox(height: 16),
                          Text(
                            languageProvider.isArabic ? 'المصدر:' : 'Source:',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${content.source}${content.reference != null ? ' - ${content.reference}' : ''}',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                        
                        const SizedBox(height: 24),
                        
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.pop(context);
                              _addToRoutine(content);
                            },
                            icon: const Icon(Icons.add),
                            label: Text(
                              languageProvider.isArabic ? 'إضافة إلى الروتين' : 'Add to Routine',
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryGreen,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _addToRoutine(PrebuiltContent content) {
    // TODO: Implement add to routine functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Added "${content.title}" to routine'),
        backgroundColor: AppTheme.primaryGreen,
      ),
    );
    
    // Increment usage count
    PrebuiltContentService.incrementUsageCount(content.id);
  }
}
