import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'dart:math' as math;
import '../services/qibla_service.dart';
import '../theme/app_theme.dart';

class QiblaScreen extends StatefulWidget {
  const QiblaScreen({super.key});

  @override
  State<QiblaScreen> createState() => _QiblaScreenState();
}

class _QiblaScreenState extends State<QiblaScreen>
    with TickerProviderStateMixin {
  late AnimationController _compassController;
  late AnimationController _pulseController;
  QiblaData? _qiblaData;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _compassController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _initializeQibla();
  }

  @override
  void dispose() {
    _compassController.dispose();
    _pulseController.dispose();
    QiblaService.dispose();
    super.dispose();
  }

  Future<void> _initializeQibla() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      await QiblaService.initialize();
      
      QiblaService.qiblaStream.listen(
        (qiblaData) {
          setState(() {
            _qiblaData = qiblaData;
            _isLoading = false;
          });
          
          // Animate compass rotation
          _compassController.animateTo(qiblaData.direction / 360);
        },
        onError: (error) {
          setState(() {
            _error = error.toString();
            _isLoading = false;
          });
        },
      );
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Qibla Finder'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _initializeQibla,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppTheme.primaryGreen),
            SizedBox(height: 16),
            Text('Initializing compass...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Error accessing compass',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeQibla,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildLocationInfo(),
          const SizedBox(height: 32),
          _buildCompass(),
          const SizedBox(height: 32),
          _buildDirectionInfo(),
          const SizedBox(height: 32),
          _buildInstructions(),
        ],
      ),
    );
  }

  Widget _buildLocationInfo() {
    if (_qiblaData == null) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: AppTheme.primaryGreen,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Your Location',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${_qiblaData!.latitude.toStringAsFixed(4)}, ${_qiblaData!.longitude.toStringAsFixed(4)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  MdiIcons.mosque,
                  color: AppTheme.accentGold,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Distance to Kaaba',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${_qiblaData!.distance.toStringAsFixed(0)} km',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompass() {
    return SizedBox(
      width: 300,
      height: 300,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Pulse animation
          AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Container(
                width: 300 + (_pulseController.value * 20),
                height: 300 + (_pulseController.value * 20),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppTheme.primaryGreen.withValues(
                      alpha: 0.3 * (1 - _pulseController.value),
                    ),
                    width: 2,
                  ),
                ),
              );
            },
          ),
          // Compass background
          Container(
            width: 280,
            height: 280,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  Colors.white,
                  Colors.grey[100]!,
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
          ),
          // Compass markings
          ...List.generate(8, (index) {
            final angle = index * 45.0;
            return Transform.rotate(
              angle: angle * math.pi / 180,
              child: Container(
                width: 2,
                height: 40,
                margin: const EdgeInsets.only(bottom: 220),
                color: Colors.grey[400],
              ),
            );
          }),
          // Qibla direction indicator
          if (_qiblaData != null)
            AnimatedBuilder(
              animation: _compassController,
              builder: (context, child) {
                return Transform.rotate(
                  angle: (_qiblaData!.direction - _qiblaData!.magneticHeading) * math.pi / 180,
                  child: Container(
                    width: 4,
                    height: 120,
                    margin: const EdgeInsets.only(bottom: 80),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGreen,
                      borderRadius: BorderRadius.circular(2),
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.primaryGreen.withValues(alpha: 0.5),
                          blurRadius: 4,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          // Center Kaaba icon
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppTheme.accentGold,
              boxShadow: [
                BoxShadow(
                  color: AppTheme.accentGold.withValues(alpha: 0.3),
                  blurRadius: 8,
                ),
              ],
            ),
            child: Icon(
              MdiIcons.mosque,
              color: Colors.white,
              size: 30,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDirectionInfo() {
    if (_qiblaData == null) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      color: AppTheme.primaryGreen,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'Qibla Direction',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${_qiblaData!.direction.toStringAsFixed(1)}°',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _getDirectionText(_qiblaData!.direction),
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Instructions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInstructionItem(
              Icons.phone_android,
              'Hold your phone flat and level',
            ),
            _buildInstructionItem(
              Icons.explore,
              'The green line points toward the Qibla',
            ),
            _buildInstructionItem(
              Icons.rotate_right,
              'Rotate yourself until facing the green line',
            ),
            _buildInstructionItem(
              MdiIcons.handsPray,
              'You are now facing the Qibla for prayer',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppTheme.primaryGreen,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  String _getDirectionText(double direction) {
    if (direction >= 337.5 || direction < 22.5) return 'North';
    if (direction >= 22.5 && direction < 67.5) return 'Northeast';
    if (direction >= 67.5 && direction < 112.5) return 'East';
    if (direction >= 112.5 && direction < 157.5) return 'Southeast';
    if (direction >= 157.5 && direction < 202.5) return 'South';
    if (direction >= 202.5 && direction < 247.5) return 'Southwest';
    if (direction >= 247.5 && direction < 292.5) return 'West';
    if (direction >= 292.5 && direction < 337.5) return 'Northwest';
    return 'Unknown';
  }
}
