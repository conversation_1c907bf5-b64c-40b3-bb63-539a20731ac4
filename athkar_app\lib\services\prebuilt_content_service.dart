import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/prebuilt_content_models.dart';

class PrebuiltContentService {
  static List<PrebuiltContent> _allContent = [];
  static bool _isLoaded = false;

  /// Initialize and load all prebuilt content
  static Future<void> initialize() async {
    if (_isLoaded) return;

    try {
      await _loadPrebuiltContent();
      _isLoaded = true;
      debugPrint('Prebuilt content loaded: ${_allContent.length} items');
    } catch (e) {
      debugPrint('Error loading prebuilt content: $e');
    }
  }

  /// Load prebuilt content from assets
  static Future<void> _loadPrebuiltContent() async {
    try {
      final String data = await rootBundle.loadString('assets/data/prebuilt_content.json');
      final Map<String, dynamic> jsonData = json.decode(data);
      
      _allContent.clear();
      
      // Load different categories
      if (jsonData['athkar'] != null) {
        for (final item in jsonData['athkar']) {
          _allContent.add(PrebuiltContent.fromJson(item));
        }
      }
      
      if (jsonData['duas'] != null) {
        for (final item in jsonData['duas']) {
          _allContent.add(PrebuiltContent.fromJson(item));
        }
      }
      
      if (jsonData['tasbeeh'] != null) {
        for (final item in jsonData['tasbeeh']) {
          _allContent.add(PrebuiltContent.fromJson(item));
        }
      }
      
      if (jsonData['routines'] != null) {
        for (final item in jsonData['routines']) {
          _allContent.add(PrebuiltContent.fromJson(item));
        }
      }
    } catch (e) {
      debugPrint('Error parsing prebuilt content: $e');
    }
  }

  /// Get all prebuilt content
  static Future<List<PrebuiltContent>> getAllContent() async {
    await initialize();
    return List.from(_allContent);
  }

  /// Get content by category
  static Future<List<PrebuiltContent>> getContentByCategory(ContentCategory category) async {
    await initialize();
    return _allContent.where((content) => content.category == category).toList();
  }

  /// Get content by subcategory
  static Future<List<PrebuiltContent>> getContentBySubcategory(String subcategory) async {
    await initialize();
    return _allContent.where((content) => content.subcategory == subcategory).toList();
  }

  /// Search content
  static Future<List<PrebuiltContent>> searchContent(String query) async {
    await initialize();
    final lowerQuery = query.toLowerCase();
    
    return _allContent.where((content) {
      return content.title.toLowerCase().contains(lowerQuery) ||
             content.arabicText.toLowerCase().contains(lowerQuery) ||
             (content.transliteration?.toLowerCase().contains(lowerQuery) ?? false) ||
             (content.translation?.toLowerCase().contains(lowerQuery) ?? false) ||
             (content.description?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  /// Get popular content
  static Future<List<PrebuiltContent>> getPopularContent({int limit = 20}) async {
    await initialize();
    final popular = _allContent.where((content) => content.isPopular).toList();
    popular.sort((a, b) => (b.usageCount ?? 0).compareTo(a.usageCount ?? 0));
    return popular.take(limit).toList();
  }

  /// Get content by difficulty level
  static Future<List<PrebuiltContent>> getContentByDifficulty(DifficultyLevel difficulty) async {
    await initialize();
    return _allContent.where((content) => content.difficulty == difficulty).toList();
  }

  /// Get morning athkar
  static Future<List<PrebuiltContent>> getMorningAthkar() async {
    await initialize();
    return _allContent.where((content) => 
      content.category == ContentCategory.athkar && 
      content.subcategory == 'morning'
    ).toList();
  }

  /// Get evening athkar
  static Future<List<PrebuiltContent>> getEveningAthkar() async {
    await initialize();
    return _allContent.where((content) => 
      content.category == ContentCategory.athkar && 
      content.subcategory == 'evening'
    ).toList();
  }

  /// Get prayer athkar
  static Future<List<PrebuiltContent>> getPrayerAthkar() async {
    await initialize();
    return _allContent.where((content) => 
      content.category == ContentCategory.athkar && 
      content.subcategory == 'prayer'
    ).toList();
  }

  /// Get sleeping athkar
  static Future<List<PrebuiltContent>> getSleepingAthkar() async {
    await initialize();
    return _allContent.where((content) => 
      content.category == ContentCategory.athkar && 
      content.subcategory == 'sleeping'
    ).toList();
  }

  /// Get duas by occasion
  static Future<List<PrebuiltContent>> getDuasByOccasion(String occasion) async {
    await initialize();
    return _allContent.where((content) => 
      content.category == ContentCategory.dua && 
      content.subcategory == occasion
    ).toList();
  }

  /// Get tasbeeh content
  static Future<List<PrebuiltContent>> getTasbeehContent() async {
    await initialize();
    return _allContent.where((content) => content.category == ContentCategory.tasbeeh).toList();
  }

  /// Get complete routines
  static Future<List<PrebuiltContent>> getCompleteRoutines() async {
    await initialize();
    return _allContent.where((content) => content.category == ContentCategory.routine).toList();
  }

  /// Get content by tags
  static Future<List<PrebuiltContent>> getContentByTags(List<String> tags) async {
    await initialize();
    return _allContent.where((content) {
      if (content.tags == null || content.tags!.isEmpty) return false;
      return tags.any((tag) => content.tags!.contains(tag));
    }).toList();
  }

  /// Get recommended content based on user preferences
  static Future<List<PrebuiltContent>> getRecommendedContent({
    ContentCategory? preferredCategory,
    DifficultyLevel? preferredDifficulty,
    int limit = 10,
  }) async {
    await initialize();
    
    List<PrebuiltContent> recommended = List.from(_allContent);
    
    // Filter by preferred category
    if (preferredCategory != null) {
      recommended = recommended.where((content) => content.category == preferredCategory).toList();
    }
    
    // Filter by preferred difficulty
    if (preferredDifficulty != null) {
      recommended = recommended.where((content) => content.difficulty == preferredDifficulty).toList();
    }
    
    // Sort by popularity and usage
    recommended.sort((a, b) {
      final aScore = (a.usageCount ?? 0) + (a.isPopular ? 100 : 0);
      final bScore = (b.usageCount ?? 0) + (b.isPopular ? 100 : 0);
      return bScore.compareTo(aScore);
    });
    
    return recommended.take(limit).toList();
  }

  /// Get content suitable for beginners
  static Future<List<PrebuiltContent>> getBeginnerContent() async {
    await initialize();
    return _allContent.where((content) => 
      content.difficulty == DifficultyLevel.beginner ||
      content.isPopular
    ).toList();
  }

  /// Get advanced content
  static Future<List<PrebuiltContent>> getAdvancedContent() async {
    await initialize();
    return _allContent.where((content) => 
      content.difficulty == DifficultyLevel.advanced
    ).toList();
  }

  /// Get content by source (Quran, Hadith, etc.)
  static Future<List<PrebuiltContent>> getContentBySource(String source) async {
    await initialize();
    return _allContent.where((content) => 
      content.source?.toLowerCase() == source.toLowerCase()
    ).toList();
  }

  /// Get all available categories
  static Future<List<ContentCategory>> getAvailableCategories() async {
    await initialize();
    final categories = _allContent.map((content) => content.category).toSet().toList();
    return categories;
  }

  /// Get all available subcategories for a category
  static Future<List<String>> getSubcategoriesForCategory(ContentCategory category) async {
    await initialize();
    final subcategories = _allContent
        .where((content) => content.category == category)
        .map((content) => content.subcategory)
        .where((sub) => sub != null)
        .cast<String>()
        .toSet()
        .toList();
    return subcategories;
  }

  /// Get all available tags
  static Future<List<String>> getAvailableTags() async {
    await initialize();
    final allTags = <String>{};
    for (final content in _allContent) {
      if (content.tags != null) {
        allTags.addAll(content.tags!);
      }
    }
    return allTags.toList();
  }

  /// Increment usage count for content
  static Future<void> incrementUsageCount(String contentId) async {
    final index = _allContent.indexWhere((content) => content.id == contentId);
    if (index != -1) {
      final content = _allContent[index];
      _allContent[index] = content.copyWith(
        usageCount: (content.usageCount ?? 0) + 1,
      );
    }
  }

  /// Mark content as favorite
  static Future<void> markAsFavorite(String contentId, bool isFavorite) async {
    final index = _allContent.indexWhere((content) => content.id == contentId);
    if (index != -1) {
      final content = _allContent[index];
      _allContent[index] = content.copyWith(isFavorite: isFavorite);
    }
  }

  /// Get favorite content
  static Future<List<PrebuiltContent>> getFavoriteContent() async {
    await initialize();
    return _allContent.where((content) => content.isFavorite).toList();
  }

  /// Get recently used content
  static Future<List<PrebuiltContent>> getRecentlyUsedContent({int limit = 10}) async {
    await initialize();
    final recentlyUsed = _allContent.where((content) => (content.usageCount ?? 0) > 0).toList();
    recentlyUsed.sort((a, b) => (b.usageCount ?? 0).compareTo(a.usageCount ?? 0));
    return recentlyUsed.take(limit).toList();
  }
}
