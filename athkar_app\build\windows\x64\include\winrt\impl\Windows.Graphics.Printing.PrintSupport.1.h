// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Graphics_Printing_PrintSupport_1_H
#define WINRT_Windows_Graphics_Printing_PrintSupport_1_H
#include "winrt/impl/Windows.Graphics.Printing.PrintSupport.0.h"
WINRT_EXPORT namespace winrt::Windows::Graphics::Printing::PrintSupport
{
    struct __declspec(empty_bases) IPrintSupportCommunicationErrorDetectedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportCommunicationErrorDetectedEventArgs>
    {
        IPrintSupportCommunicationErrorDetectedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportCommunicationErrorDetectedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportExtensionSession :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportExtensionSession>
    {
        IPrintSupportExtensionSession(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportExtensionSession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportExtensionSession2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportExtensionSession2>
    {
        IPrintSupportExtensionSession2(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportExtensionSession2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportExtensionSession3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportExtensionSession3>
    {
        IPrintSupportExtensionSession3(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportExtensionSession3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportExtensionTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportExtensionTriggerDetails>
    {
        IPrintSupportExtensionTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportExtensionTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportIppCommunicationConfiguration :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportIppCommunicationConfiguration>
    {
        IPrintSupportIppCommunicationConfiguration(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportIppCommunicationConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportIppCommunicationTimeouts :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportIppCommunicationTimeouts>
    {
        IPrintSupportIppCommunicationTimeouts(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportIppCommunicationTimeouts(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportMxdcImageQualityConfiguration :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportMxdcImageQualityConfiguration>
    {
        IPrintSupportMxdcImageQualityConfiguration(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportMxdcImageQualityConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportPrintDeviceCapabilitiesChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportPrintDeviceCapabilitiesChangedEventArgs>
    {
        IPrintSupportPrintDeviceCapabilitiesChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportPrintDeviceCapabilitiesChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportPrintDeviceCapabilitiesChangedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportPrintDeviceCapabilitiesChangedEventArgs2>
    {
        IPrintSupportPrintDeviceCapabilitiesChangedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportPrintDeviceCapabilitiesChangedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportPrintDeviceCapabilitiesChangedEventArgs3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportPrintDeviceCapabilitiesChangedEventArgs3>
    {
        IPrintSupportPrintDeviceCapabilitiesChangedEventArgs3(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportPrintDeviceCapabilitiesChangedEventArgs3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportPrintDeviceCapabilitiesChangedEventArgs4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportPrintDeviceCapabilitiesChangedEventArgs4>
    {
        IPrintSupportPrintDeviceCapabilitiesChangedEventArgs4(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportPrintDeviceCapabilitiesChangedEventArgs4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportPrintDeviceCapabilitiesUpdatePolicy :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportPrintDeviceCapabilitiesUpdatePolicy>
    {
        IPrintSupportPrintDeviceCapabilitiesUpdatePolicy(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportPrintDeviceCapabilitiesUpdatePolicy(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportPrintDeviceCapabilitiesUpdatePolicyStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportPrintDeviceCapabilitiesUpdatePolicyStatics>
    {
        IPrintSupportPrintDeviceCapabilitiesUpdatePolicyStatics(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportPrintDeviceCapabilitiesUpdatePolicyStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportPrintTicketElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportPrintTicketElement>
    {
        IPrintSupportPrintTicketElement(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportPrintTicketElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportPrintTicketValidationRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportPrintTicketValidationRequestedEventArgs>
    {
        IPrintSupportPrintTicketValidationRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportPrintTicketValidationRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportPrinterSelectedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportPrinterSelectedEventArgs>
    {
        IPrintSupportPrinterSelectedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportPrinterSelectedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportSessionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportSessionInfo>
    {
        IPrintSupportSessionInfo(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportSessionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportSettingsActivatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportSettingsActivatedEventArgs>
    {
        IPrintSupportSettingsActivatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportSettingsActivatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportSettingsActivatedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportSettingsActivatedEventArgs2>
    {
        IPrintSupportSettingsActivatedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportSettingsActivatedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPrintSupportSettingsUISession :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrintSupportSettingsUISession>
    {
        IPrintSupportSettingsUISession(std::nullptr_t = nullptr) noexcept {}
        IPrintSupportSettingsUISession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
