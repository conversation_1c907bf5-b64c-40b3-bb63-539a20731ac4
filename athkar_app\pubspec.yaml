name: athkar_app
description: "Islamic Athkar (Remembrance) Mobile Application - Cross-platform app for Islamic supplications and dhikr with offline-first architecture and Supabase sync."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

  # UI and Icons
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296

  # Supabase Integration
  supabase_flutter: ^2.5.6

  # Local Database
  sqflite: ^2.3.3+1
  sqflite_common_ffi: ^2.3.3
  path: ^1.9.0

  # State Management
  provider: ^6.1.2

  # Local Notifications
  flutter_local_notifications: ^17.2.2
  timezone: ^0.9.4

  # Floating Window (Android)
  flutter_overlay_window: ^0.5.0

  # Permissions
  permission_handler: ^11.3.1

  # Connectivity
  connectivity_plus: ^6.0.5

  # File and Sharing
  share_plus: ^10.0.2

  # JSON and Data Handling
  json_annotation: ^4.9.0
  uuid: ^4.4.0

  # Date and Time (already included above for internationalization)

  # Shared Preferences
  shared_preferences: ^2.2.3

  # File Handling
  path_provider: ^2.1.4

  # HTTP Requests
  http: ^1.2.1

  # Animations
  lottie: ^3.1.2

  # Charts and Progress
  fl_chart: ^0.68.0

  # Calendar
  table_calendar: ^3.1.2

  # Location and Sensors (for Qibla)
  geolocator: ^12.0.0
  sensors_plus: ^5.0.1

  # URL Launcher
  url_launcher: ^6.3.0

  # Device Info
  device_info_plus: ^10.1.2

  # Package Info
  package_info_plus: ^8.0.2

  # Crypto for security
  crypto: ^3.0.3

  # Image handling
  image: ^4.2.0

  # Audio playback
  audioplayers: ^6.0.0

  # Prayer Times
  adhan: ^2.0.0
  prayers_times: ^0.0.6

  # Quran
  quran: ^1.3.3

  # Hadith
  hadith: ^1.0.1

  # Video player
  video_player: ^2.9.1

  # Camera
  camera: ^0.11.0+2

  # Image picker
  image_picker: ^1.1.2

  # File picker
  file_picker: ^8.0.6

  # PDF generation
  pdf: ^3.11.1

  # Excel generation
  excel: ^4.0.6

  # CSV handling
  csv: ^6.0.0

  # QR Code
  qr_flutter: ^4.1.0

  # Biometric authentication
  local_auth: ^2.2.0

  # Secure storage
  flutter_secure_storage: ^9.2.2

  # In-app purchases
  in_app_purchase: ^3.2.0

  # Firebase (optional for analytics)
  firebase_core: ^3.3.0
  firebase_analytics: ^11.2.1
  firebase_crashlytics: ^4.0.4

  # Push notifications
  firebase_messaging: ^15.0.4

  # Social login
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.1

  # WebView
  webview_flutter: ^4.8.0

  # Markdown
  flutter_markdown: ^0.7.3

  # HTML rendering
  flutter_html: ^3.0.0-beta.2

  # Cached network images
  cached_network_image: ^3.3.1

  # SVG support
  flutter_svg: ^2.0.10+1

  # Shimmer loading
  shimmer: ^3.0.0

  # Pull to refresh
  pull_to_refresh: ^2.0.0

  # Infinite scroll
  infinite_scroll_pagination: ^4.0.0

  # Smooth page indicator
  smooth_page_indicator: ^1.2.0+3

  # Expandable widgets
  expandable: ^5.0.1

  # Sliding up panel
  sliding_up_panel: ^2.0.0+1

  # Bottom sheet
  modal_bottom_sheet: ^3.0.0

  # Animations
  flutter_staggered_animations: ^1.1.1
  # rive: ^0.13.1  # Commented out due to Windows build issues with ClangCL

  # Haptic feedback
  vibration: ^3.1.3

  # Screen utilities
  flutter_screenutil: ^5.9.3

  # Safe area (using built-in SafeArea widget)

  # Keyboard visibility
  flutter_keyboard_visibility: ^6.0.0

  # App lifecycle (removed - not available)

  # Background tasks
  workmanager: ^0.8.0

  # Wakelock
  wakelock_plus: ^1.2.8

  # Battery optimization
  battery_plus: ^6.0.2

  # Network info
  network_info_plus: ^5.0.3

  # System settings
  app_settings: ^5.1.1

  # Launcher icons
  flutter_launcher_icons: ^0.13.1

  # Native splash
  flutter_native_splash: ^2.4.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.12
  json_serializable: ^6.8.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Internationalization
  generate: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/data/
    - assets/audio/
    - assets/sounds/
    - assets/audio/athan/
    - assets/sql/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Arabic and Islamic fonts (will be added later)
  # fonts:
  #   - family: Amiri
  #     fonts:
  #       - asset: assets/fonts/Amiri-Regular.ttf
  #       - asset: assets/fonts/Amiri-Bold.ttf
  #         weight: 700
  #   - family: NotoSansArabic
  #     fonts:
  #       - asset: assets/fonts/NotoSansArabic-Regular.ttf
  #       - asset: assets/fonts/NotoSansArabic-Bold.ttf
  #         weight: 700
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
