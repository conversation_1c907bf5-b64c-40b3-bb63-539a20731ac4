import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../providers/quran_provider.dart';
import '../providers/hadith_provider.dart';
import '../providers/athkar_provider.dart';
import '../services/prayer_times_service.dart';
import '../services/enhanced_location_service.dart';

class OfflineVerificationService {
  static final OfflineVerificationService _instance = OfflineVerificationService._internal();
  factory OfflineVerificationService() => _instance;
  OfflineVerificationService._internal();

  /// Comprehensive offline functionality verification
  Future<OfflineVerificationResult> verifyOfflineCapabilities() async {
    final result = OfflineVerificationResult();
    
    try {
      debugPrint('Starting comprehensive offline verification...');
      
      // Test 1: Quran Module Offline Capability
      result.quranOffline = await _verifyQuranOffline();
      
      // Test 2: Hadith Module Offline Capability
      result.hadithOffline = await _verifyHadithOffline();
      
      // Test 3: Athkar Module Offline Capability
      result.athkarOffline = await _verifyAthkarOffline();
      
      // Test 4: Prayer Times Offline Capability
      result.prayerTimesOffline = await _verifyPrayerTimesOffline();
      
      // Test 5: Location Services Offline Capability
      result.locationOffline = await _verifyLocationOffline();
      
      // Test 6: Local Storage Integrity
      result.localStorageIntegrity = await _verifyLocalStorageIntegrity();
      
      // Test 7: Cache Performance
      result.cachePerformance = await _verifyCachePerformance();
      
      // Calculate overall score
      result.calculateOverallScore();
      
      debugPrint('Offline verification completed with score: ${result.overallScore}%');
      
    } catch (e) {
      debugPrint('Error during offline verification: $e');
      result.error = e.toString();
    }
    
    return result;
  }

  /// Verify Quran module works offline
  Future<bool> _verifyQuranOffline() async {
    try {
      final quranProvider = QuranProvider();
      await quranProvider.initialize();
      
      // Test basic functionality
      final surahs = quranProvider.surahs;
      if (surahs.length != 114) {
        debugPrint('Quran offline test failed: Expected 114 surahs, got ${surahs.length}');
        return false;
      }
      
      // Test ayah retrieval
      final firstAyah = quranProvider.getAyah(1, 1);
      if (firstAyah == null) {
        debugPrint('Quran offline test failed: Could not retrieve first ayah');
        return false;
      }
      
      // Test search functionality
      final searchResults = await quranProvider.searchQuran('الله');
      if (searchResults.isEmpty) {
        debugPrint('Quran offline test failed: Search returned no results');
        return false;
      }
      
      debugPrint('Quran offline verification: PASSED');
      return true;
    } catch (e) {
      debugPrint('Quran offline verification failed: $e');
      return false;
    }
  }

  /// Verify Hadith module works offline
  Future<bool> _verifyHadithOffline() async {
    try {
      final hadithProvider = HadithProvider();
      await hadithProvider.initialize();
      
      // Test collections loading
      final collections = hadithProvider.collections;
      if (collections.length != 6) {
        debugPrint('Hadith offline test failed: Expected 6 collections, got ${collections.length}');
        return false;
      }
      
      // Test books loading
      final books = await hadithProvider.getCollectionBooks('bukhari');
      if (books.isEmpty) {
        debugPrint('Hadith offline test failed: No books found for Bukhari');
        return false;
      }
      
      // Test hadith loading
      final hadiths = await hadithProvider.getBookHadiths('bukhari', 1);
      if (hadiths.isEmpty) {
        debugPrint('Hadith offline test failed: No hadiths found for Bukhari book 1');
        return false;
      }
      
      // Test favorites functionality
      hadithProvider.isInFavorites('bukhari', 1, 1);
      // This should work without throwing an error
      
      debugPrint('Hadith offline verification: PASSED');
      return true;
    } catch (e) {
      debugPrint('Hadith offline verification failed: $e');
      return false;
    }
  }

  /// Verify Athkar module works offline
  Future<bool> _verifyAthkarOffline() async {
    try {
      final athkarProvider = AthkarProvider();

      // Test athkar categories loading
      final categories = athkarProvider.categories;
      if (categories.isEmpty) {
        debugPrint('Athkar offline test failed: No categories found');
        return false;
      }

      // Test athkar routines loading
      final routines = athkarProvider.routines;
      if (routines.isEmpty) {
        debugPrint('Athkar offline test failed: No routines found');
        return false;
      }

      debugPrint('Athkar offline verification: PASSED');
      return true;
    } catch (e) {
      debugPrint('Athkar offline verification failed: $e');
      return false;
    }
  }

  /// Verify Prayer Times work offline
  Future<bool> _verifyPrayerTimesOffline() async {
    try {
      // Test with Jordan coordinates
      const latitude = 31.9454;
      const longitude = 35.9284;
      final date = DateTime.now();
      
      // Test Adhan package calculation (should work offline)
      final prayerTimes = await PrayerTimesService.getPrayerTimesWithAdhan(
        latitude,
        longitude,
        date,
      );
      
      if (prayerTimes == null) {
        debugPrint('Prayer times offline test failed: No prayer times calculated');
        return false;
      }
      
      // Verify all prayer times are present
      if (prayerTimes.fajr.isEmpty || 
          prayerTimes.dhuhr.isEmpty || 
          prayerTimes.asr.isEmpty || 
          prayerTimes.maghrib.isEmpty || 
          prayerTimes.isha.isEmpty) {
        debugPrint('Prayer times offline test failed: Missing prayer times');
        return false;
      }
      
      debugPrint('Prayer times offline verification: PASSED');
      return true;
    } catch (e) {
      debugPrint('Prayer times offline verification failed: $e');
      return false;
    }
  }

  /// Verify Location services work offline
  Future<bool> _verifyLocationOffline() async {
    try {
      final locationService = EnhancedLocationService();
      await locationService.initialize();
      
      // Test Jordan cities data (should work offline)
      final cities = locationService.getAllJordanCities();
      if (cities.isEmpty) {
        debugPrint('Location offline test failed: No Jordan cities found');
        return false;
      }
      
      // Test default location (should work offline)
      final ammanLocation = locationService.getLocationByCity('amman');
      if (ammanLocation == null) {
        debugPrint('Location offline test failed: Could not get Amman location');
        return false;
      }
      
      debugPrint('Location offline verification: PASSED');
      return true;
    } catch (e) {
      debugPrint('Location offline verification failed: $e');
      return false;
    }
  }

  /// Verify local storage integrity
  Future<bool> _verifyLocalStorageIntegrity() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Test write operation
      const testKey = 'offline_verification_test';
      final testValue = 'test_data_${DateTime.now().millisecondsSinceEpoch}';
      
      await prefs.setString(testKey, testValue);
      
      // Test read operation
      final retrievedValue = prefs.getString(testKey);
      if (retrievedValue != testValue) {
        debugPrint('Local storage test failed: Data mismatch');
        return false;
      }
      
      // Clean up test data
      await prefs.remove(testKey);
      
      debugPrint('Local storage integrity verification: PASSED');
      return true;
    } catch (e) {
      debugPrint('Local storage integrity verification failed: $e');
      return false;
    }
  }

  /// Verify cache performance
  Future<bool> _verifyCachePerformance() async {
    try {
      final stopwatch = Stopwatch()..start();
      
      // Test multiple cache operations
      final prefs = await SharedPreferences.getInstance();
      
      // Write test
      for (int i = 0; i < 10; i++) {
        await prefs.setString('cache_test_$i', 'data_$i');
      }
      
      // Read test
      for (int i = 0; i < 10; i++) {
        final value = prefs.getString('cache_test_$i');
        if (value != 'data_$i') {
          debugPrint('Cache performance test failed: Data mismatch at index $i');
          return false;
        }
      }
      
      // Clean up
      for (int i = 0; i < 10; i++) {
        await prefs.remove('cache_test_$i');
      }
      
      stopwatch.stop();
      final elapsedMs = stopwatch.elapsedMilliseconds;
      
      // Performance should be under 1 second for 20 operations
      if (elapsedMs > 1000) {
        debugPrint('Cache performance test failed: Too slow ($elapsedMs ms)');
        return false;
      }
      
      debugPrint('Cache performance verification: PASSED (${elapsedMs}ms)');
      return true;
    } catch (e) {
      debugPrint('Cache performance verification failed: $e');
      return false;
    }
  }

  /// Generate offline verification report
  Future<String> generateOfflineReport() async {
    final result = await verifyOfflineCapabilities();
    
    final report = StringBuffer();
    report.writeln('=== OFFLINE FUNCTIONALITY VERIFICATION REPORT ===');
    report.writeln('Generated: ${DateTime.now().toIso8601String()}');
    report.writeln('Overall Score: ${result.overallScore}%');
    report.writeln('');
    
    report.writeln('Module Test Results:');
    report.writeln('- Quran Module: ${result.quranOffline ? "✅ PASSED" : "❌ FAILED"}');
    report.writeln('- Hadith Module: ${result.hadithOffline ? "✅ PASSED" : "❌ FAILED"}');
    report.writeln('- Athkar Module: ${result.athkarOffline ? "✅ PASSED" : "❌ FAILED"}');
    report.writeln('- Prayer Times: ${result.prayerTimesOffline ? "✅ PASSED" : "❌ FAILED"}');
    report.writeln('- Location Services: ${result.locationOffline ? "✅ PASSED" : "❌ FAILED"}');
    report.writeln('- Local Storage: ${result.localStorageIntegrity ? "✅ PASSED" : "❌ FAILED"}');
    report.writeln('- Cache Performance: ${result.cachePerformance ? "✅ PASSED" : "❌ FAILED"}');
    
    if (result.error != null) {
      report.writeln('');
      report.writeln('Errors:');
      report.writeln(result.error);
    }
    
    report.writeln('');
    report.writeln('=== END OF REPORT ===');
    
    return report.toString();
  }
}

class OfflineVerificationResult {
  bool quranOffline = false;
  bool hadithOffline = false;
  bool athkarOffline = false;
  bool prayerTimesOffline = false;
  bool locationOffline = false;
  bool localStorageIntegrity = false;
  bool cachePerformance = false;
  int overallScore = 0;
  String? error;

  void calculateOverallScore() {
    final tests = [
      quranOffline,
      hadithOffline,
      athkarOffline,
      prayerTimesOffline,
      locationOffline,
      localStorageIntegrity,
      cachePerformance,
    ];
    
    final passedTests = tests.where((test) => test).length;
    overallScore = ((passedTests / tests.length) * 100).round();
  }

  bool get allTestsPassed => overallScore == 100;
}
