import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../services/floating_window_service.dart';
import '../services/permissions_service.dart';
import '../widgets/loading_overlay.dart';

class FloatingWindowScreen extends StatefulWidget {
  const FloatingWindowScreen({super.key});

  @override
  State<FloatingWindowScreen> createState() => _FloatingWindowScreenState();
}

class _FloatingWindowScreenState extends State<FloatingWindowScreen> {
  bool _hasOverlayPermission = false;
  bool _isFloatingCounterVisible = false;
  bool _isQuickAccessVisible = false;
  bool _isLoading = false;

  final TextEditingController _dhikrTextController = TextEditingController();
  final TextEditingController _transliterationController = TextEditingController();
  final TextEditingController _targetCountController = TextEditingController(text: '33');
  String _selectedColor = '2E7D32';

  final List<Map<String, dynamic>> _presetDhikr = [
    {
      'text': 'سُبْحَانَ اللَّهِ',
      'transliteration': '<PERSON>han Allah',
      'count': 33,
      'color': '2E7D32',
    },
    {
      'text': 'الْحَمْدُ لِلَّهِ',
      'transliteration': 'Alhamdulillah',
      'count': 33,
      'color': '1976D2',
    },
    {
      'text': 'اللَّهُ أَكْبَرُ',
      'transliteration': 'Allahu Akbar',
      'count': 34,
      'color': '7B1FA2',
    },
    {
      'text': 'لَا إِلَهَ إِلَّا اللَّهُ',
      'transliteration': 'La ilaha illa Allah',
      'count': 100,
      'color': '388E3C',
    },
    {
      'text': 'أَسْتَغْفِرُ اللَّهَ',
      'transliteration': 'Astaghfirullah',
      'count': 100,
      'color': 'E91E63',
    },
  ];

  @override
  void initState() {
    super.initState();
    _checkPermissions();
    _loadFloatingWindowState();
  }

  @override
  void dispose() {
    _dhikrTextController.dispose();
    _transliterationController.dispose();
    _targetCountController.dispose();
    super.dispose();
  }

  Future<void> _checkPermissions() async {
    setState(() => _isLoading = true);
    
    try {
      _hasOverlayPermission = await PermissionsService.isOverlayPermissionGranted();
    } catch (e) {
      debugPrint('Error checking overlay permission: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadFloatingWindowState() async {
    try {
      _isFloatingCounterVisible = FloatingWindowService.isWindowVisible;
    } catch (e) {
      debugPrint('Error loading floating window state: $e');
    }
  }

  Future<void> _requestOverlayPermission() async {
    setState(() => _isLoading = true);
    
    try {
      final granted = await PermissionsService.requestOverlayPermissionWithGuidance(context);
      setState(() => _hasOverlayPermission = granted);
      
      if (granted && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم منح إذن النوافذ العائمة بنجاح'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في طلب الإذن: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _showFloatingCounter() async {
    if (!_hasOverlayPermission) {
      await _requestOverlayPermission();
      return;
    }

    if (_dhikrTextController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال نص الذكر'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);
    
    try {
      await FloatingWindowService.showFloatingCounter(
        dhikrText: _dhikrTextController.text,
        transliteration: _transliterationController.text,
        targetCount: int.tryParse(_targetCountController.text) ?? 33,
        colorHex: _selectedColor,
      );
      
      setState(() => _isFloatingCounterVisible = true);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم عرض العداد العائم بنجاح'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في عرض العداد العائم: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _hideFloatingCounter() async {
    setState(() => _isLoading = true);
    
    try {
      await FloatingWindowService.hideFloatingCounter();
      setState(() => _isFloatingCounterVisible = false);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إخفاء العداد العائم'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إخفاء العداد العائم: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _showFloatingQuickAccess() async {
    if (!_hasOverlayPermission) {
      await _requestOverlayPermission();
      return;
    }

    setState(() => _isLoading = true);
    
    try {
      await FloatingWindowService.showFloatingQuickAccess();
      setState(() => _isQuickAccessVisible = true);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم عرض الوصول السريع العائم'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في عرض الوصول السريع: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _hideFloatingQuickAccess() async {
    setState(() => _isLoading = true);
    
    try {
      await FloatingWindowService.hideFloatingQuickAccess();
      setState(() => _isQuickAccessVisible = false);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إخفاء الوصول السريع العائم'),
            backgroundColor: AppTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إخفاء الوصول السريع: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _loadPresetDhikr(Map<String, dynamic> preset) {
    setState(() {
      _dhikrTextController.text = preset['text'];
      _transliterationController.text = preset['transliteration'];
      _targetCountController.text = preset['count'].toString();
      _selectedColor = preset['color'];
    });
  }

  Color _getColorFromHex(String hexColor) {
    return Color(int.parse('FF$hexColor', radix: 16));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('النوافذ العائمة'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Permission status card
            Card(
              color: _hasOverlayPermission ? Colors.green[50] : Colors.red[50],
              child: ListTile(
                leading: Icon(
                  _hasOverlayPermission ? Icons.check_circle : Icons.error,
                  color: _hasOverlayPermission ? Colors.green : Colors.red,
                ),
                title: Text(
                  _hasOverlayPermission 
                      ? 'إذن النوافذ العائمة مُفعل'
                      : 'إذن النوافذ العائمة غير مُفعل',
                ),
                subtitle: Text(
                  _hasOverlayPermission
                      ? 'يمكنك الآن استخدام النوافذ العائمة'
                      : 'يرجى تفعيل إذن النوافذ العائمة لاستخدام هذه الميزة',
                ),
                trailing: _hasOverlayPermission 
                    ? null 
                    : ElevatedButton(
                        onPressed: _requestOverlayPermission,
                        child: const Text('تفعيل'),
                      ),
              ),
            ),

            const SizedBox(height: 16),

            // Floating counter section
            Card(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'العداد العائم',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  
                  // Preset dhikr buttons
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Wrap(
                      spacing: 8.0,
                      runSpacing: 8.0,
                      children: _presetDhikr.map((preset) => 
                        ElevatedButton(
                          onPressed: () => _loadPresetDhikr(preset),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _getColorFromHex(preset['color']),
                            foregroundColor: Colors.white,
                          ),
                          child: Text(preset['transliteration']),
                        ),
                      ).toList(),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Custom dhikr input
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        TextField(
                          controller: _dhikrTextController,
                          decoration: const InputDecoration(
                            labelText: 'نص الذكر (عربي)',
                            border: OutlineInputBorder(),
                          ),
                          textDirection: TextDirection.rtl,
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _transliterationController,
                          decoration: const InputDecoration(
                            labelText: 'النطق بالإنجليزية (اختياري)',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _targetCountController,
                          decoration: const InputDecoration(
                            labelText: 'العدد المطلوب',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ],
                    ),
                  ),

                  // Counter controls
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _hasOverlayPermission && !_isFloatingCounterVisible
                                ? _showFloatingCounter
                                : null,
                            icon: const Icon(Icons.play_arrow),
                            label: const Text('عرض العداد'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryGreen,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isFloatingCounterVisible
                                ? _hideFloatingCounter
                                : null,
                            icon: const Icon(Icons.stop),
                            label: const Text('إخفاء العداد'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Quick access section
            Card(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'الوصول السريع العائم',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.0),
                    child: Text(
                      'زر عائم صغير للوصول السريع إلى الأذكار والعدادات',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _hasOverlayPermission && !_isQuickAccessVisible
                                ? _showFloatingQuickAccess
                                : null,
                            icon: const Icon(Icons.touch_app),
                            label: const Text('عرض الوصول السريع'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue[700],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isQuickAccessVisible
                                ? _hideFloatingQuickAccess
                                : null,
                            icon: const Icon(Icons.close),
                            label: const Text('إخفاء الوصول السريع'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Instructions card
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تعليمات الاستخدام',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '• النوافذ العائمة تعمل خارج التطبيق\n'
                      '• يمكنك تحريك النوافذ العائمة على الشاشة\n'
                      '• العدادات تحفظ التقدم تلقائياً\n'
                      '• يمكن استخدام النوافذ العائمة مع تطبيقات أخرى\n'
                      '• الضغط على النافذة العائمة يفتح التطبيق',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
