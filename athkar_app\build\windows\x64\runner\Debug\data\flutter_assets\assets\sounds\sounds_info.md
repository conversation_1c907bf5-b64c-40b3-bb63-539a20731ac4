# Islamic Athkar App - Audio Files

This directory contains prebuilt Islamic audio files for the Athkar app.

## Sound Categories

### Click Sounds
- `default_click.mp3` - Default counter click sound
- `soft_click.mp3` - Soft, gentle click sound
- `wood_click.mp3` - Wooden tasbih bead sound
- `bell_click.mp3` - Soft bell-like click sound

### Completion Sounds
- `completion.mp3` - General completion sound
- `dhikr_completion.mp3` - Dhikr completion with Islamic tone
- `tasbeeh_complete.mp3` - Tasbeeh completion sound

### Prayer Sounds
- `adhan_short.mp3` - Short adhan clip for reminders
- `adhan_full.mp3` - Full adhan for prayer time notifications
- `prayer_reminder.mp3` - Gentle prayer reminder tone

### Notification Sounds
- `notification_sound.mp3` - Default notification sound
- `athkar_reminder.mp3` - Athkar reminder notification
- `morning_athkar.mp3` - Morning athkar reminder
- `evening_athkar.mp3` - Evening athkar reminder

## Audio Specifications
- Format: MP3
- Quality: 128kbps
- Duration: 1-30 seconds (varies by type)
- Volume: Normalized to prevent distortion

## Usage
These sounds are used by the AudioService class for:
- Counter click feedback
- Completion notifications
- Prayer time reminders
- Custom notification sounds

## Licensing
All sounds are either:
- Created specifically for this app
- Licensed under Creative Commons
- Royalty-free Islamic audio content

## Adding Custom Sounds
Users can upload custom sounds through the app's settings.
Custom sounds are stored in the app's documents directory.
