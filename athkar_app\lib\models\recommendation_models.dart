import 'package:json_annotation/json_annotation.dart';

part 'recommendation_models.g.dart';

enum RecommendationType {
  personalized,
  trending,
  similar,
  contextual,
}

enum InteractionType {
  view,
  practice,
  complete,
  favorite,
  share,
  skip,
}

enum UserMood {
  stressed,
  grateful,
  seekingGuidance,
  repentant,
  peaceful,
  anxious,
  joyful,
}

enum UserState {
  traveling,
  sick,
  studying,
  working,
  praying,
  fasting,
}

enum TimeOfDay {
  morning,
  afternoon,
  evening,
  night,
}

@JsonSerializable()
class AthkarRecommendation {
  final String athkarId;
  final String title;
  final String description;
  final double score;
  final String reason;
  final RecommendationType type;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;

  AthkarRecommendation({
    required this.athkarId,
    required this.title,
    required this.description,
    required this.score,
    required this.reason,
    required this.type,
    this.metadata = const {},
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory AthkarRecommendation.fromJson(Map<String, dynamic> json) => _$AthkarRecommendationFromJson(json);
  Map<String, dynamic> toJson() => _$AthkarRecommendationToJson(this);
}

@JsonSerializable()
class ScheduleRecommendation {
  final String id;
  final String title;
  final String description;
  final TimeOfDay recommendedTime;
  final Duration duration;
  final double confidence;
  final String reason;
  final Map<String, dynamic> metadata;

  const ScheduleRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.recommendedTime,
    required this.duration,
    required this.confidence,
    required this.reason,
    this.metadata = const {},
  });

  factory ScheduleRecommendation.fromJson(Map<String, dynamic> json) => _$ScheduleRecommendationFromJson(json);
  Map<String, dynamic> toJson() => _$ScheduleRecommendationToJson(this);
}

@JsonSerializable()
class ContentRecommendation {
  final String id;
  final String type; // athkar, dua, verse, hadith
  final String title;
  final String content;
  final String? translation;
  final String? transliteration;
  final double relevanceScore;
  final String reason;
  final Map<String, dynamic> metadata;

  const ContentRecommendation({
    required this.id,
    required this.type,
    required this.title,
    required this.content,
    this.translation,
    this.transliteration,
    required this.relevanceScore,
    required this.reason,
    this.metadata = const {},
  });

  factory ContentRecommendation.fromJson(Map<String, dynamic> json) => _$ContentRecommendationFromJson(json);
  Map<String, dynamic> toJson() => _$ContentRecommendationToJson(this);
}

@JsonSerializable()
class UserInteraction {
  final String id;
  final String userId;
  final String athkarId;
  final InteractionType type;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const UserInteraction({
    required this.id,
    required this.userId,
    required this.athkarId,
    required this.type,
    required this.timestamp,
    this.metadata = const {},
  });

  factory UserInteraction.fromJson(Map<String, dynamic> json) => _$UserInteractionFromJson(json);
  Map<String, dynamic> toJson() => _$UserInteractionToJson(this);
}

@JsonSerializable()
class UserProfile {
  final String userId;
  final Map<String, double> preferences;
  final Map<String, dynamic> demographics;
  final Map<String, dynamic> behaviorPatterns;
  final DateTime lastUpdated;

  UserProfile({
    required this.userId,
    required this.preferences,
    required this.demographics,
    required this.behaviorPatterns,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  factory UserProfile.fromJson(Map<String, dynamic> json) => _$UserProfileFromJson(json);
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);
}

@JsonSerializable()
class UserPatterns {
  final double morningActivity;
  final double afternoonActivity;
  final double eveningActivity;
  final double nightActivity;
  final Map<String, double> categoryPreferences;
  final Map<String, double> difficultyPreferences;
  final double averageSessionDuration;

  const UserPatterns({
    required this.morningActivity,
    required this.afternoonActivity,
    required this.eveningActivity,
    required this.nightActivity,
    this.categoryPreferences = const {},
    this.difficultyPreferences = const {},
    this.averageSessionDuration = 0.0,
  });

  factory UserPatterns.fromJson(Map<String, dynamic> json) => _$UserPatternsFromJson(json);
  Map<String, dynamic> toJson() => _$UserPatternsToJson(this);
}

@JsonSerializable()
class RecommendationFeedback {
  final String id;
  final String userId;
  final String recommendationId;
  final String athkarId;
  final bool wasHelpful;
  final String? feedback;
  final DateTime timestamp;

  const RecommendationFeedback({
    required this.id,
    required this.userId,
    required this.recommendationId,
    required this.athkarId,
    required this.wasHelpful,
    this.feedback,
    required this.timestamp,
  });

  factory RecommendationFeedback.fromJson(Map<String, dynamic> json) => _$RecommendationFeedbackFromJson(json);
  Map<String, dynamic> toJson() => _$RecommendationFeedbackToJson(this);
}

@JsonSerializable()
class PersonalizationSettings {
  final String userId;
  final bool enablePersonalizedRecommendations;
  final bool enableTrendingRecommendations;
  final bool enableContextualRecommendations;
  final List<String> preferredCategories;
  final List<String> excludedCategories;
  final double difficultyPreference; // 0.0 = easy, 1.0 = hard
  final Duration preferredSessionDuration;
  final List<TimeOfDay> preferredTimes;

  const PersonalizationSettings({
    required this.userId,
    this.enablePersonalizedRecommendations = true,
    this.enableTrendingRecommendations = true,
    this.enableContextualRecommendations = true,
    this.preferredCategories = const [],
    this.excludedCategories = const [],
    this.difficultyPreference = 0.5,
    this.preferredSessionDuration = const Duration(minutes: 10),
    this.preferredTimes = const [],
  });

  factory PersonalizationSettings.fromJson(Map<String, dynamic> json) => _$PersonalizationSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$PersonalizationSettingsToJson(this);
}

@JsonSerializable()
class RecommendationMetrics {
  final String userId;
  final int totalRecommendations;
  final int acceptedRecommendations;
  final int rejectedRecommendations;
  final double acceptanceRate;
  final Map<RecommendationType, int> recommendationsByType;
  final Map<String, double> categoryAcceptanceRates;
  final DateTime lastUpdated;

  RecommendationMetrics({
    required this.userId,
    required this.totalRecommendations,
    required this.acceptedRecommendations,
    required this.rejectedRecommendations,
    required this.acceptanceRate,
    required this.recommendationsByType,
    required this.categoryAcceptanceRates,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  factory RecommendationMetrics.fromJson(Map<String, dynamic> json) => _$RecommendationMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$RecommendationMetricsToJson(this);
}

@JsonSerializable()
class SmartNotification {
  final String id;
  final String userId;
  final String title;
  final String body;
  final String? athkarId;
  final TimeOfDay optimalTime;
  final double confidence;
  final Map<String, dynamic> payload;
  final DateTime scheduledFor;
  final bool isSent;

  SmartNotification({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    this.athkarId,
    required this.optimalTime,
    required this.confidence,
    this.payload = const {},
    required this.scheduledFor,
    this.isSent = false,
  });

  factory SmartNotification.fromJson(Map<String, dynamic> json) => _$SmartNotificationFromJson(json);
  Map<String, dynamic> toJson() => _$SmartNotificationToJson(this);
}

@JsonSerializable()
class LearningModel {
  final String id;
  final String name;
  final String type; // collaborative_filtering, content_based, hybrid
  final Map<String, dynamic> parameters;
  final double accuracy;
  final DateTime trainedAt;
  final int trainingDataSize;

  const LearningModel({
    required this.id,
    required this.name,
    required this.type,
    required this.parameters,
    required this.accuracy,
    required this.trainedAt,
    required this.trainingDataSize,
  });

  factory LearningModel.fromJson(Map<String, dynamic> json) => _$LearningModelFromJson(json);
  Map<String, dynamic> toJson() => _$LearningModelToJson(this);
}

@JsonSerializable()
class ContextualData {
  final DateTime timestamp;
  final TimeOfDay timeOfDay;
  final int dayOfWeek;
  final bool isRamadan;
  final bool isJumma;
  final String? location;
  final String? weather;
  final UserMood? mood;
  final UserState? state;
  final Map<String, dynamic> additionalContext;

  ContextualData({
    required this.timestamp,
    required this.timeOfDay,
    required this.dayOfWeek,
    required this.isRamadan,
    required this.isJumma,
    this.location,
    this.weather,
    this.mood,
    this.state,
    this.additionalContext = const {},
  });

  factory ContextualData.fromJson(Map<String, dynamic> json) => _$ContextualDataFromJson(json);
  Map<String, dynamic> toJson() => _$ContextualDataToJson(this);
}

@JsonSerializable()
class RecommendationExperiment {
  final String id;
  final String name;
  final String description;
  final String algorithmA;
  final String algorithmB;
  final double trafficSplit;
  final DateTime startDate;
  final DateTime? endDate;
  final Map<String, dynamic> metrics;
  final bool isActive;

  const RecommendationExperiment({
    required this.id,
    required this.name,
    required this.description,
    required this.algorithmA,
    required this.algorithmB,
    required this.trafficSplit,
    required this.startDate,
    this.endDate,
    this.metrics = const {},
    this.isActive = true,
  });

  factory RecommendationExperiment.fromJson(Map<String, dynamic> json) => _$RecommendationExperimentFromJson(json);
  Map<String, dynamic> toJson() => _$RecommendationExperimentToJson(this);
}
