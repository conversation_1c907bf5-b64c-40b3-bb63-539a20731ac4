org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# Kotlin compiler optimizations
kotlin.daemon.useFallbackStrategy=true
kotlin.compiler.execution.strategy=in-process
kotlin.incremental=false

# Gradle optimizations
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
