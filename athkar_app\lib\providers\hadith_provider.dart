import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/hadith_models.dart';

class HadithProvider extends ChangeNotifier {
  static final HadithProvider _instance = HadithProvider._internal();
  factory HadithProvider() => _instance;
  HadithProvider._internal();

  // State variables
  List<HadithCollection> _collections = [];
  Map<String, List<HadithBook>> _collectionBooks = {};
  Map<String, List<HadithData>> _bookHadiths = {};
  List<HadithFavorite> _favorites = [];
  bool _isLoaded = false;
  bool _isLoading = false;
  String? _error;

  // Cache for search results
  Map<String, List<HadithSearchResult>> _searchCache = {};

  // Getters
  List<HadithCollection> get collections => _collections;
  List<HadithFavorite> get favorites => _favorites;
  bool get isLoaded => _isLoaded;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize Hadith provider
  Future<void> initialize() async {
    if (_isLoaded || _isLoading) return;
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _loadCollections();
      await _loadFavorites();
      _isLoaded = true;
      debugPrint('HadithProvider initialized successfully');
    } catch (e) {
      _error = 'Failed to load Hadith data: $e';
      debugPrint('Error initializing HadithProvider: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load Hadith collections
  Future<void> _loadCollections() async {
    _collections = [
      HadithCollection(
        id: 'bukhari',
        name: 'Sahih al-Bukhari',
        arabicName: 'صحيح البخاري',
        englishName: 'Sahih al-Bukhari',
        description: 'The most authentic collection of Hadith',
        arabicDescription: 'أصح كتاب بعد كتاب الله',
        totalBooks: 97,
        totalHadiths: 7563,
        author: 'Imam al-Bukhari',
        arabicAuthor: 'الإمام البخاري',
      ),
      HadithCollection(
        id: 'muslim',
        name: 'Sahih Muslim',
        arabicName: 'صحيح مسلم',
        englishName: 'Sahih Muslim',
        description: 'The second most authentic collection of Hadith',
        arabicDescription: 'ثاني أصح الكتب بعد صحيح البخاري',
        totalBooks: 56,
        totalHadiths: 7190,
        author: 'Imam Muslim',
        arabicAuthor: 'الإمام مسلم',
      ),
      HadithCollection(
        id: 'abudawud',
        name: 'Sunan Abu Dawud',
        arabicName: 'سنن أبي داود',
        englishName: 'Sunan Abu Dawud',
        description: 'Collection focusing on legal matters',
        arabicDescription: 'مجموعة تركز على الأحكام الفقهية',
        totalBooks: 43,
        totalHadiths: 5274,
        author: 'Imam Abu Dawud',
        arabicAuthor: 'الإمام أبو داود',
      ),
      HadithCollection(
        id: 'tirmidhi',
        name: 'Jami\' at-Tirmidhi',
        arabicName: 'جامع الترمذي',
        englishName: 'Jami\' at-Tirmidhi',
        description: 'Collection with grading of Hadith authenticity',
        arabicDescription: 'مجموعة مع تقييم صحة الأحاديث',
        totalBooks: 46,
        totalHadiths: 3956,
        author: 'Imam at-Tirmidhi',
        arabicAuthor: 'الإمام الترمذي',
      ),
      HadithCollection(
        id: 'nasai',
        name: 'Sunan an-Nasa\'i',
        arabicName: 'سنن النسائي',
        englishName: 'Sunan an-Nasa\'i',
        description: 'Collection known for strict authentication',
        arabicDescription: 'مجموعة معروفة بالتشدد في التوثيق',
        totalBooks: 51,
        totalHadiths: 5761,
        author: 'Imam an-Nasa\'i',
        arabicAuthor: 'الإمام النسائي',
      ),
      HadithCollection(
        id: 'ibnmajah',
        name: 'Sunan Ibn Majah',
        arabicName: 'سنن ابن ماجه',
        englishName: 'Sunan Ibn Majah',
        description: 'The sixth book of the Kutub as-Sittah',
        arabicDescription: 'الكتاب السادس من الكتب الستة',
        totalBooks: 37,
        totalHadiths: 4341,
        author: 'Imam Ibn Majah',
        arabicAuthor: 'الإمام ابن ماجه',
      ),
    ];
    
    debugPrint('Loaded ${_collections.length} Hadith collections');
  }

  /// Get books for a specific collection
  Future<List<HadithBook>> getCollectionBooks(String collectionId) async {
    if (_collectionBooks.containsKey(collectionId)) {
      return _collectionBooks[collectionId]!;
    }

    try {
      final books = <HadithBook>[];

      // Generate sample books for each collection
      final collection = _collections.firstWhere((c) => c.id == collectionId);
      final bookCount = collection.totalBooks;

      for (int i = 1; i <= bookCount; i++) {
        final book = HadithBook(
          bookNumber: i,
          name: 'Book $i',
          arabicName: 'كتاب $i',
          englishName: 'Book $i',
          collectionId: collectionId,
          totalHadiths: (collection.totalHadiths / bookCount).round(),
          description: 'Book $i of ${collection.englishName}',
          arabicDescription: 'كتاب $i من ${collection.arabicName}',
        );
        books.add(book);
      }

      _collectionBooks[collectionId] = books;
      debugPrint('Generated ${books.length} books for collection $collectionId');
      return books;
    } catch (e) {
      debugPrint('Error loading books for collection $collectionId: $e');
      return [];
    }
  }

  /// Get hadiths for a specific book
  Future<List<HadithData>> getBookHadiths(String collectionId, int bookNumber) async {
    final key = '${collectionId}_$bookNumber';

    if (_bookHadiths.containsKey(key)) {
      return _bookHadiths[key]!;
    }

    try {
      final hadiths = <HadithData>[];

      // Generate sample hadiths for demonstration
      // In a real implementation, this would use the hadith package or load from assets
      final collection = _collections.firstWhere((c) => c.id == collectionId);
      final hadithCount = (collection.totalHadiths / collection.totalBooks).round();

      for (int i = 1; i <= hadithCount && i <= 50; i++) { // Limit to 50 for demo
        final hadith = HadithData(
          hadithNumber: i,
          bookNumber: bookNumber,
          collectionId: collectionId,
          arabicText: 'هذا نص تجريبي للحديث رقم $i من كتاب $bookNumber في مجموعة ${collection.arabicName}. في التطبيق الحقيقي، سيتم تحميل النص الفعلي للحديث من قاعدة البيانات أو ملفات الأصول.',
          englishText: 'This is a sample text for hadith number $i from book $bookNumber in collection ${collection.englishName}. In a real application, the actual hadith text would be loaded from database or asset files.',
          narrator: 'Sample Narrator $i',
          arabicNarrator: 'راوي تجريبي $i',
          reference: '$collectionId:$bookNumber:$i',
          grade: i % 3 == 0 ? 'Sahih' : (i % 2 == 0 ? 'Hasan' : 'Daif'),
          arabicGrade: i % 3 == 0 ? 'صحيح' : (i % 2 == 0 ? 'حسن' : 'ضعيف'),
          notes: i % 5 == 0 ? 'Sample note for hadith $i' : null,
          arabicNotes: i % 5 == 0 ? 'ملاحظة تجريبية للحديث $i' : null,
        );
        hadiths.add(hadith);
      }

      _bookHadiths[key] = hadiths;
      debugPrint('Generated ${hadiths.length} sample hadiths for book $bookNumber in collection $collectionId');
      return hadiths;
    } catch (e) {
      debugPrint('Error loading hadiths for book $bookNumber in collection $collectionId: $e');
      return [];
    }
  }

  /// Get a specific hadith
  Future<HadithData?> getHadith(String collectionId, int bookNumber, int hadithNumber) async {
    try {
      final bookHadiths = await getBookHadiths(collectionId, bookNumber);
      return bookHadiths.firstWhere(
        (hadith) => hadith.hadithNumber == hadithNumber,
        orElse: () => bookHadiths.isNotEmpty ? bookHadiths.first : throw Exception('Hadith not found'),
      );
    } catch (e) {
      debugPrint('Error getting hadith $collectionId:$bookNumber:$hadithNumber: $e');
      return null;
    }
  }

  /// Search hadiths across all collections
  Future<List<HadithSearchResult>> searchHadiths(String query, {
    List<String>? collections,
    String searchType = 'all', // 'all', 'arabic', 'english', 'narrator'
    int limit = 50,
  }) async {
    if (query.trim().isEmpty) return [];
    
    final cacheKey = '${query}_${collections?.join(',') ?? 'all'}_${searchType}_$limit';
    if (_searchCache.containsKey(cacheKey)) {
      return _searchCache[cacheKey]!;
    }

    try {
      final results = <HadithSearchResult>[];
      final searchTerms = query.trim().toLowerCase().split(' ');
      final collectionsToSearch = collections ?? _collections.map((c) => c.id).toList();

      for (final collectionId in collectionsToSearch) {
        final collection = _collections.firstWhere((c) => c.id == collectionId);
        final books = await getCollectionBooks(collectionId);
        
        for (final book in books) {
          final hadiths = await getBookHadiths(collectionId, book.bookNumber);
          
          for (final hadith in hadiths) {
            final matchedWords = <String>[];
            double relevanceScore = 0.0;
            String matchType = 'arabic';
            
            // Search in Arabic text
            if (searchType == 'all' || searchType == 'arabic') {
              for (final term in searchTerms) {
                if (hadith.arabicText.toLowerCase().contains(term)) {
                  matchedWords.add(term);
                  relevanceScore += 2.0; // Higher weight for Arabic matches
                  matchType = 'arabic';
                }
              }
            }
            
            // Search in English text
            if (searchType == 'all' || searchType == 'english') {
              for (final term in searchTerms) {
                if (hadith.englishText.toLowerCase().contains(term)) {
                  matchedWords.add(term);
                  relevanceScore += 1.5;
                  if (matchType != 'arabic') matchType = 'english';
                }
              }
            }
            
            // Search in narrator
            if (searchType == 'all' || searchType == 'narrator') {
              for (final term in searchTerms) {
                if (hadith.narrator.toLowerCase().contains(term) ||
                    hadith.arabicNarrator.toLowerCase().contains(term)) {
                  matchedWords.add(term);
                  relevanceScore += 1.0;
                  if (matchType == 'arabic') matchType = 'narrator';
                }
              }
            }
            
            if (matchedWords.isNotEmpty) {
              results.add(HadithSearchResult(
                hadith: hadith,
                book: book,
                collection: collection,
                relevanceScore: relevanceScore / searchTerms.length,
                matchedWords: matchedWords,
                searchType: matchType,
              ));
            }
          }
        }
      }
      
      // Sort by relevance score
      results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
      
      final limitedResults = results.take(limit).toList();
      _searchCache[cacheKey] = limitedResults;
      
      debugPrint('Found ${limitedResults.length} hadith search results for query: $query');
      return limitedResults;
    } catch (e) {
      debugPrint('Error searching hadiths: $e');
      return [];
    }
  }

  /// Load favorites from local storage
  Future<void> _loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList('hadith_favorites') ?? [];
      
      _favorites = favoritesJson.map((json) {
        final Map<String, dynamic> data = Map<String, dynamic>.from(
          Uri.splitQueryString(json)
        );
        return HadithFavorite.fromJson(data);
      }).toList();
      
      debugPrint('Loaded ${_favorites.length} hadith favorites');
    } catch (e) {
      debugPrint('Error loading hadith favorites: $e');
    }
  }

  /// Add hadith to favorites
  Future<void> addToFavorites(HadithData hadith, String title, {String notes = '', List<String> tags = const []}) async {
    try {
      final favorite = HadithFavorite(
        id: '${hadith.collectionId}_${hadith.bookNumber}_${hadith.hadithNumber}',
        userId: 'current_user', // Replace with actual user ID
        collectionId: hadith.collectionId,
        bookNumber: hadith.bookNumber,
        hadithNumber: hadith.hadithNumber,
        title: title,
        notes: notes,
        dateAdded: DateTime.now(),
        tags: tags,
      );
      
      _favorites.add(favorite);
      await _saveFavorites();

      notifyListeners();
      debugPrint('Added hadith to favorites: ${favorite.id}');
    } catch (e) {
      debugPrint('Error adding hadith to favorites: $e');
    }
  }

  /// Remove hadith from favorites
  Future<void> removeFromFavorites(String favoriteId) async {
    try {
      _favorites.removeWhere((fav) => fav.id == favoriteId);
      await _saveFavorites();

      notifyListeners();
      debugPrint('Removed hadith from favorites: $favoriteId');
    } catch (e) {
      debugPrint('Error removing hadith from favorites: $e');
    }
  }

  /// Save favorites to local storage
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = _favorites.map((fav) => fav.toJson().toString()).toList();
      await prefs.setStringList('hadith_favorites', favoritesJson);
    } catch (e) {
      debugPrint('Error saving hadith favorites: $e');
    }
  }

  /// Check if hadith is in favorites
  bool isInFavorites(String collectionId, int bookNumber, int hadithNumber) {
    final id = '${collectionId}_${bookNumber}_$hadithNumber';
    return _favorites.any((fav) => fav.id == id);
  }

  /// Get random hadith
  Future<HadithData?> getRandomHadith() async {
    try {
      final randomCollection = _collections[DateTime.now().millisecond % _collections.length];
      final books = await getCollectionBooks(randomCollection.id);
      
      if (books.isEmpty) return null;
      
      final randomBook = books[DateTime.now().second % books.length];
      final hadiths = await getBookHadiths(randomCollection.id, randomBook.bookNumber);
      
      if (hadiths.isEmpty) return null;
      
      return hadiths[DateTime.now().microsecond % hadiths.length];
    } catch (e) {
      debugPrint('Error getting random hadith: $e');
      return null;
    }
  }

  /// Get hadith of the day
  Future<HadithData?> getHadithOfTheDay() async {
    try {
      final now = DateTime.now();
      final dayOfYear = now.difference(DateTime(now.year, 1, 1)).inDays;
      
      final collectionIndex = dayOfYear % _collections.length;
      final collection = _collections[collectionIndex];
      
      final books = await getCollectionBooks(collection.id);
      if (books.isEmpty) return null;
      
      final bookIndex = (dayOfYear ~/ _collections.length) % books.length;
      final book = books[bookIndex];
      
      final hadiths = await getBookHadiths(collection.id, book.bookNumber);
      if (hadiths.isEmpty) return null;
      
      final hadithIndex = (dayOfYear ~/ (_collections.length * books.length)) % hadiths.length;
      return hadiths[hadithIndex];
    } catch (e) {
      debugPrint('Error getting hadith of the day: $e');
      return null;
    }
  }

  /// Clear search cache
  void clearSearchCache() {
    _searchCache.clear();
    debugPrint('Cleared hadith search cache');
  }

  /// Refresh data
  Future<void> refresh() async {
    _isLoaded = false;
    _collectionBooks.clear();
    _bookHadiths.clear();
    clearSearchCache();
    await initialize();
  }

  /// Dispose resources
  @override
  void dispose() {
    _collections.clear();
    _collectionBooks.clear();
    _bookHadiths.clear();
    _favorites.clear();
    _searchCache.clear();
    super.dispose();
  }
}
