import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/hadith_models.dart';
import '../services/new_supabase_service.dart';
import '../services/authentic_hadith_service.dart';

class HadithProvider extends ChangeNotifier {
  static final HadithProvider _instance = HadithProvider._internal();
  factory HadithProvider() => _instance;
  HadithProvider._internal();

  // State variables
  List<HadithCollection> _collections = [];
  Map<String, List<HadithBook>> _collectionBooks = {};
  Map<String, List<HadithData>> _bookHadiths = {};
  List<HadithFavorite> _favorites = [];
  bool _isLoaded = false;
  bool _isLoading = false;
  String? _error;

  // Cache for search results
  Map<String, List<HadithSearchResult>> _searchCache = {};

  // Getters
  List<HadithCollection> get collections => _collections;
  List<HadithFavorite> get favorites => _favorites;
  bool get isLoaded => _isLoaded;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize Hadith provider
  Future<void> initialize() async {
    if (_isLoaded || _isLoading) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Initialize authentic hadith service first
      await AuthenticHadithService().initialize();

      await _loadCollections();
      await _loadFavorites();
      await _loadCachedData();

      // Sync favorites from Supabase if authenticated
      await syncFavoritesFromSupabase();

      _isLoaded = true;
      debugPrint('HadithProvider initialized successfully with authentic data');
    } catch (e) {
      _error = 'Failed to load Hadith data: $e';
      debugPrint('Error initializing HadithProvider: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load cached data from local storage
  Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load cached collections metadata
      final collectionsCache = prefs.getString('hadith_collections_cache');
      if (collectionsCache != null) {
        final cacheTime = prefs.getInt('hadith_collections_cache_time') ?? 0;
        final now = DateTime.now().millisecondsSinceEpoch;

        // Cache is valid for 7 days
        if (now - cacheTime < 7 * 24 * 60 * 60 * 1000) {
          debugPrint('Using cached hadith collections metadata');
        }
      }

      // Load frequently accessed books
      for (final collection in _collections) {
        final booksKey = 'hadith_books_${collection.id}';
        final booksCache = prefs.getStringList(booksKey);
        if (booksCache != null && booksCache.isNotEmpty) {
          debugPrint('Found cached books for collection ${collection.id}');
        }
      }

      debugPrint('Loaded cached hadith data');
    } catch (e) {
      debugPrint('Error loading cached hadith data: $e');
    }
  }

  /// Cache frequently accessed data
  Future<void> _cacheData(String key, dynamic data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      if (data is List<String>) {
        await prefs.setStringList(key, data);
        await prefs.setInt('${key}_time', timestamp);
      } else if (data is String) {
        await prefs.setString(key, data);
        await prefs.setInt('${key}_time', timestamp);
      }

      debugPrint('Cached data for key: $key');
    } catch (e) {
      debugPrint('Error caching data for key $key: $e');
    }
  }

  /// Load Hadith collections
  Future<void> _loadCollections() async {
    _collections = [
      HadithCollection(
        id: 'bukhari',
        name: 'Sahih al-Bukhari',
        arabicName: 'صحيح البخاري',
        englishName: 'Sahih al-Bukhari',
        description: 'The most authentic collection of Hadith',
        arabicDescription: 'أصح كتاب بعد كتاب الله',
        totalBooks: 97,
        totalHadiths: 7563,
        author: 'Imam al-Bukhari',
        arabicAuthor: 'الإمام البخاري',
      ),
      HadithCollection(
        id: 'muslim',
        name: 'Sahih Muslim',
        arabicName: 'صحيح مسلم',
        englishName: 'Sahih Muslim',
        description: 'The second most authentic collection of Hadith',
        arabicDescription: 'ثاني أصح الكتب بعد صحيح البخاري',
        totalBooks: 56,
        totalHadiths: 7190,
        author: 'Imam Muslim',
        arabicAuthor: 'الإمام مسلم',
      ),
      HadithCollection(
        id: 'abudawud',
        name: 'Sunan Abu Dawud',
        arabicName: 'سنن أبي داود',
        englishName: 'Sunan Abu Dawud',
        description: 'Collection focusing on legal matters',
        arabicDescription: 'مجموعة تركز على الأحكام الفقهية',
        totalBooks: 43,
        totalHadiths: 5274,
        author: 'Imam Abu Dawud',
        arabicAuthor: 'الإمام أبو داود',
      ),
      HadithCollection(
        id: 'tirmidhi',
        name: 'Jami\' at-Tirmidhi',
        arabicName: 'جامع الترمذي',
        englishName: 'Jami\' at-Tirmidhi',
        description: 'Collection with grading of Hadith authenticity',
        arabicDescription: 'مجموعة مع تقييم صحة الأحاديث',
        totalBooks: 46,
        totalHadiths: 3956,
        author: 'Imam at-Tirmidhi',
        arabicAuthor: 'الإمام الترمذي',
      ),
      HadithCollection(
        id: 'nasai',
        name: 'Sunan an-Nasa\'i',
        arabicName: 'سنن النسائي',
        englishName: 'Sunan an-Nasa\'i',
        description: 'Collection known for strict authentication',
        arabicDescription: 'مجموعة معروفة بالتشدد في التوثيق',
        totalBooks: 51,
        totalHadiths: 5761,
        author: 'Imam an-Nasa\'i',
        arabicAuthor: 'الإمام النسائي',
      ),
      HadithCollection(
        id: 'ibnmajah',
        name: 'Sunan Ibn Majah',
        arabicName: 'سنن ابن ماجه',
        englishName: 'Sunan Ibn Majah',
        description: 'The sixth book of the Kutub as-Sittah',
        arabicDescription: 'الكتاب السادس من الكتب الستة',
        totalBooks: 37,
        totalHadiths: 4341,
        author: 'Imam Ibn Majah',
        arabicAuthor: 'الإمام ابن ماجه',
      ),
    ];
    
    debugPrint('Loaded ${_collections.length} Hadith collections');
  }

  /// Get books for a specific collection using authentic data
  Future<List<HadithBook>> getCollectionBooks(String collectionId) async {
    if (_collectionBooks.containsKey(collectionId)) {
      return _collectionBooks[collectionId]!;
    }

    try {
      // Get authentic books from AuthenticHadithService
      final authenticService = AuthenticHadithService();
      final books = authenticService.getAuthenticBooks(collectionId);

      if (books.isNotEmpty) {
        _collectionBooks[collectionId] = books;

        // Cache the books data
        final booksData = books.map((book) => '${book.bookNumber}|${book.arabicName}|${book.englishName}|${book.totalHadiths}').toList();
        await _cacheData('hadith_books_$collectionId', booksData);

        debugPrint('Loaded ${books.length} authentic books for collection $collectionId');
        return books;
      }

      // Fallback to empty list if no authentic data available
      debugPrint('No authentic books found for collection $collectionId');
      return [];
    } catch (e) {
      debugPrint('Error loading books for collection $collectionId: $e');
      return [];
    }
  }

  /// Get hadiths for a specific book with pagination support using authentic data
  Future<List<HadithData>> getBookHadiths(String collectionId, int bookNumber, {int page = 1, int pageSize = 20}) async {
    final key = '${collectionId}_$bookNumber';

    // Check if we have cached data
    if (_bookHadiths.containsKey(key)) {
      final allHadiths = _bookHadiths[key]!;
      final startIndex = (page - 1) * pageSize;
      final endIndex = (startIndex + pageSize).clamp(0, allHadiths.length);

      if (startIndex < allHadiths.length) {
        return allHadiths.sublist(startIndex, endIndex);
      }
    }

    try {
      // Get authentic hadiths from AuthenticHadithService
      final authenticService = AuthenticHadithService();
      final hadiths = authenticService.getAuthenticHadiths(collectionId, bookNumber);

      if (hadiths.isNotEmpty) {
        // Cache all hadiths
        _bookHadiths[key] = hadiths;

        // Return requested page
        final startIndex = (page - 1) * pageSize;
        final endIndex = (startIndex + pageSize).clamp(0, hadiths.length);
        final pageHadiths = startIndex < hadiths.length ? hadiths.sublist(startIndex, endIndex) : <HadithData>[];

        debugPrint('Loaded ${hadiths.length} authentic hadiths, returning page $page (${pageHadiths.length} items) for book $bookNumber in collection $collectionId');
        return pageHadiths;
      }

      // Return empty list if no authentic data available
      debugPrint('No authentic hadiths found for book $bookNumber in collection $collectionId');
      return [];
    } catch (e) {
      debugPrint('Error loading hadiths for book $bookNumber in collection $collectionId: $e');
      return [];
    }
  }

  /// Get all hadiths for a book (backward compatibility)
  Future<List<HadithData>> getAllBookHadiths(String collectionId, int bookNumber) async {
    return getBookHadiths(collectionId, bookNumber, page: 1, pageSize: 1000);
  }

  /// Get a specific hadith
  Future<HadithData?> getHadith(String collectionId, int bookNumber, int hadithNumber) async {
    try {
      final bookHadiths = await getBookHadiths(collectionId, bookNumber);
      return bookHadiths.firstWhere(
        (hadith) => hadith.hadithNumber == hadithNumber,
        orElse: () => bookHadiths.isNotEmpty ? bookHadiths.first : throw Exception('Hadith not found'),
      );
    } catch (e) {
      debugPrint('Error getting hadith $collectionId:$bookNumber:$hadithNumber: $e');
      return null;
    }
  }

  /// Search hadiths across all collections
  Future<List<HadithSearchResult>> searchHadiths(String query, {
    List<String>? collections,
    String searchType = 'all', // 'all', 'arabic', 'english', 'narrator'
    int limit = 50,
  }) async {
    if (query.trim().isEmpty) return [];
    
    final cacheKey = '${query}_${collections?.join(',') ?? 'all'}_${searchType}_$limit';
    if (_searchCache.containsKey(cacheKey)) {
      return _searchCache[cacheKey]!;
    }

    try {
      final results = <HadithSearchResult>[];
      final searchTerms = query.trim().toLowerCase().split(' ');
      final collectionsToSearch = collections ?? _collections.map((c) => c.id).toList();

      for (final collectionId in collectionsToSearch) {
        final collection = _collections.firstWhere((c) => c.id == collectionId);
        final books = await getCollectionBooks(collectionId);
        
        for (final book in books) {
          final hadiths = await getBookHadiths(collectionId, book.bookNumber);
          
          for (final hadith in hadiths) {
            final matchedWords = <String>[];
            double relevanceScore = 0.0;
            String matchType = 'arabic';
            
            // Search in Arabic text
            if (searchType == 'all' || searchType == 'arabic') {
              for (final term in searchTerms) {
                if (hadith.arabicText.toLowerCase().contains(term)) {
                  matchedWords.add(term);
                  relevanceScore += 2.0; // Higher weight for Arabic matches
                  matchType = 'arabic';
                }
              }
            }
            
            // Search in English text
            if (searchType == 'all' || searchType == 'english') {
              for (final term in searchTerms) {
                if (hadith.englishText.toLowerCase().contains(term)) {
                  matchedWords.add(term);
                  relevanceScore += 1.5;
                  if (matchType != 'arabic') matchType = 'english';
                }
              }
            }
            
            // Search in narrator
            if (searchType == 'all' || searchType == 'narrator') {
              for (final term in searchTerms) {
                if (hadith.narrator.toLowerCase().contains(term) ||
                    hadith.arabicNarrator.toLowerCase().contains(term)) {
                  matchedWords.add(term);
                  relevanceScore += 1.0;
                  if (matchType == 'arabic') matchType = 'narrator';
                }
              }
            }
            
            if (matchedWords.isNotEmpty) {
              results.add(HadithSearchResult(
                hadith: hadith,
                book: book,
                collection: collection,
                relevanceScore: relevanceScore / searchTerms.length,
                matchedWords: matchedWords,
                searchType: matchType,
              ));
            }
          }
        }
      }
      
      // Sort by relevance score
      results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
      
      final limitedResults = results.take(limit).toList();
      _searchCache[cacheKey] = limitedResults;
      
      debugPrint('Found ${limitedResults.length} hadith search results for query: $query');
      return limitedResults;
    } catch (e) {
      debugPrint('Error searching hadiths: $e');
      return [];
    }
  }

  /// Load favorites from local storage
  Future<void> _loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList('hadith_favorites') ?? [];

      _favorites = favoritesJson.map((jsonString) {
        try {
          // Parse JSON string properly
          final Map<String, dynamic> data = {};
          final parts = jsonString.split('&');
          for (final part in parts) {
            final keyValue = part.split('=');
            if (keyValue.length == 2) {
              final key = Uri.decodeComponent(keyValue[0]);
              final value = Uri.decodeComponent(keyValue[1]);

              // Handle different data types
              if (key == 'date_added') {
                data[key] = value;
              } else if (key == 'book_number' || key == 'hadith_number') {
                data[key] = int.tryParse(value) ?? 0;
              } else if (key == 'tags') {
                data[key] = value.split(',').where((tag) => tag.isNotEmpty).toList();
              } else {
                data[key] = value;
              }
            }
          }
          return HadithFavorite.fromJson(data);
        } catch (e) {
          debugPrint('Error parsing favorite: $e');
          return null;
        }
      }).where((fav) => fav != null).cast<HadithFavorite>().toList();

      debugPrint('Loaded ${_favorites.length} hadith favorites from local storage');
    } catch (e) {
      debugPrint('Error loading hadith favorites: $e');
      _favorites = [];
    }
  }

  /// Add hadith to favorites
  Future<void> addToFavorites(HadithData hadith, String title, {String notes = '', List<String> tags = const []}) async {
    try {
      final userId = await _getCurrentUserId();
      final favorite = HadithFavorite(
        id: '${hadith.collectionId}_${hadith.bookNumber}_${hadith.hadithNumber}',
        userId: userId,
        collectionId: hadith.collectionId,
        bookNumber: hadith.bookNumber,
        hadithNumber: hadith.hadithNumber,
        title: title,
        notes: notes,
        dateAdded: DateTime.now(),
        tags: tags,
      );

      _favorites.add(favorite);
      await _saveFavorites();

      // Sync to Supabase if user is authenticated
      await _syncFavoriteToSupabase(favorite);

      notifyListeners();
      debugPrint('Added hadith to favorites: ${favorite.id}');
    } catch (e) {
      debugPrint('Error adding hadith to favorites: $e');
    }
  }

  /// Remove hadith from favorites
  Future<void> removeFromFavorites(String favoriteId) async {
    try {
      _favorites.removeWhere((fav) => fav.id == favoriteId);
      await _saveFavorites();

      // Remove from Supabase if user is authenticated
      await _removeFavoriteFromSupabase(favoriteId);

      notifyListeners();
      debugPrint('Removed hadith from favorites: $favoriteId');
    } catch (e) {
      debugPrint('Error removing hadith from favorites: $e');
    }
  }

  /// Save favorites to local storage
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = _favorites.map((fav) {
        final json = fav.toJson();
        // Convert to URL-encoded string for safe storage
        final parts = <String>[];
        json.forEach((key, value) {
          if (value != null) {
            String valueStr;
            if (value is List) {
              valueStr = value.join(',');
            } else {
              valueStr = value.toString();
            }
            parts.add('${Uri.encodeComponent(key)}=${Uri.encodeComponent(valueStr)}');
          }
        });
        return parts.join('&');
      }).toList();

      await prefs.setStringList('hadith_favorites', favoritesJson);
      debugPrint('Saved ${_favorites.length} hadith favorites to local storage');
    } catch (e) {
      debugPrint('Error saving hadith favorites: $e');
    }
  }

  /// Check if hadith is in favorites
  bool isInFavorites(String collectionId, int bookNumber, int hadithNumber) {
    final id = '${collectionId}_${bookNumber}_$hadithNumber';
    return _favorites.any((fav) => fav.id == id);
  }

  /// Get random hadith
  Future<HadithData?> getRandomHadith() async {
    try {
      final randomCollection = _collections[DateTime.now().millisecond % _collections.length];
      final books = await getCollectionBooks(randomCollection.id);
      
      if (books.isEmpty) return null;
      
      final randomBook = books[DateTime.now().second % books.length];
      final hadiths = await getBookHadiths(randomCollection.id, randomBook.bookNumber);
      
      if (hadiths.isEmpty) return null;
      
      return hadiths[DateTime.now().microsecond % hadiths.length];
    } catch (e) {
      debugPrint('Error getting random hadith: $e');
      return null;
    }
  }

  /// Get hadith of the day
  Future<HadithData?> getHadithOfTheDay() async {
    try {
      final now = DateTime.now();
      final dayOfYear = now.difference(DateTime(now.year, 1, 1)).inDays;
      
      final collectionIndex = dayOfYear % _collections.length;
      final collection = _collections[collectionIndex];
      
      final books = await getCollectionBooks(collection.id);
      if (books.isEmpty) return null;
      
      final bookIndex = (dayOfYear ~/ _collections.length) % books.length;
      final book = books[bookIndex];
      
      final hadiths = await getBookHadiths(collection.id, book.bookNumber);
      if (hadiths.isEmpty) return null;
      
      final hadithIndex = (dayOfYear ~/ (_collections.length * books.length)) % hadiths.length;
      return hadiths[hadithIndex];
    } catch (e) {
      debugPrint('Error getting hadith of the day: $e');
      return null;
    }
  }

  /// Clear search cache
  void clearSearchCache() {
    _searchCache.clear();
    debugPrint('Cleared hadith search cache');
  }

  /// Refresh data
  Future<void> refresh() async {
    _isLoaded = false;
    _collectionBooks.clear();
    _bookHadiths.clear();
    clearSearchCache();
    await initialize();
  }

  /// Get current user ID
  Future<String> _getCurrentUserId() async {
    try {
      if (NewSupabaseService.isAuthenticated) {
        final user = NewSupabaseService.client.auth.currentUser;
        return user?.id ?? 'anonymous';
      }
      return 'anonymous';
    } catch (e) {
      debugPrint('Error getting current user ID: $e');
      return 'anonymous';
    }
  }

  /// Sync favorite to Supabase
  Future<void> _syncFavoriteToSupabase(HadithFavorite favorite) async {
    try {
      if (!NewSupabaseService.isAuthenticated) {
        debugPrint('User not authenticated, skipping Supabase sync');
        return;
      }

      await NewSupabaseService.client.from('hadith_favorites').upsert({
        'id': favorite.id,
        'user_id': favorite.userId,
        'collection_id': favorite.collectionId,
        'book_number': favorite.bookNumber,
        'hadith_number': favorite.hadithNumber,
        'title': favorite.title,
        'notes': favorite.notes,
        'date_added': favorite.dateAdded.toIso8601String(),
        'tags': favorite.tags,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      debugPrint('Synced favorite to Supabase: ${favorite.id}');
    } catch (e) {
      debugPrint('Error syncing favorite to Supabase: $e');
      // Don't throw error - offline functionality should continue to work
    }
  }

  /// Remove favorite from Supabase
  Future<void> _removeFavoriteFromSupabase(String favoriteId) async {
    try {
      if (!NewSupabaseService.isAuthenticated) {
        debugPrint('User not authenticated, skipping Supabase sync');
        return;
      }

      await NewSupabaseService.client
          .from('hadith_favorites')
          .delete()
          .eq('id', favoriteId);

      debugPrint('Removed favorite from Supabase: $favoriteId');
    } catch (e) {
      debugPrint('Error removing favorite from Supabase: $e');
      // Don't throw error - offline functionality should continue to work
    }
  }

  /// Sync favorites from Supabase
  Future<void> syncFavoritesFromSupabase() async {
    try {
      if (!NewSupabaseService.isAuthenticated) {
        debugPrint('User not authenticated, skipping Supabase sync');
        return;
      }

      final userId = await _getCurrentUserId();
      final response = await NewSupabaseService.client
          .from('hadith_favorites')
          .select('*')
          .eq('user_id', userId);

      final supabaseFavorites = (response as List).map((data) {
        return HadithFavorite(
          id: data['id'],
          userId: data['user_id'],
          collectionId: data['collection_id'],
          bookNumber: data['book_number'],
          hadithNumber: data['hadith_number'],
          title: data['title'],
          notes: data['notes'] ?? '',
          dateAdded: DateTime.parse(data['date_added']),
          tags: List<String>.from(data['tags'] ?? []),
        );
      }).toList();

      // Merge with local favorites (Supabase takes precedence)
      final localFavoriteIds = _favorites.map((f) => f.id).toSet();
      final supabaseFavoriteIds = supabaseFavorites.map((f) => f.id).toSet();

      // Add new favorites from Supabase
      for (final supabaseFavorite in supabaseFavorites) {
        if (!localFavoriteIds.contains(supabaseFavorite.id)) {
          _favorites.add(supabaseFavorite);
        } else {
          // Update existing favorite with Supabase data
          final index = _favorites.indexWhere((f) => f.id == supabaseFavorite.id);
          if (index != -1) {
            _favorites[index] = supabaseFavorite;
          }
        }
      }

      // Remove favorites that are not in Supabase
      _favorites.removeWhere((f) => !supabaseFavoriteIds.contains(f.id));

      await _saveFavorites();
      notifyListeners();

      debugPrint('Synced ${supabaseFavorites.length} favorites from Supabase');
    } catch (e) {
      debugPrint('Error syncing favorites from Supabase: $e');
      // Don't throw error - offline functionality should continue to work
    }
  }

  /// Sync all local favorites to Supabase
  Future<void> syncAllFavoritesToSupabase() async {
    try {
      if (!NewSupabaseService.isAuthenticated) {
        debugPrint('User not authenticated, skipping Supabase sync');
        return;
      }

      for (final favorite in _favorites) {
        await _syncFavoriteToSupabase(favorite);
      }

      debugPrint('Synced all ${_favorites.length} favorites to Supabase');
    } catch (e) {
      debugPrint('Error syncing all favorites to Supabase: $e');
    }
  }



  /// Get cache statistics
  Map<String, dynamic> getCacheStatistics() {
    return {
      'search_cache_size': _searchCache.length,
      'collection_books_cached': _collectionBooks.length,
      'book_hadiths_cached': _bookHadiths.length,
      'favorites_count': _favorites.length,
    };
  }

  /// Get hadith grading explanation
  Map<String, dynamic> getHadithGradingExplanation(String grade) {
    final gradingExplanations = {
      'Sahih': {
        'arabic': 'صحيح',
        'definition_arabic': 'الحديث الصحيح هو ما اتصل سنده بنقل العدل الضابط عن العدل الضابط إلى منتهاه، ولم يكن شاذاً ولا معللاً.',
        'definition_english': 'A Sahih hadith is one with a connected chain of narrators who are trustworthy and precise, without any irregularities or hidden defects.',
        'reliability': 'عالية جداً',
        'reliability_english': 'Very High',
        'color': '#4CAF50',
        'icon': '✓',
      },
      'Hasan': {
        'arabic': 'حسن',
        'definition_arabic': 'الحديث الحسن هو ما اتصل سنده بنقل العدل الذي خف ضبطه عن العدل الضابط إلى منتهاه، ولم يكن شاذاً ولا معللاً.',
        'definition_english': 'A Hasan hadith has a connected chain but with narrators whose precision is slightly less than those in Sahih hadiths.',
        'reliability': 'عالية',
        'reliability_english': 'High',
        'color': '#FF9800',
        'icon': '○',
      },
      'Daif': {
        'arabic': 'ضعيف',
        'definition_arabic': 'الحديث الضعيف هو ما لم تجتمع فيه صفات الحديث الصحيح أو الحسن.',
        'definition_english': 'A Daif hadith lacks the qualities required for Sahih or Hasan classification.',
        'reliability': 'ضعيفة',
        'reliability_english': 'Weak',
        'color': '#F44336',
        'icon': '×',
      },
      'Maudu': {
        'arabic': 'موضوع',
        'definition_arabic': 'الحديث الموضوع هو المختلق المصنوع المنسوب إلى رسول الله صلى الله عليه وسلم كذباً.',
        'definition_english': 'A Maudu hadith is fabricated and falsely attributed to Prophet Muhammad (peace be upon him).',
        'reliability': 'مرفوض',
        'reliability_english': 'Rejected',
        'color': '#9C27B0',
        'icon': '!',
      },
    };

    return gradingExplanations[grade] ?? {
      'arabic': 'غير محدد',
      'definition_arabic': 'درجة الحديث غير محددة أو غير معروفة.',
      'definition_english': 'Hadith grading is unspecified or unknown.',
      'reliability': 'غير محدد',
      'reliability_english': 'Unspecified',
      'color': '#9E9E9E',
      'icon': '?',
    };
  }

  /// Get hadith narrator information
  Future<Map<String, dynamic>?> getNarratorInfo(String narratorName) async {
    try {
      // In a real implementation, this would load from a narrator database
      final narratorInfo = {
        'name': narratorName,
        'full_name_arabic': 'الاسم الكامل للراوي',
        'birth_year': 'سنة الولادة',
        'death_year': 'سنة الوفاة',
        'reliability': 'ثقة',
        'reliability_english': 'Trustworthy',
        'teachers': ['أسماء الشيوخ'],
        'students': ['أسماء التلاميذ'],
        'biography': 'نبذة مختصرة عن حياة الراوي ومكانته العلمية.',
        'biography_english': 'Brief biography about the narrator\'s life and scholarly status.',
      };

      return narratorInfo;
    } catch (e) {
      debugPrint('Error getting narrator info for $narratorName: $e');
      return null;
    }
  }

  /// Get hadith collection information
  Map<String, dynamic> getCollectionInfo(String collectionId) {
    final collectionInfos = {
      'bukhari': {
        'full_name_arabic': 'صحيح البخاري',
        'full_name_english': 'Sahih al-Bukhari',
        'author_arabic': 'الإمام محمد بن إسماعيل البخاري',
        'author_english': 'Imam Muhammad ibn Ismail al-Bukhari',
        'compilation_year': '846 CE / 232 AH',
        'total_hadiths': 7563,
        'authentic_hadiths': 7275,
        'description_arabic': 'أصح كتاب بعد كتاب الله تعالى، جمع فيه الإمام البخاري أصح الأحاديث النبوية.',
        'description_english': 'The most authentic book after the Quran, compiled by Imam Bukhari with the most authentic prophetic traditions.',
        'methodology': 'اشترط البخاري شروطاً صارمة في قبول الأحاديث',
        'methodology_english': 'Bukhari applied strict conditions for accepting hadiths',
      },
      'muslim': {
        'full_name_arabic': 'صحيح مسلم',
        'full_name_english': 'Sahih Muslim',
        'author_arabic': 'الإمام مسلم بن الحجاج النيسابوري',
        'author_english': 'Imam Muslim ibn al-Hajjaj al-Naysaburi',
        'compilation_year': '875 CE / 261 AH',
        'total_hadiths': 7470,
        'authentic_hadiths': 4000,
        'description_arabic': 'ثاني أصح كتب الحديث بعد صحيح البخاري.',
        'description_english': 'The second most authentic hadith collection after Sahih al-Bukhari.',
        'methodology': 'رتب الأحاديث حسب المواضيع الفقهية',
        'methodology_english': 'Arranged hadiths according to jurisprudential topics',
      },
      // Add other collections...
    };

    return collectionInfos[collectionId] ?? {
      'full_name_arabic': 'مجموعة أحاديث',
      'full_name_english': 'Hadith Collection',
      'description_arabic': 'مجموعة من الأحاديث النبوية الشريفة.',
      'description_english': 'A collection of prophetic traditions.',
    };
  }

  /// Get related hadiths based on topic or keywords
  Future<List<HadithData>> getRelatedHadiths(HadithData hadith, {int limit = 5}) async {
    try {
      final relatedHadiths = <HadithData>[];

      // Extract keywords from hadith text
      final keywords = _extractKeywords(hadith.arabicText);

      // Search for hadiths with similar keywords
      for (final keyword in keywords.take(3)) { // Use top 3 keywords
        final searchResults = await searchHadiths(keyword, limit: 10);
        for (final result in searchResults) {
          if (result.hadith.reference != hadith.reference &&
              !relatedHadiths.any((h) => h.reference == result.hadith.reference)) {
            relatedHadiths.add(result.hadith);
            if (relatedHadiths.length >= limit) break;
          }
        }
        if (relatedHadiths.length >= limit) break;
      }

      return relatedHadiths;
    } catch (e) {
      debugPrint('Error getting related hadiths: $e');
      return [];
    }
  }

  /// Extract keywords from Arabic text
  List<String> _extractKeywords(String text) {
    // Simple keyword extraction - in a real implementation, this would be more sophisticated
    final commonWords = ['في', 'من', 'إلى', 'على', 'عن', 'أن', 'قال', 'كان', 'هو', 'هي', 'ما', 'لا', 'إذا'];
    final words = text.split(' ').where((word) =>
      word.length > 2 && !commonWords.contains(word)).toList();

    // Return most frequent words (simplified)
    return words.take(10).toList();
  }

  /// Dispose resources
  @override
  void dispose() {
    _collections.clear();
    _collectionBooks.clear();
    _bookHadiths.clear();
    _favorites.clear();
    _searchCache.clear();
    super.dispose();
  }
}


