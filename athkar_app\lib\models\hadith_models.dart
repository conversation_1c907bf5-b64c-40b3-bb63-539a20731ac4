class HadithCollection {
  final String id;
  final String name;
  final String arabicName;
  final String englishName;
  final String description;
  final String arabicDescription;
  final int totalBooks;
  final int totalHadiths;
  final String author;
  final String arabicAuthor;

  HadithCollection({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.englishName,
    required this.description,
    required this.arabicDescription,
    required this.totalBooks,
    required this.totalHadiths,
    required this.author,
    required this.arabicAuthor,
  });

  factory HadithCollection.fromJson(Map<String, dynamic> json) {
    return HadithCollection(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      arabicName: json['arabic_name'] ?? '',
      englishName: json['english_name'] ?? '',
      description: json['description'] ?? '',
      arabicDescription: json['arabic_description'] ?? '',
      totalBooks: json['total_books'] ?? 0,
      totalHadiths: json['total_hadiths'] ?? 0,
      author: json['author'] ?? '',
      arabicAuthor: json['arabic_author'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'arabic_name': arabicName,
      'english_name': englishName,
      'description': description,
      'arabic_description': arabicDescription,
      'total_books': totalBooks,
      'total_hadiths': totalHadiths,
      'author': author,
      'arabic_author': arabicAuthor,
    };
  }

  @override
  String toString() {
    return 'HadithCollection(id: $id, name: $name, arabicName: $arabicName)';
  }
}

class HadithBook {
  final int bookNumber;
  final String name;
  final String arabicName;
  final String englishName;
  final String collectionId;
  final int totalHadiths;
  final String? description;
  final String? arabicDescription;

  HadithBook({
    required this.bookNumber,
    required this.name,
    required this.arabicName,
    required this.englishName,
    required this.collectionId,
    required this.totalHadiths,
    this.description,
    this.arabicDescription,
  });

  factory HadithBook.fromJson(Map<String, dynamic> json) {
    return HadithBook(
      bookNumber: json['book_number'] ?? 0,
      name: json['name'] ?? '',
      arabicName: json['arabic_name'] ?? '',
      englishName: json['english_name'] ?? '',
      collectionId: json['collection_id'] ?? '',
      totalHadiths: json['total_hadiths'] ?? 0,
      description: json['description'],
      arabicDescription: json['arabic_description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'book_number': bookNumber,
      'name': name,
      'arabic_name': arabicName,
      'english_name': englishName,
      'collection_id': collectionId,
      'total_hadiths': totalHadiths,
      'description': description,
      'arabic_description': arabicDescription,
    };
  }

  @override
  String toString() {
    return 'HadithBook(bookNumber: $bookNumber, name: $name, collectionId: $collectionId)';
  }
}

class HadithData {
  final int hadithNumber;
  final int bookNumber;
  final String collectionId;
  final String arabicText;
  final String englishText;
  final String narrator;
  final String arabicNarrator;
  final String reference;
  final String grade;
  final String arabicGrade;
  final String? notes;
  final String? arabicNotes;
  final DateTime? dateAdded;
  final bool isFavorite;
  final List<String> tags;

  HadithData({
    required this.hadithNumber,
    required this.bookNumber,
    required this.collectionId,
    required this.arabicText,
    required this.englishText,
    required this.narrator,
    required this.arabicNarrator,
    required this.reference,
    required this.grade,
    required this.arabicGrade,
    this.notes,
    this.arabicNotes,
    this.dateAdded,
    this.isFavorite = false,
    this.tags = const [],
  });

  factory HadithData.fromJson(Map<String, dynamic> json) {
    return HadithData(
      hadithNumber: json['hadith_number'] ?? 0,
      bookNumber: json['book_number'] ?? 0,
      collectionId: json['collection_id'] ?? '',
      arabicText: json['arabic_text'] ?? '',
      englishText: json['english_text'] ?? '',
      narrator: json['narrator'] ?? '',
      arabicNarrator: json['arabic_narrator'] ?? '',
      reference: json['reference'] ?? '',
      grade: json['grade'] ?? '',
      arabicGrade: json['arabic_grade'] ?? '',
      notes: json['notes'],
      arabicNotes: json['arabic_notes'],
      dateAdded: json['date_added'] != null 
          ? DateTime.parse(json['date_added']) 
          : null,
      isFavorite: json['is_favorite'] ?? false,
      tags: json['tags'] != null 
          ? List<String>.from(json['tags']) 
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hadith_number': hadithNumber,
      'book_number': bookNumber,
      'collection_id': collectionId,
      'arabic_text': arabicText,
      'english_text': englishText,
      'narrator': narrator,
      'arabic_narrator': arabicNarrator,
      'reference': reference,
      'grade': grade,
      'arabic_grade': arabicGrade,
      'notes': notes,
      'arabic_notes': arabicNotes,
      'date_added': dateAdded?.toIso8601String(),
      'is_favorite': isFavorite,
      'tags': tags,
    };
  }

  HadithData copyWith({
    int? hadithNumber,
    int? bookNumber,
    String? collectionId,
    String? arabicText,
    String? englishText,
    String? narrator,
    String? arabicNarrator,
    String? reference,
    String? grade,
    String? arabicGrade,
    String? notes,
    String? arabicNotes,
    DateTime? dateAdded,
    bool? isFavorite,
    List<String>? tags,
  }) {
    return HadithData(
      hadithNumber: hadithNumber ?? this.hadithNumber,
      bookNumber: bookNumber ?? this.bookNumber,
      collectionId: collectionId ?? this.collectionId,
      arabicText: arabicText ?? this.arabicText,
      englishText: englishText ?? this.englishText,
      narrator: narrator ?? this.narrator,
      arabicNarrator: arabicNarrator ?? this.arabicNarrator,
      reference: reference ?? this.reference,
      grade: grade ?? this.grade,
      arabicGrade: arabicGrade ?? this.arabicGrade,
      notes: notes ?? this.notes,
      arabicNotes: arabicNotes ?? this.arabicNotes,
      dateAdded: dateAdded ?? this.dateAdded,
      isFavorite: isFavorite ?? this.isFavorite,
      tags: tags ?? this.tags,
    );
  }

  @override
  String toString() {
    return 'HadithData(hadithNumber: $hadithNumber, bookNumber: $bookNumber, collectionId: $collectionId)';
  }
}

class HadithSearchResult {
  final HadithData hadith;
  final HadithBook book;
  final HadithCollection collection;
  final double relevanceScore;
  final List<String> matchedWords;
  final String searchType; // 'arabic', 'english', 'narrator', 'reference'

  HadithSearchResult({
    required this.hadith,
    required this.book,
    required this.collection,
    required this.relevanceScore,
    required this.matchedWords,
    required this.searchType,
  });

  factory HadithSearchResult.fromJson(Map<String, dynamic> json) {
    return HadithSearchResult(
      hadith: HadithData.fromJson(json['hadith']),
      book: HadithBook.fromJson(json['book']),
      collection: HadithCollection.fromJson(json['collection']),
      relevanceScore: json['relevance_score']?.toDouble() ?? 0.0,
      matchedWords: List<String>.from(json['matched_words'] ?? []),
      searchType: json['search_type'] ?? 'arabic',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hadith': hadith.toJson(),
      'book': book.toJson(),
      'collection': collection.toJson(),
      'relevance_score': relevanceScore,
      'matched_words': matchedWords,
      'search_type': searchType,
    };
  }

  @override
  String toString() {
    return 'HadithSearchResult(hadith: ${hadith.hadithNumber}, relevanceScore: $relevanceScore)';
  }
}

class HadithFavorite {
  final String id;
  final String userId;
  final String collectionId;
  final int bookNumber;
  final int hadithNumber;
  final String title;
  final String notes;
  final DateTime dateAdded;
  final List<String> tags;

  HadithFavorite({
    required this.id,
    required this.userId,
    required this.collectionId,
    required this.bookNumber,
    required this.hadithNumber,
    required this.title,
    required this.notes,
    required this.dateAdded,
    required this.tags,
  });

  factory HadithFavorite.fromJson(Map<String, dynamic> json) {
    return HadithFavorite(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      collectionId: json['collection_id'] ?? '',
      bookNumber: json['book_number'] ?? 0,
      hadithNumber: json['hadith_number'] ?? 0,
      title: json['title'] ?? '',
      notes: json['notes'] ?? '',
      dateAdded: json['date_added'] != null 
          ? DateTime.parse(json['date_added']) 
          : DateTime.now(),
      tags: List<String>.from(json['tags'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'collection_id': collectionId,
      'book_number': bookNumber,
      'hadith_number': hadithNumber,
      'title': title,
      'notes': notes,
      'date_added': dateAdded.toIso8601String(),
      'tags': tags,
    };
  }

  @override
  String toString() {
    return 'HadithFavorite(id: $id, collectionId: $collectionId, hadithNumber: $hadithNumber)';
  }
}
