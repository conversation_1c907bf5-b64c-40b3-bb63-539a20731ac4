import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/view_models.dart';
import '../../services/language_service.dart';
import '../../theme/app_theme.dart';

class FontSelectorWidget extends StatelessWidget {
  final FontFamily currentFont;
  final Function(FontFamily) onFontChanged;

  const FontSelectorWidget({
    super.key,
    required this.currentFont,
    required this.onFontChanged,
  });

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Font Family Selection
        Text(
          languageService.isArabic ? 'اختر نوع الخط' : 'Choose Font Family',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 12),
        
        // Font Options
        ...FontFamily.values.map((font) {
          final isSelected = currentFont == font;
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: InkWell(
              onTap: () => onFontChanged(font),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.primaryGreen.withValues(alpha: 0.1) : Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? AppTheme.primaryGreen : Colors.grey[300]!,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  children: [
                    // Font Preview
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            font.displayName,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: isSelected ? AppTheme.primaryGreen : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            font.fontName,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Arabic Preview
                    Expanded(
                      flex: 3,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: font == FontFamily.system ? null : font.fontName,
                              color: Colors.black87,
                            ),
                            textDirection: TextDirection.rtl,
                            textAlign: TextAlign.right,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'In the name of Allah',
                            style: TextStyle(
                              fontSize: 12,
                              fontFamily: font == FontFamily.system ? null : font.fontName,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ],
                      ),
                    ),
                    
                    // Selection Indicator
                    const SizedBox(width: 12),
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected ? AppTheme.primaryGreen : Colors.transparent,
                        border: Border.all(
                          color: isSelected ? AppTheme.primaryGreen : Colors.grey[400]!,
                          width: 2,
                        ),
                      ),
                      child: isSelected
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : null,
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
        
        const SizedBox(height: 16),
        
        // Font Size Preview
        _buildFontSizePreview(currentFont, languageService),
      ],
    );
  }

  Widget _buildFontSizePreview(FontFamily font, LanguageService languageService) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageService.isArabic ? 'معاينة أحجام الخط' : 'Font Size Preview',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 12),
          
          // Small size
          _buildSizePreview(
            font,
            14,
            languageService.isArabic ? 'صغير (14)' : 'Small (14)',
            'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
            'Praise be to Allah, Lord of the worlds',
          ),
          
          const SizedBox(height: 8),
          
          // Medium size
          _buildSizePreview(
            font,
            16,
            languageService.isArabic ? 'متوسط (16)' : 'Medium (16)',
            'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
            'Praise be to Allah, Lord of the worlds',
          ),
          
          const SizedBox(height: 8),
          
          // Large size
          _buildSizePreview(
            font,
            18,
            languageService.isArabic ? 'كبير (18)' : 'Large (18)',
            'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
            'Praise be to Allah, Lord of the worlds',
          ),
        ],
      ),
    );
  }

  Widget _buildSizePreview(
    FontFamily font,
    double fontSize,
    String sizeLabel,
    String arabicText,
    String englishText,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          sizeLabel,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          arabicText,
          style: TextStyle(
            fontSize: fontSize,
            fontFamily: font == FontFamily.system ? null : font.fontName,
            color: Colors.black87,
            height: 1.5,
          ),
          textDirection: TextDirection.rtl,
        ),
        Text(
          englishText,
          style: TextStyle(
            fontSize: fontSize - 2,
            fontFamily: font == FontFamily.system ? null : font.fontName,
            color: Colors.grey[700],
          ),
        ),
      ],
    );
  }
}

/// Compact font selector for quick switching
class CompactFontSelector extends StatelessWidget {
  final FontFamily currentFont;
  final Function(FontFamily) onFontChanged;

  const CompactFontSelector({
    super.key,
    required this.currentFont,
    required this.onFontChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<FontFamily>(
          value: currentFont,
          isExpanded: true,
          icon: const Icon(Icons.arrow_drop_down),
          onChanged: (FontFamily? newFont) {
            if (newFont != null) {
              onFontChanged(newFont);
            }
          },
          items: FontFamily.values.map<DropdownMenuItem<FontFamily>>((FontFamily font) {
            return DropdownMenuItem<FontFamily>(
              value: font,
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      font.displayName,
                      style: TextStyle(
                        fontFamily: font == FontFamily.system ? null : font.fontName,
                      ),
                    ),
                  ),
                  Text(
                    'أب',
                    style: TextStyle(
                      fontFamily: font == FontFamily.system ? null : font.fontName,
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

/// Font weight selector
class FontWeightSelector extends StatelessWidget {
  final FontWeight currentWeight;
  final Function(FontWeight) onWeightChanged;

  const FontWeightSelector({
    super.key,
    required this.currentWeight,
    required this.onWeightChanged,
  });

  static const List<FontWeight> fontWeights = [
    FontWeight.w300,
    FontWeight.w400,
    FontWeight.w500,
    FontWeight.w600,
    FontWeight.w700,
  ];

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.isArabic ? 'سُمك الخط' : 'Font Weight',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: fontWeights.map((weight) {
            final isSelected = currentWeight == weight;
            return GestureDetector(
              onTap: () => onWeightChanged(weight),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.primaryGreen : Colors.grey[200],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _getWeightName(weight, languageService.isArabic),
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black87,
                    fontWeight: weight,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  String _getWeightName(FontWeight weight, bool isArabic) {
    switch (weight) {
      case FontWeight.w300:
        return isArabic ? 'خفيف' : 'Light';
      case FontWeight.w400:
        return isArabic ? 'عادي' : 'Normal';
      case FontWeight.w500:
        return isArabic ? 'متوسط' : 'Medium';
      case FontWeight.w600:
        return isArabic ? 'نصف عريض' : 'Semi Bold';
      case FontWeight.w700:
        return isArabic ? 'عريض' : 'Bold';
      default:
        return isArabic ? 'عادي' : 'Normal';
    }
  }
}
