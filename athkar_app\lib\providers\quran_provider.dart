import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/quran_models.dart';
import '../database/database_helper.dart';

class QuranProvider extends ChangeNotifier {
  static final QuranProvider _instance = QuranProvider._internal();
  factory QuranProvider() => _instance;
  QuranProvider._internal();

  List<Surah> _surahs = [];
  List<Ayah> _allAyahs = [];
  Map<int, List<Ayah>> _surahAyahs = {};
  List<QuranBookmark> _bookmarks = [];
  bool _isLoaded = false;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Surah> get surahs => _surahs;
  List<Ayah> get allAyahs => _allAyahs;
  List<QuranBookmark> get bookmarks => _bookmarks;
  bool get isLoaded => _isLoaded;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize Quran provider
  Future<void> initialize() async {
    if (_isLoaded || _isLoading) return;
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _loadQuranData();
      await _loadSurahsInfo();
      await _loadBookmarks();
      _isLoaded = true;
      debugPrint('QuranProvider initialized successfully');
    } catch (e) {
      _error = 'Failed to load Quran data: $e';
      debugPrint('Error initializing QuranProvider: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load Quran text data from assets
  Future<void> _loadQuranData() async {
    try {
      final String quranText = await rootBundle.loadString('assets/data/quran.txt');
      final List<String> lines = quranText.split('\n');
      
      _allAyahs.clear();
      _surahAyahs.clear();
      
      for (String line in lines) {
        if (line.trim().isEmpty) continue;
        
        final parts = line.split('|');
        if (parts.length >= 3) {
          final surahNumber = int.tryParse(parts[0]) ?? 0;
          final ayahNumber = int.tryParse(parts[1]) ?? 0;
          final textArabic = parts[2];
          
          final ayah = Ayah(
            surahNumber: surahNumber,
            ayahNumber: ayahNumber,
            textArabic: textArabic,
            juzNumber: _calculateJuzNumber(surahNumber, ayahNumber),
            pageNumber: _calculatePageNumber(surahNumber, ayahNumber),
          );
          
          _allAyahs.add(ayah);
          
          if (!_surahAyahs.containsKey(surahNumber)) {
            _surahAyahs[surahNumber] = [];
          }
          _surahAyahs[surahNumber]!.add(ayah);
        }
      }
      
      debugPrint('Loaded ${_allAyahs.length} ayahs from ${_surahAyahs.length} surahs');
    } catch (e) {
      debugPrint('Error loading Quran data: $e');
      throw Exception('Failed to load Quran data: $e');
    }
  }

  /// Load Surahs information
  Future<void> _loadSurahsInfo() async {
    _surahs = _getSurahsInfo();
    
    // Add ayahs to each surah
    for (int i = 0; i < _surahs.length; i++) {
      final surahNumber = _surahs[i].number;
      final ayahs = _surahAyahs[surahNumber] ?? [];
      _surahs[i] = Surah(
        number: _surahs[i].number,
        nameArabic: _surahs[i].nameArabic,
        nameEnglish: _surahs[i].nameEnglish,
        nameTransliteration: _surahs[i].nameTransliteration,
        totalAyahs: ayahs.length,
        revelationType: _surahs[i].revelationType,
        revelationOrder: _surahs[i].revelationOrder,
        ayahs: ayahs,
      );
    }
  }

  /// Load bookmarks from database
  Future<void> _loadBookmarks() async {
    try {
      final db = await DatabaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('quran_bookmarks');
      
      _bookmarks = maps.map((map) => QuranBookmark.fromJson(map)).toList();
      debugPrint('Loaded ${_bookmarks.length} bookmarks');
    } catch (e) {
      debugPrint('Error loading bookmarks: $e');
    }
  }

  /// Get Surah by number
  Surah? getSurah(int surahNumber) {
    if (surahNumber < 1 || surahNumber > 114) return null;
    try {
      return _surahs.firstWhere((surah) => surah.number == surahNumber);
    } catch (e) {
      return null;
    }
  }

  /// Get Ayah by surah and ayah number
  Ayah? getAyah(int surahNumber, int ayahNumber) {
    final surahAyahs = _surahAyahs[surahNumber];
    if (surahAyahs == null) return null;
    
    try {
      return surahAyahs.firstWhere((ayah) => ayah.ayahNumber == ayahNumber);
    } catch (e) {
      return null;
    }
  }

  /// Get ayahs for a specific surah
  List<Ayah> getSurahAyahs(int surahNumber) {
    return _surahAyahs[surahNumber] ?? [];
  }

  /// Search in Quran
  Future<List<QuranSearchResult>> searchQuran(String query) async {
    if (query.trim().isEmpty) return [];
    
    final results = <QuranSearchResult>[];
    final searchTerms = query.trim().split(' ');
    
    for (final ayah in _allAyahs) {
      final matchedWords = <String>[];
      double relevanceScore = 0.0;
      
      for (final term in searchTerms) {
        if (ayah.textArabic.contains(term)) {
          matchedWords.add(term);
          relevanceScore += 1.0;
        }
      }
      
      if (matchedWords.isNotEmpty) {
        final surah = getSurah(ayah.surahNumber);
        if (surah != null) {
          results.add(QuranSearchResult(
            ayah: ayah,
            surah: surah,
            matchedWords: matchedWords,
            relevanceScore: relevanceScore / searchTerms.length,
          ));
        }
      }
    }
    
    // Sort by relevance score
    results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
    
    return results.take(50).toList(); // Limit to 50 results
  }

  /// Get random ayah
  Ayah getRandomAyah() {
    if (_allAyahs.isEmpty) return _createDefaultAyah();
    
    final random = DateTime.now().millisecondsSinceEpoch % _allAyahs.length;
    return _allAyahs[random];
  }

  /// Get ayah of the day
  Ayah getAyahOfTheDay() {
    if (_allAyahs.isEmpty) return _createDefaultAyah();
    
    final now = DateTime.now();
    final dayOfYear = now.difference(DateTime(now.year, 1, 1)).inDays;
    final index = dayOfYear % _allAyahs.length;
    
    return _allAyahs[index];
  }

  /// Add bookmark
  Future<void> addBookmark(QuranBookmark bookmark) async {
    try {
      final db = await DatabaseHelper.database;
      final id = await db.insert('quran_bookmarks', bookmark.toJson());
      
      final newBookmark = QuranBookmark(
        id: id,
        surahNumber: bookmark.surahNumber,
        ayahNumber: bookmark.ayahNumber,
        title: bookmark.title,
        notes: bookmark.notes,
        color: bookmark.color,
        category: bookmark.category,
        createdAt: bookmark.createdAt,
      );
      
      _bookmarks.add(newBookmark);
      notifyListeners();
      
      debugPrint('Bookmark added: ${bookmark.title}');
    } catch (e) {
      debugPrint('Error adding bookmark: $e');
    }
  }

  /// Remove bookmark
  Future<void> removeBookmark(int bookmarkId) async {
    try {
      final db = await DatabaseHelper.database;
      await db.delete('quran_bookmarks', where: 'id = ?', whereArgs: [bookmarkId]);
      
      _bookmarks.removeWhere((bookmark) => bookmark.id == bookmarkId);
      notifyListeners();
      
      debugPrint('Bookmark removed: $bookmarkId');
    } catch (e) {
      debugPrint('Error removing bookmark: $e');
    }
  }

  /// Calculate Juz number (approximate)
  int _calculateJuzNumber(int surahNumber, int ayahNumber) {
    // Simplified calculation - in a real app, you'd have exact data
    if (surahNumber <= 2) return 1;
    if (surahNumber <= 4) return 2;
    if (surahNumber <= 6) return 3;
    if (surahNumber <= 9) return 4;
    if (surahNumber <= 11) return 5;
    if (surahNumber <= 15) return 6;
    if (surahNumber <= 18) return 7;
    if (surahNumber <= 22) return 8;
    if (surahNumber <= 27) return 9;
    if (surahNumber <= 36) return 10;
    if (surahNumber <= 41) return 11;
    if (surahNumber <= 49) return 12;
    if (surahNumber <= 57) return 13;
    if (surahNumber <= 66) return 14;
    if (surahNumber <= 77) return 15;
    if (surahNumber <= 87) return 16;
    if (surahNumber <= 97) return 17;
    if (surahNumber <= 106) return 18;
    if (surahNumber <= 110) return 19;
    return 30;
  }

  /// Calculate page number (approximate)
  int _calculatePageNumber(int surahNumber, int ayahNumber) {
    // Simplified calculation - in a real app, you'd have exact data
    return ((surahNumber - 1) * 20 + ayahNumber) ~/ 15 + 1;
  }

  /// Create default ayah for fallback
  Ayah _createDefaultAyah() {
    return Ayah(
      surahNumber: 1,
      ayahNumber: 1,
      textArabic: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
    );
  }

  /// Get Surahs information (first 10 for now)
  List<Surah> _getSurahsInfo() {
    return [
      Surah(number: 1, nameArabic: 'الفاتحة', nameEnglish: 'Al-Fatihah', nameTransliteration: 'Al-Fatihah', totalAyahs: 7, revelationType: 'Meccan', revelationOrder: 5),
      Surah(number: 2, nameArabic: 'البقرة', nameEnglish: 'Al-Baqarah', nameTransliteration: 'Al-Baqarah', totalAyahs: 286, revelationType: 'Medinan', revelationOrder: 87),
      Surah(number: 3, nameArabic: 'آل عمران', nameEnglish: 'Ali \'Imran', nameTransliteration: 'Ali \'Imran', totalAyahs: 200, revelationType: 'Medinan', revelationOrder: 89),
      Surah(number: 4, nameArabic: 'النساء', nameEnglish: 'An-Nisa', nameTransliteration: 'An-Nisa', totalAyahs: 176, revelationType: 'Medinan', revelationOrder: 92),
      Surah(number: 5, nameArabic: 'المائدة', nameEnglish: 'Al-Ma\'idah', nameTransliteration: 'Al-Ma\'idah', totalAyahs: 120, revelationType: 'Medinan', revelationOrder: 112),
      Surah(number: 6, nameArabic: 'الأنعام', nameEnglish: 'Al-An\'am', nameTransliteration: 'Al-An\'am', totalAyahs: 165, revelationType: 'Meccan', revelationOrder: 55),
      Surah(number: 7, nameArabic: 'الأعراف', nameEnglish: 'Al-A\'raf', nameTransliteration: 'Al-A\'raf', totalAyahs: 206, revelationType: 'Meccan', revelationOrder: 39),
      Surah(number: 8, nameArabic: 'الأنفال', nameEnglish: 'Al-Anfal', nameTransliteration: 'Al-Anfal', totalAyahs: 75, revelationType: 'Medinan', revelationOrder: 88),
      Surah(number: 9, nameArabic: 'التوبة', nameEnglish: 'At-Tawbah', nameTransliteration: 'At-Tawbah', totalAyahs: 129, revelationType: 'Medinan', revelationOrder: 113),
      Surah(number: 10, nameArabic: 'يونس', nameEnglish: 'Yunus', nameTransliteration: 'Yunus', totalAyahs: 109, revelationType: 'Meccan', revelationOrder: 51),
    ];
  }

  /// Refresh data
  Future<void> refresh() async {
    _isLoaded = false;
    await initialize();
  }

  /// Dispose resources
  @override
  void dispose() {
    _surahs.clear();
    _allAyahs.clear();
    _surahAyahs.clear();
    _bookmarks.clear();
    super.dispose();
  }
}
