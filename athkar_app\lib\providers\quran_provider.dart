import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:quran/quran.dart' as quran_package;
import '../models/quran_models.dart';

class QuranProvider extends ChangeNotifier {
  static final QuranProvider _instance = QuranProvider._internal();
  factory QuranProvider() => _instance;
  QuranProvider._internal();

  List<Surah> _surahs = [];
  List<Ayah> _allAyahs = [];
  Map<int, List<Ayah>> _surahAyahs = {};
  bool _isLoaded = false;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Surah> get surahs => _surahs;
  List<Ayah> get allAyahs => _allAyahs;
  bool get isLoaded => _isLoaded;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize Quran provider
  Future<void> initialize() async {
    if (_isLoaded || _isLoading) return;
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _loadQuranData();
      await _loadSurahsInfo();
      _isLoaded = true;
      debugPrint('QuranProvider initialized successfully');
    } catch (e) {
      _error = 'Failed to load Quran data: $e';
      debugPrint('Error initializing QuranProvider: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load Quran text data using quran package
  Future<void> _loadQuranData() async {
    try {
      _allAyahs.clear();
      _surahAyahs.clear();

      int ayahId = 1;

      // Load all ayahs from all surahs using quran package
      for (int surahNumber = 1; surahNumber <= quran_package.totalSurahCount; surahNumber++) {
        final verseCount = quran_package.getVerseCount(surahNumber);
        final List<Ayah> surahAyahs = [];

        for (int verseNumber = 1; verseNumber <= verseCount; verseNumber++) {
          final textArabic = quran_package.getVerse(surahNumber, verseNumber);

          final ayah = Ayah(
            id: ayahId++,
            verseNumber: verseNumber,
            textUthmani: textArabic,
            textSimple: textArabic,
            chapterId: surahNumber,
          );

          _allAyahs.add(ayah);
          surahAyahs.add(ayah);
        }

        _surahAyahs[surahNumber] = surahAyahs;
      }

      debugPrint('Loaded ${_allAyahs.length} ayahs from ${_surahAyahs.length} surahs using quran package');
    } catch (e) {
      debugPrint('Error loading Quran data: $e');
      // Fallback to loading from assets if quran package fails
      await _loadQuranDataFromAssets();
    }
  }

  /// Fallback method to load from assets
  Future<void> _loadQuranDataFromAssets() async {
    try {
      final String quranText = await rootBundle.loadString('assets/data/quran.txt');
      final List<String> lines = quranText.split('\n');

      _allAyahs.clear();
      _surahAyahs.clear();

      for (String line in lines) {
        if (line.trim().isEmpty) continue;

        final parts = line.split('|');
        if (parts.length >= 3) {
          final surahNumber = int.tryParse(parts[0]) ?? 0;
          final ayahNumber = int.tryParse(parts[1]) ?? 0;
          final textArabic = parts[2];

          final ayah = Ayah(
            id: _allAyahs.length + 1,
            verseNumber: ayahNumber,
            textUthmani: textArabic,
            textSimple: textArabic,
            chapterId: surahNumber,
          );

          _allAyahs.add(ayah);

          if (!_surahAyahs.containsKey(surahNumber)) {
            _surahAyahs[surahNumber] = [];
          }
          _surahAyahs[surahNumber]!.add(ayah);
        }
      }

      debugPrint('Loaded ${_allAyahs.length} ayahs from ${_surahAyahs.length} surahs from assets');
    } catch (e) {
      debugPrint('Error loading Quran data from assets: $e');
      throw Exception('Failed to load Quran data: $e');
    }
  }

  /// Load Surahs information using quran package
  Future<void> _loadSurahsInfo() async {
    _surahs.clear();

    // Load all 114 surahs using quran package
    for (int i = 1; i <= quran_package.totalSurahCount; i++) {
      final surah = Surah(
        id: i,
        name: quran_package.getSurahName(i),
        arabicName: quran_package.getSurahNameArabic(i),
        englishName: quran_package.getSurahNameEnglish(i),
        numberOfAyahs: quran_package.getVerseCount(i),
        revelationPlace: quran_package.getPlaceOfRevelation(i).toLowerCase(),
      );
      _surahs.add(surah);
    }

    debugPrint('Loaded ${_surahs.length} surahs using quran package');
  }

  /// Get Surah by number
  Surah? getSurah(int surahNumber) {
    if (surahNumber < 1 || surahNumber > 114) return null;
    try {
      return _surahs.firstWhere((surah) => surah.id == surahNumber);
    } catch (e) {
      return null;
    }
  }

  /// Get Ayah by surah and ayah number
  Ayah? getAyah(int surahNumber, int ayahNumber) {
    final surahAyahs = _surahAyahs[surahNumber];
    if (surahAyahs == null) return null;
    
    try {
      return surahAyahs.firstWhere((ayah) => ayah.verseNumber == ayahNumber);
    } catch (e) {
      return null;
    }
  }

  /// Get ayahs for a specific surah
  List<Ayah> getSurahAyahs(int surahNumber) {
    return _surahAyahs[surahNumber] ?? [];
  }

  /// Search in Quran
  Future<List<Map<String, dynamic>>> searchQuran(String query) async {
    if (query.trim().isEmpty) return [];
    
    final results = <Map<String, dynamic>>[];
    final searchTerms = query.trim().split(' ');
    
    for (final ayah in _allAyahs) {
      final matchedWords = <String>[];
      double relevanceScore = 0.0;
      
      for (final term in searchTerms) {
        if (ayah.textUthmani.contains(term)) {
          matchedWords.add(term);
          relevanceScore += 1.0;
        }
      }
      
      if (matchedWords.isNotEmpty) {
        final surah = getSurah(ayah.chapterId ?? 0);
        if (surah != null) {
          results.add({
            'ayah': ayah,
            'surah': surah,
            'matchedWords': matchedWords,
            'relevanceScore': relevanceScore / searchTerms.length,
            'reference': '${surah.arabicName} (${ayah.chapterId}:${ayah.verseNumber})',
          });
        }
      }
    }
    
    // Sort by relevance score
    results.sort((a, b) => (b['relevanceScore'] as double).compareTo(a['relevanceScore'] as double));
    
    return results.take(50).toList(); // Limit to 50 results
  }

  /// Get random ayah
  Ayah getRandomAyah() {
    if (_allAyahs.isEmpty) return _createDefaultAyah();
    
    final random = DateTime.now().millisecondsSinceEpoch % _allAyahs.length;
    return _allAyahs[random];
  }

  /// Get ayah of the day
  Ayah getAyahOfTheDay() {
    if (_allAyahs.isEmpty) return _createDefaultAyah();
    
    final now = DateTime.now();
    final dayOfYear = now.difference(DateTime(now.year, 1, 1)).inDays;
    final index = dayOfYear % _allAyahs.length;
    
    return _allAyahs[index];
  }

  /// Create default ayah for fallback
  Ayah _createDefaultAyah() {
    return Ayah(
      id: 1,
      verseNumber: 1,
      textUthmani: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
      textSimple: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
      chapterId: 1,
    );
  }



  /// Refresh data
  Future<void> refresh() async {
    _isLoaded = false;
    await initialize();
  }

  /// Get verse with translation
  String getVerseWithTranslation(int surahNumber, int verseNumber, {String language = 'en'}) {
    try {
      final arabicText = quran_package.getVerse(surahNumber, verseNumber);
      final translation = quran_package.getVerseTranslation(
        surahNumber,
        verseNumber,
        translation: language == 'en' ? quran_package.Translation.enSaheeh : quran_package.Translation.enSaheeh,
      );
      return '$arabicText\n\n$translation';
    } catch (e) {
      debugPrint('Error getting verse with translation: $e');
      return getAyah(surahNumber, verseNumber)?.textUthmani ?? '';
    }
  }

  /// Get Juz number for a verse
  int getJuzNumber(int surahNumber, int verseNumber) {
    try {
      return quran_package.getJuzNumber(surahNumber, verseNumber);
    } catch (e) {
      debugPrint('Error getting Juz number: $e');
      return 1;
    }
  }

  /// Get page number for a verse
  int getPageNumber(int surahNumber, int verseNumber) {
    try {
      return quran_package.getPageNumber(surahNumber, verseNumber);
    } catch (e) {
      debugPrint('Error getting page number: $e');
      return 1;
    }
  }

  /// Check if verse is a Sajdah verse
  bool isSajdahVerse(int surahNumber, int verseNumber) {
    try {
      return quran_package.isSajdahVerse(surahNumber, verseNumber);
    } catch (e) {
      debugPrint('Error checking Sajdah verse: $e');
      return false;
    }
  }

  /// Get audio URL for a surah
  String getAudioURLBySurah(int surahNumber) {
    try {
      return quran_package.getAudioURLBySurah(surahNumber);
    } catch (e) {
      debugPrint('Error getting audio URL for surah: $e');
      return '';
    }
  }

  /// Get audio URL for a verse
  String getAudioURLByVerse(int surahNumber, int verseNumber) {
    try {
      return quran_package.getAudioURLByVerse(surahNumber, verseNumber);
    } catch (e) {
      debugPrint('Error getting audio URL for verse: $e');
      return '';
    }
  }

  /// Get Surah URL (from Quran.com)
  String getSurahURL(int surahNumber) {
    try {
      return quran_package.getSurahURL(surahNumber);
    } catch (e) {
      debugPrint('Error getting Surah URL: $e');
      return '';
    }
  }

  /// Get verse URL (from Quran.com)
  String getVerseURL(int surahNumber, int verseNumber) {
    try {
      return quran_package.getVerseURL(surahNumber, verseNumber);
    } catch (e) {
      debugPrint('Error getting verse URL: $e');
      return '';
    }
  }

  /// Search words in Quran
  Map<String, dynamic> searchWordsInQuran(List<String> words) {
    try {
      final result = quran_package.searchWords(words);
      return Map<String, dynamic>.from(result);
    } catch (e) {
      debugPrint('Error searching words in Quran: $e');
      return {'count': 0, 'result': []};
    }
  }

  /// Search words in translation
  Map<String, dynamic> searchWordsInTranslation(List<String> words, {String language = 'en'}) {
    try {
      final result = quran_package.searchWordsInTranslation(
        words,
        translation: language == 'en' ? quran_package.Translation.enSaheeh : quran_package.Translation.enSaheeh,
      );
      return Map<String, dynamic>.from(result);
    } catch (e) {
      debugPrint('Error searching words in translation: $e');
      return {'count': 0, 'result': []};
    }
  }

  /// Get enhanced search results
  Future<List<Map<String, dynamic>>> getEnhancedSearchResults(String query) async {
    if (query.trim().isEmpty) return [];

    final words = query.trim().split(' ');
    final results = <Map<String, dynamic>>[];

    try {
      // Search in Arabic text
      final arabicResults = searchWordsInQuran(words);
      if (arabicResults['result'] != null) {
        for (final result in arabicResults['result']) {
          final surahNumber = result['surah'] as int;
          final verseNumber = result['verse'] as int;
          final surah = getSurah(surahNumber);
          final ayah = getAyah(surahNumber, verseNumber);

          if (surah != null && ayah != null) {
            results.add({
              'ayah': ayah,
              'surah': surah,
              'type': 'arabic',
              'juz': getJuzNumber(surahNumber, verseNumber),
              'page': getPageNumber(surahNumber, verseNumber),
              'isSajdah': isSajdahVerse(surahNumber, verseNumber),
              'audioURL': getAudioURLByVerse(surahNumber, verseNumber),
              'reference': '${surah.arabicName} ($surahNumber:$verseNumber)',
            });
          }
        }
      }

      // Search in translation
      final translationResults = searchWordsInTranslation(words);
      if (translationResults['result'] != null) {
        for (final result in translationResults['result']) {
          final surahNumber = result['surah'] as int;
          final verseNumber = result['verse'] as int;
          final surah = getSurah(surahNumber);
          final ayah = getAyah(surahNumber, verseNumber);

          if (surah != null && ayah != null) {
            // Check if already added from Arabic search
            final exists = results.any((r) =>
              r['surah'].id == surahNumber && r['ayah'].verseNumber == verseNumber);

            if (!exists) {
              results.add({
                'ayah': ayah,
                'surah': surah,
                'type': 'translation',
                'juz': getJuzNumber(surahNumber, verseNumber),
                'page': getPageNumber(surahNumber, verseNumber),
                'isSajdah': isSajdahVerse(surahNumber, verseNumber),
                'audioURL': getAudioURLByVerse(surahNumber, verseNumber),
                'reference': '${surah.arabicName} ($surahNumber:$verseNumber)',
              });
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error in enhanced search: $e');
    }

    return results.take(50).toList(); // Limit to 50 results
  }

  /// Dispose resources
  @override
  void dispose() {
    _surahs.clear();
    _allAyahs.clear();
    _surahAyahs.clear();
    super.dispose();
  }
}
