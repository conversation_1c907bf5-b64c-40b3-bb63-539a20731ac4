import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/quran_models.dart';

class QuranProvider extends ChangeNotifier {
  static final QuranProvider _instance = QuranProvider._internal();
  factory QuranProvider() => _instance;
  QuranProvider._internal();

  List<Surah> _surahs = [];
  List<Ayah> _allAyahs = [];
  Map<int, List<Ayah>> _surahAyahs = {};
  bool _isLoaded = false;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Surah> get surahs => _surahs;
  List<Ayah> get allAyahs => _allAyahs;
  bool get isLoaded => _isLoaded;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize Quran provider
  Future<void> initialize() async {
    if (_isLoaded || _isLoading) return;
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      await _loadQuranData();
      await _loadSurahsInfo();
      _isLoaded = true;
      debugPrint('QuranProvider initialized successfully');
    } catch (e) {
      _error = 'Failed to load Quran data: $e';
      debugPrint('Error initializing QuranProvider: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load Quran text data from assets
  Future<void> _loadQuranData() async {
    try {
      final String quranText = await rootBundle.loadString('assets/data/quran.txt');
      final List<String> lines = quranText.split('\n');
      
      _allAyahs.clear();
      _surahAyahs.clear();
      
      for (String line in lines) {
        if (line.trim().isEmpty) continue;
        
        final parts = line.split('|');
        if (parts.length >= 3) {
          final surahNumber = int.tryParse(parts[0]) ?? 0;
          final ayahNumber = int.tryParse(parts[1]) ?? 0;
          final textArabic = parts[2];
          
          final ayah = Ayah(
            id: _allAyahs.length + 1,
            verseNumber: ayahNumber,
            textUthmani: textArabic,
            textSimple: textArabic,
            chapterId: surahNumber,
          );
          
          _allAyahs.add(ayah);
          
          if (!_surahAyahs.containsKey(surahNumber)) {
            _surahAyahs[surahNumber] = [];
          }
          _surahAyahs[surahNumber]!.add(ayah);
        }
      }
      
      debugPrint('Loaded ${_allAyahs.length} ayahs from ${_surahAyahs.length} surahs');
    } catch (e) {
      debugPrint('Error loading Quran data: $e');
      throw Exception('Failed to load Quran data: $e');
    }
  }

  /// Load Surahs information
  Future<void> _loadSurahsInfo() async {
    _surahs = _getSurahsInfo();
    
    // Update ayah counts based on loaded data
    for (int i = 0; i < _surahs.length; i++) {
      final surahNumber = _surahs[i].id;
      final ayahs = _surahAyahs[surahNumber] ?? [];
      
      // Create new Surah with correct ayah count
      _surahs[i] = Surah(
        id: _surahs[i].id,
        name: _surahs[i].name,
        arabicName: _surahs[i].arabicName,
        englishName: _surahs[i].englishName,
        numberOfAyahs: ayahs.length,
        revelationPlace: _surahs[i].revelationPlace,
      );
    }
  }

  /// Get Surah by number
  Surah? getSurah(int surahNumber) {
    if (surahNumber < 1 || surahNumber > 114) return null;
    try {
      return _surahs.firstWhere((surah) => surah.id == surahNumber);
    } catch (e) {
      return null;
    }
  }

  /// Get Ayah by surah and ayah number
  Ayah? getAyah(int surahNumber, int ayahNumber) {
    final surahAyahs = _surahAyahs[surahNumber];
    if (surahAyahs == null) return null;
    
    try {
      return surahAyahs.firstWhere((ayah) => ayah.verseNumber == ayahNumber);
    } catch (e) {
      return null;
    }
  }

  /// Get ayahs for a specific surah
  List<Ayah> getSurahAyahs(int surahNumber) {
    return _surahAyahs[surahNumber] ?? [];
  }

  /// Search in Quran
  Future<List<Map<String, dynamic>>> searchQuran(String query) async {
    if (query.trim().isEmpty) return [];
    
    final results = <Map<String, dynamic>>[];
    final searchTerms = query.trim().split(' ');
    
    for (final ayah in _allAyahs) {
      final matchedWords = <String>[];
      double relevanceScore = 0.0;
      
      for (final term in searchTerms) {
        if (ayah.textUthmani.contains(term)) {
          matchedWords.add(term);
          relevanceScore += 1.0;
        }
      }
      
      if (matchedWords.isNotEmpty) {
        final surah = getSurah(ayah.chapterId ?? 0);
        if (surah != null) {
          results.add({
            'ayah': ayah,
            'surah': surah,
            'matchedWords': matchedWords,
            'relevanceScore': relevanceScore / searchTerms.length,
            'reference': '${surah.arabicName} (${ayah.chapterId}:${ayah.verseNumber})',
          });
        }
      }
    }
    
    // Sort by relevance score
    results.sort((a, b) => (b['relevanceScore'] as double).compareTo(a['relevanceScore'] as double));
    
    return results.take(50).toList(); // Limit to 50 results
  }

  /// Get random ayah
  Ayah getRandomAyah() {
    if (_allAyahs.isEmpty) return _createDefaultAyah();
    
    final random = DateTime.now().millisecondsSinceEpoch % _allAyahs.length;
    return _allAyahs[random];
  }

  /// Get ayah of the day
  Ayah getAyahOfTheDay() {
    if (_allAyahs.isEmpty) return _createDefaultAyah();
    
    final now = DateTime.now();
    final dayOfYear = now.difference(DateTime(now.year, 1, 1)).inDays;
    final index = dayOfYear % _allAyahs.length;
    
    return _allAyahs[index];
  }

  /// Create default ayah for fallback
  Ayah _createDefaultAyah() {
    return Ayah(
      id: 1,
      verseNumber: 1,
      textUthmani: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
      textSimple: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
      chapterId: 1,
    );
  }

  /// Get Surahs information (first 10 for now)
  List<Surah> _getSurahsInfo() {
    return [
      Surah(id: 1, name: 'Al-Fatihah', arabicName: 'الفاتحة', englishName: 'The Opening', numberOfAyahs: 7, revelationPlace: 'makkah'),
      Surah(id: 2, name: 'Al-Baqarah', arabicName: 'البقرة', englishName: 'The Cow', numberOfAyahs: 286, revelationPlace: 'madinah'),
      Surah(id: 3, name: 'Ali \'Imran', arabicName: 'آل عمران', englishName: 'Family of Imran', numberOfAyahs: 200, revelationPlace: 'madinah'),
      Surah(id: 4, name: 'An-Nisa', arabicName: 'النساء', englishName: 'The Women', numberOfAyahs: 176, revelationPlace: 'madinah'),
      Surah(id: 5, name: 'Al-Ma\'idah', arabicName: 'المائدة', englishName: 'The Table Spread', numberOfAyahs: 120, revelationPlace: 'madinah'),
      Surah(id: 6, name: 'Al-An\'am', arabicName: 'الأنعام', englishName: 'The Cattle', numberOfAyahs: 165, revelationPlace: 'makkah'),
      Surah(id: 7, name: 'Al-A\'raf', arabicName: 'الأعراف', englishName: 'The Heights', numberOfAyahs: 206, revelationPlace: 'makkah'),
      Surah(id: 8, name: 'Al-Anfal', arabicName: 'الأنفال', englishName: 'The Spoils of War', numberOfAyahs: 75, revelationPlace: 'madinah'),
      Surah(id: 9, name: 'At-Tawbah', arabicName: 'التوبة', englishName: 'The Repentance', numberOfAyahs: 129, revelationPlace: 'madinah'),
      Surah(id: 10, name: 'Yunus', arabicName: 'يونس', englishName: 'Jonah', numberOfAyahs: 109, revelationPlace: 'makkah'),
    ];
  }

  /// Refresh data
  Future<void> refresh() async {
    _isLoaded = false;
    await initialize();
  }

  /// Dispose resources
  @override
  void dispose() {
    _surahs.clear();
    _allAyahs.clear();
    _surahAyahs.clear();
    super.dispose();
  }
}
