import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../database/database_helper.dart';
import '../config/app_config.dart';
import '../models/athkar_models.dart';
import '../models/user_models.dart';

class NewSupabaseService {
  static SupabaseClient? _supabase;
  static final DatabaseHelper _dbHelper = DatabaseHelper();
  static bool _isInitialized = false;
  static bool _isSyncing = false;
  static DateTime? _lastSyncTime;

  /// Get Supabase client instance
  static SupabaseClient get client {
    if (_supabase == null) {
      throw Exception('Supabase not initialized. Call initialize() first.');
    }
    return _supabase!;
  }

  /// Initialize Supabase with fresh configuration
  static Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('Supabase already initialized');
      return;
    }
    
    try {
      debugPrint('Initializing Supabase with fresh configuration...');
      debugPrint('URL: ${AppConfig.supabaseUrl}');
      
      // Initialize Supabase with new credentials
      await Supabase.initialize(
        url: AppConfig.supabaseUrl,
        anonKey: AppConfig.supabaseAnonKey,
        debug: !AppConfig.isProduction,
      );
      
      _supabase = Supabase.instance.client;
      _isInitialized = true;
      
      debugPrint('Supabase initialized successfully');
      
      // Test connection
      await _testConnection();
      
      debugPrint('Supabase setup completed');
      
    } catch (e) {
      debugPrint('Error initializing Supabase: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  /// Test Supabase connection
  static Future<void> _testConnection() async {
    try {
      // Test with a simple query that should work even with empty tables
      final response = await _supabase!.from('user_profiles').select('count').limit(1);
      debugPrint('Supabase connection test successful');
    } catch (e) {
      debugPrint('Supabase connection test: $e');
      // Don't throw error as this is just a test
    }
  }

  /// Sync user profile to Supabase
  static Future<void> syncUserProfile(UserProfile profile) async {
    if (!_isInitialized) await initialize();
    
    try {
      final user = _supabase!.auth.currentUser;
      if (user == null) {
        debugPrint('No authenticated user for profile sync');
        return;
      }

      final profileData = {
        'id': profile.id,
        'user_id': user.id,
        'full_name': profile.fullName,
        'email': profile.email,
        'phone_number': profile.phoneNumber,
        'avatar_url': profile.avatarUrl,
        'location': profile.location,
        'language': profile.language,
        'theme': profile.theme,
        'created_at': profile.createdAt.toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase!.from('user_profiles').upsert(profileData);
      debugPrint('User profile synced successfully');
      
    } catch (e) {
      debugPrint('Error syncing user profile: $e');
      rethrow;
    }
  }

  /// Sync athkar routine to Supabase
  static Future<void> syncAthkarRoutine(AthkarRoutine routine) async {
    if (!_isInitialized) await initialize();
    
    try {
      final user = _supabase!.auth.currentUser;
      if (user == null) {
        debugPrint('No authenticated user for routine sync');
        return;
      }

      final routineData = {
        'id': routine.id,
        'user_id': user.id,
        'title': routine.title,
        'description': routine.description,
        'category': routine.category,
        'estimated_duration': routine.estimatedDuration,
        'color_hex': routine.colorHex,
        'created_at': routine.createdAt.toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase!.from('athkar_routines').upsert(routineData);
      
      // Sync steps
      if (routine.steps != null) {
        for (final step in routine.steps!) {
          await syncAthkarStep(step);
        }
      }
      
      debugPrint('Athkar routine synced successfully');
      
    } catch (e) {
      debugPrint('Error syncing athkar routine: $e');
      rethrow;
    }
  }

  /// Sync athkar step to Supabase
  static Future<void> syncAthkarStep(AthkarStep step) async {
    if (!_isInitialized) await initialize();
    
    try {
      final user = _supabase!.auth.currentUser;
      if (user == null) {
        debugPrint('No authenticated user for step sync');
        return;
      }

      final stepData = {
        'id': step.id,
        'routine_id': step.routineId,
        'user_id': user.id,
        'step_order': step.stepOrder,
        'arabic_text': step.arabicText,
        'transliteration': step.transliteration,
        'translation': step.translation,
        'target_count': step.targetCount,
        'audio_url': step.audioUrl,
        'color_hex': step.colorHex,
        'created_at': step.createdAt.toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase!.from('athkar_steps').upsert(stepData);
      debugPrint('Athkar step synced successfully');
      
    } catch (e) {
      debugPrint('Error syncing athkar step: $e');
      rethrow;
    }
  }

  /// Fetch user routines from Supabase
  static Future<List<AthkarRoutine>> fetchUserRoutines() async {
    if (!_isInitialized) await initialize();
    
    try {
      final user = _supabase!.auth.currentUser;
      if (user == null) {
        debugPrint('No authenticated user for fetching routines');
        return [];
      }

      final response = await _supabase!
          .from('athkar_routines')
          .select('*, athkar_steps(*)')
          .eq('user_id', user.id)
          .order('created_at', ascending: false);

      final routines = <AthkarRoutine>[];
      for (final routineData in response) {
        final steps = <AthkarStep>[];
        if (routineData['athkar_steps'] != null) {
          for (final stepData in routineData['athkar_steps']) {
            steps.add(AthkarStep(
              id: stepData['id'],
              routineId: stepData['routine_id'],
              stepOrder: stepData['step_order'],
              arabicText: stepData['arabic_text'],
              transliteration: stepData['transliteration'],
              translation: stepData['translation'],
              targetCount: stepData['target_count'] ?? 1,
              audioUrl: stepData['audio_url'],
              colorHex: stepData['color_hex'],
              createdAt: DateTime.parse(stepData['created_at']),
              updatedAt: DateTime.parse(stepData['updated_at']),
            ));
          }
        }

        routines.add(AthkarRoutine(
          id: routineData['id'],
          title: routineData['title'],
          description: routineData['description'],
          category: routineData['category'],
          estimatedDuration: routineData['estimated_duration'],
          colorHex: routineData['color_hex'],
          createdAt: DateTime.parse(routineData['created_at']),
          updatedAt: DateTime.parse(routineData['updated_at']),
          steps: steps,
        ));
      }

      debugPrint('Fetched ${routines.length} routines from Supabase');
      return routines;
      
    } catch (e) {
      debugPrint('Error fetching user routines: $e');
      return [];
    }
  }

  /// Delete routine from Supabase
  static Future<void> deleteRoutine(String routineId) async {
    if (!_isInitialized) await initialize();
    
    try {
      final user = _supabase!.auth.currentUser;
      if (user == null) {
        debugPrint('No authenticated user for deleting routine');
        return;
      }

      // Delete steps first (due to foreign key constraint)
      await _supabase!
          .from('athkar_steps')
          .delete()
          .eq('routine_id', routineId)
          .eq('user_id', user.id);

      // Delete routine
      await _supabase!
          .from('athkar_routines')
          .delete()
          .eq('id', routineId)
          .eq('user_id', user.id);

      debugPrint('Routine deleted from Supabase successfully');
      
    } catch (e) {
      debugPrint('Error deleting routine from Supabase: $e');
      rethrow;
    }
  }

  /// Full sync - upload local data to Supabase
  static Future<void> performFullSync() async {
    if (_isSyncing) {
      debugPrint('Sync already in progress');
      return;
    }

    _isSyncing = true;
    
    try {
      debugPrint('Starting full sync to Supabase...');
      
      // Sync all local routines
      final localRoutines = await _dbHelper.getAllRoutines();
      for (final routine in localRoutines) {
        await syncAthkarRoutine(routine);
      }
      
      _lastSyncTime = DateTime.now();
      debugPrint('Full sync completed successfully');
      
    } catch (e) {
      debugPrint('Error during full sync: $e');
      rethrow;
    } finally {
      _isSyncing = false;
    }
  }

  /// Check if user is authenticated
  static bool get isAuthenticated {
    return _supabase?.auth.currentUser != null;
  }

  /// Get current user
  static User? get currentUser {
    return _supabase?.auth.currentUser;
  }

  /// Sign out user
  static Future<void> signOut() async {
    if (!_isInitialized) return;
    
    try {
      await _supabase!.auth.signOut();
      debugPrint('User signed out successfully');
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }
}
