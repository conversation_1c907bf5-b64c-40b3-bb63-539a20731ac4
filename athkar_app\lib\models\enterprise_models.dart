import 'package:json_annotation/json_annotation.dart';

part 'enterprise_models.g.dart';

enum OrganizationType {
  mosque,
  islamicCenter,
  school,
  university,
  company,
  nonprofit,
  government,
}

enum UserRole {
  superAdmin,
  admin,
  manager,
  moderator,
  user,
  guest,
}

enum Permission {
  manageUsers,
  manageRoles,
  managePrograms,
  viewReports,
  manageSettings,
  moderateContent,
  viewAnalytics,
  exportData,
}

enum ReportType {
  userActivity,
  programProgress,
  departmentPerformance,
  compliance,
}

@JsonSerializable()
class Organization {
  final String id;
  final String name;
  final String description;
  final OrganizationType type;
  final String? logoUrl;
  final String? website;
  final String? contactEmail;
  final String? phone;
  final String? address;
  final String subscriptionPlan;
  final int maxUsers;
  final int currentUsers;
  final List<String> features;
  final Map<String, dynamic> settings;
  final DateTime createdAt;
  final DateTime updatedAt;

  Organization({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    this.logoUrl,
    this.website,
    this.contactEmail,
    this.phone,
    this.address,
    this.subscriptionPlan = 'basic',
    this.maxUsers = 100,
    this.currentUsers = 0,
    this.features = const [],
    this.settings = const {},
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory Organization.fromJson(Map<String, dynamic> json) => _$OrganizationFromJson(json);
  Map<String, dynamic> toJson() => _$OrganizationToJson(this);
}

@JsonSerializable()
class OrganizationUser {
  final String id;
  final String organizationId;
  final String userId;
  final UserRole role;
  final List<Permission> permissions;
  final String? department;
  final String? position;
  final bool isActive;
  final String? invitedBy;
  final DateTime joinedAt;
  final DateTime? lastActive;

  OrganizationUser({
    required this.id,
    required this.organizationId,
    required this.userId,
    required this.role,
    this.permissions = const [],
    this.department,
    this.position,
    this.isActive = true,
    this.invitedBy,
    DateTime? joinedAt,
    this.lastActive,
  }) : joinedAt = joinedAt ?? DateTime.now();

  factory OrganizationUser.fromJson(Map<String, dynamic> json) => _$OrganizationUserFromJson(json);
  Map<String, dynamic> toJson() => _$OrganizationUserToJson(this);
}

@JsonSerializable()
class Department {
  final String id;
  final String organizationId;
  final String name;
  final String description;
  final String? headUserId;
  final int memberCount;
  final double budget;
  final DateTime createdAt;

  Department({
    required this.id,
    required this.organizationId,
    required this.name,
    required this.description,
    this.headUserId,
    this.memberCount = 0,
    this.budget = 0.0,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory Department.fromJson(Map<String, dynamic> json) => _$DepartmentFromJson(json);
  Map<String, dynamic> toJson() => _$DepartmentToJson(this);
}

@JsonSerializable()
class Team {
  final String id;
  final String organizationId;
  final String? departmentId;
  final String name;
  final String description;
  final String? leaderUserId;
  final int memberCount;
  final List<String> goals;
  final DateTime createdAt;

  Team({
    required this.id,
    required this.organizationId,
    this.departmentId,
    required this.name,
    required this.description,
    this.leaderUserId,
    this.memberCount = 0,
    this.goals = const [],
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory Team.fromJson(Map<String, dynamic> json) => _$TeamFromJson(json);
  Map<String, dynamic> toJson() => _$TeamToJson(this);
}

@JsonSerializable()
class OrganizationProgram {
  final String id;
  final String organizationId;
  final String name;
  final String description;
  final String type;
  final String targetAudience;
  final int durationDays;
  final List<String> athkarRoutines;
  final List<String> goals;
  final Map<String, dynamic> metrics;
  final bool isActive;
  final String createdBy;
  final DateTime createdAt;

  OrganizationProgram({
    required this.id,
    required this.organizationId,
    required this.name,
    required this.description,
    required this.type,
    required this.targetAudience,
    required this.durationDays,
    this.athkarRoutines = const [],
    this.goals = const [],
    this.metrics = const {},
    this.isActive = true,
    required this.createdBy,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory OrganizationProgram.fromJson(Map<String, dynamic> json) => _$OrganizationProgramFromJson(json);
  Map<String, dynamic> toJson() => _$OrganizationProgramToJson(this);
}

@JsonSerializable()
class ProgramEnrollment {
  final String id;
  final String programId;
  final String userId;
  final String enrolledBy;
  final double progress;
  final String status;
  final DateTime startedAt;
  final DateTime? completedAt;

  ProgramEnrollment({
    required this.id,
    required this.programId,
    required this.userId,
    required this.enrolledBy,
    this.progress = 0.0,
    this.status = 'active',
    DateTime? startedAt,
    this.completedAt,
  }) : startedAt = startedAt ?? DateTime.now();

  factory ProgramEnrollment.fromJson(Map<String, dynamic> json) => _$ProgramEnrollmentFromJson(json);
  Map<String, dynamic> toJson() => _$ProgramEnrollmentToJson(this);
}

@JsonSerializable()
class OrganizationReport {
  final String id;
  final String organizationId;
  final ReportType type;
  final String title;
  final Map<String, dynamic> data;
  final String generatedBy;
  final DateTime generatedAt;
  final DateTime periodStart;
  final DateTime periodEnd;

  const OrganizationReport({
    required this.id,
    required this.organizationId,
    required this.type,
    required this.title,
    required this.data,
    required this.generatedBy,
    required this.generatedAt,
    required this.periodStart,
    required this.periodEnd,
  });

  factory OrganizationReport.fromJson(Map<String, dynamic> json) => _$OrganizationReportFromJson(json);
  Map<String, dynamic> toJson() => _$OrganizationReportToJson(this);
}

@JsonSerializable()
class AuditLog {
  final String id;
  final String organizationId;
  final String userId;
  final String action;
  final String resourceType;
  final String resourceId;
  final Map<String, dynamic> details;
  final String? ipAddress;
  final String? userAgent;
  final DateTime timestamp;

  AuditLog({
    required this.id,
    required this.organizationId,
    required this.userId,
    required this.action,
    required this.resourceType,
    required this.resourceId,
    this.details = const {},
    this.ipAddress,
    this.userAgent,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory AuditLog.fromJson(Map<String, dynamic> json) => _$AuditLogFromJson(json);
  Map<String, dynamic> toJson() => _$AuditLogToJson(this);
}

@JsonSerializable()
class OrganizationSetting {
  final String id;
  final String organizationId;
  final String category;
  final String key;
  final String value;
  final String updatedBy;
  final DateTime updatedAt;

  OrganizationSetting({
    required this.id,
    required this.organizationId,
    required this.category,
    required this.key,
    required this.value,
    required this.updatedBy,
    DateTime? updatedAt,
  }) : updatedAt = updatedAt ?? DateTime.now();

  factory OrganizationSetting.fromJson(Map<String, dynamic> json) => _$OrganizationSettingFromJson(json);
  Map<String, dynamic> toJson() => _$OrganizationSettingToJson(this);
}

@JsonSerializable()
class UserInvitation {
  final String id;
  final String organizationId;
  final String email;
  final UserRole role;
  final String? department;
  final String? position;
  final List<Permission> permissions;
  final String invitedBy;
  final DateTime createdAt;
  final DateTime? acceptedAt;
  final bool isExpired;

  UserInvitation({
    required this.id,
    required this.organizationId,
    required this.email,
    required this.role,
    this.department,
    this.position,
    this.permissions = const [],
    required this.invitedBy,
    DateTime? createdAt,
    this.acceptedAt,
    this.isExpired = false,
  }) : createdAt = createdAt ?? DateTime.now();

  factory UserInvitation.fromJson(Map<String, dynamic> json) => _$UserInvitationFromJson(json);
  Map<String, dynamic> toJson() => _$UserInvitationToJson(this);
}

@JsonSerializable()
class OrganizationAnalytics {
  final String organizationId;
  final int totalUsers;
  final int activeUsers;
  final Map<String, dynamic> userEngagement;
  final Map<String, dynamic> programMetrics;
  final Map<String, dynamic> departmentMetrics;
  final DateTime generatedAt;

  const OrganizationAnalytics({
    required this.organizationId,
    required this.totalUsers,
    required this.activeUsers,
    required this.userEngagement,
    required this.programMetrics,
    required this.departmentMetrics,
    required this.generatedAt,
  });

  factory OrganizationAnalytics.empty(String organizationId) {
    return OrganizationAnalytics(
      organizationId: organizationId,
      totalUsers: 0,
      activeUsers: 0,
      userEngagement: {},
      programMetrics: {},
      departmentMetrics: {},
      generatedAt: DateTime.now(),
    );
  }

  factory OrganizationAnalytics.fromJson(Map<String, dynamic> json) => _$OrganizationAnalyticsFromJson(json);
  Map<String, dynamic> toJson() => _$OrganizationAnalyticsToJson(this);
}

@JsonSerializable()
class ComplianceMetrics {
  final String organizationId;
  final double overallComplianceScore;
  final Map<String, double> departmentScores;
  final Map<String, double> programCompletionRates;
  final List<String> nonCompliantUsers;
  final Map<String, int> violationCounts;
  final DateTime calculatedAt;

  const ComplianceMetrics({
    required this.organizationId,
    required this.overallComplianceScore,
    required this.departmentScores,
    required this.programCompletionRates,
    required this.nonCompliantUsers,
    required this.violationCounts,
    required this.calculatedAt,
  });

  factory ComplianceMetrics.fromJson(Map<String, dynamic> json) => _$ComplianceMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$ComplianceMetricsToJson(this);
}

@JsonSerializable()
class OrganizationSubscription {
  final String id;
  final String organizationId;
  final String planId;
  final String planName;
  final double monthlyPrice;
  final int maxUsers;
  final List<String> features;
  final DateTime startDate;
  final DateTime? endDate;
  final bool isActive;
  final String paymentMethod;
  final DateTime? nextBillingDate;

  OrganizationSubscription({
    required this.id,
    required this.organizationId,
    required this.planId,
    required this.planName,
    required this.monthlyPrice,
    required this.maxUsers,
    this.features = const [],
    DateTime? startDate,
    this.endDate,
    this.isActive = true,
    required this.paymentMethod,
    this.nextBillingDate,
  }) : startDate = startDate ?? DateTime.now();

  factory OrganizationSubscription.fromJson(Map<String, dynamic> json) => _$OrganizationSubscriptionFromJson(json);
  Map<String, dynamic> toJson() => _$OrganizationSubscriptionToJson(this);
}

@JsonSerializable()
class AdminDashboardData {
  final String organizationId;
  final int totalUsers;
  final int activeUsers;
  final int totalPrograms;
  final int activePrograms;
  final double averageEngagement;
  final Map<String, int> usersByDepartment;
  final Map<String, double> programCompletionRates;
  final List<Map<String, dynamic>> recentActivity;
  final Map<String, dynamic> systemHealth;
  final DateTime lastUpdated;

  const AdminDashboardData({
    required this.organizationId,
    required this.totalUsers,
    required this.activeUsers,
    required this.totalPrograms,
    required this.activePrograms,
    required this.averageEngagement,
    required this.usersByDepartment,
    required this.programCompletionRates,
    required this.recentActivity,
    required this.systemHealth,
    required this.lastUpdated,
  });

  factory AdminDashboardData.fromJson(Map<String, dynamic> json) => _$AdminDashboardDataFromJson(json);
  Map<String, dynamic> toJson() => _$AdminDashboardDataToJson(this);
}
