import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import '../models/athkar_models.dart';
import '../providers/tasbeeh_dua_provider.dart';
import '../widgets/color_picker_widget.dart';
import '../theme/app_theme.dart';

enum CreateType { tasbeeh, dua }

class CreateTasbeehDuaScreen extends StatefulWidget {
  final CreateType type;
  
  const CreateTasbeehDuaScreen({super.key, required this.type});

  @override
  State<CreateTasbeehDuaScreen> createState() => _CreateTasbeehDuaScreenState();
}

class _CreateTasbeehDuaScreenState extends State<CreateTasbeehDuaScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _arabicController = TextEditingController();
  final _transliterationController = TextEditingController();
  final _translationController = TextEditingController();
  final _referenceController = TextEditingController();
  
  String _selectedCategory = 'Custom';
  String _selectedSource = 'Custom';
  Color _selectedColor = AppTheme.primaryGreen;
  
  final List<String> _tasbeehCategories = [
    'Custom',
    'Names of Allah',
    'Praise',
    'Glorification',
    'Seeking Forgiveness',
  ];
  
  final List<String> _duaCategories = [
    'Custom',
    'Daily Duas',
    'Quranic Duas',
    'Prophetic Duas',
    'Travel',
    'Health',
    'Gratitude',
    'Seeking Forgiveness',
    'Protection',
  ];
  
  final List<String> _sources = [
    'Custom',
    'Quran',
    'Hadith',
    'Scholarly',
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _arabicController.dispose();
    _transliterationController.dispose();
    _translationController.dispose();
    _referenceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Create ${widget.type == CreateType.tasbeeh ? 'Tasbeeh' : 'Dua'}'),
        actions: [
          TextButton(
            onPressed: _saveItem,
            child: const Text(
              'Save',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfo(),
              const SizedBox(height: 24),
              _buildTextFields(),
              const SizedBox(height: 24),
              _buildCategoryAndSource(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            if (widget.type == CreateType.dua) ...[
              // Title field for duas
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Title *',
                  hintText: 'e.g., Dua for guidance',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTextFields() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Text Content',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Arabic text
            TextFormField(
              controller: _arabicController,
              decoration: const InputDecoration(
                labelText: 'Arabic Text *',
                hintText: 'Enter the Arabic text',
              ),
              textDirection: TextDirection.rtl,
              maxLines: widget.type == CreateType.dua ? 4 : 2,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Arabic text is required';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // Transliteration
            TextFormField(
              controller: _transliterationController,
              decoration: const InputDecoration(
                labelText: 'Transliteration',
                hintText: 'Phonetic pronunciation',
              ),
              maxLines: widget.type == CreateType.dua ? 3 : 2,
            ),
            
            const SizedBox(height: 16),
            
            // Translation
            TextFormField(
              controller: _translationController,
              decoration: const InputDecoration(
                labelText: 'Translation',
                hintText: 'English translation',
              ),
              maxLines: widget.type == CreateType.dua ? 4 : 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryAndSource() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Classification',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Category
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'Category',
              ),
              items: (widget.type == CreateType.tasbeeh ? _tasbeehCategories : _duaCategories)
                  .map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(category),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Source
            DropdownButtonFormField<String>(
              value: _selectedSource,
              decoration: const InputDecoration(
                labelText: 'Source',
              ),
              items: _sources.map((source) {
                return DropdownMenuItem(
                  value: source,
                  child: Text(source),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedSource = value!;
                });
              },
            ),

            const SizedBox(height: 16),

            // Color Picker
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[400]!),
                borderRadius: BorderRadius.circular(4),
              ),
              child: ListTile(
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _selectedColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                ),
                title: Text('${widget.type == CreateType.tasbeeh ? 'Tasbeeh' : 'Dua'} Color'),
                subtitle: const Text('Tap to change color'),
                trailing: const Icon(Icons.color_lens),
                onTap: () async {
                  final color = await showColorPickerDialog(
                    context,
                    initialColor: _selectedColor,
                    title: 'Choose ${widget.type == CreateType.tasbeeh ? 'Tasbeeh' : 'Dua'} Color',
                    presetColors: [
                      AppTheme.primaryGreen,
                      AppTheme.lightGreen,
                      AppTheme.accentGold,
                      AppTheme.lightGold,
                      AppTheme.darkBlue,
                      AppTheme.lightBlue,
                      const Color(0xFF8E24AA), // Purple
                      const Color(0xFFD32F2F), // Red
                      const Color(0xFFFF6F00), // Orange
                      const Color(0xFF388E3C), // Green
                      const Color(0xFF1976D2), // Blue
                      const Color(0xFF7B1FA2), // Deep Purple
                      const Color(0xFF5D4037), // Brown
                      const Color(0xFF455A64), // Blue Grey
                    ],
                  );
                  if (color != null) {
                    setState(() {
                      _selectedColor = color;
                    });
                  }
                },
              ),
            ),

            const SizedBox(height: 16),

            // Reference
            TextFormField(
              controller: _referenceController,
              decoration: const InputDecoration(
                labelText: 'Reference',
                hintText: 'e.g., Quran 2:255, Bukhari 123',
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveItem() async {
    if (!_formKey.currentState!.validate()) return;

    final provider = context.read<TasbeehDuaProvider>();
    final uuid = const Uuid();
    final now = DateTime.now();

    try {
      if (widget.type == CreateType.tasbeeh) {
        final tasbeehItem = TasbeehItem(
          id: uuid.v4(),
          arabicText: _arabicController.text.trim(),
          transliteration: _transliterationController.text.trim().isEmpty
              ? null : _transliterationController.text.trim(),
          translation: _translationController.text.trim().isEmpty
              ? null : _translationController.text.trim(),
          category: _selectedCategory == 'Custom' ? null : _selectedCategory,
          colorHex: _selectedColor.toARGB32().toRadixString(16).substring(2).toUpperCase(),
          isDefault: false,
          createdAt: now,
          updatedAt: now,
        );

        await provider.addTasbeehItem(tasbeehItem);
      } else {
        final duaItem = DuaItem(
          id: uuid.v4(),
          title: _titleController.text.trim(),
          arabicText: _arabicController.text.trim(),
          transliteration: _transliterationController.text.trim().isEmpty
              ? null : _transliterationController.text.trim(),
          translation: _translationController.text.trim().isEmpty
              ? null : _translationController.text.trim(),
          category: _selectedCategory == 'Custom' ? null : _selectedCategory,
          source: _selectedSource == 'Custom' ? null : _selectedSource,
          reference: _referenceController.text.trim().isEmpty
              ? null : _referenceController.text.trim(),
          colorHex: _selectedColor.toARGB32().toRadixString(16).substring(2).toUpperCase(),
          isDefault: false,
          createdAt: now,
          updatedAt: now,
        );

        await provider.addDuaItem(duaItem);
      }

      if (mounted) {
        final itemType = widget.type == CreateType.tasbeeh ? 'Tasbeeh' : 'Dua';
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('$itemType Created'),
            content: Text('Your custom $itemType has been saved successfully.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context); // Close dialog
                  Navigator.pop(context); // Close create screen
                },
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving item: $e')),
        );
      }
    }
  }
}
