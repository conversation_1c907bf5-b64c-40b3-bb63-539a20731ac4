import 'package:flutter/foundation.dart';
import '../models/athkar_models.dart';
import '../repositories/athkar_repository.dart';
import '../services/error_handler.dart';

class AthkarProvider extends ChangeNotifier {
  final AthkarRepository _repository = AthkarRepository();
  
  List<AthkarCategory> _categories = [];
  List<AthkarRoutine> _routines = [];
  List<UserProgress> _userProgress = [];
  List<AthkarStep> _currentSteps = [];
  AthkarRoutine? _currentRoutine;

  bool _isLoading = false;
  String? _errorMessage;
  
  // Getters
  List<AthkarCategory> get categories => _categories;
  List<AthkarRoutine> get routines => _routines;
  List<UserProgress> get userProgress => _userProgress;
  List<AthkarStep> get currentSteps => _currentSteps;
  AthkarRoutine? get currentRoutine => _currentRoutine;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Favorite routines
  List<AthkarRoutine> get favoriteRoutines => 
      _routines.where((routine) => routine.isFavorite).toList();

  // Recent routines (placeholder - would be based on user activity)
  List<AthkarRoutine> get recentRoutines => 
      _routines.take(5).toList();

  AthkarProvider() {
    _initializeData();
  }

  Future<void> _initializeData() async {
    await loadCategories();
    await loadRoutines();
  }

  // Categories
  Future<void> loadCategories() async {
    try {
      _setLoading(true);
      _categories = await _repository.getAllCategories();
    } catch (e, stackTrace) {
      ErrorHandler.handleError(e, stackTrace, context: 'loadCategories');
      _setError('Failed to load categories: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> addCategory(AthkarCategory category) async {
    try {
      _setLoading(true);
      await _repository.insertCategory(category);
      await loadCategories();
    } catch (e) {
      _setError('Failed to add category: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateCategory(AthkarCategory category) async {
    try {
      _setLoading(true);
      await _repository.updateCategory(category);
      await loadCategories();
    } catch (e) {
      _setError('Failed to update category: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteCategory(String categoryId) async {
    try {
      _setLoading(true);
      await _repository.deleteCategory(categoryId);
      await loadCategories();
      await loadRoutines(); // Refresh routines as they might be affected
    } catch (e) {
      _setError('Failed to delete category: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Routines
  Future<void> loadRoutines({String? userId}) async {
    try {
      _setLoading(true);
      _routines = await _repository.getAllRoutines(userId: userId);
    } catch (e) {
      _setError('Failed to load routines: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadRoutineById(String routineId) async {
    try {
      _setLoading(true);
      _currentRoutine = await _repository.getRoutineById(routineId);
      if (_currentRoutine != null) {
        _currentSteps = _currentRoutine!.steps ?? [];
      }
    } catch (e) {
      _setError('Failed to load routine: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<String?> addRoutine(AthkarRoutine routine) async {
    try {
      debugPrint('AthkarProvider.addRoutine called for: ${routine.title}');
      _setLoading(true);
      final routineId = await _repository.insertRoutine(routine);
      debugPrint('Repository returned routine ID: $routineId');
      await loadRoutines();
      debugPrint('Routines reloaded, total count: ${_routines.length}');
      return routineId;
    } catch (e, stackTrace) {
      debugPrint('Error in addRoutine: $e');
      debugPrint('Stack trace: $stackTrace');
      ErrorHandler.handleError(e, stackTrace, context: 'addRoutine');
      _setError('Failed to add routine: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateRoutine(AthkarRoutine routine) async {
    try {
      _setLoading(true);
      await _repository.updateRoutine(routine);
      await loadRoutines();
      
      // Update current routine if it's the same one
      if (_currentRoutine?.id == routine.id) {
        _currentRoutine = routine;
      }
    } catch (e) {
      _setError('Failed to update routine: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteRoutine(String routineId) async {
    try {
      _setLoading(true);
      await _repository.deleteRoutine(routineId);
      await loadRoutines();
      
      // Clear current routine if it was deleted
      if (_currentRoutine?.id == routineId) {
        _currentRoutine = null;
        _currentSteps = [];
      }
    } catch (e) {
      _setError('Failed to delete routine: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> toggleFavorite(String routineId) async {
    try {
      final routine = _routines.firstWhere((r) => r.id == routineId);
      final updatedRoutine = AthkarRoutine(
        id: routine.id,
        userId: routine.userId,
        categoryId: routine.categoryId,
        title: routine.title,
        description: routine.description,
        isPublic: routine.isPublic,
        isFavorite: !routine.isFavorite,
        totalSteps: routine.totalSteps,
        estimatedDuration: routine.estimatedDuration,
        createdAt: routine.createdAt,
        updatedAt: DateTime.now(),
        steps: routine.steps,
      );
      
      await updateRoutine(updatedRoutine);
    } catch (e) {
      _setError('Failed to toggle favorite: $e');
    }
  }

  // Steps
  Future<void> addStep(AthkarStep step) async {
    try {
      debugPrint('AthkarProvider.addStep called for routine: ${step.routineId}');
      _setLoading(true);
      await _repository.insertStep(step);
      debugPrint('Step inserted successfully');

      // Reload current routine if it matches
      if (_currentRoutine?.id == step.routineId) {
        await loadRoutineById(step.routineId);
      }
    } catch (e, stackTrace) {
      debugPrint('Error in addStep: $e');
      debugPrint('Stack trace: $stackTrace');
      ErrorHandler.handleError(e, stackTrace, context: 'addStep');
      _setError('Failed to add step: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateStep(AthkarStep step) async {
    try {
      _setLoading(true);
      await _repository.updateStep(step);
      
      // Reload current routine if it matches
      if (_currentRoutine?.id == step.routineId) {
        await loadRoutineById(step.routineId);
      }
    } catch (e) {
      _setError('Failed to update step: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteStep(String stepId) async {
    try {
      _setLoading(true);
      await _repository.deleteStep(stepId);
      
      // Remove from current steps if present
      _currentSteps.removeWhere((step) => step.id == stepId);
      
      // Reload current routine
      if (_currentRoutine != null) {
        await loadRoutineById(_currentRoutine!.id);
      }
    } catch (e) {
      _setError('Failed to delete step: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Utility methods
  List<AthkarRoutine> getRoutinesByCategory(String categoryId) {
    return _routines.where((routine) => routine.categoryId == categoryId).toList();
  }

  AthkarCategory? getCategoryById(String categoryId) {
    try {
      return _categories.firstWhere((category) => category.id == categoryId);
    } catch (e) {
      return null;
    }
  }

  void clearCurrentRoutine() {
    _currentRoutine = null;
    _currentSteps = [];
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // User Progress
  Future<void> loadUserProgress(String userId) async {
    try {
      _setLoading(true);
      _userProgress = await _repository.getUserProgress(userId);
    } catch (e) {
      _setError('Failed to load user progress: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> addUserProgress(UserProgress progress) async {
    try {
      _setLoading(true);
      await _repository.insertUserProgress(progress);
      await loadUserProgress(progress.userId);
    } catch (e) {
      _setError('Failed to add user progress: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Search functionality
  List<AthkarRoutine> _filteredRoutines = [];
  String _searchQuery = '';
  bool _isSearching = false;

  List<AthkarRoutine> get filteredRoutines => _isSearching ? _filteredRoutines : _routines;
  String get searchQuery => _searchQuery;
  bool get isSearching => _isSearching;

  void searchRoutines(String query) {
    _searchQuery = query.toLowerCase();
    _isSearching = query.isNotEmpty;

    if (_isSearching) {
      _filteredRoutines = _routines.where((routine) {
        return routine.title.toLowerCase().contains(_searchQuery) ||
               routine.description?.toLowerCase().contains(_searchQuery) == true ||
               _getStepsForRoutine(routine.id).any((step) =>
                 step.arabicText.toLowerCase().contains(_searchQuery) ||
                 step.transliteration?.toLowerCase().contains(_searchQuery) == true ||
                 step.translation?.toLowerCase().contains(_searchQuery) == true
               );
      }).toList();
    } else {
      _filteredRoutines = [];
    }

    notifyListeners();
  }

  void clearSearch() {
    _searchQuery = '';
    _isSearching = false;
    _filteredRoutines = [];
    notifyListeners();
  }

  List<AthkarStep> _getStepsForRoutine(String routineId) {
    return _currentSteps.where((step) => step.routineId == routineId).toList();
  }
}
