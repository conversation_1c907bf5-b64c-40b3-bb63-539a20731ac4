import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/athkar_models.dart';
import '../theme/app_theme.dart';

class AthkarCard extends StatelessWidget {
  final AthkarRoutine routine;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onToggleFavorite;
  final bool showActions;

  const AthkarCard({
    super.key,
    required this.routine,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onToggleFavorite,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: routine.categoryId != null
                ? AppTheme.getAthkarGradient(routine.categoryId!)
                : const LinearGradient(
                    colors: AppTheme.generalGradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.white.withValues(alpha: 0.9),
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: routine.categoryId != null
                            ? AppTheme.getCategoryColor(routine.categoryId!)
                            : AppTheme.primaryGreen,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getCategoryIcon(routine.categoryId),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            routine.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (routine.description != null)
                            Text(
                              routine.description!,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                    if (showActions && onToggleFavorite != null)
                      IconButton(
                        onPressed: onToggleFavorite,
                        icon: Icon(
                          routine.isFavorite
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: routine.isFavorite
                              ? Colors.red
                              : Colors.grey[400],
                          size: 20,
                        ),
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    _buildInfoChip(
                      icon: MdiIcons.formatListNumbered,
                      label: '${routine.totalSteps} steps',
                      color: Colors.blue,
                    ),
                    const SizedBox(width: 8),
                    if (routine.estimatedDuration != null)
                      _buildInfoChip(
                        icon: Icons.access_time,
                        label: '${routine.estimatedDuration}min',
                        color: Colors.orange,
                      ),
                  ],
                ),
                if (showActions && (onEdit != null || onDelete != null))
                  const SizedBox(height: 12),
                if (showActions && (onEdit != null || onDelete != null))
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (onEdit != null)
                        IconButton(
                          onPressed: onEdit,
                          icon: const Icon(Icons.edit),
                          iconSize: 18,
                          constraints: const BoxConstraints(),
                          padding: const EdgeInsets.all(4),
                        ),
                      if (onDelete != null)
                        IconButton(
                          onPressed: onDelete,
                          icon: const Icon(Icons.delete),
                          iconSize: 18,
                          constraints: const BoxConstraints(),
                          padding: const EdgeInsets.all(4),
                          color: Colors.red,
                        ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String? categoryId) {
    switch (categoryId) {
      case 'morning-athkar':
        return MdiIcons.weatherSunny;
      case 'evening-athkar':
        return MdiIcons.weatherSunset;
      case 'prayer-athkar':
        return MdiIcons.mosque;
      case 'sleep-athkar':
        return MdiIcons.sleep;
      case 'general-dhikr':
        return MdiIcons.counter;
      case 'dua-collection':
        return MdiIcons.handsPray;
      default:
        return MdiIcons.bookOpenPageVariant;
    }
  }
}
