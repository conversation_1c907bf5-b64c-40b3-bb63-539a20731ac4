# Athkar - Islamic Remembrance Mobile Application

A beautiful, cross-platform Flutter mobile application for Islamic remembrance (Athkar) and supplications (Duas) with offline-first architecture and optional cloud synchronization.

## 🌟 Features

### Core Functionality
- **Custom Athkar Routines**: Create, edit, and manage personalized athkar routines
- **Multi-step Workflows**: Each routine can contain multiple steps with individual counters
- **Offline-First**: Full functionality without internet connection
- **Cloud Sync**: Optional Supabase integration for cross-device synchronization
- **Beautiful UI**: Modern, Islamic-themed design with green and gold color scheme

### Main Tabs
1. **Home Tab**: Dashboard with recent activities, quick access, and progress summaries
2. **My Athkar Tab**: Personal athkar management with CRUD operations
3. **Library Tab**: Pre-built traditional Islamic athkar collections
4. **Settings Tab**: User preferences, notifications, and sync configuration

### Advanced Features
- **Smart Notifications**: Customizable reminder system with scheduling
- **Floating Counter**: Overlay dhikr counter that works over other apps (Android)
- **Progress Tracking**: Visual charts and statistics for spiritual progress
- **Multi-language Support**: Arabic text with transliteration and translations
- **Categories**: Organized athkar by time (morning, evening) and purpose

## 🛠 Technical Stack

- **Framework**: Flutter (Cross-platform)
- **Database**: SQLite (Local) + Supabase (Cloud)
- **State Management**: Provider
- **Authentication**: Supabase Auth
- **Notifications**: flutter_local_notifications
- **Charts**: fl_chart
- **Permissions**: permission_handler
- **Overlay**: flutter_overlay_window (Android)

## 📱 Installation

### Prerequisites
- Flutter SDK (3.0+)
- Android Studio / VS Code
- Android SDK (API 21+)

### Setup
1. Clone the repository:
```bash
git clone <repository-url>
cd athkar_app
```

2. Install dependencies:
```bash
flutter pub get
```

3. Configure Supabase (Optional):
   - Create a Supabase project
   - Update `lib/config/supabase_config.dart` with your credentials
   - Run the SQL schema from the config file

4. Build and run:
```bash
flutter run
```
