1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.islamicapps.athkar.athkar_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:13:5-67
15-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:13:22-64
16    <!-- Permissions for notifications -->
17    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
17-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:3:5-80
17-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:3:22-78
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:4:5-66
18-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:4:22-63
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:5:5-68
19-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:5:22-65
20    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Permissions for floating window overlay -->
20-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:6:5-76
20-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:6:22-74
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:9:5-78
21-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:9:22-75
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
22-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:10:5-77
22-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:10:22-74
23    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
23-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:14:5-79
23-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:14:22-76
24    <!--
25 Required to query activities that can process text, see:
26         https://developer.android.com/training/package-visibility and
27         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
28
29         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
30    -->
31    <queries>
31-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:73:5-78:15
32        <intent>
32-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:74:9-77:18
33            <action android:name="android.intent.action.PROCESS_TEXT" />
33-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:75:13-72
33-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:75:21-70
34
35            <data android:mimeType="text/plain" />
35-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:76:13-50
35-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:76:19-48
36        </intent>
37        <intent>
37-->[:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
38            <action android:name="android.intent.action.GET_CONTENT" />
38-->[:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
38-->[:file_picker] D:\projects\12july\athkar\athkar_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
39
40            <data android:mimeType="*/*" />
40-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:76:13-50
40-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:76:19-48
41        </intent>
42        <intent>
42-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:13:9-15:18
43            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
43-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:13-91
43-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:21-88
44        </intent>
45        <intent>
45-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:16:9-18:18
46            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
46-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:13-116
46-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:21-113
47        </intent>
48    </queries>
49
50    <uses-feature android:name="android.hardware.camera.any" />
50-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-64
50-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:19-61
51
52    <uses-permission android:name="android.permission.CAMERA" />
52-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-65
52-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-62
53    <uses-permission android:name="android.permission.RECORD_AUDIO" />
53-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
53-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-68
54    <uses-permission
54-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-13:38
55        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
55-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-65
56        android:maxSdkVersion="28" />
56-->[:camera_android_camerax] D:\projects\12july\athkar\athkar_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-35
57    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
58    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
58-->[:network_info_plus] D:\projects\12july\athkar\athkar_app\build\network_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
58-->[:network_info_plus] D:\projects\12july\athkar\athkar_app\build\network_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
59    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
59-->[:local_auth_android] D:\projects\12july\athkar\athkar_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-72
59-->[:local_auth_android] D:\projects\12july\athkar\athkar_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-69
60    <uses-permission android:name="com.android.vending.BILLING" /> <!-- suppress DeprecatedClassUsageInspection -->
60-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:5-67
60-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:22-64
61    <uses-permission android:name="android.permission.USE_FINGERPRINT" /> <!-- Required by older versions of Google Play services to create IID tokens -->
61-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
61-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
62    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
62-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
62-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
63    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
63-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
63-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
64    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
64-->[com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:27:5-79
64-->[com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\AndroidManifest.xml:27:22-76
65    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
65-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
65-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
66    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
66-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
66-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
67
68    <permission
68-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
69        android:name="com.islamicapps.athkar.athkar_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
70        android:protectionLevel="signature" />
70-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
71
72    <uses-permission android:name="com.islamicapps.athkar.athkar_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
72-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
72-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
73
74    <application
75        android:name="android.app.Application"
76        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
76-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
77        android:debuggable="true"
78        android:extractNativeLibs="true"
79        android:icon="@mipmap/ic_launcher"
80        android:label="Athkar - Islamic Remembrance" >
81        <activity
82            android:name="com.islamicapps.athkar.athkar_app.MainActivity"
83            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
84            android:exported="true"
85            android:hardwareAccelerated="true"
86            android:launchMode="singleTop"
87            android:taskAffinity=""
88            android:theme="@style/LaunchTheme"
89            android:windowSoftInputMode="adjustResize" >
90
91            <!--
92                 Specifies an Android theme to apply to this Activity as soon as
93                 the Android process has started. This theme is visible to the user
94                 while the Flutter UI initializes. After that, this theme continues
95                 to determine the Window background behind the Flutter UI.
96            -->
97            <meta-data
98                android:name="io.flutter.embedding.android.NormalTheme"
99                android:resource="@style/NormalTheme" />
100
101            <intent-filter>
102                <action android:name="android.intent.action.MAIN" />
103
104                <category android:name="android.intent.category.LAUNCHER" />
105            </intent-filter>
106        </activity>
107        <!--
108             Don't delete the meta-data below.
109             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
110        -->
111        <meta-data
112            android:name="flutterEmbedding"
113            android:value="2" />
114
115        <!-- Notification receiver for boot completed -->
116        <receiver
117            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
118            android:exported="false" >
119            <intent-filter>
120                <action android:name="android.intent.action.BOOT_COMPLETED" />
120-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
120-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
121                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
122                <action android:name="android.intent.action.PACKAGE_REPLACED" />
123
124                <data android:scheme="package" />
124-->D:\projects\12july\athkar\athkar_app\android\app\src\main\AndroidManifest.xml:76:13-50
125            </intent-filter>
126        </receiver>
127
128        <!-- Notification receiver -->
129        <receiver
130            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver"
131            android:exported="false" />
132
133        <!-- Floating window service -->
134        <service
135            android:name="flutter.overlay.window.OverlayService"
136            android:exported="false" />
137        <service
137-->[:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:19
138            android:name="com.google.firebase.components.ComponentDiscoveryService"
138-->[:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:18-89
139            android:directBootAware="true"
139-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
140            android:exported="false" >
140-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:56:13-37
141            <meta-data
141-->[:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:85
142                android:name="com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar"
142-->[:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-128
143                android:value="com.google.firebase.components.ComponentRegistrar" />
143-->[:firebase_analytics] D:\projects\12july\athkar\athkar_app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-82
144            <meta-data
144-->[:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
145                android:name="com.google.firebase.components:io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar"
145-->[:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-130
146                android:value="com.google.firebase.components.ComponentRegistrar" />
146-->[:firebase_crashlytics] D:\projects\12july\athkar\athkar_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
147            <meta-data
147-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
148                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
148-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
149                android:value="com.google.firebase.components.ComponentRegistrar" />
149-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
150            <meta-data
150-->[:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
151                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
151-->[:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
152                android:value="com.google.firebase.components.ComponentRegistrar" />
152-->[:firebase_core] D:\projects\12july\athkar\athkar_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
153            <meta-data
153-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
154                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
154-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
155                android:value="com.google.firebase.components.ComponentRegistrar" />
155-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
156            <meta-data
156-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
157                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
157-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
158                android:value="com.google.firebase.components.ComponentRegistrar" />
158-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
159            <meta-data
159-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
160                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
160-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
161                android:value="com.google.firebase.components.ComponentRegistrar" />
161-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
162            <meta-data
162-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:18:13-20:85
163                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
163-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:19:17-115
164                android:value="com.google.firebase.components.ComponentRegistrar" />
164-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:20:17-82
165            <meta-data
165-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
166                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
166-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
167                android:value="com.google.firebase.components.ComponentRegistrar" />
167-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
168            <meta-data
168-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
169                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
169-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
170                android:value="com.google.firebase.components.ComponentRegistrar" />
170-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
171            <meta-data
171-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
172                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
172-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
173                android:value="com.google.firebase.components.ComponentRegistrar" />
173-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
174            <meta-data
174-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
175                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
175-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
176                android:value="com.google.firebase.components.ComponentRegistrar" />
176-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
177            <meta-data
177-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
178                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
178-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
179                android:value="com.google.firebase.components.ComponentRegistrar" />
179-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
180            <meta-data
180-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
181                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
181-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
182                android:value="com.google.firebase.components.ComponentRegistrar" />
182-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
183            <meta-data
183-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
184                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
184-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
185                android:value="com.google.firebase.components.ComponentRegistrar" />
185-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
186        </service>
187        <!--
188           Declares a provider which allows us to store files to share in
189           '.../caches/share_plus' and grant the receiving action access
190        -->
191        <provider
191-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
192            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
192-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
193            android:authorities="com.islamicapps.athkar.athkar_app.flutter.share_provider"
193-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
194            android:exported="false"
194-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
195            android:grantUriPermissions="true" >
195-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
196            <meta-data
196-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
197                android:name="android.support.FILE_PROVIDER_PATHS"
197-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
198                android:resource="@xml/flutter_share_file_paths" />
198-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
199        </provider>
200        <!--
201           This manifest declared broadcast receiver allows us to use an explicit
202           Intent when creating a PendingItent to be informed of the user's choice
203        -->
204        <receiver
204-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
205            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
205-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
206            android:exported="false" >
206-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
207            <intent-filter>
207-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
208                <action android:name="EXTRA_CHOSEN_COMPONENT" />
208-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
208-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
209            </intent-filter>
210        </receiver>
211
212        <service
212-->[:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
213            android:name="com.baseflow.geolocator.GeolocatorLocationService"
213-->[:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
214            android:enabled="true"
214-->[:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
215            android:exported="false"
215-->[:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
216            android:foregroundServiceType="location" />
216-->[:geolocator_android] D:\projects\12july\athkar\athkar_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
217
218        <provider
218-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
219            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
219-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
220            android:authorities="com.islamicapps.athkar.athkar_app.flutter.image_provider"
220-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
221            android:exported="false"
221-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
222            android:grantUriPermissions="true" >
222-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
223            <meta-data
223-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
224                android:name="android.support.FILE_PROVIDER_PATHS"
224-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
225                android:resource="@xml/flutter_image_picker_file_paths" />
225-->[:share_plus] D:\projects\12july\athkar\athkar_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
226        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
227        <service
227-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
228            android:name="com.google.android.gms.metadata.ModuleDependencies"
228-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
229            android:enabled="false"
229-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
230            android:exported="false" >
230-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
231            <intent-filter>
231-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
232                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
232-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
232-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
233            </intent-filter>
234
235            <meta-data
235-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
236                android:name="photopicker_activity:0:required"
236-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
237                android:value="" />
237-->[:image_picker_android] D:\projects\12july\athkar\athkar_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
238        </service>
239
240        <activity
240-->[:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
241            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
241-->[:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
242            android:exported="false"
242-->[:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
243            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
243-->[:url_launcher_android] D:\projects\12july\athkar\athkar_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
244
245        <service
245-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
246            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
246-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
247            android:exported="false"
247-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
248            android:permission="android.permission.BIND_JOB_SERVICE" />
248-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
249        <service
249-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
250            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
250-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
251            android:exported="false" >
251-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
252            <intent-filter>
252-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
253                <action android:name="com.google.firebase.MESSAGING_EVENT" />
253-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
253-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
254            </intent-filter>
255        </service>
256
257        <receiver
257-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
258            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
258-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
259            android:exported="true"
259-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
260            android:permission="com.google.android.c2dm.permission.SEND" >
260-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
261            <intent-filter>
261-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
262                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
262-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
262-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
263            </intent-filter>
264        </receiver>
265
266        <provider
266-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
267            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
267-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
268            android:authorities="com.islamicapps.athkar.athkar_app.flutterfirebasemessaginginitprovider"
268-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
269            android:exported="false"
269-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
270            android:initOrder="99" />
270-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
271
272        <service
272-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
273            android:name="androidx.camera.core.impl.MetadataHolderService"
273-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
274            android:enabled="false"
274-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
275            android:exported="false" >
275-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
276            <meta-data
276-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
277                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
277-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
278                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
278-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
279        </service>
280
281        <meta-data
281-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:22:9-24:37
282            android:name="com.google.android.play.billingclient.version"
282-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:23:13-73
283            android:value="7.1.1" />
283-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:24:13-34
284
285        <activity
285-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:26:9-30:75
286            android:name="com.android.billingclient.api.ProxyBillingActivity"
286-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:27:13-78
287            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
287-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:28:13-96
288            android:exported="false"
288-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:29:13-37
289            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
289-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:30:13-72
290        <activity
290-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:31:9-35:75
291            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
291-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:32:13-80
292            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
292-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:33:13-96
293            android:exported="false"
293-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:34:13-37
294            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
294-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\AndroidManifest.xml:35:13-72
295        <activity
295-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
296            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
296-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
297            android:excludeFromRecents="true"
297-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
298            android:exported="false"
298-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
299            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
299-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
300        <!--
301            Service handling Google Sign-In user revocation. For apps that do not integrate with
302            Google Sign-In, this service will never be started.
303        -->
304        <service
304-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
305            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
305-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
306            android:exported="true"
306-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
307            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
307-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
308            android:visibleToInstantApps="true" />
308-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
309
310        <receiver
310-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
311            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
311-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
312            android:exported="true"
312-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
313            android:permission="com.google.android.c2dm.permission.SEND" >
313-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
314            <intent-filter>
314-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
315                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
315-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
315-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
316            </intent-filter>
317
318            <meta-data
318-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
319                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
319-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
320                android:value="true" />
320-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
321        </receiver>
322        <!--
323             FirebaseMessagingService performs security checks at runtime,
324             but set to not exported to explicitly avoid allowing another app to call it.
325        -->
326        <service
326-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
327            android:name="com.google.firebase.messaging.FirebaseMessagingService"
327-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
328            android:directBootAware="true"
328-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
329            android:exported="false" >
329-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
330            <intent-filter android:priority="-500" >
330-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
331                <action android:name="com.google.firebase.MESSAGING_EVENT" />
331-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
331-->[:firebase_messaging] D:\projects\12july\athkar\athkar_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
332            </intent-filter>
333        </service>
334
335        <receiver
335-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
336            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
336-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
337            android:enabled="true"
337-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
338            android:exported="false" >
338-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
339        </receiver>
340
341        <service
341-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
342            android:name="com.google.android.gms.measurement.AppMeasurementService"
342-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
343            android:enabled="true"
343-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
344            android:exported="false" />
344-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
345        <service
345-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
346            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
346-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
347            android:enabled="true"
347-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
348            android:exported="false"
348-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
349            android:permission="android.permission.BIND_JOB_SERVICE" />
349-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
350
351        <activity
351-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
352            android:name="com.google.android.gms.common.api.GoogleApiActivity"
352-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
353            android:exported="false"
353-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
354            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
354-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
355
356        <service
356-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
357            android:name="com.google.firebase.sessions.SessionLifecycleService"
357-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
358            android:enabled="true"
358-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
359            android:exported="false" />
359-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
360
361        <provider
361-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
362            android:name="com.google.firebase.provider.FirebaseInitProvider"
362-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
363            android:authorities="com.islamicapps.athkar.athkar_app.firebaseinitprovider"
363-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
364            android:directBootAware="true"
364-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
365            android:exported="false"
365-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
366            android:initOrder="100" />
366-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
367        <provider
367-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
368            android:name="androidx.startup.InitializationProvider"
368-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
369            android:authorities="com.islamicapps.athkar.athkar_app.androidx-startup"
369-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
370            android:exported="false" >
370-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
371            <meta-data
371-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
372                android:name="androidx.work.WorkManagerInitializer"
372-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
373                android:value="androidx.startup" />
373-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
374            <meta-data
374-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
375                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
375-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
376                android:value="androidx.startup" />
376-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
377            <meta-data
377-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
378                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
378-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
379                android:value="androidx.startup" />
379-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
380        </provider>
381
382        <service
382-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
383            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
383-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
384            android:directBootAware="false"
384-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
385            android:enabled="@bool/enable_system_alarm_service_default"
385-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
386            android:exported="false" />
386-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
387        <service
387-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
388            android:name="androidx.work.impl.background.systemjob.SystemJobService"
388-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
389            android:directBootAware="false"
389-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
390            android:enabled="@bool/enable_system_job_service_default"
390-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
391            android:exported="true"
391-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
392            android:permission="android.permission.BIND_JOB_SERVICE" />
392-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
393        <service
393-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
394            android:name="androidx.work.impl.foreground.SystemForegroundService"
394-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
395            android:directBootAware="false"
395-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
396            android:enabled="@bool/enable_system_foreground_service_default"
396-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
397            android:exported="false" />
397-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
398
399        <receiver
399-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
400            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
400-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
401            android:directBootAware="false"
401-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
402            android:enabled="true"
402-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
403            android:exported="false" />
403-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
404        <receiver
404-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
405            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
405-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
406            android:directBootAware="false"
406-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
407            android:enabled="false"
407-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
408            android:exported="false" >
408-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
409            <intent-filter>
409-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
410                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
410-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
410-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
411                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
411-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
411-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
412            </intent-filter>
413        </receiver>
414        <receiver
414-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
415            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
415-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
416            android:directBootAware="false"
416-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
417            android:enabled="false"
417-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
418            android:exported="false" >
418-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
419            <intent-filter>
419-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
420                <action android:name="android.intent.action.BATTERY_OKAY" />
420-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
420-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
421                <action android:name="android.intent.action.BATTERY_LOW" />
421-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
421-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
422            </intent-filter>
423        </receiver>
424        <receiver
424-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
425            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
425-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
426            android:directBootAware="false"
426-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
427            android:enabled="false"
427-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
428            android:exported="false" >
428-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
429            <intent-filter>
429-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
430                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
430-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
430-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
431                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
431-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
431-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
432            </intent-filter>
433        </receiver>
434        <receiver
434-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
435            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
435-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
436            android:directBootAware="false"
436-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
437            android:enabled="false"
437-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
438            android:exported="false" >
438-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
439            <intent-filter>
439-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
440                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
440-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
440-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
441            </intent-filter>
442        </receiver>
443        <receiver
443-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
444            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
444-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
445            android:directBootAware="false"
445-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
446            android:enabled="false"
446-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
447            android:exported="false" >
447-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
448            <intent-filter>
448-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
449                <action android:name="android.intent.action.BOOT_COMPLETED" />
449-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
449-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
450                <action android:name="android.intent.action.TIME_SET" />
450-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
450-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
451                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
451-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
451-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
452            </intent-filter>
453        </receiver>
454        <receiver
454-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
455            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
455-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
456            android:directBootAware="false"
456-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
457            android:enabled="@bool/enable_system_alarm_service_default"
457-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
458            android:exported="false" >
458-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
459            <intent-filter>
459-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
460                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
460-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
460-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
461            </intent-filter>
462        </receiver>
463        <receiver
463-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
464            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
464-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
465            android:directBootAware="false"
465-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
466            android:enabled="true"
466-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
467            android:exported="true"
467-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
468            android:permission="android.permission.DUMP" >
468-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
469            <intent-filter>
469-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
470                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
470-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
470-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9afb31c75cdd321eb1d7dc2fef0c4e5a\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
471            </intent-filter>
472        </receiver>
473
474        <uses-library
474-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
475            android:name="androidx.window.extensions"
475-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
476            android:required="false" />
476-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
477        <uses-library
477-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
478            android:name="androidx.window.sidecar"
478-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
479            android:required="false" />
479-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
480        <uses-library
480-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
481            android:name="android.ext.adservices"
481-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
482            android:required="false" />
482-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
483
484        <meta-data
484-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
485            android:name="com.google.android.gms.version"
485-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
486            android:value="@integer/google_play_services_version" />
486-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
487
488        <service
488-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
489            android:name="androidx.room.MultiInstanceInvalidationService"
489-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
490            android:directBootAware="true"
490-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
491            android:exported="false" />
491-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
492
493        <receiver
493-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
494            android:name="androidx.profileinstaller.ProfileInstallReceiver"
494-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
495            android:directBootAware="false"
495-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
496            android:enabled="true"
496-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
497            android:exported="true"
497-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
498            android:permission="android.permission.DUMP" >
498-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
499            <intent-filter>
499-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
500                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
500-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
500-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
501            </intent-filter>
502            <intent-filter>
502-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
503                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
503-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
503-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
504            </intent-filter>
505            <intent-filter>
505-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
506                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
506-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
506-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
507            </intent-filter>
508            <intent-filter>
508-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
509                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
509-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
509-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
510            </intent-filter>
511        </receiver>
512
513        <service
513-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
514            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
514-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
515            android:exported="false" >
515-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
516            <meta-data
516-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
517                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
517-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
518                android:value="cct" />
518-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
519        </service>
520        <service
520-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
521            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
521-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
522            android:exported="false"
522-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
523            android:permission="android.permission.BIND_JOB_SERVICE" >
523-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
524        </service>
525
526        <receiver
526-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
527            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
527-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
528            android:exported="false" />
528-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
529    </application>
530
531</manifest>
