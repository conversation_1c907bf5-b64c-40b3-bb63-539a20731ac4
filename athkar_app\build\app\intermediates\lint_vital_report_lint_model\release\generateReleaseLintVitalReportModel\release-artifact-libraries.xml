<libraries>
  <library
      name="__local_aars__:D:\projects\12july\athkar\athkar_app\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
      jars="D:\projects\12july\athkar\athkar_app\build\app\intermediates\flutter\release\libs.jar"
      resolved="__local_aars__:D:\projects\12july\athkar\athkar_app\build\app\intermediates\flutter\release\libs.jar:unspecified"/>
  <library
      name=":@@:camera_android_camerax::release"
      project=":camera_android_camerax"/>
  <library
      name=":@@:firebase_analytics::release"
      project=":firebase_analytics"/>
  <library
      name=":@@:shared_preferences_android::release"
      project=":shared_preferences_android"/>
  <library
      name=":@@:webview_flutter_android::release"
      project=":webview_flutter_android"/>
  <library
      name=":@@:workmanager_android::release"
      project=":workmanager_android"/>
  <library
      name=":@@:local_auth_android::release"
      project=":local_auth_android"/>
  <library
      name=":@@:app_links::release"
      project=":app_links"/>
  <library
      name=":@@:app_settings::release"
      project=":app_settings"/>
  <library
      name=":@@:audioplayers_android::release"
      project=":audioplayers_android"/>
  <library
      name=":@@:battery_plus::release"
      project=":battery_plus"/>
  <library
      name=":@@:connectivity_plus::release"
      project=":connectivity_plus"/>
  <library
      name=":@@:device_info_plus::release"
      project=":device_info_plus"/>
  <library
      name=":@@:file_picker::release"
      project=":file_picker"/>
  <library
      name=":@@:firebase_messaging::release"
      project=":firebase_messaging"/>
  <library
      name=":@@:firebase_core::release"
      project=":firebase_core"/>
  <library
      name=":@@:firebase_crashlytics::release"
      project=":firebase_crashlytics"/>
  <library
      name=":@@:flutter_keyboard_visibility::release"
      project=":flutter_keyboard_visibility"/>
  <library
      name=":@@:flutter_local_notifications::release"
      project=":flutter_local_notifications"/>
  <library
      name=":@@:flutter_native_splash::release"
      project=":flutter_native_splash"/>
  <library
      name=":@@:flutter_overlay_window::release"
      project=":flutter_overlay_window"/>
  <library
      name=":@@:flutter_plugin_android_lifecycle::release"
      project=":flutter_plugin_android_lifecycle"/>
  <library
      name=":@@:flutter_secure_storage::release"
      project=":flutter_secure_storage"/>
  <library
      name=":@@:geolocator_android::release"
      project=":geolocator_android"/>
  <library
      name=":@@:google_sign_in_android::release"
      project=":google_sign_in_android"/>
  <library
      name=":@@:image_picker_android::release"
      project=":image_picker_android"/>
  <library
      name=":@@:in_app_purchase_android::release"
      project=":in_app_purchase_android"/>
  <library
      name=":@@:network_info_plus::release"
      project=":network_info_plus"/>
  <library
      name=":@@:package_info_plus::release"
      project=":package_info_plus"/>
  <library
      name=":@@:path_provider_android::release"
      project=":path_provider_android"/>
  <library
      name=":@@:permission_handler_android::release"
      project=":permission_handler_android"/>
  <library
      name=":@@:sensors_plus::release"
      project=":sensors_plus"/>
  <library
      name=":@@:share_plus::release"
      project=":share_plus"/>
  <library
      name=":@@:sign_in_with_apple::release"
      project=":sign_in_with_apple"/>
  <library
      name=":@@:sqflite_android::release"
      project=":sqflite_android"/>
  <library
      name=":@@:url_launcher_android::release"
      project=":url_launcher_android"/>
  <library
      name=":@@:vibration::release"
      project=":vibration"/>
  <library
      name=":@@:video_player_android::release"
      project=":video_player_android"/>
  <library
      name=":@@:wakelock_plus::release"
      project=":wakelock_plus"/>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\flutter_embedding_release\1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa\e49eb3292687e1b573c417f19ce5fb36ba995ecc\flutter_embedding_release-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa"/>
  <library
      name="androidx.biometric:biometric:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\jars\classes.jar"
      resolved="androidx.biometric:biometric:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.9.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\93eeca70efd8419049cd49df8af72af1\transformed\jetified-activity-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity:1.9.3"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\93eeca70efd8419049cd49df8af72af1\transformed\jetified-activity-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\faeaeacf92c1b16df86d680ce0e0019c\transformed\loader-1.1.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\faeaeacf92c1b16df86d680ce0e0019c\transformed\loader-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.7.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.7.0\85334205d65cca70ed0109c3acbd29e22a2d9cb1\lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\08d6944c906bcd30c9d42a63993176cf\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\08d6944c906bcd30c9d42a63993176cf\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\jars\classes.jar"
      resolved="androidx.window:window:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window-java:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\jars\classes.jar"
      resolved="androidx.window:window-java:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.2.0\34dbc21d203cc4d4d623ac572a21acd4ccd716af\collection-1.2.0.jar"
      resolved="androidx.collection:collection:1.2.0"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.22\4dabb8248310d833bb6a8b516024a91fd3d275c\kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.1.0\85f8b81009cda5890e54ba67d64b5e599c645020\kotlin-stdlib-2.1.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.1.0"/>
  <library
      name="io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\armeabi_v7a_release\1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa\4a1622e4221ab8ec37d49ea9525f6bac5bffb95e\armeabi_v7a_release-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar"
      resolved="io.flutter:armeabi_v7a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa"/>
  <library
      name="io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\arm64_v8a_release\1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa\93179db9fcde85c97d8080f6eba90a3a219e1d7\arm64_v8a_release-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar"
      resolved="io.flutter:arm64_v8a_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa"/>
  <library
      name="io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\x86_64_release\1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa\92310057aa1fe51d117a6f550f682f1994f14d8d\x86_64_release-1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa.jar"
      resolved="io.flutter:x86_64_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\jars\classes.jar"
      resolved="com.getkeepsafe.relinker:relinker:1.4.5"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-video:1.5.0-beta01@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1908db6613957e4395aaf44167e7ffc8\transformed\jetified-camera-video-1.5.0-beta01\jars\classes.jar"
      resolved="androidx.camera:camera-video:1.5.0-beta01"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1908db6613957e4395aaf44167e7ffc8\transformed\jetified-camera-video-1.5.0-beta01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-lifecycle:1.5.0-beta01@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\85ddfbdedd6c8e7f8f223d7a66a912f2\transformed\jetified-camera-lifecycle-1.5.0-beta01\jars\classes.jar"
      resolved="androidx.camera:camera-lifecycle:1.5.0-beta01"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\85ddfbdedd6c8e7f8f223d7a66a912f2\transformed\jetified-camera-lifecycle-1.5.0-beta01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-camera2:1.5.0-beta01@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01\jars\classes.jar"
      resolved="androidx.camera:camera-camera2:1.5.0-beta01"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a4864d31f5ad80016316091c0dcb3e17\transformed\jetified-camera-camera2-1.5.0-beta01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-core:1.5.0-beta01@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\aa901e4755a3670193aa5d35ba930f95\transformed\jetified-camera-core-1.5.0-beta01\jars\classes.jar"
      resolved="androidx.camera:camera-core:1.5.0-beta01"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\aa901e4755a3670193aa5d35ba930f95\transformed\jetified-camera-core-1.5.0-beta01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f84db7003533a22de0405c5251ecb704\transformed\media-1.1.0\jars\classes.jar"
      resolved="androidx.media:media:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f84db7003533a22de0405c5251ecb704\transformed\media-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.billingclient:billing:7.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1\jars\classes.jar"
      resolved="com.android.billingclient:billing:7.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\cfe1337d46a5f6de2b589ae5d64f5126\transformed\jetified-billing-7.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-location:21.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1f7a922323fd2e7e330d6c2a2b6d1d4b\transformed\jetified-play-services-location-21.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-location:21.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1f7a922323fd2e7e330d6c2a2b6d1d4b\transformed\jetified-play-services-location-21.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:24.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\34d42fe4c975f0d33f3375454392120e\transformed\jetified-firebase-messaging-24.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\14c47a061c0f4340fe3cb3b4b456289f\transformed\jetified-play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\14c47a061c0f4340fe3cb3b4b456289f\transformed\jetified-play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd3d9e1ce1f4362a391d5da65b7fedd\transformed\jetified-play-services-auth-base-18.0.10\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.10"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd3d9e1ce1f4362a391d5da65b7fedd\transformed\jetified-play-services-auth-base-18.0.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-analytics:22.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\de09d56ede5c875ebbc0232053614904\transformed\jetified-firebase-analytics-22.5.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-analytics:22.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\de09d56ede5c875ebbc0232053614904\transformed\jetified-firebase-analytics-22.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement:22.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement:22.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8233ea89672697f3876f24affa9cbe08\transformed\jetified-play-services-measurement-22.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk:22.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a8fbf22bcb175ef393e26705bdeff75e\transformed\jetified-play-services-measurement-sdk-22.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk:22.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a8fbf22bcb175ef393e26705bdeff75e\transformed\jetified-play-services-measurement-sdk-22.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-impl:22.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-impl:22.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c534b28ad3ba39c2f9b5b46c50d64cb9\transformed\jetified-play-services-measurement-impl-22.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b83b8b00b8346c9e7414a1f1298f055d\transformed\preference-1.2.1\jars\classes.jar"
      resolved="androidx.preference:preference:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b83b8b00b8346c9e7414a1f1298f055d\transformed\preference-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a295c1332cd792405fffabf7b4bbac54\transformed\appcompat-1.2.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a295c1332cd792405fffabf7b4bbac54\transformed\appcompat-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\972419750b36e9fbf2d0c26a45927d82\transformed\jetified-fragment-ktx-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\972419750b36e9fbf2d0c26a45927d82\transformed\jetified-fragment-ktx-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-crashlytics:19.4.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4\jars\classes.jar"
      resolved="com.google.firebase:firebase-crashlytics:19.4.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbeb1ebf3f69d5188a8a974b11ed975\transformed\jetified-firebase-crashlytics-19.4.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-api:22.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-api:22.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\43080682432ccc0c31fbec6f5e1ca14a\transformed\jetified-play-services-measurement-api-22.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-sessions:2.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-sessions:2.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8e18204f9ec7b4d7b0ef040f4bf3b4e7\transformed\jetified-firebase-sessions-2.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e36da2f60198548da2dac483ab601381\transformed\jetified-firebase-installations-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\eb70f75a7acc5c45772a8855e98a189b\transformed\jetified-firebase-installations-interop-17.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\eb70f75a7acc5c45772a8855e98a189b\transformed\jetified-firebase-installations-interop-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\244ebfbd542d32fc159ad1c2e863258a\transformed\jetified-firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\244ebfbd542d32fc159ad1c2e863258a\transformed\jetified-firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8402a43808227de1a42b20b4957f5701\transformed\jetified-play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\2816110f0ece8bbb20566f9b1472d7e7\transformed\work-runtime-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\500a0c9cb71e092f235465c1951cf452\transformed\jetified-play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\500a0c9cb71e092f235465c1951cf452\transformed\jetified-play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\79275990ee9dddfd68bc7c9d7157e0cd\transformed\recyclerview-1.0.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\79275990ee9dddfd68bc7c9d7157e0cd\transformed\recyclerview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.9.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\32f4a2813ac7c771e056d42a720055af\transformed\jetified-activity-ktx-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.9.3"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\32f4a2813ac7c771e056d42a720055af\transformed\jetified-activity-ktx-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c244a6ce50b3288fe79d3f6ae212397f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c244a6ce50b3288fe79d3f6ae212397f\transformed\jetified-lifecycle-runtime-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\306016bcb4195b3238dbb4d76cafb64c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\306016bcb4195b3238dbb4d76cafb64c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7d813ca98b23cd30a6261e035c9b7933\transformed\jetified-lifecycle-service-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7d813ca98b23cd30a6261e035c9b7933\transformed\jetified-lifecycle-service-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e72f610bb8a20735f78a04c908b9b793\transformed\lifecycle-livedata-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e72f610bb8a20735f78a04c908b9b793\transformed\lifecycle-livedata-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\425b3275685a974b685af27ff4ed6b1d\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\425b3275685a974b685af27ff4ed6b1d\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7ec0f7adadd1101dbfaed7c3614b7840\transformed\jetified-room-ktx-2.5.0\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7ec0f7adadd1101dbfaed7c3614b7840\transformed\jetified-room-ktx-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f71e40716bc29995f4cada24da499d83\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f71e40716bc29995f4cada24da499d83\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.1.0\b4c245baf36d1a9e7defaf3be84f7a2ad4e1c797\concurrent-futures-ktx-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.1.0"/>
  <library
      name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-external-protobuf\1.1.3\ddd0a2d64e3c928359c993d1291e535d5d7fc9a3\datastore-preferences-external-protobuf-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-external-protobuf:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-proto\1.1.3\6d7430ed8d2b5f2b8675dad8d196ba5dd710921b\datastore-preferences-proto-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-proto:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-core-jvm\1.1.3\fb991f11389ccf2a5d5d4c99783ff958bc400\datastore-preferences-core-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-core-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-core-okio-jvm\1.1.3\ce08f132812044a9778547b299fd812e34dbd602\datastore-core-okio-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-core-okio-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\31eccd218f5b1fd8272959453f411784\transformed\jetified-datastore-core-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-core-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\31eccd218f5b1fd8272959453f411784\transformed\jetified-datastore-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7535a935f9e65beb6c79d36312378a64\transformed\jetified-datastore-preferences-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-preferences-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7535a935f9e65beb6c79d36312378a64\transformed\jetified-datastore-preferences-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\479b3bf32f00901a230d7d79262001b9\transformed\jetified-datastore-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\479b3bf32f00901a230d7d79262001b9\transformed\jetified-datastore-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\99bb3b712005fd2917858cd813cd885d\transformed\jetified-ads-adservices-java-1.1.0-beta11\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\99bb3b712005fd2917858cd813cd885d\transformed\jetified-ads-adservices-java-1.1.0-beta11"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a1a24f4bb2c37cae8017775dcf0c1a7d\transformed\jetified-ads-adservices-1.1.0-beta11"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.7.3\7087d47913cfb0062c9909dacbfc78fe44c5ecff\kotlinx-coroutines-play-services-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7795f05fbf8283409257f8d4d50c7a58\transformed\jetified-play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7795f05fbf8283409257f8d4d50c7a58\transformed\jetified-play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:20.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\526f75cc9f188c5990293f2bea0a07f1\transformed\jetified-firebase-measurement-connector-20.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:20.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\526f75cc9f188c5990293f2bea0a07f1\transformed\jetified-firebase-measurement-connector-20.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:22.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:22.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\70bc8784d05e62c347fa6d2289a79f9d\transformed\jetified-play-services-measurement-sdk-api-22.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:22.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\03c47e1b6cff250dae244f6621ea4a52\transformed\jetified-play-services-measurement-base-22.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:22.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\03c47e1b6cff250dae244f6621ea4a52\transformed\jetified-play-services-measurement-base-22.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7b33c4ac072486c90a47d13cee761d9b\transformed\jetified-play-services-basement-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-extractor:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\83912700adcc0cb17e29b0477e0a9782\transformed\jetified-media3-extractor-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-extractor:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\83912700adcc0cb17e29b0477e0a9782\transformed\jetified-media3-extractor-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-container:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3d4a68d34c8d0707a882a3ad9413a7d5\transformed\jetified-media3-container-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-container:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3d4a68d34c8d0707a882a3ad9413a7d5\transformed\jetified-media3-container-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-datasource:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c231ba1d521ae8b6b7437abf156b2096\transformed\jetified-media3-datasource-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-datasource:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c231ba1d521ae8b6b7437abf156b2096\transformed\jetified-media3-datasource-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-decoder:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0fa9e73b263a4d4afdc522127c7d12c6\transformed\jetified-media3-decoder-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-decoder:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0fa9e73b263a4d4afdc522127c7d12c6\transformed\jetified-media3-decoder-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-database:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a235e50dcfe56df509b4e045938a6261\transformed\jetified-media3-database-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-database:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a235e50dcfe56df509b4e045938a6261\transformed\jetified-media3-database-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-common:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3f2e728699f00eba298007738e88a2\transformed\jetified-media3-common-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-common:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3f2e728699f00eba298007738e88a2\transformed\jetified-media3-common-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-hls:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c4e3c52189f16aa8ca269e158ba45254\transformed\jetified-media3-exoplayer-hls-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-hls:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c4e3c52189f16aa8ca269e158ba45254\transformed\jetified-media3-exoplayer-hls-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-dash:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\123aa427351ecf51faaf5e936235c4a4\transformed\jetified-media3-exoplayer-dash-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-dash:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\123aa427351ecf51faaf5e936235c4a4\transformed\jetified-media3-exoplayer-dash-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-rtsp:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\553b9a3de3dbb119c5d26ae33e3e2242\transformed\jetified-media3-exoplayer-rtsp-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-rtsp:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\553b9a3de3dbb119c5d26ae33e3e2242\transformed\jetified-media3-exoplayer-rtsp-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1bb6847dc23bab7c067fa1b5dd94da7e\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1bb6847dc23bab7c067fa1b5dd94da7e\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\66c3f8d759689e7c8bf8d566a47d4905\transformed\jetified-media3-exoplayer-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\66c3f8d759689e7c8bf8d566a47d4905\transformed\jetified-media3-exoplayer-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.webkit:webkit:1.12.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\6d1284f70efaea5e2f7428ac2d9ae231\transformed\webkit-1.12.1\jars\classes.jar"
      resolved="androidx.webkit:webkit:1.12.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\6d1284f70efaea5e2f7428ac2d9ae231\transformed\webkit-1.12.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\185f2479ab24942c0bba65b9ff947d79\transformed\jetified-appcompat-resources-1.2.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\185f2479ab24942c0bba65b9ff947d79\transformed\jetified-appcompat-resources-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f87704cc6ac259b753f491455f413615\transformed\transition-1.4.1\jars\classes.jar"
      resolved="androidx.transition:transition:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f87704cc6ac259b753f491455f413615\transformed\transition-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-runtime:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\2b9757e07d6885a4e2c46b4721fadc50\transformed\room-runtime-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7995343eeb8e0fb97f769a330ffeb5c4\transformed\jetified-tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7995343eeb8e0fb97f769a330ffeb5c4\transformed\jetified-tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7058885b6dd2982185c832c670598b5a\transformed\localbroadcastmanager-1.1.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7058885b6dd2982185c832c670598b5a\transformed\localbroadcastmanager-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.security:security-crypto:1.1.0-alpha06@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b87823aa9ee377cf4f75094088b20314\transformed\jetified-security-crypto-1.1.0-alpha06\jars\classes.jar"
      resolved="androidx.security:security-crypto:1.1.0-alpha06"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b87823aa9ee377cf4f75094088b20314\transformed\jetified-security-crypto-1.1.0-alpha06"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.crypto.tink:tink-android:1.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.crypto.tink\tink-android\1.9.0\4c3bb230542a11ad51492cd9913979466b56fa16\tink-android-1.9.0.jar"
      resolved="com.google.crypto.tink:tink-android:1.9.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\exifinterface-1.3.7\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.7"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\exifinterface-1.3.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\4759cb2b9879373320a8cc691959fe18\transformed\sqlite-framework-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\4759cb2b9879373320a8cc691959fe18\transformed\sqlite-framework-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\277474532995b9fd32fdd0e838dc6db6\transformed\jetified-firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\277474532995b9fd32fdd0e838dc6db6\transformed\jetified-firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-datatransport:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\13ad4925afbc1d8fa1a75e8e75c94be0\transformed\jetified-firebase-datatransport-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0b6157769b66214aeab68654cea8b14e\transformed\jetified-transport-backend-cct-3.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1e2de46f090720bd19bfd6e725e44a\transformed\jetified-transport-runtime-3.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-api:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\36ca2e486ebda362c3c4d416726daa9c\transformed\jetified-transport-api-3.2.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\36ca2e486ebda362c3c4d416726daa9c\transformed\jetified-transport-api-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-config-interop:16.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\6afb3321da3bdd543c39e1a76ead511d\transformed\jetified-firebase-config-interop-16.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-config-interop:16.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\6afb3321da3bdd543c39e1a76ead511d\transformed\jetified-firebase-config-interop-16.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c94026d29d9c0642fe4083bec5be2f24\transformed\jetified-firebase-encoders-json-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c94026d29d9c0642fe4083bec5be2f24\transformed\jetified-firebase-encoders-json-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.5.0\829a83fb92f1696a8a32f3beea884dfc87b2693\room-common-2.5.0.jar"
      resolved="androidx.room:room-common:2.5.0"/>
  <library
      name="androidx.sqlite:sqlite:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3c6ec98e94082ed41f89d97124b61597\transformed\sqlite-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3c6ec98e94082ed41f89d97124b61597\transformed\sqlite-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window.extensions.core:core:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\jars\classes.jar"
      resolved="androidx.window.extensions.core:core:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okio:okio-jvm:3.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.4.0\4e8bd78a52ab935ce383d0092646922154295e54\okio-jvm-3.4.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.4.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.22\de4a21d6560cadd035c69ba3af3ad1afecc95299\kotlin-parcelize-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.22\ee3bc0c3b55cb516ac92d6a093e1b939166b86a2\kotlin-android-extensions-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22"/>
  <library
      name="org.microg:safe-parcel:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\587570bc1d83a08ca6f63ae47663ef81\transformed\jetified-safe-parcel-1.7.0\jars\classes.jar"
      resolved="org.microg:safe-parcel:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\587570bc1d83a08ca6f63ae47663ef81\transformed\jetified-safe-parcel-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:guava:33.4.0-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\33.4.0-android\b19b82d0a09dd29efb86e51954558e5db34972e\guava-33.4.0-android.jar"
      resolved="com.google.guava:guava:33.4.0-android"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"/>
  <library
      name="com.google.auto.value:auto-value-annotations:1.6.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.auto.value\auto-value-annotations\1.6.3\b88c1bb7f149f6d2cc03898359283e57b08f39cc\auto-value-annotations-1.6.3.jar"
      resolved="com.google.auto.value:auto-value-annotations:1.6.3"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="com.google.guava:failureaccess:1.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.2\c4a06a64e650562f30b7bf9aaec1bfed43aca12b\failureaccess-1.0.2.jar"
      resolved="com.google.guava:failureaccess:1.0.2"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.checkerframework:checker-qual:3.43.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.43.0\9425eee39e56b116d2b998b7c2cebcbd11a3c98b\checker-qual-3.43.0.jar"
      resolved="org.checkerframework:checker-qual:3.43.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.36.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.36.0\227d4d4957ccc3dc5761bd897e3a0ee587e750a7\error_prone_annotations-2.36.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.36.0"/>
  <library
      name="com.google.j2objc:j2objc-annotations:3.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\3.0.0\7399e65dd7e9ff3404f4535b2f017093bdb134c7\j2objc-annotations-3.0.0.jar"
      resolved="com.google.j2objc:j2objc-annotations:3.0.0"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
</libraries>
