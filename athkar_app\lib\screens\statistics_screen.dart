import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../theme/app_theme.dart';
import '../providers/athkar_provider.dart';
import '../providers/tasbeeh_dua_provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/progress_chart.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    final authProvider = context.read<AuthProvider>();
    final userId = authProvider.user?.id;
    
    if (userId != null) {
      context.read<AthkarProvider>().loadUserProgress(userId);
      context.read<TasbeehDuaProvider>().loadTasbeehSessions(userId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Statistics'),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: Icon(MdiIcons.chartLine),
              text: 'Overview',
            ),
            Tab(
              icon: Icon(MdiIcons.bookOpenPageVariant),
              text: 'Athkar',
            ),
            Tab(
              icon: Icon(MdiIcons.counter),
              text: 'Tasbeeh',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildAthkarTab(),
          _buildTasbeehTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return Consumer3<AuthProvider, AthkarProvider, TasbeehDuaProvider>(
      builder: (context, authProvider, athkarProvider, tasbeehProvider, child) {
        final userId = authProvider.user?.id;
        if (userId == null) {
          return const Center(child: Text('Please log in to view statistics'));
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeCard(authProvider.user?.userMetadata?['full_name'] ?? 'User'),
              const SizedBox(height: 16),
              _buildQuickStats(athkarProvider, tasbeehProvider, userId),
              const SizedBox(height: 16),
              _buildWeeklyProgress(athkarProvider),
              const SizedBox(height: 16),
              _buildRecentActivity(athkarProvider, tasbeehProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAthkarTab() {
    return Consumer<AthkarProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildAthkarStats(provider),
              const SizedBox(height: 16),
              _buildRoutineProgress(provider),
              const SizedBox(height: 16),
              _buildCategoryBreakdown(provider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTasbeehTab() {
    return Consumer2<AuthProvider, TasbeehDuaProvider>(
      builder: (context, authProvider, provider, child) {
        final userId = authProvider.user?.id;
        if (userId == null) {
          return const Center(child: Text('Please log in to view statistics'));
        }

        final stats = provider.getTasbeehStats(userId);
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTasbeehStats(stats),
              const SizedBox(height: 16),
              _buildTasbeehSessions(provider, userId),
            ],
          ),
        );
      },
    );
  }

  Widget _buildWelcomeCard(String userName) {
    final hour = DateTime.now().hour;
    String greeting;
    IconData icon;
    
    if (hour < 12) {
      greeting = 'Good Morning';
      icon = Icons.wb_sunny;
    } else if (hour < 18) {
      greeting = 'Good Afternoon';
      icon = Icons.wb_sunny_outlined;
    } else {
      greeting = 'Good Evening';
      icon = Icons.nights_stay;
    }

    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryGreen,
              AppTheme.primaryGreen.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.white, size: 28),
                const SizedBox(width: 12),
                Text(
                  greeting,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              userName,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'May Allah bless your spiritual journey',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.9),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(AthkarProvider athkarProvider, TasbeehDuaProvider tasbeehProvider, String userId) {
    final tasbeehStats = tasbeehProvider.getTasbeehStats(userId);
    
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Routines Completed',
            '${athkarProvider.userProgress.length}',
            Icons.check_circle,
            AppTheme.primaryGreen,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Tasbeeh Count',
            '${tasbeehStats['totalCount'] ?? 0}',
            MdiIcons.counter,
            AppTheme.accentGold,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeeklyProgress(AthkarProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Weekly Progress',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: ProgressChart(
                data: provider.userProgress,
                showLegend: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity(AthkarProvider athkarProvider, TasbeehDuaProvider tasbeehProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Activity',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            // Recent athkar sessions
            ...athkarProvider.userProgress.take(3).map((progress) {
              return ListTile(
                leading: Icon(MdiIcons.bookOpenPageVariant, color: AppTheme.primaryGreen),
                title: Text('Completed Routine'),
                subtitle: Text('${progress.currentCount} repetitions completed'),
                trailing: Text(
                  _formatDate(progress.sessionDate),
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              );
            }),
            // Recent tasbeeh sessions
            ...tasbeehProvider.tasbeehSessions.take(2).map((session) {
              return ListTile(
                leading: Icon(MdiIcons.counter, color: AppTheme.accentGold),
                title: Text('Tasbeeh Session'),
                subtitle: Text('${session.currentCount} repetitions'),
                trailing: Text(
                  _formatDate(session.startTime),
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildAthkarStats(AthkarProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Athkar Statistics',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem('Total Routines', '${provider.routines.length}'),
                _buildStatItem('Completed', '${provider.userProgress.length}'),
                _buildStatItem('Favorites', '${provider.routines.where((r) => r.isFavorite).length}'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildRoutineProgress(AthkarProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Routine Progress',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...provider.routines.take(5).map((routine) {
              final progress = provider.userProgress
                  .where((p) => p.routineId == routine.id)
                  .length;
              return ListTile(
                title: Text(routine.title),
                subtitle: Text('${routine.totalSteps} steps'),
                trailing: Text('$progress times'),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryBreakdown(AthkarProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Category Breakdown',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...provider.categories.map((category) {
              final routineCount = provider.routines
                  .where((r) => r.categoryId == category.id)
                  .length;
              return ListTile(
                leading: Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _parseColor(category.color),
                    shape: BoxShape.circle,
                  ),
                ),
                title: Text(category.name),
                trailing: Text('$routineCount routines'),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildTasbeehStats(Map<String, int> stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Tasbeeh Statistics',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem('Total Sessions', '${stats['totalSessions'] ?? 0}'),
                _buildStatItem('Completed', '${stats['completedSessions'] ?? 0}'),
                _buildStatItem('Total Count', '${stats['totalCount'] ?? 0}'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTasbeehSessions(TasbeehDuaProvider provider, String userId) {
    final sessions = provider.getUserSessions(userId);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Tasbeeh Sessions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...sessions.take(10).map((session) {
              return ListTile(
                leading: Icon(
                  session.isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
                  color: session.isCompleted ? AppTheme.primaryGreen : Colors.grey,
                ),
                title: Text('${session.currentCount} / ${session.targetCount}'),
                subtitle: Text(_formatDate(session.startTime)),
                trailing: session.isCompleted
                    ? const Icon(Icons.star, color: AppTheme.accentGold)
                    : null,
              );
            }),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Color _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) {
      return AppTheme.primaryGreen;
    }

    try {
      // Remove # if present
      String hexColor = colorString.replaceAll('#', '');

      // Add alpha if not present
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }

      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      return AppTheme.primaryGreen;
    }
  }
}
