import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/language_service.dart';
import '../services/advanced_search_service.dart';
import '../models/search_models.dart';
import '../theme/app_theme.dart';
import '../widgets/search/search_results_widget.dart';
import '../widgets/search/search_filters_widget.dart';

class AdvancedSearchScreen extends StatefulWidget {
  final String? initialQuery;
  final String searchType; // 'hadith', 'quran', 'athkar', 'all'

  const AdvancedSearchScreen({
    super.key,
    this.initialQuery,
    this.searchType = 'all',
  });

  @override
  State<AdvancedSearchScreen> createState() => _AdvancedSearchScreenState();
}

class _AdvancedSearchScreenState extends State<AdvancedSearchScreen>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  
  late TabController _tabController;
  
  bool _isLoading = false;
  bool _showFilters = false;
  
  List<HadithSearchResult> _hadithResults = [];
  List<QuranSearchResult> _quranResults = [];
  List<AthkarSearchResult> _athkarResults = [];
  List<String> _searchSuggestions = [];
  
  SearchFilters _filters = const SearchFilters();
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    if (widget.initialQuery != null) {
      _searchController.text = widget.initialQuery!;
      _performSearch();
    }
    
    // Set initial tab based on search type
    switch (widget.searchType) {
      case 'hadith':
        _tabController.index = 1;
        break;
      case 'quran':
        _tabController.index = 2;
        break;
      case 'athkar':
        _tabController.index = 3;
        break;
      default:
        _tabController.index = 0;
    }
    
    _searchController.addListener(_onSearchTextChanged);
    AdvancedSearchService.initialize();
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _tabController.dispose();
    super.dispose();
  }
  
  void _onSearchTextChanged() {
    final query = _searchController.text.trim();
    if (query.length >= 2) {
      _getSuggestions(query);
    } else {
      setState(() {
        _searchSuggestions.clear();
      });
    }
  }
  
  Future<void> _getSuggestions(String query) async {
    try {
      final suggestions = await AdvancedSearchService.getSearchSuggestions(query);
      if (mounted) {
        setState(() {
          _searchSuggestions = suggestions;
        });
      }
    } catch (e) {
      debugPrint('Error getting suggestions: $e');
    }
  }
  
  Future<void> _performSearch() async {
    final query = _searchController.text.trim();
    if (query.isEmpty) return;
    
    setState(() {
      _isLoading = true;
      _searchSuggestions.clear();
    });
    
    try {
      // Save search to history
      await AdvancedSearchService.saveSearchHistory(query);
      
      // Perform searches based on current tab
      final currentTab = _tabController.index;
      
      if (currentTab == 0 || currentTab == 1) {
        // All or Hadith tab
        final hadithResults = await AdvancedSearchService.searchHadith(
          query: query,
          collections: _filters.collections,
          narrator: _filters.narrator,
          limit: 50,
        );
        
        if (mounted) {
          setState(() {
            _hadithResults = hadithResults;
          });
        }
      }
      
      if (currentTab == 0 || currentTab == 2) {
        // All or Quran tab
        final quranResults = await AdvancedSearchService.searchQuran(
          query: query,
          surahs: _filters.surahs,
          includeTranslation: _filters.includeTranslation,
          includeTafseer: _filters.includeTafseer,
          limit: 50,
        );
        
        if (mounted) {
          setState(() {
            _quranResults = quranResults;
          });
        }
      }
      
      if (currentTab == 0 || currentTab == 3) {
        // All or Athkar tab
        final athkarResults = await AdvancedSearchService.searchAthkar(
          query: query,
          categories: _filters.categories,
          limit: 50,
        );
        
        if (mounted) {
          setState(() {
            _athkarResults = athkarResults;
          });
        }
      }
      
    } catch (e) {
      debugPrint('Error performing search: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في البحث: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _hadithResults.clear();
      _quranResults.clear();
      _athkarResults.clear();
      _searchSuggestions.clear();
    });
  }
  
  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;
    });
  }
  
  void _updateFilters(SearchFilters newFilters) {
    setState(() {
      _filters = newFilters;
    });
    
    // Re-perform search with new filters
    if (_searchController.text.trim().isNotEmpty) {
      _performSearch();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          languageService.isArabic ? 'البحث المتقدم' : 'Advanced Search',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryGreen,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: Icon(_showFilters ? Icons.filter_list_off : Icons.filter_list),
            onPressed: _toggleFilters,
            tooltip: languageService.isArabic ? 'المرشحات' : 'Filters',
          ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearSearch,
            tooltip: languageService.isArabic ? 'مسح' : 'Clear',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              text: languageService.isArabic ? 'الكل' : 'All',
              icon: const Icon(Icons.search, size: 20),
            ),
            Tab(
              text: languageService.isArabic ? 'الأحاديث' : 'Hadith',
              icon: const Icon(Icons.book, size: 20),
            ),
            Tab(
              text: languageService.isArabic ? 'القرآن' : 'Quran',
              icon: const Icon(Icons.menu_book, size: 20),
            ),
            Tab(
              text: languageService.isArabic ? 'الأذكار' : 'Athkar',
              icon: const Icon(Icons.favorite, size: 20),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: const BoxDecoration(
              color: AppTheme.primaryGreen,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                TextField(
                  controller: _searchController,
                  focusNode: _searchFocusNode,
                  textDirection: languageService.isArabic ? TextDirection.rtl : TextDirection.ltr,
                  decoration: InputDecoration(
                    hintText: languageService.isArabic 
                        ? 'ابحث في الأحاديث والقرآن والأذكار...'
                        : 'Search in Hadith, Quran, and Athkar...',
                    hintStyle: TextStyle(color: Colors.grey[600]),
                    prefixIcon: const Icon(Icons.search, color: AppTheme.primaryGreen),
                    suffixIcon: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
                            ),
                          )
                        : _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear, color: Colors.grey),
                                onPressed: _clearSearch,
                              )
                            : null,
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 15,
                    ),
                  ),
                  onSubmitted: (_) => _performSearch(),
                ),
                
                // Search Suggestions
                if (_searchSuggestions.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 5,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: _searchSuggestions.map((suggestion) {
                        return ListTile(
                          dense: true,
                          leading: const Icon(Icons.history, color: Colors.grey),
                          title: Text(
                            suggestion,
                            style: const TextStyle(fontSize: 14),
                          ),
                          onTap: () {
                            _searchController.text = suggestion;
                            _performSearch();
                          },
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // Filters Panel
          if (_showFilters)
            SearchFiltersWidget(
              filters: _filters,
              onFiltersChanged: _updateFilters,
            ),
          
          // Search Results
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // All Results Tab
                SearchResultsWidget(
                  hadithResults: _hadithResults,
                  quranResults: _quranResults,
                  athkarResults: _athkarResults,
                  isLoading: _isLoading,
                  searchType: 'all',
                ),
                
                // Hadith Results Tab
                SearchResultsWidget(
                  hadithResults: _hadithResults,
                  quranResults: const [],
                  athkarResults: const [],
                  isLoading: _isLoading,
                  searchType: 'hadith',
                ),
                
                // Quran Results Tab
                SearchResultsWidget(
                  hadithResults: const [],
                  quranResults: _quranResults,
                  athkarResults: const [],
                  isLoading: _isLoading,
                  searchType: 'quran',
                ),
                
                // Athkar Results Tab
                SearchResultsWidget(
                  hadithResults: const [],
                  quranResults: const [],
                  athkarResults: _athkarResults,
                  isLoading: _isLoading,
                  searchType: 'athkar',
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _performSearch,
        backgroundColor: AppTheme.primaryGreen,
        child: const Icon(Icons.search, color: Colors.white),
      ),
    );
  }
}
