import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/language_service.dart';
import '../../models/search_models.dart';
import '../../theme/app_theme.dart';
import '../../widgets/search/hadith_result_card.dart';
import '../../widgets/search/quran_result_card.dart';
import '../../widgets/search/athkar_result_card.dart';

class SearchResultsWidget extends StatelessWidget {
  final List<HadithSearchResult> hadithResults;
  final List<QuranSearchResult> quranResults;
  final List<AthkarSearchResult> athkarResults;
  final bool isLoading;
  final String searchType;

  const SearchResultsWidget({
    super.key,
    required this.hadithResults,
    required this.quranResults,
    required this.athkarResults,
    required this.isLoading,
    required this.searchType,
  });

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
            ),
            SizedBox(height: 16),
            Text(
              'جاري البحث...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    // Calculate total results
    final totalResults = hadithResults.length + quranResults.length + athkarResults.length;

    if (totalResults == 0) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              languageService.isArabic 
                  ? 'لم يتم العثور على نتائج'
                  : 'No results found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              languageService.isArabic 
                  ? 'جرب كلمات مختلفة أو تحقق من الإملاء'
                  : 'Try different keywords or check spelling',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return CustomScrollView(
      slivers: [
        // Results Summary
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.search,
                  color: AppTheme.primaryGreen,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  languageService.isArabic 
                      ? 'النتائج: $totalResults'
                      : 'Results: $totalResults',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryGreen,
                  ),
                ),
                const Spacer(),
                if (searchType == 'all') ...[
                  _buildResultTypeChip(
                    languageService.isArabic ? 'أحاديث' : 'Hadith',
                    hadithResults.length,
                    Icons.book,
                  ),
                  const SizedBox(width: 8),
                  _buildResultTypeChip(
                    languageService.isArabic ? 'قرآن' : 'Quran',
                    quranResults.length,
                    Icons.menu_book,
                  ),
                  const SizedBox(width: 8),
                  _buildResultTypeChip(
                    languageService.isArabic ? 'أذكار' : 'Athkar',
                    athkarResults.length,
                    Icons.favorite,
                  ),
                ],
              ],
            ),
          ),
        ),

        // Hadith Results
        if (hadithResults.isNotEmpty) ...[
          if (searchType == 'all')
            SliverToBoxAdapter(
              child: _buildSectionHeader(
                languageService.isArabic ? 'الأحاديث' : 'Hadith',
                hadithResults.length,
                Icons.book,
              ),
            ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  child: HadithResultCard(result: hadithResults[index]),
                );
              },
              childCount: hadithResults.length,
            ),
          ),
        ],

        // Quran Results
        if (quranResults.isNotEmpty) ...[
          if (searchType == 'all')
            SliverToBoxAdapter(
              child: _buildSectionHeader(
                languageService.isArabic ? 'القرآن الكريم' : 'Holy Quran',
                quranResults.length,
                Icons.menu_book,
              ),
            ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  child: QuranResultCard(result: quranResults[index]),
                );
              },
              childCount: quranResults.length,
            ),
          ),
        ],

        // Athkar Results
        if (athkarResults.isNotEmpty) ...[
          if (searchType == 'all')
            SliverToBoxAdapter(
              child: _buildSectionHeader(
                languageService.isArabic ? 'الأذكار' : 'Athkar',
                athkarResults.length,
                Icons.favorite,
              ),
            ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  child: AthkarResultCard(result: athkarResults[index]),
                );
              },
              childCount: athkarResults.length,
            ),
          ),
        ],

        // Bottom padding
        const SliverToBoxAdapter(
          child: SizedBox(height: 80),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title, int count, IconData icon) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryGreen.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryGreen,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppTheme.primaryGreen,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultTypeChip(String label, int count, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: count > 0 ? AppTheme.primaryGreen.withValues(alpha: 0.1) : Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: count > 0 ? AppTheme.primaryGreen : Colors.grey[400]!,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: count > 0 ? AppTheme.primaryGreen : Colors.grey[600],
          ),
          const SizedBox(width: 4),
          Text(
            '$count',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: count > 0 ? AppTheme.primaryGreen : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}

/// Empty state widget for when no results are found
class EmptySearchResults extends StatelessWidget {
  final String searchQuery;

  const EmptySearchResults({
    super.key,
    required this.searchQuery,
  });

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.search_off,
                size: 48,
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              languageService.isArabic 
                  ? 'لم يتم العثور على نتائج'
                  : 'No results found',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              languageService.isArabic 
                  ? 'لم نجد أي نتائج لـ "$searchQuery"'
                  : 'We couldn\'t find any results for "$searchQuery"',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    languageService.isArabic ? 'نصائح للبحث:' : 'Search tips:',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryGreen,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildSearchTip(
                    languageService.isArabic 
                        ? '• تحقق من الإملاء'
                        : '• Check your spelling',
                  ),
                  _buildSearchTip(
                    languageService.isArabic 
                        ? '• جرب كلمات مختلفة'
                        : '• Try different keywords',
                  ),
                  _buildSearchTip(
                    languageService.isArabic 
                        ? '• استخدم كلمات أقل'
                        : '• Use fewer words',
                  ),
                  _buildSearchTip(
                    languageService.isArabic 
                        ? '• جرب البحث بالعربية أو الإنجليزية'
                        : '• Try searching in Arabic or English',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchTip(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        tip,
        style: TextStyle(
          fontSize: 13,
          color: Colors.grey[700],
        ),
      ),
    );
  }
}
