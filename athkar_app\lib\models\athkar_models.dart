import 'package:json_annotation/json_annotation.dart';

part 'athkar_models.g.dart';

@JsonSerializable()
class AthkarCategory {
  final String id;
  final String name;
  final String? description;
  final String? icon;
  final String? color;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;

  AthkarCategory({
    required this.id,
    required this.name,
    this.description,
    this.icon,
    this.color,
    this.isDefault = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AthkarCategory.fromJson(Map<String, dynamic> json) =>
      _$AthkarCategoryFromJson(json);
  Map<String, dynamic> toJson() => _$AthkarCategoryToJson(this);
}

@JsonSerializable()
class AthkarRoutine {
  final String id;
  final String? userId;
  final String? categoryId;
  final String title;
  final String? description;
  final bool isPublic;
  final bool isFavorite;
  final int totalSteps;
  final int? estimatedDuration; // in minutes
  final String? colorHex; // Color in hex format
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<AthkarStep>? steps;

  AthkarRoutine({
    required this.id,
    this.userId,
    this.categoryId,
    required this.title,
    this.description,
    this.isPublic = false,
    this.isFavorite = false,
    this.totalSteps = 0,
    this.estimatedDuration,
    this.colorHex,
    required this.createdAt,
    required this.updatedAt,
    this.steps,
  });

  factory AthkarRoutine.fromJson(Map<String, dynamic> json) =>
      _$AthkarRoutineFromJson(json);
  Map<String, dynamic> toJson() => _$AthkarRoutineToJson(this);
}

@JsonSerializable()
class AthkarStep {
  final String id;
  final String routineId;
  final int stepOrder;
  final String arabicText;
  final String? transliteration;
  final String? translation;
  final int targetCount;
  final String? audioUrl;
  final DateTime createdAt;
  final DateTime updatedAt;

  AthkarStep({
    required this.id,
    required this.routineId,
    required this.stepOrder,
    required this.arabicText,
    this.transliteration,
    this.translation,
    this.targetCount = 1,
    this.audioUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AthkarStep.fromJson(Map<String, dynamic> json) =>
      _$AthkarStepFromJson(json);
  Map<String, dynamic> toJson() => _$AthkarStepToJson(this);
}

@JsonSerializable()
class UserProgress {
  final String id;
  final String userId;
  final String routineId;
  final String stepId;
  final int currentCount;
  final DateTime? completedAt;
  final DateTime sessionDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProgress({
    required this.id,
    required this.userId,
    required this.routineId,
    required this.stepId,
    this.currentCount = 0,
    this.completedAt,
    required this.sessionDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserProgress.fromJson(Map<String, dynamic> json) =>
      _$UserProgressFromJson(json);
  Map<String, dynamic> toJson() => _$UserProgressToJson(this);
}

@JsonSerializable()
class ReminderSetting {
  final String id;
  final String userId;
  final String routineId;
  final bool isEnabled;
  final List<String> reminderTimes; // Time strings in HH:mm format
  final List<int> daysOfWeek; // 0=Sunday, 1=Monday, etc.
  final String? notificationTitle;
  final String? notificationBody;
  final DateTime createdAt;
  final DateTime updatedAt;

  ReminderSetting({
    required this.id,
    required this.userId,
    required this.routineId,
    this.isEnabled = true,
    this.reminderTimes = const [],
    this.daysOfWeek = const [],
    this.notificationTitle,
    this.notificationBody,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ReminderSetting.fromJson(Map<String, dynamic> json) =>
      _$ReminderSettingFromJson(json);
  Map<String, dynamic> toJson() => _$ReminderSettingToJson(this);
}

@JsonSerializable()
class UserProfile {
  final String id;
  final String? username;
  final String? fullName;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProfile({
    required this.id,
    this.username,
    this.fullName,
    this.avatarUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);
}

// Enums for better type safety
enum AthkarCategoryType {
  morning,
  evening,
  prayer,
  sleep,
  general,
  dua,
  custom,
}

enum NotificationFrequency {
  daily,
  weekly,
  custom,
}

enum ProgressStatus {
  notStarted,
  inProgress,
  completed,
}

@JsonSerializable()
class TasbeehItem {
  final String id;
  final String arabicText;
  final String? transliteration;
  final String? translation;
  final String? category;
  final String? colorHex; // Color in hex format
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;

  TasbeehItem({
    required this.id,
    required this.arabicText,
    this.transliteration,
    this.translation,
    this.category,
    this.colorHex,
    this.isDefault = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TasbeehItem.fromJson(Map<String, dynamic> json) =>
      _$TasbeehItemFromJson(json);
  Map<String, dynamic> toJson() => _$TasbeehItemToJson(this);
}

@JsonSerializable()
class TasbeehSession {
  final String id;
  final String userId;
  final String tasbeehId;
  final int targetCount;
  final int currentCount;
  final DateTime startTime;
  final DateTime? endTime;
  final bool isCompleted;
  final DateTime createdAt;

  TasbeehSession({
    required this.id,
    required this.userId,
    required this.tasbeehId,
    required this.targetCount,
    this.currentCount = 0,
    required this.startTime,
    this.endTime,
    this.isCompleted = false,
    required this.createdAt,
  });

  factory TasbeehSession.fromJson(Map<String, dynamic> json) =>
      _$TasbeehSessionFromJson(json);
  Map<String, dynamic> toJson() => _$TasbeehSessionToJson(this);
}

@JsonSerializable()
class DuaItem {
  final String id;
  final String title;
  final String arabicText;
  final String? transliteration;
  final String? translation;
  final String? category;
  final String? source; // Quran, Hadith, etc.
  final String? reference; // Verse number, hadith reference
  final String? colorHex; // Color in hex format
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;

  DuaItem({
    required this.id,
    required this.title,
    required this.arabicText,
    this.transliteration,
    this.translation,
    this.category,
    this.source,
    this.reference,
    this.colorHex,
    this.isDefault = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DuaItem.fromJson(Map<String, dynamic> json) =>
      _$DuaItemFromJson(json);
  Map<String, dynamic> toJson() => _$DuaItemToJson(this);
}
