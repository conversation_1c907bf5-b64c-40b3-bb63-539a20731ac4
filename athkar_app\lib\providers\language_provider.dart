import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageProvider extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  static const String _defaultLanguage = 'ar'; // Arabic as default
  
  Locale _currentLocale = const Locale('ar');
  
  Locale get currentLocale => _currentLocale;
  
  bool get isArabic => _currentLocale.languageCode == 'ar';
  bool get isEnglish => _currentLocale.languageCode == 'en';
  
  /// Initialize language from saved preferences
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString(_languageKey) ?? _defaultLanguage;
      _currentLocale = Locale(savedLanguage);
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing language: $e');
      _currentLocale = const Locale(_defaultLanguage);
    }
  }
  
  /// Change the app language
  Future<void> changeLanguage(String languageCode) async {
    if (_currentLocale.languageCode == languageCode) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
      
      _currentLocale = Locale(languageCode);
      notifyListeners();
    } catch (e) {
      debugPrint('Error changing language: $e');
    }
  }
  
  /// Toggle between Arabic and English
  Future<void> toggleLanguage() async {
    final newLanguage = isArabic ? 'en' : 'ar';
    await changeLanguage(newLanguage);
  }
  
  /// Get text direction based on current language
  TextDirection get textDirection {
    return isArabic ? TextDirection.rtl : TextDirection.ltr;
  }
  
  /// Get alignment based on current language
  Alignment get alignment {
    return isArabic ? Alignment.centerRight : Alignment.centerLeft;
  }
  
  /// Get text align based on current language
  TextAlign get textAlign {
    return isArabic ? TextAlign.right : TextAlign.left;
  }
  
  /// Get cross axis alignment based on current language
  CrossAxisAlignment get crossAxisAlignment {
    return isArabic ? CrossAxisAlignment.end : CrossAxisAlignment.start;
  }
  
  /// Get main axis alignment for RTL support
  MainAxisAlignment get mainAxisAlignment {
    return isArabic ? MainAxisAlignment.end : MainAxisAlignment.start;
  }
  
  /// Get edge insets with RTL support
  EdgeInsets getEdgeInsets({
    double left = 0,
    double right = 0,
    double top = 0,
    double bottom = 0,
  }) {
    if (isArabic) {
      return EdgeInsets.only(
        left: right,
        right: left,
        top: top,
        bottom: bottom,
      );
    }
    return EdgeInsets.only(
      left: left,
      right: right,
      top: top,
      bottom: bottom,
    );
  }
  
  /// Get border radius with RTL support
  BorderRadius getBorderRadius({
    double topLeft = 0,
    double topRight = 0,
    double bottomLeft = 0,
    double bottomRight = 0,
  }) {
    if (isArabic) {
      return BorderRadius.only(
        topLeft: Radius.circular(topRight),
        topRight: Radius.circular(topLeft),
        bottomLeft: Radius.circular(bottomRight),
        bottomRight: Radius.circular(bottomLeft),
      );
    }
    return BorderRadius.only(
      topLeft: Radius.circular(topLeft),
      topRight: Radius.circular(topRight),
      bottomLeft: Radius.circular(bottomLeft),
      bottomRight: Radius.circular(bottomRight),
    );
  }
  
  /// Get appropriate font family for current language
  String get fontFamily {
    return isArabic ? 'NotoSansArabic' : null;
  }
  
  /// Get appropriate Arabic font family for Quranic text
  String get arabicFontFamily => 'Amiri';
  
  /// Format numbers for current locale
  String formatNumber(int number) {
    if (isArabic) {
      // Convert to Arabic-Indic numerals
      const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
      return number.toString().split('').map((digit) {
        final index = int.tryParse(digit);
        return index != null ? arabicNumerals[index] : digit;
      }).join();
    }
    return number.toString();
  }
  
  /// Get localized day names
  List<String> get dayNames {
    if (isArabic) {
      return ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    }
    return ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  }
  
  /// Get localized month names
  List<String> get monthNames {
    if (isArabic) {
      return [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ];
    }
    return [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
  }
  
  /// Get Islamic month names
  List<String> get islamicMonthNames {
    if (isArabic) {
      return [
        'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
        'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
      ];
    }
    return [
      'Muharram', 'Safar', 'Rabi\' al-awwal', 'Rabi\' al-thani', 'Jumada al-awwal', 'Jumada al-thani',
      'Rajab', 'Sha\'ban', 'Ramadan', 'Shawwal', 'Dhu al-Qi\'dah', 'Dhu al-Hijjah'
    ];
  }
}
