import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:async';
import 'dart:io';

/// Comprehensive floating overlay service for dhikr counters and Islamic widgets
/// Provides system-level overlay functionality with SYSTEM_ALERT_WINDOW permissions
class FloatingOverlayService {
  static final FloatingOverlayService _instance = FloatingOverlayService._internal();
  factory FloatingOverlayService() => _instance;
  FloatingOverlayService._internal();

  static const MethodChannel _channel = MethodChannel('com.islamic.athkar/floating_overlay');
  
  final Map<String, FloatingWidget> _activeWidgets = {};
  final StreamController<FloatingWidgetEvent> _eventController = StreamController<FloatingWidgetEvent>.broadcast();
  
  bool _isInitialized = false;
  // bool _hasOverlayPermission = false; // Temporarily unused
  // OverlayConfiguration? _configuration; // Temporarily unused

  /// Initialize floating overlay service
  Future<OverlayInitResult> initialize() async {
    if (_isInitialized) return OverlayInitResult.success();

    try {
      // Check platform support
      if (!Platform.isAndroid) {
        debugPrint('Floating overlay is primarily supported on Android');
      }

      // Request overlay permission
      final permissionResult = await _requestOverlayPermission();
      if (!permissionResult.isGranted) {
        debugPrint('Overlay permission not granted: ${permissionResult.error}');
      }

      // Initialize native overlay service (with fallback)
      try {
        await _channel.invokeMethod('initialize');
        debugPrint('Native overlay service initialized');
      } catch (e) {
        debugPrint('Native overlay not available, using fallback: $e');
      }
      
      // Set up method call handler
      _channel.setMethodCallHandler(_handleMethodCall);
      
      // Load configuration
      // _configuration = await _loadConfiguration(); // Temporarily disabled

      _isInitialized = true;
      // _hasOverlayPermission = permissionResult.isGranted; // Temporarily disabled
      
      debugPrint('Floating overlay service initialized successfully');
      return OverlayInitResult.success();

    } catch (e) {
      debugPrint('Failed to initialize floating overlay service: $e');
      return OverlayInitResult.error('فشل في تهيئة خدمة الويدجت العائم: ${e.toString()}');
    }
  }

  /// Create dhikr counter floating widget
  Future<WidgetCreationResult> createDhikrCounter({
    required String id,
    required String title,
    required int targetCount,
    int currentCount = 0,
    Color? backgroundColor,
    Color? textColor,
    double? size,
    FloatingPosition? position,
    bool showProgress = true,
    bool enableVibration = true,
    bool enableSound = true,
  }) async {
    if (!_isInitialized) {
      final initResult = await initialize();
      if (!initResult.isSuccess) {
        return WidgetCreationResult.error(initResult.error!);
      }
    }

    try {
      final widget = FloatingWidget(
        id: id,
        type: FloatingWidgetType.dhikrCounter,
        title: title,
        position: position ?? FloatingPosition.topRight(),
        size: size ?? 120.0,
        backgroundColor: backgroundColor ?? const Color(0xFF2E7D32),
        textColor: textColor ?? Colors.white,
        data: {
          'current_count': currentCount,
          'target_count': targetCount,
          'show_progress': showProgress,
          'enable_vibration': enableVibration,
          'enable_sound': enableSound,
        },
        createdAt: DateTime.now(),
        isVisible: true,
      );

      // Try native implementation first
      try {
        final result = await _channel.invokeMethod('createDhikrCounter', {
          'id': id,
          'title': title,
          'currentCount': currentCount,
          'targetCount': targetCount,
          'x': position?.x ?? 100,
          'y': position?.y ?? 100,
          'size': size ?? 120.0,
          'backgroundColor': backgroundColor?.value ?? 0xFF2E7D32,
          'textColor': textColor?.value ?? 0xFFFFFFFF,
          'showProgress': showProgress,
          'enableVibration': enableVibration,
          'enableSound': enableSound,
        });

        if (result['success'] == true) {
          _activeWidgets[id] = widget;
          _eventController.add(FloatingWidgetEvent(
            type: FloatingWidgetEventType.created,
            widgetId: id,
            timestamp: DateTime.now(),
          ));
          
          return WidgetCreationResult.success(widget);
        } else {
          return WidgetCreationResult.error(result['error'] ?? 'فشل في إنشاء الويدجت');
        }
      } catch (e) {
        // Fallback implementation
        _activeWidgets[id] = widget;
        _eventController.add(FloatingWidgetEvent(
          type: FloatingWidgetEventType.created,
          widgetId: id,
          timestamp: DateTime.now(),
        ));
        
        debugPrint('Using fallback dhikr counter implementation');
        return WidgetCreationResult.success(widget);
      }

    } catch (e) {
      debugPrint('Failed to create dhikr counter: $e');
      return WidgetCreationResult.error('فشل في إنشاء عداد الذكر: ${e.toString()}');
    }
  }

  /// Update widget data
  Future<bool> updateWidget(String widgetId, Map<String, dynamic> data) async {
    if (!_activeWidgets.containsKey(widgetId)) {
      return false;
    }

    try {
      // Try native update
      try {
        final result = await _channel.invokeMethod('updateWidget', {
          'id': widgetId,
          'data': data,
        });

        if (result['success'] == true) {
          final widget = _activeWidgets[widgetId]!;
          widget.data.addAll(data);
          
          _eventController.add(FloatingWidgetEvent(
            type: FloatingWidgetEventType.updated,
            widgetId: widgetId,
            timestamp: DateTime.now(),
            data: data,
          ));
          
          return true;
        }
      } catch (e) {
        debugPrint('Native widget update failed, using fallback: $e');
      }

      // Fallback update
      final widget = _activeWidgets[widgetId]!;
      widget.data.addAll(data);
      
      _eventController.add(FloatingWidgetEvent(
        type: FloatingWidgetEventType.updated,
        widgetId: widgetId,
        timestamp: DateTime.now(),
        data: data,
      ));
      
      return true;
      
    } catch (e) {
      debugPrint('Failed to update widget: $e');
      return false;
    }
  }

  /// Remove widget
  Future<bool> removeWidget(String widgetId) async {
    if (!_activeWidgets.containsKey(widgetId)) {
      return false;
    }

    try {
      // Try native removal
      try {
        final result = await _channel.invokeMethod('removeWidget', {
          'id': widgetId,
        });

        if (result['success'] == true) {
          _activeWidgets.remove(widgetId);
          
          _eventController.add(FloatingWidgetEvent(
            type: FloatingWidgetEventType.removed,
            widgetId: widgetId,
            timestamp: DateTime.now(),
          ));
          
          return true;
        }
      } catch (e) {
        debugPrint('Native widget removal failed, using fallback: $e');
      }

      // Fallback removal
      _activeWidgets.remove(widgetId);
      
      _eventController.add(FloatingWidgetEvent(
        type: FloatingWidgetEventType.removed,
        widgetId: widgetId,
        timestamp: DateTime.now(),
      ));
      
      return true;
      
    } catch (e) {
      debugPrint('Failed to remove widget: $e');
      return false;
    }
  }

  /// Get active widgets
  List<FloatingWidget> getActiveWidgets() {
    return List.unmodifiable(_activeWidgets.values);
  }

  /// Get widget by ID
  FloatingWidget? getWidget(String widgetId) {
    return _activeWidgets[widgetId];
  }

  /// Get widget events stream
  Stream<FloatingWidgetEvent> get eventStream => _eventController.stream;

  /// Check if overlay permission is granted
  Future<bool> hasOverlayPermission() async {
    if (Platform.isAndroid) {
      try {
        return await Permission.systemAlertWindow.isGranted;
      } catch (e) {
        debugPrint('Error checking overlay permission: $e');
        return false;
      }
    }
    return false;
  }

  /// Request overlay permission
  Future<void> requestOverlayPermission() async {
    await _requestOverlayPermission();
  }

  /// Legacy methods for backward compatibility
  Future<void> showDhikrCounter({
    required String title,
    required int targetCount,
    int currentCount = 0,
  }) async {
    await createDhikrCounter(
      id: 'legacy_counter',
      title: title,
      targetCount: targetCount,
      currentCount: currentCount,
    );
  }

  Future<void> hideDhikrCounter() async {
    await removeWidget('legacy_counter');
  }

  bool get isOverlayVisible => _activeWidgets.isNotEmpty;

  Future<void> updateCounter(int newCount) async {
    await updateWidget('legacy_counter', {'current_count': newCount});
  }

  /// Private helper methods
  Future<PermissionResult> _requestOverlayPermission() async {
    try {
      if (Platform.isAndroid) {
        final status = await Permission.systemAlertWindow.request();
        
        if (status.isGranted) {
          return PermissionResult.granted();
        } else if (status.isDenied) {
          return PermissionResult.denied('إذن الويدجت العائم مرفوض');
        } else if (status.isPermanentlyDenied) {
          return PermissionResult.permanentlyDenied('إذن الويدجت العائم مرفوض نهائياً');
        }
      }
      
      return PermissionResult.denied('المنصة غير مدعومة');
    } catch (e) {
      return PermissionResult.error('خطأ في طلب الإذن: ${e.toString()}');
    }
  }

  /// Handle method calls from native side
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    try {
      switch (call.method) {
        case 'onWidgetClicked':
          final widgetId = call.arguments['id'] as String;
          _eventController.add(FloatingWidgetEvent(
            type: FloatingWidgetEventType.clicked,
            widgetId: widgetId,
            timestamp: DateTime.now(),
          ));
          break;
          
        case 'onCounterIncremented':
          final widgetId = call.arguments['id'] as String;
          final newCount = call.arguments['count'] as int;
          
          _eventController.add(FloatingWidgetEvent(
            type: FloatingWidgetEventType.counterIncremented,
            widgetId: widgetId,
            timestamp: DateTime.now(),
            data: {'count': newCount},
          ));
          break;
      }
    } catch (e) {
      debugPrint('Error handling method call: $e');
    }
  }

  /// Load configuration (temporarily disabled)
  // Future<OverlayConfiguration> _loadConfiguration() async {
  //   return OverlayConfiguration.defaultConfig();
  // }

  /// Dispose resources
  Future<void> dispose() async {
    final widgetIds = List<String>.from(_activeWidgets.keys);
    for (final id in widgetIds) {
      await removeWidget(id);
    }
    await _eventController.close();
    _isInitialized = false;
  }
}

/// Floating widget types
enum FloatingWidgetType {
  dhikrCounter,
  prayerTimes,
  quranVerse,
  compass,
  custom,
}

/// Floating widget model
class FloatingWidget {
  final String id;
  final FloatingWidgetType type;
  final String title;
  FloatingPosition position;
  final double size;
  final Color backgroundColor;
  final Color textColor;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  bool isVisible;

  FloatingWidget({
    required this.id,
    required this.type,
    required this.title,
    required this.position,
    required this.size,
    required this.backgroundColor,
    required this.textColor,
    required this.data,
    required this.createdAt,
    required this.isVisible,
  });
}

/// Floating position
class FloatingPosition {
  final double x;
  final double y;

  FloatingPosition({required this.x, required this.y});

  factory FloatingPosition.topLeft() => FloatingPosition(x: 50, y: 100);
  factory FloatingPosition.topRight() => FloatingPosition(x: 300, y: 100);
  factory FloatingPosition.bottomLeft() => FloatingPosition(x: 50, y: 500);
  factory FloatingPosition.bottomRight() => FloatingPosition(x: 300, y: 500);
  factory FloatingPosition.center() => FloatingPosition(x: 200, y: 300);
  factory FloatingPosition.bottomCenter() => FloatingPosition(x: 200, y: 500);
}

/// Widget event types
enum FloatingWidgetEventType {
  created,
  updated,
  removed,
  shown,
  hidden,
  clicked,
  moved,
  counterIncremented,
}

/// Floating widget event
class FloatingWidgetEvent {
  final FloatingWidgetEventType type;
  final String widgetId;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  FloatingWidgetEvent({
    required this.type,
    required this.widgetId,
    required this.timestamp,
    this.data,
  });
}

/// Overlay configuration
class OverlayConfiguration {
  final bool enableVibration;
  final bool enableSound;
  final bool enableAutoHide;
  final int autoHideDelay; // seconds
  final double defaultSize;
  final Color defaultBackgroundColor;
  final Color defaultTextColor;

  OverlayConfiguration({
    required this.enableVibration,
    required this.enableSound,
    required this.enableAutoHide,
    required this.autoHideDelay,
    required this.defaultSize,
    required this.defaultBackgroundColor,
    required this.defaultTextColor,
  });

  factory OverlayConfiguration.defaultConfig() {
    return OverlayConfiguration(
      enableVibration: true,
      enableSound: true,
      enableAutoHide: false,
      autoHideDelay: 30,
      defaultSize: 120.0,
      defaultBackgroundColor: const Color(0xFF2E7D32),
      defaultTextColor: Colors.white,
    );
  }
}

/// Permission result
class PermissionResult {
  final bool isGranted;
  final String? error;
  final bool isPermanentlyDenied;

  PermissionResult._({
    required this.isGranted,
    this.error,
    this.isPermanentlyDenied = false,
  });

  factory PermissionResult.granted() {
    return PermissionResult._(isGranted: true);
  }

  factory PermissionResult.denied(String error) {
    return PermissionResult._(isGranted: false, error: error);
  }

  factory PermissionResult.permanentlyDenied(String error) {
    return PermissionResult._(
      isGranted: false,
      error: error,
      isPermanentlyDenied: true,
    );
  }

  factory PermissionResult.error(String error) {
    return PermissionResult._(isGranted: false, error: error);
  }
}

/// Overlay initialization result
class OverlayInitResult {
  final bool isSuccess;
  final String? error;

  OverlayInitResult._({required this.isSuccess, this.error});

  factory OverlayInitResult.success() {
    return OverlayInitResult._(isSuccess: true);
  }

  factory OverlayInitResult.error(String error) {
    return OverlayInitResult._(isSuccess: false, error: error);
  }
}

/// Widget creation result
class WidgetCreationResult {
  final bool isSuccess;
  final String? error;
  final FloatingWidget? widget;

  WidgetCreationResult._({
    required this.isSuccess,
    this.error,
    this.widget,
  });

  factory WidgetCreationResult.success(FloatingWidget widget) {
    return WidgetCreationResult._(isSuccess: true, widget: widget);
  }

  factory WidgetCreationResult.error(String error) {
    return WidgetCreationResult._(isSuccess: false, error: error);
  }
}
