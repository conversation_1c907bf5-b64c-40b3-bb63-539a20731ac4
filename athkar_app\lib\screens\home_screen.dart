import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/athkar_provider.dart';
import '../providers/auth_provider.dart';
import '../theme/app_theme.dart';

import '../widgets/athkar_card.dart';
import '../widgets/progress_chart.dart';
import '../services/new_supabase_service.dart';
import '../services/prayer_times_service.dart';

import '../models/prayer_times_models.dart';
import '../screens/prebuilt_content_screen.dart';
import '../screens/notification_settings_screen.dart';
import '../screens/audio_settings_screen.dart';
import '../screens/floating_window_screen.dart';
import 'dart:async';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  PrayerTimes? _prayerTimes;
  String _currentPrayer = '';
  String _nextPrayer = '';
  Timer? _timer;
  bool _isLoadingPrayerTimes = true;

  @override
  void initState() {
    super.initState();
    _loadLiveData();
    _startTimer();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AthkarProvider>().loadRoutines();
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _updateCurrentPrayer();
    });
  }

  Future<void> _loadLiveData() async {
    try {
      final prayerTimes = await PrayerTimesService.getAdjustedPrayerTimes();
      setState(() {
        _prayerTimes = prayerTimes;
        _isLoadingPrayerTimes = false;
      });
      _updateCurrentPrayer();
    } catch (e) {
      setState(() {
        _isLoadingPrayerTimes = false;
      });
    }
  }

  void _updateCurrentPrayer() {
    if (_prayerTimes == null) return;

    final now = DateTime.now();
    final currentTime = now.hour * 60 + now.minute;

    final prayers = [
      ('Fajr', _parseTime(_prayerTimes!.fajr)),
      ('Dhuhr', _parseTime(_prayerTimes!.dhuhr)),
      ('Asr', _parseTime(_prayerTimes!.asr)),
      ('Maghrib', _parseTime(_prayerTimes!.maghrib)),
      ('Isha', _parseTime(_prayerTimes!.isha)),
    ];

    String current = 'Isha'; // Default to Isha
    String next = 'Fajr'; // Default to next day's Fajr

    for (int i = 0; i < prayers.length; i++) {
      if (currentTime < prayers[i].$2) {
        current = i > 0 ? prayers[i - 1].$1 : 'Isha';
        next = prayers[i].$1;
        break;
      }
    }

    setState(() {
      _currentPrayer = current;
      _nextPrayer = next;
    });
  }

  int _parseTime(String timeString) {
    final parts = timeString.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  // Removed duplicate initState

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: _buildUnifiedDrawer(context),
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            _buildAppBar(context),
            SliverPadding(
              padding: const EdgeInsets.all(16.0),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  _buildWelcomeCard(context),
                  const SizedBox(height: 20),
                  _buildLivePrayerTimesCard(context),
                  const SizedBox(height: 20),
                  _buildQuickActions(context),
                  const SizedBox(height: 20),
                  _buildProgressSection(context),
                  const SizedBox(height: 20),
                  _buildRecentAthkar(context),
                  const SizedBox(height: 20),
                  _buildFavoriteAthkar(context),
                ]),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: "home_fab",
        onPressed: () {
          Navigator.pushNamed(context, '/create-routine');
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.primaryGreen,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'Athkar',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: AppTheme.generalGradient,
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.library_books, color: Colors.white),
          tooltip: 'Prebuilt Content',
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const PrebuiltContentScreen(),
              ),
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.bar_chart, color: Colors.white),
          onPressed: () {
            Navigator.pushNamed(context, '/statistics');
          },
        ),
        IconButton(
          icon: const Icon(Icons.sync, color: Colors.white),
          onPressed: () {
            _performSync();
          },
        ),
        IconButton(
          icon: const Icon(Icons.notifications_outlined, color: Colors.white),
          onPressed: () {
            _showNotifications();
          },
        ),
        Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            return IconButton(
              icon: CircleAvatar(
                radius: 16,
                backgroundColor: Colors.white,
                child: authProvider.userProfile?.avatarUrl != null
                    ? ClipOval(
                        child: Image.network(
                          authProvider.userProfile!.avatarUrl!,
                          width: 32,
                          height: 32,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Icon(
                        Icons.person,
                        color: AppTheme.primaryGreen,
                        size: 20,
                      ),
              ),
              onPressed: () {
                _navigateToProfile();
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildWelcomeCard(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final userName = authProvider.userProfile?.fullName ?? 
                        authProvider.user?.email?.split('@').first ?? 
                        'User';
        
        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Assalamu Alaikum, $userName',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'May Allah bless your day with peace and remembrance',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Icon(MdiIcons.star, color: Colors.white, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      _getIslamicGreeting(),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                context,
                'Prayer Times',
                MdiIcons.clockTimeEight,
                AppTheme.morningGradient,
                () => _navigateToTab(1),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                context,
                'Qibla Finder',
                MdiIcons.compass,
                AppTheme.eveningGradient,
                () => _navigateToTab(2),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                context,
                'Holy Quran',
                MdiIcons.bookOpenPageVariant,
                AppTheme.generalGradient,
                () => _navigateToTab(3),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                context,
                'Athkar & Dhikr',
                MdiIcons.counter,
                AppTheme.nightGradient,
                () => _navigateToTab(4),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    IconData icon,
    List<Color> gradient,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 80,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: gradient,
          ),
          boxShadow: [
            BoxShadow(
              color: gradient.first.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.white, size: 28),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Progress',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Consumer<AthkarProvider>(
          builder: (context, provider, child) => ProgressChart(data: provider.userProgress),
        ), // Custom widget for progress visualization
      ],
    );
  }

  Widget _buildRecentAthkar(BuildContext context) {
    return Consumer<AthkarProvider>(
      builder: (context, athkarProvider, child) {
        final recentRoutines = athkarProvider.recentRoutines;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Athkar',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    _navigateToAllAthkar();
                  },
                  child: const Text('See All'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (recentRoutines.isEmpty)
              const Center(
                child: Text('No recent athkar found'),
              )
            else
              SizedBox(
                height: 200,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: recentRoutines.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: EdgeInsets.only(
                        right: index < recentRoutines.length - 1 ? 12 : 0,
                      ),
                      child: SizedBox(
                        width: 160,
                        child: AthkarCard(routine: recentRoutines[index]),
                      ),
                    );
                  },
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildFavoriteAthkar(BuildContext context) {
    return Consumer<AthkarProvider>(
      builder: (context, athkarProvider, child) {
        final favoriteRoutines = athkarProvider.favoriteRoutines;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Favorite Athkar',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (favoriteRoutines.isEmpty)
              const Center(
                child: Text('No favorite athkar yet'),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: favoriteRoutines.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: AthkarCard(routine: favoriteRoutines[index]),
                  );
                },
              ),
          ],
        );
      },
    );
  }

  String _getIslamicGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'صباح الخير'; // Good morning
    } else if (hour < 18) {
      return 'مساء الخير'; // Good afternoon
    } else {
      return 'مساء الخير'; // Good evening
    }
  }

  void _navigateToTab(int index) {
    // Simple navigation to Islamic feature screens
    switch (index) {
      case 1:
        Navigator.pushNamed(context, '/prayer-times');
        break;
      case 2:
        Navigator.pushNamed(context, '/qibla');
        break;
      case 3:
        Navigator.pushNamed(context, '/quran');
        break;
      case 4:
        Navigator.pushNamed(context, '/athkar');
        break;
      default:
        break;
    }
  }

  /*
  void _navigateToAthkarByCategory(String categoryId) {
    final athkarProvider = context.read<AthkarProvider>();
    final routines = athkarProvider.getRoutinesByCategory(categoryId);

    if (routines.isNotEmpty) {
      // Navigate to the first routine in the category
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PracticeAthkarScreen(routine: routines.first),
        ),
      );
    } else {
      // Show message that no routines are available
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('No athkar routines found for this category'),
          action: SnackBarAction(
            label: 'Create One',
            onPressed: () => Navigator.pushNamed(context, '/create-routine'),
          ),
        ),
      );
    }
  }
  */

  /*
  void _openFloatingCounter() async {
    final success = await FloatingCounterService.startFloatingCounter(
      dhikr: 'سُبْحَانَ اللهِ',
      initialCount: 0,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Floating counter started. You can now minimize the app.'),
          duration: Duration(seconds: 3),
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Could not start floating counter. Please check permissions.'),
          action: SnackBarAction(
            label: 'Settings',
            onPressed: () {
              // Navigate to settings
            },
          ),
        ),
      );
    }
  }
  */

  void _showNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notifications'),
        content: const Text('No new notifications'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _navigateToProfile() {
    // Navigate to settings screen
    Navigator.pushNamed(context, '/settings');
  }

  void _navigateToAllAthkar() {
    // Navigate to athkar screen
    Navigator.pushNamed(context, '/athkar');
  }

  Widget _buildLivePrayerTimesCard(BuildContext context) {
    if (_isLoadingPrayerTimes) {
      return Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          height: 120,
          padding: const EdgeInsets.all(16),
          child: const Center(
            child: CircularProgressIndicator(color: AppTheme.primaryGreen),
          ),
        ),
      );
    }

    if (_prayerTimes == null) {
      return Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(Icons.error_outline, color: Colors.red[400], size: 32),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Unable to load prayer times',
                  style: TextStyle(fontSize: 16),
                ),
              ),
              TextButton(
                onPressed: _loadLiveData,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () => Navigator.pushNamed(context, '/prayer-times'),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                AppTheme.primaryGreen,
                AppTheme.primaryGreen.withValues(alpha: 0.8),
              ],
            ),
          ),
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    MdiIcons.clockTimeEight,
                    color: Colors.white,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Prayer Times',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      _prayerTimes!.location,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Prayer',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          _currentPrayer,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 1,
                    height: 40,
                    color: Colors.white.withValues(alpha: 0.3),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Next Prayer',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          '$_nextPrayer at ${_getNextPrayerTime()}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getNextPrayerTime() {
    if (_prayerTimes == null) return '';

    switch (_nextPrayer) {
      case 'Fajr': return _prayerTimes!.fajr;
      case 'Dhuhr': return _prayerTimes!.dhuhr;
      case 'Asr': return _prayerTimes!.asr;
      case 'Maghrib': return _prayerTimes!.maghrib;
      case 'Isha': return _prayerTimes!.isha;
      default: return '';
    }
  }

  void _performSync() async {
    final authProvider = context.read<AuthProvider>();
    if (!authProvider.isAuthenticated) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please sign in to sync your data')),
      );
      return;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Syncing data...')),
    );

    try {
      await NewSupabaseService.performFullSync();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sync completed successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sync failed'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildUnifiedDrawer(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppTheme.primaryGreen, Colors.blue[700]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Icon(
                  Icons.mosque,
                  color: Colors.white,
                  size: 48,
                ),
                SizedBox(height: 8),
                Text(
                  'أذكار المسلم',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'تطبيق شامل للأذكار والأدعية',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.home, color: AppTheme.primaryGreen),
            title: const Text('الرئيسية'),
            selected: true,
            selectedTileColor: AppTheme.primaryGreen.withValues(alpha: 0.1),
            onTap: () => Navigator.pop(context),
          ),
          const Divider(),

          // Athkar Section
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Text(
              'الأذكار والأدعية',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.auto_stories, color: AppTheme.primaryGreen),
            title: const Text('أذكار الصباح'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/morning-athkar');
            },
          ),
          ListTile(
            leading: const Icon(Icons.nights_stay, color: AppTheme.primaryGreen),
            title: const Text('أذكار المساء'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/evening-athkar');
            },
          ),
          ListTile(
            leading: const Icon(Icons.favorite, color: AppTheme.primaryGreen),
            title: const Text('أذكار عامة'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/general-athkar');
            },
          ),
          ListTile(
            leading: const Icon(Icons.beenhere, color: AppTheme.primaryGreen),
            title: const Text('التسبيح'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/tasbeeh');
            },
          ),
          ListTile(
            leading: const Icon(Icons.library_books, color: AppTheme.primaryGreen),
            title: const Text('المحتوى المبني مسبقاً'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PrebuiltContentScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.notifications, color: AppTheme.primaryGreen),
            title: const Text('إعدادات الإشعارات'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NotificationSettingsScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.volume_up, color: AppTheme.primaryGreen),
            title: const Text('إعدادات الصوت'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AudioSettingsScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.picture_in_picture, color: AppTheme.primaryGreen),
            title: const Text('النوافذ العائمة'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FloatingWindowScreen(),
                ),
              );
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.access_time, color: AppTheme.primaryGreen),
            title: const Text('أوقات الصلاة'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/prayer-times');
            },
          ),
          ListTile(
            leading: const Icon(Icons.explore, color: AppTheme.primaryGreen),
            title: const Text('اتجاه القبلة'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/qibla');
            },
          ),
          ListTile(
            leading: const Icon(Icons.book, color: AppTheme.primaryGreen),
            title: const Text('القرآن الكريم'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/quran');
            },
          ),
          ListTile(
            leading: const Icon(Icons.article, color: AppTheme.primaryGreen),
            title: const Text('الأحاديث النبوية'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/hadith');
            },
          ),
          const Divider(),

          // Advanced Features Section
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Text(
              'الميزات المتقدمة',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.picture_in_picture, color: AppTheme.primaryGreen),
            title: const Text('النوافذ العائمة'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FloatingWindowScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.backup, color: AppTheme.primaryGreen),
            title: const Text('النسخ الاحتياطي'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/backup');
            },
          ),
          ListTile(
            leading: const Icon(Icons.bug_report, color: AppTheme.primaryGreen),
            title: const Text('اختبار الميزات'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/testing');
            },
          ),

          const Divider(),

          // Settings Section
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Text(
              'الإعدادات',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.settings, color: AppTheme.primaryGreen),
            title: const Text('الإعدادات العامة'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/settings');
            },
          ),
          ListTile(
            leading: const Icon(Icons.person, color: AppTheme.primaryGreen),
            title: const Text('الملف الشخصي'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/profile');
            },
          ),
        ],
      ),
    );
  }
}
