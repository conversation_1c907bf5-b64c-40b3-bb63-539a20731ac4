<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient definitions -->
    <radialGradient id="backgroundGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </radialGradient>
    
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFB300;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8F00;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="lightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#F5F5F5;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- Background circle with Islamic green gradient -->
  <circle cx="256" cy="256" r="240" fill="url(#backgroundGradient)" stroke="#1B5E20" stroke-width="8"/>
  
  <!-- Outer decorative ring -->
  <circle cx="256" cy="256" r="220" fill="none" stroke="url(#goldGradient)" stroke-width="4" opacity="0.6"/>
  
  <!-- Islamic geometric pattern (8-pointed star) -->
  <g transform="translate(256,256)">
    <!-- Main 8-pointed star -->
    <path d="M 0,-80 L 20,-20 L 80,0 L 20,20 L 0,80 L -20,20 L -80,0 L -20,-20 Z" 
          fill="url(#goldGradient)" 
          stroke="#FF8F00" 
          stroke-width="2"/>
    
    <!-- Inner star -->
    <path d="M 0,-50 L 12,-12 L 50,0 L 12,12 L 0,50 L -12,12 L -50,0 L -12,-12 Z" 
          fill="url(#lightGradient)" 
          stroke="#FFB300" 
          stroke-width="1"/>
  </g>
  
  <!-- Crescent moon -->
  <g transform="translate(256,180)">
    <path d="M -25,0 A 25,25 0 1,1 25,0 A 20,20 0 1,0 -25,0 Z" 
          fill="url(#lightGradient)" 
          stroke="#FFB300" 
          stroke-width="2"/>
  </g>
  
  <!-- Prayer beads (Tasbih) representation -->
  <g transform="translate(256,320)">
    <!-- Central bead -->
    <circle cx="0" cy="0" r="8" fill="url(#goldGradient)" stroke="#FF8F00" stroke-width="1"/>
    
    <!-- Side beads -->
    <circle cx="-20" cy="0" r="6" fill="url(#lightGradient)" stroke="#FFB300" stroke-width="1"/>
    <circle cx="20" cy="0" r="6" fill="url(#lightGradient)" stroke="#FFB300" stroke-width="1"/>
    <circle cx="-35" cy="5" r="4" fill="url(#goldGradient)" stroke="#FF8F00" stroke-width="1"/>
    <circle cx="35" cy="5" r="4" fill="url(#goldGradient)" stroke="#FF8F00" stroke-width="1"/>
    
    <!-- Connecting thread -->
    <path d="M -35,5 Q -20,0 0,0 Q 20,0 35,5" 
          fill="none" 
          stroke="#FFB300" 
          stroke-width="2" 
          opacity="0.7"/>
  </g>
  
  <!-- Arabic calligraphy-inspired decorative elements -->
  <g transform="translate(256,256)" opacity="0.3">
    <!-- Decorative curves -->
    <path d="M -60,-60 Q -30,-80 0,-60 Q 30,-80 60,-60" 
          fill="none" 
          stroke="url(#lightGradient)" 
          stroke-width="3"/>
    <path d="M -60,60 Q -30,80 0,60 Q 30,80 60,60" 
          fill="none" 
          stroke="url(#lightGradient)" 
          stroke-width="3"/>
  </g>
  
  <!-- Corner decorative elements -->
  <g opacity="0.4">
    <!-- Top left -->
    <circle cx="80" cy="80" r="12" fill="url(#goldGradient)"/>
    <circle cx="80" cy="80" r="8" fill="url(#lightGradient)"/>
    
    <!-- Top right -->
    <circle cx="432" cy="80" r="12" fill="url(#goldGradient)"/>
    <circle cx="432" cy="80" r="8" fill="url(#lightGradient)"/>
    
    <!-- Bottom left -->
    <circle cx="80" cy="432" r="12" fill="url(#goldGradient)"/>
    <circle cx="80" cy="432" r="8" fill="url(#lightGradient)"/>
    
    <!-- Bottom right -->
    <circle cx="432" cy="432" r="12" fill="url(#goldGradient)"/>
    <circle cx="432" cy="432" r="8" fill="url(#lightGradient)"/>
  </g>
  
  <!-- Subtle inner glow -->
  <circle cx="256" cy="256" r="200" fill="none" stroke="url(#lightGradient)" stroke-width="2" opacity="0.3"/>
</svg>
