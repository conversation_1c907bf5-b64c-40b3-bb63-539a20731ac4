import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../theme/app_theme.dart';
import '../models/athkar_models.dart';
import 'practice_athkar_screen.dart';

class PrebuiltCategoryScreen extends StatefulWidget {
  final String categoryId;
  final String categoryTitle;
  final Color categoryColor;

  const PrebuiltCategoryScreen({
    super.key,
    required this.categoryId,
    required this.categoryTitle,
    required this.categoryColor,
  });

  @override
  State<PrebuiltCategoryScreen> createState() => _PrebuiltCategoryScreenState();
}

class _PrebuiltCategoryScreenState extends State<PrebuiltCategoryScreen> {
  List<PrebuiltAthkarRoutine> _routines = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRoutines();
  }

  void _loadRoutines() {
    // Simulate loading and populate with sample data based on category
    Future.delayed(const Duration(milliseconds: 500), () {
      setState(() {
        _routines = _getRoutinesForCategory(widget.categoryId);
        _isLoading = false;
      });
    });
  }

  List<PrebuiltAthkarRoutine> _getRoutinesForCategory(String categoryId) {
    switch (categoryId) {
      case 'morning':
        return [
          PrebuiltAthkarRoutine(
            id: 'morning_1',
            title: 'Essential Morning Athkar',
            description: 'Core morning supplications for daily protection',
            estimatedDuration: 10,
            stepsCount: 7,
            steps: [
              PrebuiltAthkarStep(
                arabicText: 'أَعُوذُ بِاللهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
                transliteration: 'A\'udhu billahi min ash-shaytani\'r-rajim',
                translation: 'I seek refuge in Allah from Satan, the accursed',
                targetCount: 1,
              ),
              PrebuiltAthkarStep(
                arabicText: 'بِسْمِ اللهِ الرَّحْمٰنِ الرَّحِيْمِ',
                transliteration: 'Bismillahi\'r-rahmani\'r-rahim',
                translation: 'In the name of Allah, the Most Gracious, the Most Merciful',
                targetCount: 1,
              ),
              PrebuiltAthkarStep(
                arabicText: 'سُبْحَانَ اللهِ وَبِحَمْدِهِ',
                transliteration: 'Subhan Allahi wa bihamdihi',
                translation: 'Glory be to Allah and praise be to Him',
                targetCount: 100,
              ),
            ],
          ),
          PrebuiltAthkarRoutine(
            id: 'morning_2',
            title: 'Ayat al-Kursi',
            description: 'The Throne Verse for morning protection',
            estimatedDuration: 3,
            stepsCount: 1,
            steps: [
              PrebuiltAthkarStep(
                arabicText: 'اللّهُ لاَ إِلَـهَ إِلاَّ هُوَ الْحَيُّ الْقَيُّومُ لاَ تَأْخُذُهُ سِنَةٌ وَلاَ نَوْمٌ',
                transliteration: 'Allahu la ilaha illa huwa\'l-hayyu\'l-qayyum la ta\'khudhuhu sinatun wa la nawm',
                translation: 'Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence. Neither drowsiness overtakes Him nor sleep.',
                targetCount: 1,
              ),
            ],
          ),
        ];
      case 'evening':
        return [
          PrebuiltAthkarRoutine(
            id: 'evening_1',
            title: 'Essential Evening Athkar',
            description: 'Core evening supplications for protection',
            estimatedDuration: 8,
            stepsCount: 5,
            steps: [
              PrebuiltAthkarStep(
                arabicText: 'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ',
                transliteration: 'Amsayna wa amsa\'l-mulku lillah',
                translation: 'We have reached the evening and at this very time unto Allah belongs all sovereignty',
                targetCount: 1,
              ),
            ],
          ),
        ];
      case 'prayer':
        return [
          PrebuiltAthkarRoutine(
            id: 'prayer_1',
            title: 'After Fard Prayer',
            description: 'Supplications after obligatory prayers',
            estimatedDuration: 5,
            stepsCount: 4,
            steps: [
              PrebuiltAthkarStep(
                arabicText: 'سُبْحَانَ اللهِ',
                transliteration: 'Subhan Allah',
                translation: 'Glory be to Allah',
                targetCount: 33,
              ),
              PrebuiltAthkarStep(
                arabicText: 'الْحَمْدُ لِلَّهِ',
                transliteration: 'Alhamdulillah',
                translation: 'Praise be to Allah',
                targetCount: 33,
              ),
              PrebuiltAthkarStep(
                arabicText: 'اللهُ أَكْبَرُ',
                transliteration: 'Allahu Akbar',
                translation: 'Allah is the Greatest',
                targetCount: 34,
              ),
            ],
          ),
        ];
      default:
        return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.categoryTitle),
        backgroundColor: widget.categoryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _routines.isEmpty
              ? _buildEmptyState()
              : _buildRoutinesList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            MdiIcons.bookOpenPageVariant,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'No routines available',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'Check back later for new content',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildRoutinesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _routines.length,
      itemBuilder: (context, index) {
        final routine = _routines[index];
        return _buildRoutineCard(routine);
      },
    );
  }

  Widget _buildRoutineCard(PrebuiltAthkarRoutine routine) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _startPractice(routine),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          routine.title,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          routine.description,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: widget.categoryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${routine.estimatedDuration} min',
                      style: TextStyle(
                        color: widget.categoryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    MdiIcons.formatListNumbered,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${routine.stepsCount} steps',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    MdiIcons.clockOutline,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '~${routine.estimatedDuration} minutes',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.play_arrow,
                    color: AppTheme.primaryGreen,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _startPractice(PrebuiltAthkarRoutine routine) {
    // Convert PrebuiltAthkarRoutine to AthkarRoutine for practice
    final athkarRoutine = AthkarRoutine(
      id: routine.id,
      userId: null,
      categoryId: widget.categoryId,
      title: routine.title,
      description: routine.description,
      isPublic: true,
      isFavorite: false,
      totalSteps: routine.stepsCount,
      estimatedDuration: routine.estimatedDuration,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final athkarSteps = routine.steps.asMap().entries.map((entry) {
      final index = entry.key;
      final step = entry.value;
      return AthkarStep(
        id: '${routine.id}_step_$index',
        routineId: routine.id,
        stepOrder: index + 1,
        arabicText: step.arabicText,
        transliteration: step.transliteration,
        translation: step.translation,
        targetCount: step.targetCount,
        audioUrl: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }).toList();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PracticeAthkarScreen(
          routine: athkarRoutine,
          steps: athkarSteps,
        ),
      ),
    );
  }
}

// Data models for prebuilt content
class PrebuiltAthkarRoutine {
  final String id;
  final String title;
  final String description;
  final int estimatedDuration;
  final int stepsCount;
  final List<PrebuiltAthkarStep> steps;

  PrebuiltAthkarRoutine({
    required this.id,
    required this.title,
    required this.description,
    required this.estimatedDuration,
    required this.stepsCount,
    required this.steps,
  });
}

class PrebuiltAthkarStep {
  final String arabicText;
  final String transliteration;
  final String translation;
  final int targetCount;

  PrebuiltAthkarStep({
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.targetCount,
  });
}
