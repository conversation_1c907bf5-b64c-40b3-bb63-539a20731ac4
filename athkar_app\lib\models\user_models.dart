import 'package:json_annotation/json_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

part 'user_models.g.dart';

@JsonSerializable()
class UserProfile {
  final String id;
  final String email;
  final String fullName;
  final String? phoneNumber;
  final String? avatarUrl;
  final bool isEmailVerified;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // App preferences
  final String? preferredLanguage;
  final bool darkMode;
  final bool notificationsEnabled;
  final bool prayerNotifications;
  final bool athkarReminders;
  
  // Islamic preferences
  final String? madhab;
  final String? qiblaCalculationMethod;
  final Map<String, int>? prayerTimeAdjustments;

  UserProfile({
    required this.id,
    required this.email,
    required this.fullName,
    this.phoneNumber,
    this.avatarUrl,
    this.isEmailVerified = false,
    required this.createdAt,
    required this.updatedAt,
    this.preferredLanguage = 'ar',
    this.darkMode = false,
    this.notificationsEnabled = true,
    this.prayerNotifications = true,
    this.athkarReminders = true,
    this.madhab,
    this.qiblaCalculationMethod,
    this.prayerTimeAdjustments,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  UserProfile copyWith({
    String? id,
    String? email,
    String? fullName,
    String? phoneNumber,
    String? avatarUrl,
    bool? isEmailVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? preferredLanguage,
    bool? darkMode,
    bool? notificationsEnabled,
    bool? prayerNotifications,
    bool? athkarReminders,
    String? madhab,
    String? qiblaCalculationMethod,
    Map<String, int>? prayerTimeAdjustments,
  }) {
    return UserProfile(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      darkMode: darkMode ?? this.darkMode,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      prayerNotifications: prayerNotifications ?? this.prayerNotifications,
      athkarReminders: athkarReminders ?? this.athkarReminders,
      madhab: madhab ?? this.madhab,
      qiblaCalculationMethod: qiblaCalculationMethod ?? this.qiblaCalculationMethod,
      prayerTimeAdjustments: prayerTimeAdjustments ?? this.prayerTimeAdjustments,
    );
  }
}

class AuthResult {
  final bool success;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final User? user;
  final String message;
  final String? errorCode;

  AuthResult({
    required this.success,
    this.user,
    required this.message,
    this.errorCode,
  });

  // Manual JSON methods since User type can't be serialized
  factory AuthResult.fromJson(Map<String, dynamic> json) {
    return AuthResult(
      success: json['success'] as bool,
      message: json['message'] as String,
      errorCode: json['errorCode'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'errorCode': errorCode,
    };
  }
}

@JsonSerializable()
class UserStats {
  final String userId;
  final int totalAthkarCompleted;
  final int totalPrayersTracked;
  final int currentStreak;
  final int longestStreak;
  final DateTime lastActivity;
  final Map<String, int> monthlyStats;
  final Map<String, int> categoryStats;

  UserStats({
    required this.userId,
    this.totalAthkarCompleted = 0,
    this.totalPrayersTracked = 0,
    this.currentStreak = 0,
    this.longestStreak = 0,
    required this.lastActivity,
    this.monthlyStats = const {},
    this.categoryStats = const {},
  });

  factory UserStats.fromJson(Map<String, dynamic> json) =>
      _$UserStatsFromJson(json);
  Map<String, dynamic> toJson() => _$UserStatsToJson(this);
}

@JsonSerializable()
class UserBookmark {
  final String id;
  final String userId;
  final String type; // 'quran_verse', 'athkar', 'dua'
  final String title;
  final String content;
  final String? reference; // Surah:Ayah for Quran, category for athkar
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserBookmark({
    required this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.content,
    this.reference,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserBookmark.fromJson(Map<String, dynamic> json) =>
      _$UserBookmarkFromJson(json);
  Map<String, dynamic> toJson() => _$UserBookmarkToJson(this);
}

@JsonSerializable()
class SyncStatus {
  final String userId;
  final DateTime lastSyncTime;
  final bool isOnline;
  final int pendingChanges;
  final Map<String, DateTime> lastSyncByTable;
  final List<String> failedSyncs;

  SyncStatus({
    required this.userId,
    required this.lastSyncTime,
    this.isOnline = false,
    this.pendingChanges = 0,
    this.lastSyncByTable = const {},
    this.failedSyncs = const [],
  });

  factory SyncStatus.fromJson(Map<String, dynamic> json) =>
      _$SyncStatusFromJson(json);
  Map<String, dynamic> toJson() => _$SyncStatusToJson(this);
}

@JsonSerializable()
class UserPreferences {
  final String userId;
  final String language;
  final bool darkMode;
  final bool notificationsEnabled;
  final bool autoSync;
  final int syncFrequencyMinutes;
  final Map<String, dynamic> customSettings;
  final DateTime updatedAt;

  UserPreferences({
    required this.userId,
    this.language = 'ar',
    this.darkMode = false,
    this.notificationsEnabled = true,
    this.autoSync = true,
    this.syncFrequencyMinutes = 30,
    this.customSettings = const {},
    required this.updatedAt,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) =>
      _$UserPreferencesFromJson(json);
  Map<String, dynamic> toJson() => _$UserPreferencesToJson(this);
}

// Enum for authentication providers
enum AuthProvider {
  email,
  google,
  apple,
  facebook,
}

// Enum for sync status
enum SyncState {
  idle,
  syncing,
  success,
  error,
  offline,
}

// Extension for AuthProvider
extension AuthProviderExtension on AuthProvider {
  String get name {
    switch (this) {
      case AuthProvider.email:
        return 'email';
      case AuthProvider.google:
        return 'google';
      case AuthProvider.apple:
        return 'apple';
      case AuthProvider.facebook:
        return 'facebook';
    }
  }

  String get displayName {
    switch (this) {
      case AuthProvider.email:
        return 'Email';
      case AuthProvider.google:
        return 'Google';
      case AuthProvider.apple:
        return 'Apple';
      case AuthProvider.facebook:
        return 'Facebook';
    }
  }
}

// Extension for SyncState
extension SyncStateExtension on SyncState {
  String get displayName {
    switch (this) {
      case SyncState.idle:
        return 'Ready';
      case SyncState.syncing:
        return 'Syncing...';
      case SyncState.success:
        return 'Synced';
      case SyncState.error:
        return 'Error';
      case SyncState.offline:
        return 'Offline';
    }
  }

  bool get isActive => this == SyncState.syncing;
  bool get hasError => this == SyncState.error;
  bool get isOffline => this == SyncState.offline;
}
