import 'package:flutter/material.dart';
import '../providers/quran_provider.dart';
import '../providers/hadith_provider.dart';
import '../providers/athkar_provider.dart';
import '../services/prayer_times_service.dart';
import '../services/enhanced_location_service.dart';
import '../services/offline_verification_service.dart';
import '../services/islamic_auth_service.dart';

class ComprehensiveTestingService {
  static final ComprehensiveTestingService _instance = ComprehensiveTestingService._internal();
  factory ComprehensiveTestingService() => _instance;
  ComprehensiveTestingService._internal();

  /// Run five-round comprehensive testing validation
  Future<TestingResult> runFiveRoundTesting() async {
    final result = TestingResult();
    
    try {
      debugPrint('🚀 Starting Five-Round Comprehensive Testing...');
      
      // Round 1: Basic Functionality Testing
      result.round1 = await _runRound1BasicFunctionality();
      debugPrint('✅ Round 1 completed: ${result.round1?.score ?? 0}%');

      // Round 2: Arabic RTL Display Testing
      result.round2 = await _runRound2ArabicRTL();
      debugPrint('✅ Round 2 completed: ${result.round2?.score ?? 0}%');

      // Round 3: Islamic Content Authenticity Testing
      result.round3 = await _runRound3IslamicAuthenticity();
      debugPrint('✅ Round 3 completed: ${result.round3?.score ?? 0}%');

      // Round 4: Navigation Flow Testing
      result.round4 = await _runRound4NavigationFlow();
      debugPrint('✅ Round 4 completed: ${result.round4?.score ?? 0}%');

      // Round 5: Performance and Integration Testing
      result.round5 = await _runRound5PerformanceIntegration();
      debugPrint('✅ Round 5 completed: ${result.round5?.score ?? 0}%');
      
      // Calculate overall score
      result.calculateOverallScore();
      
      debugPrint('🎯 Five-Round Testing completed with overall score: ${result.overallScore}%');
      
    } catch (e) {
      debugPrint('❌ Error during five-round testing: $e');
      result.error = e.toString();
    }
    
    return result;
  }

  /// Round 1: Basic Functionality Testing
  Future<RoundResult> _runRound1BasicFunctionality() async {
    final round = RoundResult('Basic Functionality');
    
    try {
      // Test 1.1: Quran Provider Initialization
      final quranProvider = QuranProvider();
      await quranProvider.initialize();
      round.addTest('Quran Provider Init', quranProvider.isLoaded);
      
      // Test 1.2: Hadith Provider Initialization
      final hadithProvider = HadithProvider();
      await hadithProvider.initialize();
      round.addTest('Hadith Provider Init', hadithProvider.isLoaded);
      
      // Test 1.3: Athkar Provider Initialization
      final athkarProvider = AthkarProvider();
      // AthkarProvider doesn't have initialize method, check if categories are loaded
      final categories = athkarProvider.categories;
      round.addTest('Athkar Provider Init', categories.isNotEmpty);
      
      // Test 1.4: Prayer Times Calculation
      final prayerTimes = await PrayerTimesService.getPrayerTimesWithAdhan(31.9454, 35.9284, DateTime.now());
      round.addTest('Prayer Times Calculation', prayerTimes != null);
      
      // Test 1.5: Location Services
      final locationService = EnhancedLocationService();
      await locationService.initialize();
      final cities = locationService.getAllJordanCities();
      round.addTest('Location Services', cities.isNotEmpty);
      
      // Test 1.6: Offline Verification
      final offlineService = OfflineVerificationService();
      final offlineResult = await offlineService.verifyOfflineCapabilities();
      round.addTest('Offline Capabilities', offlineResult.overallScore >= 80);
      
    } catch (e) {
      round.error = e.toString();
    }
    
    return round;
  }

  /// Round 2: Arabic RTL Display Testing
  Future<RoundResult> _runRound2ArabicRTL() async {
    final round = RoundResult('Arabic RTL Display');
    
    try {
      // Test 2.1: Quran Arabic Text
      final quranProvider = QuranProvider();
      final firstAyah = quranProvider.getAyah(1, 1);
      final hasArabicText = firstAyah?.textUthmani.isNotEmpty == true;
      final containsArabicChars = firstAyah?.textUthmani.contains(RegExp(r'[\u0600-\u06FF]')) == true;
      round.addTest('Quran Arabic Text', hasArabicText && containsArabicChars);
      
      // Test 2.2: Hadith Arabic Text
      final hadithProvider = HadithProvider();
      final hadiths = await hadithProvider.getBookHadiths('bukhari', 1);
      final firstHadith = hadiths.isNotEmpty ? hadiths.first : null;
      final hadithHasArabic = firstHadith?.arabicText.isNotEmpty == true;
      final hadithContainsArabic = firstHadith?.arabicText.contains(RegExp(r'[\u0600-\u06FF]')) == true;
      round.addTest('Hadith Arabic Text', hadithHasArabic && hadithContainsArabic);
      
      // Test 2.3: Athkar Arabic Text
      final athkarProvider = AthkarProvider();
      final categories = athkarProvider.categories;
      final firstCategory = categories.isNotEmpty ? categories.first : null;
      final categoryHasName = firstCategory?.name.isNotEmpty == true;
      round.addTest('Athkar Category Names', categoryHasName);
      
      // Test 2.4: Collection Names Arabic
      final collections = hadithProvider.collections;
      final allCollectionsHaveArabic = collections.every((c) => 
        c.arabicName.isNotEmpty && c.arabicName.contains(RegExp(r'[\u0600-\u06FF]')));
      round.addTest('Collection Arabic Names', allCollectionsHaveArabic);
      
      // Test 2.5: Surah Names Arabic
      final surahs = quranProvider.surahs;
      final allSurahsHaveArabic = surahs.every((s) => 
        s.arabicName.isNotEmpty && s.arabicName.contains(RegExp(r'[\u0600-\u06FF]')));
      round.addTest('Surah Arabic Names', allSurahsHaveArabic);
      
    } catch (e) {
      round.error = e.toString();
    }
    
    return round;
  }

  /// Round 3: Islamic Content Authenticity Testing
  Future<RoundResult> _runRound3IslamicAuthenticity() async {
    final round = RoundResult('Islamic Content Authenticity');
    
    try {
      // Test 3.1: Quran Surah Count
      final quranProvider = QuranProvider();
      round.addTest('Quran 114 Surahs', quranProvider.surahs.length == 114);
      
      // Test 3.2: Hadith Collections Count
      final hadithProvider = HadithProvider();
      round.addTest('6 Major Hadith Collections', hadithProvider.collections.length == 6);
      
      // Test 3.3: Hadith Collection Names Authenticity
      final expectedCollections = {'bukhari', 'muslim', 'abudawud', 'tirmidhi', 'nasai', 'ibnmajah'};
      final actualCollections = hadithProvider.collections.map((c) => c.id).toSet();
      round.addTest('Authentic Collection Names', expectedCollections.difference(actualCollections).isEmpty);
      
      // Test 3.4: Prayer Times Method Authenticity
      final prayerTimes = await PrayerTimesService.getPrayerTimesWithAdhan(31.9454, 35.9284, DateTime.now());
      final hasAllPrayers = prayerTimes != null && 
        prayerTimes.fajr.isNotEmpty &&
        prayerTimes.dhuhr.isNotEmpty &&
        prayerTimes.asr.isNotEmpty &&
        prayerTimes.maghrib.isNotEmpty &&
        prayerTimes.isha.isNotEmpty;
      round.addTest('Complete Prayer Times', hasAllPrayers);
      
      // Test 3.5: Hadith Grading System
      final hadiths = await hadithProvider.getBookHadiths('bukhari', 1);
      final hasGrading = hadiths.isNotEmpty && hadiths.first.grade.isNotEmpty;
      round.addTest('Hadith Grading Present', hasGrading);
      
      // Test 3.6: Proper References
      final hasReferences = hadiths.isNotEmpty && hadiths.first.reference.isNotEmpty;
      round.addTest('Hadith References Present', hasReferences);
      
    } catch (e) {
      round.error = e.toString();
    }
    
    return round;
  }

  /// Round 4: Navigation Flow Testing
  Future<RoundResult> _runRound4NavigationFlow() async {
    final round = RoundResult('Navigation Flow');
    
    try {
      // Test 4.1: Provider Accessibility
      final quranProvider = QuranProvider();
      final hadithProvider = HadithProvider();
      final athkarProvider = AthkarProvider();
      
      round.addTest('Quran Provider Accessible', quranProvider.isLoaded);
      round.addTest('Hadith Provider Accessible', hadithProvider.isLoaded);
      round.addTest('Athkar Provider Accessible', athkarProvider.categories.isNotEmpty);
      
      // Test 4.2: Data Relationships
      final surahs = quranProvider.surahs;
      final firstSurah = surahs.isNotEmpty ? surahs.first : null;
      final ayahs = firstSurah != null ? quranProvider.getSurahAyahs(firstSurah.id) : [];
      round.addTest('Surah-Ayah Relationship', ayahs.isNotEmpty);
      
      // Test 4.3: Collection-Book-Hadith Relationship
      final collections = hadithProvider.collections;
      final firstCollection = collections.isNotEmpty ? collections.first : null;
      if (firstCollection != null) {
        final books = await hadithProvider.getCollectionBooks(firstCollection.id);
        final firstBook = books.isNotEmpty ? books.first : null;
        if (firstBook != null) {
          final hadiths = await hadithProvider.getBookHadiths(firstCollection.id, firstBook.bookNumber);
          round.addTest('Collection-Book-Hadith Flow', hadiths.isNotEmpty);
        } else {
          round.addTest('Collection-Book-Hadith Flow', false);
        }
      } else {
        round.addTest('Collection-Book-Hadith Flow', false);
      }
      
      // Test 4.4: Search Functionality
      final quranSearchResults = await quranProvider.searchQuran('الله');
      round.addTest('Quran Search Works', quranSearchResults.isNotEmpty);
      
      final hadithSearchResults = await hadithProvider.searchHadiths('الله');
      round.addTest('Hadith Search Works', hadithSearchResults.isNotEmpty);
      
    } catch (e) {
      round.error = e.toString();
    }
    
    return round;
  }

  /// Round 5: Performance and Integration Testing
  Future<RoundResult> _runRound5PerformanceIntegration() async {
    final round = RoundResult('Performance & Integration');
    
    try {
      // Test 5.1: Authentication Integration
      final authService = IslamicAuthService();
      await authService.initialize();
      round.addTest('Auth Service Init', authService.isInitialized);
      
      // Test 5.2: Data Isolation Test
      final isolationTest = await authService.testDataIsolation();
      round.addTest('Data Isolation', isolationTest);
      
      // Test 5.3: Performance - Quran Loading
      final stopwatch1 = Stopwatch()..start();
      final quranProvider = QuranProvider();
      if (!quranProvider.isLoaded) {
        await quranProvider.initialize();
      }
      stopwatch1.stop();
      round.addTest('Quran Load Performance', stopwatch1.elapsedMilliseconds < 5000);
      
      // Test 5.4: Performance - Hadith Loading
      final stopwatch2 = Stopwatch()..start();
      final hadithProvider = HadithProvider();
      if (!hadithProvider.isLoaded) {
        await hadithProvider.initialize();
      }
      stopwatch2.stop();
      round.addTest('Hadith Load Performance', stopwatch2.elapsedMilliseconds < 3000);
      
      // Test 5.5: Memory Usage (Simplified)
      final quranSurahs = quranProvider.surahs.length;
      final hadithCollections = hadithProvider.collections.length;
      round.addTest('Memory Efficiency', quranSurahs > 0 && hadithCollections > 0);
      
      // Test 5.6: Error Handling
      try {
        await hadithProvider.getBookHadiths('invalid_collection', 999);
        round.addTest('Error Handling', true); // Should not throw
      } catch (e) {
        round.addTest('Error Handling', false); // Should handle gracefully
      }
      
    } catch (e) {
      round.error = e.toString();
    }
    
    return round;
  }

  /// Generate comprehensive testing report
  Future<String> generateTestingReport() async {
    final result = await runFiveRoundTesting();
    
    final report = StringBuffer();
    report.writeln('🕌 COMPREHENSIVE ISLAMIC ATHKAR APP TESTING REPORT 🕌');
    report.writeln('Generated: ${DateTime.now().toIso8601String()}');
    report.writeln('Overall Score: ${result.overallScore}% ${result.overallScore >= 90 ? "🟢" : result.overallScore >= 70 ? "🟡" : "🔴"}');
    report.writeln('');
    
    // Round-by-round results
    for (int i = 1; i <= 5; i++) {
      final round = result.getRound(i);
      if (round != null) {
        report.writeln('📋 Round $i: ${round.name}');
        report.writeln('Score: ${round.score}% (${round.passedTests}/${round.totalTests} tests passed)');
        
        for (final test in round.tests) {
          final status = test['passed'] ? '✅' : '❌';
          report.writeln('  $status ${test['name']}');
        }
        
        if (round.error != null) {
          report.writeln('  ⚠️ Error: ${round.error}');
        }
        report.writeln('');
      }
    }
    
    // Recommendations
    report.writeln('📝 RECOMMENDATIONS:');
    if (result.overallScore >= 95) {
      report.writeln('🎉 Excellent! The app is ready for production deployment.');
    } else if (result.overallScore >= 85) {
      report.writeln('✅ Good! Minor improvements recommended before deployment.');
    } else if (result.overallScore >= 70) {
      report.writeln('⚠️ Moderate issues found. Address failing tests before deployment.');
    } else {
      report.writeln('❌ Critical issues found. Significant improvements needed.');
    }
    
    report.writeln('');
    report.writeln('🤲 May Allah bless this Islamic application and its users. Ameen.');
    
    return report.toString();
  }
}

class TestingResult {
  RoundResult? round1;
  RoundResult? round2;
  RoundResult? round3;
  RoundResult? round4;
  RoundResult? round5;
  int overallScore = 0;
  String? error;

  void calculateOverallScore() {
    final rounds = [round1, round2, round3, round4, round5].where((r) => r != null).cast<RoundResult>();
    if (rounds.isEmpty) {
      overallScore = 0;
      return;
    }
    
    final totalScore = rounds.map((r) => r.score).reduce((a, b) => a + b);
    overallScore = (totalScore / rounds.length).round();
  }

  RoundResult? getRound(int roundNumber) {
    switch (roundNumber) {
      case 1: return round1;
      case 2: return round2;
      case 3: return round3;
      case 4: return round4;
      case 5: return round5;
      default: return null;
    }
  }
}

class RoundResult {
  final String name;
  final List<Map<String, dynamic>> tests = [];
  String? error;

  RoundResult(this.name);

  void addTest(String testName, bool passed) {
    tests.add({'name': testName, 'passed': passed});
  }

  int get passedTests => tests.where((test) => test['passed'] == true).length;
  int get totalTests => tests.length;
  int get score => totalTests > 0 ? ((passedTests / totalTests) * 100).round() : 0;
}
