class QuranKhatma {
  final String id;
  final String name;
  final String arabicName;
  final DateTime startDate;
  final DateTime targetEndDate;
  final int totalDays;
  final KhatmaType type;
  final List<DailyWird> dailyWirds;
  final double progress;
  final bool isCompleted;
  final DateTime? completedDate;
  final String? notes;
  final KhatmaSettings settings;

  QuranKhatma({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.startDate,
    required this.targetEndDate,
    required this.totalDays,
    required this.type,
    required this.dailyWirds,
    this.progress = 0.0,
    this.isCompleted = false,
    this.completedDate,
    this.notes,
    required this.settings,
  });

  factory QuranKhatma.fromJson(Map<String, dynamic> json) {
    return QuranKhatma(
      id: json['id'],
      name: json['name'],
      arabicName: json['arabic_name'],
      startDate: DateTime.parse(json['start_date']),
      targetEndDate: DateTime.parse(json['target_end_date']),
      totalDays: json['total_days'],
      type: KhatmaType.values[json['type']],
      dailyWirds: (json['daily_wirds'] as List)
          .map((w) => DailyWird.fromJson(w))
          .toList(),
      progress: json['progress']?.toDouble() ?? 0.0,
      isCompleted: json['is_completed'] ?? false,
      completedDate: json['completed_date'] != null 
          ? DateTime.parse(json['completed_date']) 
          : null,
      notes: json['notes'],
      settings: KhatmaSettings.fromJson(json['settings']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'arabic_name': arabicName,
      'start_date': startDate.toIso8601String(),
      'target_end_date': targetEndDate.toIso8601String(),
      'total_days': totalDays,
      'type': type.index,
      'daily_wirds': dailyWirds.map((w) => w.toJson()).toList(),
      'progress': progress,
      'is_completed': isCompleted,
      'completed_date': completedDate?.toIso8601String(),
      'notes': notes,
      'settings': settings.toJson(),
    };
  }
}

class DailyWird {
  final int day;
  final DateTime date;
  final List<WirdSection> sections;
  final bool isCompleted;
  final DateTime? completedAt;
  final int readingTimeMinutes;
  final String? notes;

  DailyWird({
    required this.day,
    required this.date,
    required this.sections,
    this.isCompleted = false,
    this.completedAt,
    this.readingTimeMinutes = 0,
    this.notes,
  });

  factory DailyWird.fromJson(Map<String, dynamic> json) {
    return DailyWird(
      day: json['day'],
      date: DateTime.parse(json['date']),
      sections: (json['sections'] as List)
          .map((s) => WirdSection.fromJson(s))
          .toList(),
      isCompleted: json['is_completed'] ?? false,
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at']) 
          : null,
      readingTimeMinutes: json['reading_time_minutes'] ?? 0,
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'date': date.toIso8601String(),
      'sections': sections.map((s) => s.toJson()).toList(),
      'is_completed': isCompleted,
      'completed_at': completedAt?.toIso8601String(),
      'reading_time_minutes': readingTimeMinutes,
      'notes': notes,
    };
  }
}

class WirdSection {
  final int surahId;
  final String surahName;
  final String surahArabicName;
  final int fromAyah;
  final int toAyah;
  final int totalAyahs;
  final bool isCompleted;
  final DateTime? completedAt;

  WirdSection({
    required this.surahId,
    required this.surahName,
    required this.surahArabicName,
    required this.fromAyah,
    required this.toAyah,
    required this.totalAyahs,
    this.isCompleted = false,
    this.completedAt,
  });

  factory WirdSection.fromJson(Map<String, dynamic> json) {
    return WirdSection(
      surahId: json['surah_id'],
      surahName: json['surah_name'],
      surahArabicName: json['surah_arabic_name'],
      fromAyah: json['from_ayah'],
      toAyah: json['to_ayah'],
      totalAyahs: json['total_ayahs'],
      isCompleted: json['is_completed'] ?? false,
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'surah_id': surahId,
      'surah_name': surahName,
      'surah_arabic_name': surahArabicName,
      'from_ayah': fromAyah,
      'to_ayah': toAyah,
      'total_ayahs': totalAyahs,
      'is_completed': isCompleted,
      'completed_at': completedAt?.toIso8601String(),
    };
  }
}

class KhatmaSettings {
  final bool enableReminders;
  final List<int> reminderTimes; // Hours of day
  final bool enableProgress;
  final bool enableCertificate;
  final String readingMode;
  final Map<String, dynamic> customization;

  KhatmaSettings({
    this.enableReminders = true,
    this.reminderTimes = const [9, 15, 21], // 9 AM, 3 PM, 9 PM
    this.enableProgress = true,
    this.enableCertificate = true,
    this.readingMode = 'normal',
    this.customization = const {},
  });

  factory KhatmaSettings.fromJson(Map<String, dynamic> json) {
    return KhatmaSettings(
      enableReminders: json['enable_reminders'] ?? true,
      reminderTimes: List<int>.from(json['reminder_times'] ?? [9, 15, 21]),
      enableProgress: json['enable_progress'] ?? true,
      enableCertificate: json['enable_certificate'] ?? true,
      readingMode: json['reading_mode'] ?? 'normal',
      customization: Map<String, dynamic>.from(json['customization'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enable_reminders': enableReminders,
      'reminder_times': reminderTimes,
      'enable_progress': enableProgress,
      'enable_certificate': enableCertificate,
      'reading_mode': readingMode,
      'customization': customization,
    };
  }
}

enum KhatmaType {
  thirtyDays,
  sixtyDays,
  custom,
  ramadan,
  weekly,
}

class QuranMemorization {
  final String id;
  final String name;
  final List<MemorizationSection> sections;
  final double progress;
  final DateTime startDate;
  final DateTime? targetDate;
  final MemorizationSettings settings;
  final List<RevisionSchedule> revisionSchedules;

  QuranMemorization({
    required this.id,
    required this.name,
    required this.sections,
    this.progress = 0.0,
    required this.startDate,
    this.targetDate,
    required this.settings,
    this.revisionSchedules = const [],
  });

  factory QuranMemorization.fromJson(Map<String, dynamic> json) {
    return QuranMemorization(
      id: json['id'],
      name: json['name'],
      sections: (json['sections'] as List)
          .map((s) => MemorizationSection.fromJson(s))
          .toList(),
      progress: json['progress']?.toDouble() ?? 0.0,
      startDate: DateTime.parse(json['start_date']),
      targetDate: json['target_date'] != null 
          ? DateTime.parse(json['target_date']) 
          : null,
      settings: MemorizationSettings.fromJson(json['settings']),
      revisionSchedules: (json['revision_schedules'] as List? ?? [])
          .map((r) => RevisionSchedule.fromJson(r))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'sections': sections.map((s) => s.toJson()).toList(),
      'progress': progress,
      'start_date': startDate.toIso8601String(),
      'target_date': targetDate?.toIso8601String(),
      'settings': settings.toJson(),
      'revision_schedules': revisionSchedules.map((r) => r.toJson()).toList(),
    };
  }
}

class MemorizationSection {
  final int surahId;
  final String surahName;
  final int fromAyah;
  final int toAyah;
  final MemorizationStatus status;
  final DateTime? memorizedDate;
  final int revisionCount;
  final DateTime? lastRevisionDate;
  final double accuracy;

  MemorizationSection({
    required this.surahId,
    required this.surahName,
    required this.fromAyah,
    required this.toAyah,
    this.status = MemorizationStatus.notStarted,
    this.memorizedDate,
    this.revisionCount = 0,
    this.lastRevisionDate,
    this.accuracy = 0.0,
  });

  factory MemorizationSection.fromJson(Map<String, dynamic> json) {
    return MemorizationSection(
      surahId: json['surah_id'],
      surahName: json['surah_name'],
      fromAyah: json['from_ayah'],
      toAyah: json['to_ayah'],
      status: MemorizationStatus.values[json['status'] ?? 0],
      memorizedDate: json['memorized_date'] != null 
          ? DateTime.parse(json['memorized_date']) 
          : null,
      revisionCount: json['revision_count'] ?? 0,
      lastRevisionDate: json['last_revision_date'] != null 
          ? DateTime.parse(json['last_revision_date']) 
          : null,
      accuracy: json['accuracy']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'surah_id': surahId,
      'surah_name': surahName,
      'from_ayah': fromAyah,
      'to_ayah': toAyah,
      'status': status.index,
      'memorized_date': memorizedDate?.toIso8601String(),
      'revision_count': revisionCount,
      'last_revision_date': lastRevisionDate?.toIso8601String(),
      'accuracy': accuracy,
    };
  }
}

class MemorizationSettings {
  final int dailyTarget; // Ayahs per day
  final bool enableRevisionReminders;
  final int revisionInterval; // Days
  final bool enableAccuracyTracking;

  MemorizationSettings({
    this.dailyTarget = 5,
    this.enableRevisionReminders = true,
    this.revisionInterval = 7,
    this.enableAccuracyTracking = true,
  });

  factory MemorizationSettings.fromJson(Map<String, dynamic> json) {
    return MemorizationSettings(
      dailyTarget: json['daily_target'] ?? 5,
      enableRevisionReminders: json['enable_revision_reminders'] ?? true,
      revisionInterval: json['revision_interval'] ?? 7,
      enableAccuracyTracking: json['enable_accuracy_tracking'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'daily_target': dailyTarget,
      'enable_revision_reminders': enableRevisionReminders,
      'revision_interval': revisionInterval,
      'enable_accuracy_tracking': enableAccuracyTracking,
    };
  }
}

class RevisionSchedule {
  final String id;
  final int surahId;
  final DateTime scheduledDate;
  final bool isCompleted;
  final DateTime? completedDate;
  final double? accuracy;

  RevisionSchedule({
    required this.id,
    required this.surahId,
    required this.scheduledDate,
    this.isCompleted = false,
    this.completedDate,
    this.accuracy,
  });

  factory RevisionSchedule.fromJson(Map<String, dynamic> json) {
    return RevisionSchedule(
      id: json['id'],
      surahId: json['surah_id'],
      scheduledDate: DateTime.parse(json['scheduled_date']),
      isCompleted: json['is_completed'] ?? false,
      completedDate: json['completed_date'] != null 
          ? DateTime.parse(json['completed_date']) 
          : null,
      accuracy: json['accuracy']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'surah_id': surahId,
      'scheduled_date': scheduledDate.toIso8601String(),
      'is_completed': isCompleted,
      'completed_date': completedDate?.toIso8601String(),
      'accuracy': accuracy,
    };
  }
}

enum MemorizationStatus {
  notStarted,
  inProgress,
  memorized,
  needsRevision,
}
