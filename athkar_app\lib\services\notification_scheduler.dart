import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart' show TimeOfDay;
import 'notification_service.dart';

class NotificationScheduler {
  static const int _morningNotificationId = 1000;
  static const int _eveningNotificationId = 1001;
  static const int _customNotificationBaseId = 2000;

  // Schedule daily athkar reminders
  static Future<void> scheduleDailyReminders({
    bool enableMorning = true,
    bool enableEvening = true,
    TimeOfDay morningTime = const TimeOfDay(hour: 7, minute: 0),
    TimeOfDay eveningTime = const TimeOfDay(hour: 18, minute: 0),
  }) async {
    try {
      // Cancel existing daily reminders
      await cancelDailyReminders();

      if (enableMorning) {
        await NotificationService.scheduleDailyNotification(
          id: _morningNotificationId,
          title: 'Morning Athkar Reminder',
          body: 'Start your day with morning athkar and duas',
          time: morningTime,
        );
      }

      if (enableEvening) {
        await NotificationService.scheduleDailyNotification(
          id: _eveningNotificationId,
          title: 'Evening Athkar Reminder',
          body: 'End your day with evening athkar and duas',
          time: eveningTime,
        );
      }
    } catch (e) {
      debugPrint('Error scheduling daily reminders: $e');
    }
  }

  // Schedule custom routine reminders
  static Future<void> scheduleRoutineReminder({
    required String routineId,
    required String routineTitle,
    required List<TimeOfDay> reminderTimes,
    List<int> weekdays = const [1, 2, 3, 4, 5, 6, 7], // Monday to Sunday
  }) async {
    try {
      // Cancel existing reminders for this routine
      await cancelRoutineReminders(routineId);

      for (int i = 0; i < reminderTimes.length; i++) {
        final time = reminderTimes[i];
        final notificationId = _getRoutineNotificationId(routineId, i);

        await NotificationService.scheduleWeeklyNotification(
          id: notificationId,
          title: 'Athkar Reminder',
          body: 'Time for your "$routineTitle" routine',
          time: time,
          daysOfWeek: weekdays,
        );
      }
    } catch (e) {
      debugPrint('Error scheduling routine reminder: $e');
    }
  }

  // Schedule tasbeeh reminders
  static Future<void> scheduleTasbeehReminders({
    required List<TimeOfDay> reminderTimes,
    String title = 'Tasbeeh Reminder',
    String body = 'Time for dhikr and remembrance of Allah',
  }) async {
    try {
      // Cancel existing tasbeeh reminders
      await cancelTasbeehReminders();

      for (int i = 0; i < reminderTimes.length; i++) {
        final time = reminderTimes[i];
        final notificationId = _customNotificationBaseId + 100 + i;

        await NotificationService.scheduleDailyNotification(
          id: notificationId,
          title: title,
          body: body,
          time: time,
        );
      }
    } catch (e) {
      debugPrint('Error scheduling tasbeeh reminders: $e');
    }
  }

  // Cancel all daily reminders
  static Future<void> cancelDailyReminders() async {
    try {
      await NotificationService.cancelNotification(_morningNotificationId);
      await NotificationService.cancelNotification(_eveningNotificationId);
    } catch (e) {
      debugPrint('Error canceling daily reminders: $e');
    }
  }

  // Cancel routine reminders
  static Future<void> cancelRoutineReminders(String routineId) async {
    try {
      // Cancel up to 10 reminder times per routine, 7 days each
      for (int i = 0; i < 10; i++) {
        final baseId = _getRoutineNotificationId(routineId, i);
        for (int weekday = 1; weekday <= 7; weekday++) {
          await NotificationService.cancelNotification(baseId + weekday);
        }
      }
    } catch (e) {
      debugPrint('Error canceling routine reminders: $e');
    }
  }

  // Cancel tasbeeh reminders
  static Future<void> cancelTasbeehReminders() async {
    try {
      // Cancel up to 10 tasbeeh reminder times
      for (int i = 0; i < 10; i++) {
        final notificationId = _customNotificationBaseId + 100 + i;
        await NotificationService.cancelNotification(notificationId);
      }
    } catch (e) {
      debugPrint('Error canceling tasbeeh reminders: $e');
    }
  }

  // Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    try {
      await NotificationService.cancelAllNotifications();
    } catch (e) {
      debugPrint('Error canceling all notifications: $e');
    }
  }

  // Private helper methods
  static int _getRoutineNotificationId(String routineId, int reminderIndex) {
    // Generate a unique ID based on routine ID hash and reminder index
    final routineHash = routineId.hashCode.abs() % 10000;
    return _customNotificationBaseId + routineHash + (reminderIndex * 100);
  }
}

enum RepeatInterval {
  daily,
  weekly,
  monthly,
}
