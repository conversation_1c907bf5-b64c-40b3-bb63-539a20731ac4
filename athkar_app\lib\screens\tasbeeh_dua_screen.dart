import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:provider/provider.dart';
import '../widgets/tasbeeh_counter_widget.dart';
import '../widgets/dua_list_widget.dart';
import '../providers/tasbeeh_dua_provider.dart';
import 'create_tasbeeh_dua_screen.dart';

class TasbeehDuaScreen extends StatefulWidget {
  const TasbeehDuaScreen({super.key});

  @override
  State<TasbeehDuaScreen> createState() => _TasbeehDuaScreenState();
}

class _TasbeehDuaScreenState extends State<TasbeehDuaScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tasbeeh & Dua'),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: Icon(MdiIcons.counter),
              text: 'Tasbeeh',
            ),
            Tab(
              icon: Icon(MdiIcons.handsPray),
              text: 'Duas',
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              _showSearch();
            },
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          TasbeehCounterWidget(),
          DuaListWidget(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: "tasbeeh_dua_fab",
        onPressed: () {
          _showCreateDialog();
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showSearch() {
    showSearch(
      context: context,
      delegate: TasbeehDuaSearchDelegate(),
    );
  }

  void _showCreateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New'),
        content: const Text('What would you like to create?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToCreateTasbeeh();
            },
            child: const Text('Tasbeeh'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToCreateDua();
            },
            child: const Text('Dua'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _navigateToCreateTasbeeh() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateTasbeehDuaScreen(type: CreateType.tasbeeh),
      ),
    );
  }

  void _navigateToCreateDua() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateTasbeehDuaScreen(type: CreateType.dua),
      ),
    );
  }
}

class TasbeehDuaSearchDelegate extends SearchDelegate<String> {
  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults();
  }

  Widget _buildSearchResults() {
    if (query.isEmpty) {
      return const Center(
        child: Text('Enter search terms to find tasbeeh or duas'),
      );
    }

    return Consumer<TasbeehDuaProvider>(
      builder: (context, provider, child) {
        final tasbeehResults = provider.searchTasbeeh(query);
        final duaResults = provider.searchDuas(query);

        if (tasbeehResults.isEmpty && duaResults.isEmpty) {
          return const Center(
            child: Text('No results found'),
          );
        }

        return ListView(
          children: [
            if (tasbeehResults.isNotEmpty) ...[
              const Padding(
                padding: EdgeInsets.all(16),
                child: Text(
                  'Tasbeeh Results',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ...tasbeehResults.map((item) => ListTile(
                title: Text(item.arabicText),
                subtitle: Text(item.transliteration ?? ''),
                trailing: Text(item.translation ?? ''),
                onTap: () {
                  close(context, item.arabicText);
                },
              )),
            ],
            if (duaResults.isNotEmpty) ...[
              const Padding(
                padding: EdgeInsets.all(16),
                child: Text(
                  'Dua Results',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ...duaResults.map((item) => ListTile(
                title: Text(item.title),
                subtitle: Text(item.arabicText),
                trailing: Text(item.translation ?? ''),
                onTap: () {
                  close(context, item.title);
                },
              )),
            ],
          ],
        );
      },
    );
  }
}
