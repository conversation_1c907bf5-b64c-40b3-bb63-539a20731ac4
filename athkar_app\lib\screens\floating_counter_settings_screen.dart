import 'package:flutter/material.dart';

class FloatingCounterSettingsScreen extends StatefulWidget {
  const FloatingCounterSettingsScreen({super.key});

  @override
  State<FloatingCounterSettingsScreen> createState() => _FloatingCounterSettingsScreenState();
}

class _FloatingCounterSettingsScreenState extends State<FloatingCounterSettingsScreen> {
  bool _enableFloatingCounter = true;
  bool _showOnLockScreen = false;
  bool _enableVibration = true;
  bool _enableSound = false;
  double _counterSize = 80.0;
  double _transparency = 0.8;
  Color _counterColor = Colors.green;
  String _selectedPosition = 'bottom_right';

  final List<String> _positions = [
    'top_left',
    'top_right',
    'bottom_left',
    'bottom_right',
    'center',
  ];

  final Map<String, String> _positionLabels = {
    'top_left': 'Top Left',
    'top_right': 'Top Right',
    'bottom_left': 'Bottom Left',
    'bottom_right': 'Bottom Right',
    'center': 'Center',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Floating Counter Settings'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Enable/Disable Floating Counter
          Card(
            child: SwitchListTile(
              title: const Text('Enable Floating Counter'),
              subtitle: const Text('Show floating dhikr counter overlay'),
              value: _enableFloatingCounter,
              onChanged: (value) {
                setState(() {
                  _enableFloatingCounter = value;
                });
              },
            ),
          ),

          const SizedBox(height: 16),

          // Counter Appearance
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Appearance',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),

                  // Counter Size
                  Text('Counter Size: ${_counterSize.round()}px'),
                  Slider(
                    value: _counterSize,
                    min: 60.0,
                    max: 120.0,
                    divisions: 12,
                    onChanged: _enableFloatingCounter ? (value) {
                      setState(() {
                        _counterSize = value;
                      });
                    } : null,
                  ),

                  const SizedBox(height: 16),

                  // Transparency
                  Text('Transparency: ${(_transparency * 100).round()}%'),
                  Slider(
                    value: _transparency,
                    min: 0.3,
                    max: 1.0,
                    divisions: 7,
                    onChanged: _enableFloatingCounter ? (value) {
                      setState(() {
                        _transparency = value;
                      });
                    } : null,
                  ),

                  const SizedBox(height: 16),

                  // Counter Color
                  const Text('Counter Color'),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [
                      Colors.green,
                      Colors.blue,
                      Colors.purple,
                      Colors.orange,
                      Colors.red,
                      Colors.teal,
                    ].map((color) {
                      return GestureDetector(
                        onTap: _enableFloatingCounter ? () {
                          setState(() {
                            _counterColor = color;
                          });
                        } : null,
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: color,
                            shape: BoxShape.circle,
                            border: _counterColor == color
                                ? Border.all(color: Colors.black, width: 3)
                                : null,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Position Settings
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Position',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _selectedPosition,
                    decoration: const InputDecoration(
                      labelText: 'Counter Position',
                      border: OutlineInputBorder(),
                    ),
                    items: _positions.map((position) {
                      return DropdownMenuItem(
                        value: position,
                        child: Text(_positionLabels[position] ?? position),
                      );
                    }).toList(),
                    onChanged: _enableFloatingCounter ? (value) {
                      if (value != null) {
                        setState(() {
                          _selectedPosition = value;
                        });
                      }
                    } : null,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Behavior Settings
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Show on Lock Screen'),
                  subtitle: const Text('Display counter even when screen is locked'),
                  value: _showOnLockScreen,
                  onChanged: _enableFloatingCounter ? (value) {
                    setState(() {
                      _showOnLockScreen = value;
                    });
                  } : null,
                ),
                SwitchListTile(
                  title: const Text('Enable Vibration'),
                  subtitle: const Text('Vibrate on counter tap'),
                  value: _enableVibration,
                  onChanged: _enableFloatingCounter ? (value) {
                    setState(() {
                      _enableVibration = value;
                    });
                  } : null,
                ),
                SwitchListTile(
                  title: const Text('Enable Sound'),
                  subtitle: const Text('Play sound on counter tap'),
                  value: _enableSound,
                  onChanged: _enableFloatingCounter ? (value) {
                    setState(() {
                      _enableSound = value;
                    });
                  } : null,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Save Button
          ElevatedButton(
            onPressed: () {
              // Save settings
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Floating counter settings saved!'),
                ),
              );
              Navigator.pop(context);
            },
            child: const Text('Save Settings'),
          ),
        ],
      ),
    );
  }
}
