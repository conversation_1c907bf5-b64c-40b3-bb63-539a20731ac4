import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/permissions_service.dart';
import '../services/language_service.dart';
import '../l10n/app_localizations.dart';
import '../theme/app_theme.dart';
import '../widgets/loading_overlay.dart';

class PermissionsRequestScreen extends StatefulWidget {
  const PermissionsRequestScreen({super.key});

  @override
  State<PermissionsRequestScreen> createState() => _PermissionsRequestScreenState();
}

class _PermissionsRequestScreenState extends State<PermissionsRequestScreen> {
  bool _isLoading = false;
  Map<String, bool> _permissionStatus = {};
  int _currentStep = 0;
  
  final List<Map<String, dynamic>> _permissionSteps = [
    {
      'key': 'notifications',
      'icon': Icons.notifications,
      'titleAr': 'إذن الإشعارات',
      'titleEn': 'Notification Permission',
      'descriptionAr': 'يحتاج التطبيق إلى إذن الإشعارات لإرسال تذكيرات الأذكار وأوقات الصلاة في الأوقات المحددة.',
      'descriptionEn': 'The app needs notification permission to send athkar reminders and prayer time notifications at scheduled times.',
      'required': true,
    },
    {
      'key': 'location',
      'icon': Icons.location_on,
      'titleAr': 'إذن الموقع',
      'titleEn': 'Location Permission',
      'descriptionAr': 'يحتاج التطبيق إلى إذن الموقع لتحديد أوقات الصلاة الدقيقة واتجاه القبلة حسب موقعك.',
      'descriptionEn': 'The app needs location permission to determine accurate prayer times and Qibla direction based on your location.',
      'required': true,
    },
    {
      'key': 'overlay',
      'icon': Icons.picture_in_picture,
      'titleAr': 'إذن النوافذ العائمة',
      'titleEn': 'Overlay Permission',
      'descriptionAr': 'يحتاج التطبيق إلى إذن النوافذ العائمة لعرض عدادات الذكر خارج التطبيق (اختياري).',
      'descriptionEn': 'The app needs overlay permission to display dhikr counters outside the app (optional).',
      'required': false,
    },
    {
      'key': 'storage',
      'icon': Icons.storage,
      'titleAr': 'إذن التخزين',
      'titleEn': 'Storage Permission',
      'descriptionAr': 'يحتاج التطبيق إلى إذن التخزين لحفظ الأصوات المخصصة والنسخ الاحتياطية.',
      'descriptionEn': 'The app needs storage permission to save custom sounds and backup files.',
      'required': false,
    },
  ];

  @override
  void initState() {
    super.initState();
    _checkInitialPermissions();
  }

  Future<void> _checkInitialPermissions() async {
    setState(() => _isLoading = true);
    
    try {
      final status = await PermissionsService.getPermissionsStatus();
      setState(() {
        _permissionStatus = status;
      });
    } catch (e) {
      debugPrint('Error checking permissions: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _requestPermission(String permissionKey) async {
    setState(() => _isLoading = true);
    
    try {
      bool granted = false;
      
      switch (permissionKey) {
        case 'notifications':
          granted = await PermissionsService.requestNotificationPermissionWithGuidance(context);
          break;
        case 'location':
          granted = await PermissionsService.requestLocationPermissionWithGuidance(context);
          break;
        case 'overlay':
          granted = await PermissionsService.requestOverlayPermissionWithGuidance(context);
          break;
        case 'storage':
          granted = await PermissionsService.requestStoragePermission();
          break;
      }
      
      setState(() {
        _permissionStatus[permissionKey] = granted;
      });
      
      if (granted) {
        _nextStep();
      }
    } catch (e) {
      debugPrint('Error requesting permission: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _nextStep() {
    if (_currentStep < _permissionSteps.length - 1) {
      setState(() => _currentStep++);
    } else {
      _completeSetup();
    }
  }

  void _skipStep() {
    final currentPermission = _permissionSteps[_currentStep];
    if (!currentPermission['required']) {
      _nextStep();
    }
  }

  void _completeSetup() {
    // Check if all required permissions are granted
    final requiredPermissions = _permissionSteps.where((p) => p['required']).toList();
    final allRequiredGranted = requiredPermissions.every((p) => 
        _permissionStatus[p['key']] == true);
    
    if (allRequiredGranted) {
      Navigator.of(context).pushReplacementNamed('/home');
    } else {
      // Show error dialog
      _showRequiredPermissionsDialog();
    }
  }

  void _showRequiredPermissionsDialog() {
    final languageService = Provider.of<LanguageService>(context, listen: false);
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(
          languageService.isArabic ? 'صلاحيات مطلوبة' : 'Required Permissions',
        ),
        content: Text(
          languageService.isArabic 
              ? 'يحتاج التطبيق إلى الصلاحيات المطلوبة للعمل بشكل صحيح. يرجى منح الصلاحيات المطلوبة للمتابعة.'
              : 'The app needs required permissions to function properly. Please grant the required permissions to continue.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() => _currentStep = 0);
            },
            child: Text(languageService.isArabic ? 'إعادة المحاولة' : 'Retry'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final languageService = Provider.of<LanguageService>(context);
    final localizations = AppLocalizations.of(context)!;
    
    if (_permissionSteps.isEmpty) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }
    
    final currentPermission = _permissionSteps[_currentStep];
    final isGranted = _permissionStatus[currentPermission['key']] == true;
    
    return Directionality(
      textDirection: languageService.textDirection,
      child: Scaffold(
        body: LoadingOverlay(
          isLoading: _isLoading,
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  // Progress indicator
                  LinearProgressIndicator(
                    value: (_currentStep + 1) / _permissionSteps.length,
                    backgroundColor: Colors.grey[300],
                    valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Text(
                    '${_currentStep + 1} ${languageService.isArabic ? "من" : "of"} ${_permissionSteps.length}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // App logo
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGreen,
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: const Icon(
                      Icons.mosque,
                      color: Colors.white,
                      size: 50,
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  Text(
                    localizations.appTitle,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryGreen,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  Text(
                    languageService.isArabic ? 'إعداد الصلاحيات' : 'Permission Setup',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                  
                  const SizedBox(height: 48),
                  
                  // Permission card
                  Expanded(
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: Column(
                          children: [
                            // Permission icon
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: isGranted ? Colors.green[100] : AppTheme.primaryGreen.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(40),
                              ),
                              child: Icon(
                                isGranted ? Icons.check_circle : currentPermission['icon'],
                                color: isGranted ? Colors.green : AppTheme.primaryGreen,
                                size: 40,
                              ),
                            ),
                            
                            const SizedBox(height: 24),
                            
                            // Permission title
                            Text(
                              languageService.isArabic 
                                  ? currentPermission['titleAr']
                                  : currentPermission['titleEn'],
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            
                            const SizedBox(height: 16),
                            
                            // Permission description
                            Expanded(
                              child: Text(
                                languageService.isArabic 
                                    ? currentPermission['descriptionAr']
                                    : currentPermission['descriptionEn'],
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey[600],
                                  height: 1.5,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            
                            const SizedBox(height: 24),
                            
                            // Required/Optional badge
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: currentPermission['required'] 
                                    ? Colors.red[100] 
                                    : Colors.blue[100],
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                currentPermission['required']
                                    ? (languageService.isArabic ? 'مطلوب' : 'Required')
                                    : (languageService.isArabic ? 'اختياري' : 'Optional'),
                                style: TextStyle(
                                  color: currentPermission['required'] 
                                      ? Colors.red[700] 
                                      : Colors.blue[700],
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Action buttons
                  Row(
                    children: [
                      if (!currentPermission['required'])
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _skipStep,
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.grey[600],
                              side: BorderSide(color: Colors.grey[400]!),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: Text(languageService.isArabic ? 'تخطي' : 'Skip'),
                          ),
                        ),
                      
                      if (!currentPermission['required'])
                        const SizedBox(width: 16),
                      
                      Expanded(
                        flex: currentPermission['required'] ? 1 : 2,
                        child: ElevatedButton(
                          onPressed: isGranted ? _nextStep : () => _requestPermission(currentPermission['key']),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isGranted ? Colors.green : AppTheme.primaryGreen,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: Text(
                            isGranted 
                                ? (languageService.isArabic ? 'التالي' : 'Next')
                                : (languageService.isArabic ? 'منح الإذن' : 'Grant Permission'),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
