import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/analytics_models.dart';
import '../database/database_helper.dart';

class AnalyticsService {

  
  static final DatabaseHelper _dbHelper = DatabaseHelper();
  static SharedPreferences? _prefs;
  
  // Initialize analytics service
  static Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _createAnalyticsTables();
      await _startSession();
    } catch (e) {
      debugPrint('Error initializing analytics service: $e');
    }
  }

  static Future<void> _createAnalyticsTables() async {
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS user_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        start_time INTEGER,
        end_time INTEGER,
        duration INTEGER,
        screen_views INTEGER,
        actions_count INTEGER,
        device_info TEXT,
        app_version TEXT,
        created_at INTEGER
      )
    ''');

    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS user_actions (
        id TEXT PRIMARY KEY,
        session_id TEXT,
        user_id TEXT,
        action_type TEXT,
        action_name TEXT,
        screen_name TEXT,
        parameters TEXT,
        timestamp INTEGER,
        created_at INTEGER
      )
    ''');

    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS app_performance (
        id TEXT PRIMARY KEY,
        session_id TEXT,
        metric_name TEXT,
        metric_value REAL,
        timestamp INTEGER,
        metadata TEXT,
        created_at INTEGER
      )
    ''');

    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS user_preferences (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        preference_key TEXT,
        preference_value TEXT,
        timestamp INTEGER,
        created_at INTEGER
      )
    ''');
  }

  // Session Management
  static String? _currentSessionId;
  static DateTime? _sessionStartTime;

  static Future<void> _startSession() async {
    _currentSessionId = _generateId();
    _sessionStartTime = DateTime.now();
    
    final session = UserSession(
      id: _currentSessionId!,
      userId: await _getCurrentUserId(),
      startTime: _sessionStartTime!,
      deviceInfo: await _getDeviceInfo(),
      appVersion: await _getAppVersion(),
    );

    await _saveSession(session);
  }

  static Future<void> endSession() async {
    if (_currentSessionId == null || _sessionStartTime == null) return;

    final endTime = DateTime.now();
    final duration = endTime.difference(_sessionStartTime!);

    await _dbHelper.update(
      'user_sessions',
      {
        'end_time': endTime.millisecondsSinceEpoch,
        'duration': duration.inSeconds,
      },
      where: 'id = ?',
      whereArgs: [_currentSessionId!],
    );

    _currentSessionId = null;
    _sessionStartTime = null;
  }

  // Event Tracking
  static Future<void> trackEvent(String eventName, {
    Map<String, dynamic>? parameters,
    String? screenName,
  }) async {
    try {
      final action = UserAction(
        id: _generateId(),
        sessionId: _currentSessionId ?? 'unknown',
        userId: await _getCurrentUserId(),
        actionType: 'event',
        actionName: eventName,
        screenName: screenName,
        parameters: parameters ?? {},
        timestamp: DateTime.now(),
      );

      await _saveAction(action);
    } catch (e) {
      debugPrint('Error tracking event: $e');
    }
  }

  static Future<void> trackScreenView(String screenName) async {
    await trackEvent('screen_view', parameters: {'screen_name': screenName}, screenName: screenName);
  }

  static Future<void> trackUserAction(String actionName, {
    Map<String, dynamic>? parameters,
    String? screenName,
  }) async {
    await trackEvent(actionName, parameters: parameters, screenName: screenName);
  }

  // Performance Tracking
  static Future<void> trackPerformance(String metricName, double value, {
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final performance = AppPerformance(
        id: _generateId(),
        sessionId: _currentSessionId ?? 'unknown',
        metricName: metricName,
        metricValue: value,
        timestamp: DateTime.now(),
        metadata: metadata ?? {},
      );

      await _savePerformance(performance);
    } catch (e) {
      debugPrint('Error tracking performance: $e');
    }
  }

  // User Behavior Analytics
  static Future<UserBehaviorAnalytics> getUserBehaviorAnalytics(String userId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final start = startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    final sessions = await _getUserSessions(userId, start, end);
    final actions = await _getUserActions(userId, start, end);

    return UserBehaviorAnalytics(
      userId: userId,
      totalSessions: sessions.length,
      totalActions: actions.length,
      averageSessionDuration: _calculateAverageSessionDuration(sessions),
      mostUsedFeatures: _getMostUsedFeatures(actions),
      screenTimeDistribution: _getScreenTimeDistribution(actions),
      engagementScore: _calculateEngagementScore(sessions, actions),
      retentionRate: await _calculateRetentionRate(userId),
      preferredTimes: _getPreferredUsageTimes(sessions),
    );
  }

  // Business Intelligence
  static Future<BusinessIntelligence> getBusinessIntelligence({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final start = startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    final allSessions = await _getAllSessions(start, end);
    final allActions = await _getAllActions(start, end);

    return BusinessIntelligence(
      totalUsers: await _getTotalUsers(),
      activeUsers: await _getActiveUsers(start, end),
      newUsers: await _getNewUsers(start, end),
      sessionCount: allSessions.length,
      averageSessionDuration: _calculateAverageSessionDuration(allSessions),
      bounceRate: _calculateBounceRate(allSessions),
      retentionRates: await _calculateRetentionRates(),
      featureUsage: _getFeatureUsage(allActions),
      userGrowth: await _getUserGrowth(start, end),
      engagementMetrics: _getEngagementMetrics(allSessions, allActions),
    );
  }

  // Helper Methods
  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString() + 
           math.Random().nextInt(1000).toString();
  }

  static Future<String> _getCurrentUserId() async {
    // Get from auth service
    try {
      // Try to get from shared preferences first
      final storedUserId = _prefs?.getString('current_user_id');
      if (storedUserId != null && storedUserId.isNotEmpty) {
        return storedUserId;
      }

      // Generate a unique anonymous user ID if none exists
      final anonymousId = 'anon_${DateTime.now().millisecondsSinceEpoch}';
      await _prefs?.setString('current_user_id', anonymousId);
      return anonymousId;
    } catch (e) {
      return 'anonymous';
    }
  }

  static Future<String> _getDeviceInfo() async {
    // Get actual device info
    try {
      // In a real app, you would use device_info_plus package
      // For now, return platform-specific info
      if (Platform.isAndroid) {
        return 'Android Device';
      } else if (Platform.isIOS) {
        return 'iOS Device';
      } else if (Platform.isWindows) {
        return 'Windows Device';
      } else if (Platform.isMacOS) {
        return 'macOS Device';
      } else if (Platform.isLinux) {
        return 'Linux Device';
      } else {
        return 'Unknown Device';
      }
    } catch (e) {
      return 'Unknown Device';
    }
  }

  static Future<String> _getAppVersion() async {
    // Get from package info
    try {
      // In a real app, you would use package_info_plus package
      // For now, return a default version
      return '1.0.0+1';
    } catch (e) {
      return '1.0.0';
    }
  }

  static Future<void> _saveSession(UserSession session) async {
    await _dbHelper.insert('user_sessions', session.toJson());
  }

  static Future<void> _saveAction(UserAction action) async {
    await _dbHelper.insert('user_actions', action.toJson());
  }

  static Future<void> _savePerformance(AppPerformance performance) async {
    await _dbHelper.insert('app_performance', performance.toJson());
  }

  static Future<List<UserSession>> _getUserSessions(String userId, DateTime start, DateTime end) async {
    final results = await _dbHelper.query(
      'user_sessions',
      where: 'user_id = ? AND start_time >= ? AND start_time <= ?',
      whereArgs: [userId, start.millisecondsSinceEpoch, end.millisecondsSinceEpoch],
    );
    return results.map((json) => UserSession.fromJson(json)).toList();
  }

  static Future<List<UserAction>> _getUserActions(String userId, DateTime start, DateTime end) async {
    final results = await _dbHelper.query(
      'user_actions',
      where: 'user_id = ? AND timestamp >= ? AND timestamp <= ?',
      whereArgs: [userId, start.millisecondsSinceEpoch, end.millisecondsSinceEpoch],
    );
    return results.map((json) => UserAction.fromJson(json)).toList();
  }

  static Future<List<UserSession>> _getAllSessions(DateTime start, DateTime end) async {
    final results = await _dbHelper.query(
      'user_sessions',
      where: 'start_time >= ? AND start_time <= ?',
      whereArgs: [start.millisecondsSinceEpoch, end.millisecondsSinceEpoch],
    );
    return results.map((json) => UserSession.fromJson(json)).toList();
  }

  static Future<List<UserAction>> _getAllActions(DateTime start, DateTime end) async {
    final results = await _dbHelper.query(
      'user_actions',
      where: 'timestamp >= ? AND timestamp <= ?',
      whereArgs: [start.millisecondsSinceEpoch, end.millisecondsSinceEpoch],
    );
    return results.map((json) => UserAction.fromJson(json)).toList();
  }

  static Duration _calculateAverageSessionDuration(List<UserSession> sessions) {
    if (sessions.isEmpty) return Duration.zero;
    
    final totalDuration = sessions
        .where((s) => s.duration != null)
        .fold<int>(0, (sum, session) => sum + session.duration!.inSeconds);
    
    return Duration(seconds: totalDuration ~/ sessions.length);
  }

  static List<String> _getMostUsedFeatures(List<UserAction> actions) {
    final featureCounts = <String, int>{};
    
    for (final action in actions) {
      featureCounts[action.actionName] = (featureCounts[action.actionName] ?? 0) + 1;
    }
    
    final sortedFeatures = featureCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedFeatures.take(10).map((e) => e.key).toList();
  }

  static Map<String, Duration> _getScreenTimeDistribution(List<UserAction> actions) {
    final screenTimes = <String, Duration>{};
    
    for (final action in actions) {
      if (action.screenName != null) {
        screenTimes[action.screenName!] = (screenTimes[action.screenName!] ?? Duration.zero) + 
                                         const Duration(seconds: 1);
      }
    }
    
    return screenTimes;
  }

  static double _calculateEngagementScore(List<UserSession> sessions, List<UserAction> actions) {
    if (sessions.isEmpty) return 0.0;
    
    final avgSessionDuration = _calculateAverageSessionDuration(sessions).inMinutes;
    final actionsPerSession = actions.length / sessions.length;
    
    // Engagement score based on session duration and actions
    return math.min(100.0, (avgSessionDuration * 2 + actionsPerSession * 5));
  }

  static Future<double> _calculateRetentionRate(String userId) async {
    // Implement retention rate calculation
    try {
      final db = await _dbHelper.database;

      // Get user's first activity date
      final firstActivity = await db.query(
        'user_events',
        where: 'user_id = ?',
        whereArgs: [userId],
        orderBy: 'timestamp ASC',
        limit: 1,
      );

      if (firstActivity.isEmpty) return 0.0;

      final firstDate = DateTime.parse(firstActivity.first['timestamp'] as String);
      final daysSinceFirst = DateTime.now().difference(firstDate).inDays;

      if (daysSinceFirst < 7) return 1.0; // Too early to calculate

      // Count active days in the last 30 days
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      final activeDays = await db.query(
        'user_events',
        where: 'user_id = ? AND timestamp >= ?',
        whereArgs: [userId, thirtyDaysAgo.toIso8601String()],
      );

      // Calculate retention as percentage of active days
      final uniqueDays = activeDays
          .map((e) => DateTime.parse(e['timestamp'] as String).day)
          .toSet()
          .length;

      return (uniqueDays / 30.0).clamp(0.0, 1.0);
    } catch (e) {
      return 0.0;
    }
  }

  static List<int> _getPreferredUsageTimes(List<UserSession> sessions) {
    final hourCounts = List<int>.filled(24, 0);
    
    for (final session in sessions) {
      final hour = session.startTime.hour;
      hourCounts[hour]++;
    }
    
    return hourCounts;
  }

  static Future<int> _getTotalUsers() async {
    final result = await _dbHelper.rawQuery('SELECT COUNT(DISTINCT user_id) as count FROM user_sessions');
    return result.first['count'] as int? ?? 0;
  }

  static Future<int> _getActiveUsers(DateTime start, DateTime end) async {
    final result = await _dbHelper.rawQuery(
      'SELECT COUNT(DISTINCT user_id) as count FROM user_sessions WHERE start_time >= ? AND start_time <= ?',
      [start.millisecondsSinceEpoch, end.millisecondsSinceEpoch],
    );
    return result.first['count'] as int? ?? 0;
  }

  static Future<int> _getNewUsers(DateTime start, DateTime end) async {
    // Implement new users calculation
    try {
      final db = await _dbHelper.database;

      final result = await db.query(
        'user_events',
        where: 'event_type = ? AND timestamp >= ? AND timestamp <= ?',
        whereArgs: ['user_registered', start.toIso8601String(), end.toIso8601String()],
      );

      // Count unique users
      final uniqueUsers = result
          .map((e) => e['user_id'] as String)
          .toSet()
          .length;

      return uniqueUsers;
    } catch (e) {
      return 0;
    }
  }

  static double _calculateBounceRate(List<UserSession> sessions) {
    if (sessions.isEmpty) return 0.0;
    
    final shortSessions = sessions.where((s) => 
      s.duration != null && s.duration!.inSeconds < 30).length;
    
    return (shortSessions / sessions.length) * 100;
  }

  static Future<Map<String, double>> _calculateRetentionRates() async {
    // Implement retention rates calculation
    try {
      final db = await _dbHelper.database;

      // Get all users
      final users = await db.query('user_events', distinct: true, columns: ['user_id']);

      final retentionRates = <String, double>{};

      for (final user in users) {
        final userId = user['user_id'] as String;
        final rate = await _calculateRetentionRate(userId);
        retentionRates[userId] = rate;
      }

      // Calculate average retention rates by time period
      final now = DateTime.now();
      final periods = {
        '7_day': now.subtract(const Duration(days: 7)),
        '30_day': now.subtract(const Duration(days: 30)),
        '90_day': now.subtract(const Duration(days: 90)),
      };

      final averageRates = <String, double>{};
      for (final entry in periods.entries) {
        final rates = retentionRates.values.where((rate) => rate > 0).toList();
        averageRates[entry.key] = rates.isEmpty ? 0.0 : rates.reduce((a, b) => a + b) / rates.length;
      }

      return averageRates;
    } catch (e) {
      return {};
    }
  }

  static Map<String, int> _getFeatureUsage(List<UserAction> actions) {
    final featureUsage = <String, int>{};
    
    for (final action in actions) {
      featureUsage[action.actionName] = (featureUsage[action.actionName] ?? 0) + 1;
    }
    
    return featureUsage;
  }

  static Future<List<int>> _getUserGrowth(DateTime start, DateTime end) async {
    // Implement user growth calculation
    try {
      final db = await _dbHelper.database;
      final growth = <int>[];

      // Calculate daily growth between start and end dates
      var currentDate = start;
      while (currentDate.isBefore(end) || currentDate.isAtSameMomentAs(end)) {
        final nextDate = currentDate.add(const Duration(days: 1));

        final newUsers = await db.query(
          'user_events',
          where: 'event_type = ? AND timestamp >= ? AND timestamp < ?',
          whereArgs: [
            'user_registered',
            currentDate.toIso8601String(),
            nextDate.toIso8601String(),
          ],
        );

        final uniqueNewUsers = newUsers
            .map((e) => e['user_id'] as String)
            .toSet()
            .length;

        growth.add(uniqueNewUsers);
        currentDate = nextDate;
      }

      return growth;
    } catch (e) {
      return [];
    }
  }

  static Map<String, double> _getEngagementMetrics(List<UserSession> sessions, List<UserAction> actions) {
    return {
      'average_session_duration': _calculateAverageSessionDuration(sessions).inMinutes.toDouble(),
      'actions_per_session': sessions.isEmpty ? 0.0 : actions.length / sessions.length,
      'bounce_rate': _calculateBounceRate(sessions),
    };
  }

  // Export analytics data
  static Future<String> exportAnalyticsData({
    DateTime? startDate,
    DateTime? endDate,
    String format = 'json',
  }) async {
    final start = startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    final sessions = await _getAllSessions(start, end);
    final actions = await _getAllActions(start, end);
    final businessIntelligence = await getBusinessIntelligence(startDate: start, endDate: end);

    final data = {
      'export_date': DateTime.now().toIso8601String(),
      'date_range': {
        'start': start.toIso8601String(),
        'end': end.toIso8601String(),
      },
      'sessions': sessions.map((s) => s.toJson()).toList(),
      'actions': actions.map((a) => a.toJson()).toList(),
      'business_intelligence': businessIntelligence.toJson(),
    };

    return json.encode(data);
  }

  // Clear analytics data
  static Future<void> clearAnalyticsData({DateTime? beforeDate}) async {
    final cutoffDate = beforeDate ?? DateTime.now().subtract(const Duration(days: 90));
    
    await _dbHelper.delete(
      'user_sessions',
      where: 'start_time < ?',
      whereArgs: [cutoffDate.millisecondsSinceEpoch],
    );
    
    await _dbHelper.delete(
      'user_actions',
      where: 'timestamp < ?',
      whereArgs: [cutoffDate.millisecondsSinceEpoch],
    );
    
    await _dbHelper.delete(
      'app_performance',
      where: 'timestamp < ?',
      whereArgs: [cutoffDate.millisecondsSinceEpoch],
    );
  }
}
