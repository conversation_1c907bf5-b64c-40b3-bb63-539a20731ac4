import 'package:flutter/foundation.dart';

class AppConfig {
  // App Information
  static const String appName = 'Athkar - Islamic Remembrance';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';
  
  // Environment Configuration
  static const bool isProduction = kReleaseMode;
  static const bool isDevelopment = kDebugMode;
  
  // API Configuration
  static const String baseApiUrl = isProduction 
      ? 'https://api.athkar-app.com/v1'
      : 'https://dev-api.athkar-app.com/v1';
  
  // Supabase Configuration
  static const String supabaseUrl = isProduction
      ? 'https://your-project.supabase.co'
      : 'https://your-dev-project.supabase.co';
  
  static const String supabaseAnonKey = isProduction
      ? 'your-production-anon-key'
      : 'your-development-anon-key';
  
  // Quran API Configuration
  static const String quranApiUrl = 'https://api.quran.com/api/v4';
  static const String quranAudioUrl = 'https://verses.quran.com';
  
  // Prayer Times API
  static const String prayerTimesApiUrl = 'https://api.aladhan.com/v1';
  
  // Islamic Calendar API
  static const String islamicCalendarApiUrl = 'https://api.aladhan.com/v1';
  
  // Analytics Configuration
  static const bool enableAnalytics = isProduction;
  static const bool enableCrashlytics = isProduction;
  static const bool enablePerformanceMonitoring = isProduction;
  
  // Firebase Configuration (if using Firebase)
  static const String firebaseProjectId = isProduction
      ? 'athkar-app-prod'
      : 'athkar-app-dev';
  
  // Push Notifications
  static const bool enablePushNotifications = true;
  static const String fcmSenderId = '123456789';
  
  // Social Login Configuration
  static const String googleClientId = isProduction
      ? 'your-prod-google-client-id'
      : 'your-dev-google-client-id';
  
  static const String appleClientId = isProduction
      ? 'your-prod-apple-client-id'
      : 'your-dev-apple-client-id';
  
  // In-App Purchase Configuration
  static const bool enableInAppPurchases = isProduction;
  static const String playStoreKey = 'your-play-store-key';
  static const String appStoreKey = 'your-app-store-key';
  
  // Premium Subscription Plans
  static const Map<String, String> subscriptionPlans = {
    'monthly': isProduction ? 'athkar_monthly_prod' : 'athkar_monthly_dev',
    'yearly': isProduction ? 'athkar_yearly_prod' : 'athkar_yearly_dev',
    'lifetime': isProduction ? 'athkar_lifetime_prod' : 'athkar_lifetime_dev',
  };
  
  // Database Configuration
  static const String databaseName = 'athkar_app.db';
  static const int databaseVersion = 5;
  
  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  
  // Network Configuration
  static const Duration networkTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;
  
  // Security Configuration
  static const bool enableBiometricAuth = true;
  static const bool enableEncryption = true;
  static const Duration sessionTimeout = Duration(hours: 24);
  
  // Feature Flags
  static const bool enableCommunityFeatures = true;
  static const bool enableEnterpriseFeatures = true;
  static const bool enableAIRecommendations = true;
  static const bool enableQiblaFinder = true;
  static const bool enablePrayerTimes = true;
  static const bool enableQuranIntegration = true;
  static const bool enableOfflineMode = true;
  static const bool enableDarkMode = true;
  static const bool enableCustomThemes = true;
  static const bool enableFloatingWidget = true;
  static const bool enableVoiceRecognition = false; // Future feature
  static const bool enableAugmentedReality = false; // Future feature
  
  // Logging Configuration
  static const bool enableLogging = isDevelopment;
  static const bool enableVerboseLogging = isDevelopment;
  
  // Performance Configuration
  static const int maxConcurrentRequests = 5;
  static const Duration backgroundTaskTimeout = Duration(minutes: 5);
  
  // UI Configuration
  static const double defaultFontSize = 16.0;
  static const double minFontSize = 12.0;
  static const double maxFontSize = 24.0;
  static const Duration animationDuration = Duration(milliseconds: 300);
  
  // Notification Configuration
  static const Duration defaultNotificationInterval = Duration(hours: 1);
  static const int maxDailyNotifications = 10;
  
  // Backup Configuration
  static const Duration autoBackupInterval = Duration(days: 1);
  static const int maxBackupRetention = 30; // days
  
  // Content Configuration
  static const int maxCustomAthkarFree = 5;
  static const int maxCustomDuasFree = 10;
  static const int maxBackupsFree = 1;
  
  // Social Features Configuration
  static const int maxCommunityMembers = 1000;
  static const int maxPostLength = 500;
  static const int maxCommentLength = 200;
  
  // Enterprise Configuration
  static const int maxOrganizationUsers = 10000;
  static const int maxDepartments = 100;
  static const int maxTeams = 500;
  
  // Localization Configuration
  static const List<String> supportedLanguages = [
    'en', // English
    'ar', // Arabic
    'ur', // Urdu
    'tr', // Turkish
    'id', // Indonesian
    'ms', // Malay
    'fr', // French
    'de', // German
    'es', // Spanish
    'ru', // Russian
  ];
  
  static const String defaultLanguage = 'en';
  static const String fallbackLanguage = 'en';
  
  // Audio Configuration
  static const List<String> supportedAudioFormats = ['mp3', 'wav', 'aac'];
  static const double defaultPlaybackSpeed = 1.0;
  static const double minPlaybackSpeed = 0.5;
  static const double maxPlaybackSpeed = 2.0;
  
  // Image Configuration
  static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png', 'webp'];
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int thumbnailSize = 200;
  
  // Video Configuration
  static const List<String> supportedVideoFormats = ['mp4', 'mov', 'avi'];
  static const int maxVideoSize = 50 * 1024 * 1024; // 50MB
  static const Duration maxVideoDuration = Duration(minutes: 10);
  
  // Export Configuration
  static const List<String> supportedExportFormats = ['json', 'csv', 'pdf', 'xlsx'];
  static const int maxExportRecords = 10000;
  
  // Search Configuration
  static const int maxSearchResults = 100;
  static const Duration searchDebounceDelay = Duration(milliseconds: 500);
  
  // Pagination Configuration
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Rate Limiting Configuration
  static const int maxApiRequestsPerMinute = 60;
  static const int maxApiRequestsPerHour = 1000;
  
  // Error Handling Configuration
  static const int maxErrorRetries = 3;
  static const Duration errorRetryDelay = Duration(seconds: 2);
  
  // Development Configuration
  static const bool enableDebugMode = isDevelopment;
  static const bool enableTestMode = isDevelopment;
  static const bool enableMockData = isDevelopment;
  
  // Legal Configuration
  static const String privacyPolicyUrl = 'https://athkar-app.com/privacy-policy';
  static const String termsOfServiceUrl = 'https://athkar-app.com/terms-of-service';
  static const String supportEmail = '<EMAIL>';
  static const String contactEmail = '<EMAIL>';
  static const String websiteUrl = 'https://athkar-app.com';
  
  // App Store Configuration
  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.athkarapp.athkar';
  static const String appStoreUrl = 'https://apps.apple.com/app/athkar-islamic-remembrance/id123456789';
  
  // Social Media Configuration
  static const String facebookUrl = 'https://facebook.com/athkarapp';
  static const String twitterUrl = 'https://twitter.com/athkarapp';
  static const String instagramUrl = 'https://instagram.com/athkarapp';
  static const String youtubeUrl = 'https://youtube.com/c/athkarapp';
  
  // Helper methods
  static String getApiEndpoint(String endpoint) {
    return '$baseApiUrl/$endpoint';
  }
  
  static String getQuranEndpoint(String endpoint) {
    return '$quranApiUrl/$endpoint';
  }
  
  static String getPrayerTimesEndpoint(String endpoint) {
    return '$prayerTimesApiUrl/$endpoint';
  }
  
  static bool isFeatureEnabled(String feature) {
    switch (feature) {
      case 'community':
        return enableCommunityFeatures;
      case 'enterprise':
        return enableEnterpriseFeatures;
      case 'ai_recommendations':
        return enableAIRecommendations;
      case 'qibla_finder':
        return enableQiblaFinder;
      case 'prayer_times':
        return enablePrayerTimes;
      case 'quran_integration':
        return enableQuranIntegration;
      case 'offline_mode':
        return enableOfflineMode;
      case 'dark_mode':
        return enableDarkMode;
      case 'custom_themes':
        return enableCustomThemes;
      case 'floating_widget':
        return enableFloatingWidget;
      default:
        return false;
    }
  }
  
  static Map<String, dynamic> getEnvironmentInfo() {
    return {
      'app_name': appName,
      'app_version': appVersion,
      'build_number': buildNumber,
      'environment': isProduction ? 'production' : 'development',
      'api_url': baseApiUrl,
      'features_enabled': {
        'community': enableCommunityFeatures,
        'enterprise': enableEnterpriseFeatures,
        'ai_recommendations': enableAIRecommendations,
        'qibla_finder': enableQiblaFinder,
        'prayer_times': enablePrayerTimes,
        'quran_integration': enableQuranIntegration,
      },
    };
  }
}
