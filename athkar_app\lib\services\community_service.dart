import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import '../models/community_models.dart';
import '../database/database_helper.dart';
import '../services/analytics_service.dart';

class CommunityService {
  static final DatabaseHelper _dbHelper = DatabaseHelper();

  
  static Future<void> initialize() async {
    try {
      await _createCommunityTables();
      await _loadUserCommunityData();
    } catch (e) {
      debugPrint('Error initializing community service: $e');
    }
  }

  static Future<void> _createCommunityTables() async {
    // User profiles table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS user_profiles (
        id TEXT PRIMARY KEY,
        user_id TEXT UNIQUE,
        display_name TEXT,
        bio TEXT,
        avatar_url TEXT,
        level INTEGER DEFAULT 1,
        experience_points INTEGER DEFAULT 0,
        total_athkar_completed INTEGER DEFAULT 0,
        streak_days INTEGER DEFAULT 0,
        badges TEXT,
        privacy_settings TEXT,
        created_at INTEGER,
        updated_at INTEGER
      )
    ''');

    // Communities table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS communities (
        id TEXT PRIMARY KEY,
        name TEXT,
        description TEXT,
        category TEXT,
        image_url TEXT,
        member_count INTEGER DEFAULT 0,
        is_private BOOLEAN DEFAULT 0,
        admin_user_id TEXT,
        created_at INTEGER,
        updated_at INTEGER
      )
    ''');

    // Community members table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS community_members (
        id TEXT PRIMARY KEY,
        community_id TEXT,
        user_id TEXT,
        role TEXT DEFAULT 'member',
        joined_at INTEGER,
        is_active BOOLEAN DEFAULT 1
      )
    ''');

    // Challenges table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS challenges (
        id TEXT PRIMARY KEY,
        title TEXT,
        description TEXT,
        type TEXT,
        target_value INTEGER,
        duration_days INTEGER,
        start_date INTEGER,
        end_date INTEGER,
        reward_points INTEGER,
        badge_id TEXT,
        is_global BOOLEAN DEFAULT 0,
        community_id TEXT,
        created_by TEXT,
        participant_count INTEGER DEFAULT 0,
        created_at INTEGER
      )
    ''');

    // Challenge participants table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS challenge_participants (
        id TEXT PRIMARY KEY,
        challenge_id TEXT,
        user_id TEXT,
        current_progress INTEGER DEFAULT 0,
        is_completed BOOLEAN DEFAULT 0,
        joined_at INTEGER,
        completed_at INTEGER
      )
    ''');

    // Leaderboards table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS leaderboards (
        id TEXT PRIMARY KEY,
        type TEXT,
        period TEXT,
        user_id TEXT,
        score INTEGER,
        rank INTEGER,
        metadata TEXT,
        created_at INTEGER
      )
    ''');

    // Social posts table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS social_posts (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        community_id TEXT,
        content TEXT,
        type TEXT,
        athkar_id TEXT,
        image_url TEXT,
        like_count INTEGER DEFAULT 0,
        comment_count INTEGER DEFAULT 0,
        share_count INTEGER DEFAULT 0,
        is_pinned BOOLEAN DEFAULT 0,
        created_at INTEGER,
        updated_at INTEGER
      )
    ''');

    // Post interactions table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS post_interactions (
        id TEXT PRIMARY KEY,
        post_id TEXT,
        user_id TEXT,
        type TEXT,
        created_at INTEGER
      )
    ''');

    // Comments table
    await _dbHelper.execute('''
      CREATE TABLE IF NOT EXISTS comments (
        id TEXT PRIMARY KEY,
        post_id TEXT,
        user_id TEXT,
        content TEXT,
        parent_comment_id TEXT,
        like_count INTEGER DEFAULT 0,
        created_at INTEGER,
        updated_at INTEGER
      )
    ''');
  }

  // User Profile Management
  static Future<UserProfile?> getUserProfile(String userId) async {
    try {
      final results = await _dbHelper.query(
        'user_profiles',
        where: 'user_id = ?',
        whereArgs: [userId],
      );
      
      if (results.isNotEmpty) {
        return UserProfile.fromJson(results.first);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user profile: $e');
      return null;
    }
  }

  static Future<bool> createUserProfile(UserProfile profile) async {
    try {
      await _dbHelper.insert('user_profiles', profile.toJson());
      return true;
    } catch (e) {
      debugPrint('Error creating user profile: $e');
      return false;
    }
  }

  static Future<bool> updateUserProfile(UserProfile profile) async {
    try {
      await _dbHelper.update(
        'user_profiles',
        profile.toJson(),
        where: 'user_id = ?',
        whereArgs: [profile.userId],
      );
      return true;
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      return false;
    }
  }

  // Community Management
  static Future<List<Community>> getPublicCommunities() async {
    try {
      final results = await _dbHelper.query(
        'communities',
        where: 'is_private = ?',
        whereArgs: [0],
        orderBy: 'member_count DESC',
      );
      
      return results.map((json) => Community.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting public communities: $e');
      return [];
    }
  }

  static Future<List<Community>> getUserCommunities(String userId) async {
    try {
      final results = await _dbHelper.rawQuery('''
        SELECT c.* FROM communities c
        INNER JOIN community_members cm ON c.id = cm.community_id
        WHERE cm.user_id = ? AND cm.is_active = 1
        ORDER BY cm.joined_at DESC
      ''', [userId]);
      
      return results.map((json) => Community.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting user communities: $e');
      return [];
    }
  }

  static Future<bool> joinCommunity(String userId, String communityId) async {
    try {
      final member = CommunityMember(
        id: _generateId(),
        communityId: communityId,
        userId: userId,
        role: CommunityRole.member,
        joinedAt: DateTime.now(),
      );
      
      await _dbHelper.insert('community_members', member.toJson());
      
      // Update member count
      await _dbHelper.rawQuery('''
        UPDATE communities 
        SET member_count = member_count + 1 
        WHERE id = ?
      ''', [communityId]);
      
      return true;
    } catch (e) {
      debugPrint('Error joining community: $e');
      return false;
    }
  }

  static Future<bool> leaveCommunity(String userId, String communityId) async {
    try {
      await _dbHelper.update(
        'community_members',
        {'is_active': 0},
        where: 'user_id = ? AND community_id = ?',
        whereArgs: [userId, communityId],
      );
      
      // Update member count
      await _dbHelper.rawQuery('''
        UPDATE communities 
        SET member_count = member_count - 1 
        WHERE id = ?
      ''', [communityId]);
      
      return true;
    } catch (e) {
      debugPrint('Error leaving community: $e');
      return false;
    }
  }

  // Challenge System
  static Future<List<Challenge>> getActiveChallenges({String? communityId}) async {
    try {
      String whereClause = 'end_date > ?';
      List<dynamic> whereArgs = [DateTime.now().millisecondsSinceEpoch];
      
      if (communityId != null) {
        whereClause += ' AND (community_id = ? OR is_global = 1)';
        whereArgs.add(communityId);
      } else {
        whereClause += ' AND is_global = 1';
      }
      
      final results = await _dbHelper.query(
        'challenges',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'created_at DESC',
      );
      
      return results.map((json) => Challenge.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting active challenges: $e');
      return [];
    }
  }

  static Future<bool> joinChallenge(String userId, String challengeId) async {
    try {
      final participant = ChallengeParticipant(
        id: _generateId(),
        challengeId: challengeId,
        userId: userId,
        currentProgress: 0,
        joinedAt: DateTime.now(),
      );
      
      await _dbHelper.insert('challenge_participants', participant.toJson());
      
      // Update participant count
      await _dbHelper.rawQuery('''
        UPDATE challenges 
        SET participant_count = participant_count + 1 
        WHERE id = ?
      ''', [challengeId]);
      
      return true;
    } catch (e) {
      debugPrint('Error joining challenge: $e');
      return false;
    }
  }

  static Future<bool> updateChallengeProgress(
    String userId, 
    String challengeId, 
    int progress,
  ) async {
    try {
      await _dbHelper.update(
        'challenge_participants',
        {'current_progress': progress},
        where: 'user_id = ? AND challenge_id = ?',
        whereArgs: [userId, challengeId],
      );
      
      // Check if challenge is completed
      final challenge = await _getChallengeById(challengeId);
      if (challenge != null && progress >= challenge.targetValue) {
        await _completeChallenge(userId, challengeId);
      }
      
      return true;
    } catch (e) {
      debugPrint('Error updating challenge progress: $e');
      return false;
    }
  }

  static Future<void> _completeChallenge(String userId, String challengeId) async {
    try {
      await _dbHelper.update(
        'challenge_participants',
        {
          'is_completed': 1,
          'completed_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'user_id = ? AND challenge_id = ?',
        whereArgs: [userId, challengeId],
      );
      
      // Award points and badges
      final challenge = await _getChallengeById(challengeId);
      if (challenge != null) {
        await _awardPoints(userId, challenge.rewardPoints);
        if (challenge.badgeId != null) {
          await _awardBadge(userId, challenge.badgeId!);
        }
      }
    } catch (e) {
      debugPrint('Error completing challenge: $e');
    }
  }

  // Leaderboard System
  static Future<List<LeaderboardEntry>> getLeaderboard(
    LeaderboardType type, {
    LeaderboardPeriod period = LeaderboardPeriod.weekly,
    int limit = 50,
  }) async {
    try {
      final results = await _dbHelper.query(
        'leaderboards',
        where: 'type = ? AND period = ?',
        whereArgs: [type.toString(), period.toString()],
        orderBy: 'rank ASC',
        limit: limit,
      );
      
      return results.map((json) => LeaderboardEntry.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting leaderboard: $e');
      return [];
    }
  }

  static Future<int?> getUserRank(
    String userId, 
    LeaderboardType type, {
    LeaderboardPeriod period = LeaderboardPeriod.weekly,
  }) async {
    try {
      final results = await _dbHelper.query(
        'leaderboards',
        where: 'user_id = ? AND type = ? AND period = ?',
        whereArgs: [userId, type.toString(), period.toString()],
      );
      
      if (results.isNotEmpty) {
        return results.first['rank'] as int?;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user rank: $e');
      return null;
    }
  }

  // Social Posts
  static Future<List<SocialPost>> getCommunityPosts(
    String communityId, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final results = await _dbHelper.query(
        'social_posts',
        where: 'community_id = ?',
        whereArgs: [communityId],
        orderBy: 'created_at DESC',
        limit: limit,
        offset: offset,
      );
      
      return results.map((json) => SocialPost.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting community posts: $e');
      return [];
    }
  }

  static Future<bool> createPost(SocialPost post) async {
    try {
      await _dbHelper.insert('social_posts', post.toJson());
      return true;
    } catch (e) {
      debugPrint('Error creating post: $e');
      return false;
    }
  }

  static Future<bool> likePost(String userId, String postId) async {
    try {
      // Check if already liked
      final existing = await _dbHelper.query(
        'post_interactions',
        where: 'user_id = ? AND post_id = ? AND type = ?',
        whereArgs: [userId, postId, 'like'],
      );
      
      if (existing.isNotEmpty) {
        // Unlike
        await _dbHelper.delete(
          'post_interactions',
          where: 'user_id = ? AND post_id = ? AND type = ?',
          whereArgs: [userId, postId, 'like'],
        );
        
        await _dbHelper.rawQuery('''
          UPDATE social_posts 
          SET like_count = like_count - 1 
          WHERE id = ?
        ''', [postId]);
      } else {
        // Like
        final interaction = PostInteraction(
          id: _generateId(),
          postId: postId,
          userId: userId,
          type: InteractionType.like,
          createdAt: DateTime.now(),
        );
        
        await _dbHelper.insert('post_interactions', interaction.toJson());
        
        await _dbHelper.rawQuery('''
          UPDATE social_posts 
          SET like_count = like_count + 1 
          WHERE id = ?
        ''', [postId]);
      }
      
      return true;
    } catch (e) {
      debugPrint('Error liking post: $e');
      return false;
    }
  }

  // Gamification
  static Future<void> _awardPoints(String userId, int points) async {
    try {
      await _dbHelper.rawQuery('''
        UPDATE user_profiles 
        SET experience_points = experience_points + ? 
        WHERE user_id = ?
      ''', [points, userId]);
      
      // Check for level up
      await _checkLevelUp(userId);
    } catch (e) {
      debugPrint('Error awarding points: $e');
    }
  }

  static Future<void> _awardBadge(String userId, String badgeId) async {
    try {
      final profile = await getUserProfile(userId);
      if (profile != null) {
        final badges = List<String>.from(profile.badges);
        if (!badges.contains(badgeId)) {
          badges.add(badgeId);
          await _dbHelper.update(
            'user_profiles',
            {'badges': json.encode(badges)},
            where: 'user_id = ?',
            whereArgs: [userId],
          );
        }
      }
    } catch (e) {
      debugPrint('Error awarding badge: $e');
    }
  }

  static Future<void> _checkLevelUp(String userId) async {
    try {
      final profile = await getUserProfile(userId);
      if (profile != null) {
        final newLevel = _calculateLevel(profile.experiencePoints);
        if (newLevel > profile.level) {
          await _dbHelper.update(
            'user_profiles',
            {'level': newLevel},
            where: 'user_id = ?',
            whereArgs: [userId],
          );
          
          // Award level up badge
          await _awardBadge(userId, 'level_$newLevel');
        }
      }
    } catch (e) {
      debugPrint('Error checking level up: $e');
    }
  }

  static int _calculateLevel(int experiencePoints) {
    // Level formula: level = sqrt(experience_points / 100)
    return math.sqrt(experiencePoints / 100).floor() + 1;
  }

  // Helper methods
  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString() + 
           math.Random().nextInt(1000).toString();
  }

  static Future<Challenge?> _getChallengeById(String challengeId) async {
    try {
      final results = await _dbHelper.query(
        'challenges',
        where: 'id = ?',
        whereArgs: [challengeId],
      );
      
      if (results.isNotEmpty) {
        return Challenge.fromJson(results.first);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting challenge by ID: $e');
      return null;
    }
  }

  static Future<void> _loadUserCommunityData() async {
    // Load user community data from server
    try {
      // In a real app, this would make an API call to load community data
      // For now, store mock data in database
      final mockCommunityData = {
        'groups': ['Islamic Study Group', 'Morning Dhikr Circle', 'Evening Remembrance'],
        'challenges': ['30-Day Dhikr Challenge', 'Weekly Quran Reading'],
        'leaderboard_position': math.Random().nextInt(100) + 1,
        'total_participants': 1000 + math.Random().nextInt(5000),
      };

      // Store in database for later retrieval
      await _dbHelper.insert('community_data', {
        'id': 'user_community',
        'data': json.encode(mockCommunityData),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      // Handle error silently
    }
  }

  // Analytics tracking
  static Future<void> trackCommunityAction(
    String userId,
    String action, {
    Map<String, dynamic>? metadata,
  }) async {
    await AnalyticsService.trackEvent(
      'community_$action',
      parameters: {
        'user_id': userId,
        ...?metadata,
      },
    );
  }
}
